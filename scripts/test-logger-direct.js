#!/usr/bin/env node
/* eslint-env node */

/**
 * Direct test of the Logger functionality
 * This tests the logging system in a Node.js environment to verify the implementation
 */

const fs = require('fs');
const path = require('path');

// Mock expo-file-system for Node.js testing
const FileSystemMock = {
  documentDirectory: path.join(__dirname, '..', 'test-logs') + '/',
  getInfoAsync: async (path) => {
    try {
      const stats = fs.statSync(path);
      return {
        exists: true,
        isDirectory: stats.isDirectory(),
        size: stats.size,
        modificationTime: stats.mtime.getTime() / 1000
      };
    } catch {
      return { exists: false };
    }
  },
  makeDirectoryAsync: async (path, options) => {
    fs.mkdirSync(path, { recursive: options?.intermediates || false });
  },
  writeAsStringAsync: async (path, content) => {
    fs.writeFileSync(path, content);
  },
  readAsStringAsync: async (path) => {
    return fs.readFileSync(path, 'utf8');
  }
};

// Create a modified version of the Logger for testing
function createTestLogger() {
  // Mock the expo-file-system import
  const Logger = eval(`
    const FileSystem = ${JSON.stringify(FileSystemMock, (key, value) => {
      if (typeof value === 'function') {
        return value.toString();
      }
      return value;
    })};
    
    // Convert string functions back to actual functions
    Object.keys(FileSystem).forEach(key => {
      if (typeof FileSystem[key] === 'string' && FileSystem[key].includes('function')) {
        FileSystem[key] = eval('(' + FileSystem[key] + ')');
      }
    });

    ${fs.readFileSync(path.join(__dirname, '..', 'src', 'shared', 'utils', 'Logger.ts'), 'utf8')
      .replace('import * as FileSystem from \'expo-file-system\';', '')
      .replace('export class Logger', 'class Logger')
    }
    
    Logger;
  `);

  return Logger;
}

async function testLogging() {
  console.log('🧪 Direct Logger Test');
  console.log('====================');

  try {
    // Create test directory
    const testLogDir = path.join(__dirname, '..', 'test-logs');
    if (fs.existsSync(testLogDir)) {
      fs.rmSync(testLogDir, { recursive: true });
    }
    console.log('📁 Created clean test directory');

    // Get Logger class
    const Logger = createTestLogger();
    const logger = Logger.getInstance();

    // Configure for testing
    logger.configure({
      level: 'debug',
      enableConsole: true,
      enableFileOutput: true,
      maxLogEntries: 1000,
      enableAnonymousReporting: false
    });

    console.log('⚙️  Logger configured');

    // Generate test logs
    console.log('📝 Generating test logs...');
    logger.debug('Test debug message', 'test', { testId: 1 });
    logger.info('Test info message', 'test', { testId: 2 });
    logger.warn('Test warning message', 'test', { testId: 3 });
    logger.error('Test error message', 'test', { testId: 4, error: 'sample error' });

    // Wait a moment then flush
    console.log('⏳ Waiting for async operations...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Manual flush
    console.log('🚀 Forcing manual flush...');
    const logPath = await logger.manualFlush();
    console.log(`📍 Expected log path: ${logPath}`);

    // Wait for file operations
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check results
    console.log('\\n📊 Results:');
    const logDir = path.join(testLogDir, 'logs', 'llm_debug');
    
    if (fs.existsSync(logDir)) {
      console.log('✅ Log directory created successfully');
      
      const files = fs.readdirSync(logDir);
      console.log(`📄 Found ${files.length} log files:`);
      
      files.forEach(file => {
        const filePath = path.join(logDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\\n').filter(line => line.trim());
        
        console.log(`   - ${file}: ${content.length} bytes, ${lines.length} log entries`);
        
        // Show first few log entries
        lines.slice(0, 3).forEach((line, i) => {
          try {
            const parsed = JSON.parse(line);
            console.log(`      ${i + 1}. [${parsed.level.toUpperCase()}] ${parsed.message}`);
          } catch {
            console.log(`      ${i + 1}. ${line.substring(0, 50)}...`);
          }
        });
      });
    } else {
      console.log('❌ Log directory was not created');
    }

    // Cleanup
    logger.cleanup();
    console.log('\\n✅ Test completed successfully');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run test
testLogging().catch(console.error);