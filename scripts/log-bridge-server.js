#!/usr/bin/env node
/* eslint-env node */

/**
 * Log Bridge Server
 * Receives logs from React Native app via HTTP and writes them to project directory
 * This allows <PERSON> to access logs from logs/llm_debug/ in the project root
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8083;
const LOG_DIR = path.join(__dirname, '..', 'logs', 'llm_debug');

// Ensure log directory exists
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
  console.log('📁 Created log directory:', LOG_DIR);
}

// Get today's log file
function getTodayLogFile() {
  const today = new Date().toISOString().split('T')[0];
  return path.join(LOG_DIR, `app-${today}.log`);
}

// Write log entry to file
function writeLogEntry(logEntry) {
  const logFile = getTodayLogFile();
  const timestamp = new Date().toISOString();
  
  try {
    const formattedLog = JSON.stringify({
      timestamp,
      ...logEntry
    }) + '\n';
    
    fs.appendFileSync(logFile, formattedLog);
    return true;
  } catch (error) {
    console.error('❌ Failed to write log:', error);
    return false;
  }
}

// HTTP Server
const server = http.createServer((req, res) => {
  // Enable CORS for React Native
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (req.method === 'POST' && req.url === '/log') {
    let body = '';
    
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const logEntry = JSON.parse(body);
        const success = writeLogEntry(logEntry);
        
        if (success) {
          console.log(`📝 [${logEntry.level?.toUpperCase()}] ${logEntry.message}`);
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: true }));
        } else {
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: false, error: 'Failed to write log' }));
        }
      } catch (error) {
        console.error('❌ Invalid log data:', error);
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
      }
    });
  } else {
    res.writeHead(404);
    res.end('Not Found');
  }
});

server.listen(PORT, '127.0.0.1', () => {
  console.log('🌉 Log Bridge Server started');
  console.log(`📡 Listening on http://localhost:${PORT}/log`);
  console.log(`📁 Writing logs to: ${LOG_DIR}`);
  console.log('🔄 Ready to receive logs from React Native app');
  console.log('');
  console.log('To test: curl -X POST http://localhost:8083/log -H "Content-Type: application/json" -d \'{"level":"info","message":"test","category":"test"}\'');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Log Bridge Server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});