#!/usr/bin/env node
/* eslint-env node */
/**
 * Migration Registry Generator
 * 
 * Automatically generates the migration registry by scanning the migrations directory.
 * Run this as a build step: npm run generate-migrations
 */

const fs = require('fs');
const path = require('path');

const MIGRATIONS_DIR = path.join(__dirname, '../src/data/migrations');
const REGISTRY_FILE = path.join(MIGRATIONS_DIR, 'migrations.registry.ts');

function generateMigrationRegistry() {
  console.log('🔍 Scanning for migration files...');
  
  try {
    const files = fs.readdirSync(MIGRATIONS_DIR);
    
    const migrationFiles = files
      .filter(file => /^\d{3}_[a-z_]+\.ts$/.test(file) && !file.includes('registry'))
      .map(file => {
        const match = file.match(/^(\d{3})_([a-z_]+)\.ts$/);
        if (!match) return null;
        
        const [, versionStr, name] = match;
        const version = parseInt(versionStr, 10);
        
        return {
          file,
          version,
          name,
          importName: 'migration' + versionStr,
          importPath: './' + versionStr + '_' + name
        };
      })
      .filter(Boolean)
      .sort((a, b) => a.version - b.version);

    if (migrationFiles.length === 0) {
      console.warn('⚠️ No migration files found!');
      return;
    }

    console.log('✅ Found ' + migrationFiles.length + ' migration files');
    
    const registryContent = generateRegistryContent(migrationFiles);
    fs.writeFileSync(REGISTRY_FILE, registryContent, 'utf8');
    
    console.log('🎉 Generated migration registry: ' + REGISTRY_FILE);
    console.log('📝 Registered migrations: ' + migrationFiles.map(m => 'v' + m.version).join(', '));
    
  } catch (error) {
    console.error('❌ Failed to generate migration registry:', error);
    process.exit(1);
  }
}

function generateRegistryContent(migrationFiles) {
  const imports = migrationFiles
    .map(m => "import { migration as " + m.importName + " } from '" + m.importPath + "';")
    .join('\n');

  const registryEntries = migrationFiles
    .map(m => '  ' + m.version + ': ' + m.importName)
    .join(',\n');

  const latestVersion = Math.max(...migrationFiles.map(m => m.version));
  const totalMigrations = migrationFiles.length;
  const generatedAt = new Date().toISOString();

  return `/**
 * Migration Registry (Auto-generated)
 * 
 * DO NOT EDIT THIS FILE MANUALLY!
 * Generated by: npm run generate-migrations
 * Generated at: ${generatedAt}
 * 
 * This file is automatically generated from migration files in the migrations directory.
 * To add a new migration, create a new file following the pattern: 001_migration_name.ts
 * Then run: npm run generate-migrations
 */

import type { Migration } from '../../shared/types/migration';

// Auto-generated imports
${imports}

/**
 * Auto-generated registry of all migrations
 */
export const MIGRATION_REGISTRY: Record<number, Migration> = {
${registryEntries}
};

/**
 * Get all registered migrations sorted by version
 */
export function getAllMigrations(): Migration[] {
  return Object.values(MIGRATION_REGISTRY).sort((a, b) => a.version - b.version);
}

/**
 * Get migration by version
 */
export function getMigrationByVersion(version: number): Migration | null {
  return MIGRATION_REGISTRY[version] || null;
}

/**
 * Get latest migration version
 */
export function getLatestVersion(): number {
  return ${latestVersion};
}

/**
 * Get migration statistics (auto-generated)
 */
export function getRegistryStats() {
  return {
    totalMigrations: ${totalMigrations},
    latestVersion: ${latestVersion},
    generatedAt: '${generatedAt}',
    migrations: [
${migrationFiles.map(m => '      { version: ' + m.version + ", name: '" + m.name + "', file: '" + m.file + "' }").join(',\n')}
    ]
  };
}

/**
 * Validate registry integrity
 */
export function validateRegistry(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const versions = Object.keys(MIGRATION_REGISTRY).map(Number).sort((a, b) => a - b);
  const migrations = Object.values(MIGRATION_REGISTRY);

  // Check for sequential versions
  for (let i = 1; i <= versions.length; i++) {
    if (!versions.includes(i)) {
      errors.push(\`Missing migration version \${i}\`);
    }
  }

  // Check for version mismatches
  for (const migration of migrations) {
    if (MIGRATION_REGISTRY[migration.version] !== migration) {
      errors.push(\`Version mismatch for migration \${migration.version}\`);
    }
  }

  // Check for duplicate names
  const names = migrations.map(m => m.name);
  const duplicateNames = names.filter((name, index) => names.indexOf(name) !== index);
  if (duplicateNames.length > 0) {
    warnings.push(\`Duplicate migration names: \${duplicateNames.join(', ')}\`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
`;
}

// Support watch mode for development
if (process.argv.includes('--watch')) {
  generateMigrationRegistry();
  console.log('👀 Watching migrations directory for changes...');
  
  fs.watch(MIGRATIONS_DIR, { recursive: false }, (eventType, filename) => {
    if (filename && /^\d{3}_[a-z_]+\.ts$/.test(filename)) {
      console.log('📝 Migration file changed: ' + filename);
      console.log('🔄 Regenerating registry...');
      generateMigrationRegistry();
    }
  });
} else {
  generateMigrationRegistry();
}