#!/bin/bash

# Local CI/CD Pipeline Test Script
# Simulates the GitHub Actions pipeline locally for development

set -e

echo "🚀 FinVibe Local CI/CD Pipeline Test"
echo "===================================="

# Create logs directory
mkdir -p logs/llm_debug

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a logs/llm_debug/local-ci-$(date +%Y%m%d-%H%M%S).log
}

# Function to check step result
check_result() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1 passed${NC}"
        log "$1 passed"
    else
        echo -e "${RED}❌ $1 failed${NC}"
        log "$1 failed"
        exit 1
    fi
}

echo "Starting CI/CD pipeline simulation..."
log "Local CI/CD pipeline started"

# Step 1: Dependencies
echo -e "\n${YELLOW}📦 Installing dependencies...${NC}"
npm ci
check_result "Dependency installation"

# Step 2: Linting
echo -e "\n${YELLOW}🔍 Running ESLint...${NC}"
npm run lint 2>&1 | tee logs/llm_debug/local-lint-$(date +%Y%m%d-%H%M%S).log
check_result "ESLint"

# Step 3: Type checking
echo -e "\n${YELLOW}🔧 Running TypeScript check...${NC}"
npm run type-check 2>&1 | tee logs/llm_debug/local-typecheck-$(date +%Y%m%d-%H%M%S).log
check_result "TypeScript check"

# Step 4: Unit tests
echo -e "\n${YELLOW}🧪 Running unit tests...${NC}"
if [ -d "src/__tests__/unit" ]; then
    npm test -- --testPathPattern="src/__tests__/unit" --coverage --verbose 2>&1 | tee logs/llm_debug/local-unit-tests-$(date +%Y%m%d-%H%M%S).log
    check_result "Unit tests"
else
    echo -e "${YELLOW}⚠️  No unit tests found, skipping${NC}"
    log "Unit tests skipped - no test files found"
fi

# Step 5: Integration tests  
echo -e "\n${YELLOW}🔗 Running integration tests...${NC}"
if [ -d "src/__tests__/integration" ]; then
    npm test -- --testPathPattern="src/__tests__/integration" --verbose 2>&1 | tee logs/llm_debug/local-integration-tests-$(date +%Y%m%d-%H%M%S).log
    check_result "Integration tests"
else
    echo -e "${YELLOW}⚠️  No integration tests found, skipping${NC}"
    log "Integration tests skipped - no test files found"
fi

# Step 6: Performance tests
echo -e "\n${YELLOW}⚡ Running performance tests...${NC}"
if [ -d "src/__tests__/performance" ]; then
    npm test -- --testPathPattern="src/__tests__/performance" --verbose 2>&1 | tee logs/llm_debug/local-performance-tests-$(date +%Y%m%d-%H%M%S).log
    check_result "Performance tests"
else
    echo -e "${YELLOW}⚠️  No performance tests found, skipping${NC}"
    log "Performance tests skipped - no test files found"
fi

# Step 7: Build
echo -e "\n${YELLOW}🏗️  Building application...${NC}"
START_TIME=$(date +%s)
npm run build 2>&1 | tee logs/llm_debug/local-build-$(date +%Y%m%d-%H%M%S).log
if [ $? -eq 0 ]; then
    END_TIME=$(date +%s)
    BUILD_TIME=$((END_TIME - START_TIME))
    echo -e "${GREEN}✅ Build completed in ${BUILD_TIME}s${NC}"
    log "Build completed in ${BUILD_TIME}s"
    
    # Check build time threshold
    if [ $BUILD_TIME -gt 10 ]; then
        echo -e "${YELLOW}⚠️  Build time exceeded 10s threshold${NC}"
        log "Build time warning: ${BUILD_TIME}s > 10s"
    fi
else
    echo -e "${RED}❌ Build failed${NC}"
    log "Build failed"
    
    # Simulate Claude Code analysis on build failure
    echo -e "\n${YELLOW}🤖 Simulating Claude Code analysis...${NC}"
    echo "Build failure detected - analyzing logs for patterns..." | tee logs/llm_debug/local-claude-analysis-$(date +%Y%m%d-%H%M%S).log
    
    # Aggregate logs for analysis
    cat logs/llm_debug/local-*.log > logs/llm_debug/combined-failure-analysis.log
    
    # Analyze for common patterns
    ERROR_COUNT=$(grep -c "ERROR\|error\|Error" logs/llm_debug/combined-failure-analysis.log || echo "0")
    TYPESCRIPT_ERRORS=$(grep -c "TS[0-9]" logs/llm_debug/combined-failure-analysis.log || echo "0")
    MODULE_ERRORS=$(grep -c "Cannot resolve module\|Module not found" logs/llm_debug/combined-failure-analysis.log || echo "0")
    
    echo "Analysis results:" | tee -a logs/llm_debug/local-claude-analysis-$(date +%Y%m%d-%H%M%S).log
    echo "- Total errors: $ERROR_COUNT" | tee -a logs/llm_debug/local-claude-analysis-$(date +%Y%m%d-%H%M%S).log
    echo "- TypeScript errors: $TYPESCRIPT_ERRORS" | tee -a logs/llm_debug/local-claude-analysis-$(date +%Y%m%d-%H%M%S).log
    echo "- Module resolution errors: $MODULE_ERRORS" | tee -a logs/llm_debug/local-claude-analysis-$(date +%Y%m%d-%H%M%S).log
    
    if [ $TYPESCRIPT_ERRORS -gt 0 ]; then
        echo "🔍 Recommendation: Review TypeScript configuration and type errors" | tee -a logs/llm_debug/local-claude-analysis-$(date +%Y%m%d-%H%M%S).log
    fi
    
    if [ $MODULE_ERRORS -gt 0 ]; then
        echo "🔍 Recommendation: Check module imports and dependencies" | tee -a logs/llm_debug/local-claude-analysis-$(date +%Y%m%d-%H%M%S).log
    fi
    
    echo -e "${RED}❌ Pipeline failed - check logs for details${NC}"
    exit 1
fi

# Step 8: Log aggregation simulation
echo -e "\n${YELLOW}📋 Aggregating logs for Claude Code...${NC}"

# Create combined log file
cat > logs/llm_debug/pipeline-summary.log << EOF
=== FinVibe Local CI/CD Pipeline Summary ===
Timestamp: $(date -Iseconds)
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "N/A")
Git Branch: $(git branch --show-current 2>/dev/null || echo "N/A")

=== Pipeline Steps ===
1. Dependencies: ✅ Passed
2. ESLint: ✅ Passed  
3. TypeScript: ✅ Passed
4. Unit Tests: ✅ Passed
5. Integration Tests: $([ -d "src/__tests__/integration" ] && echo "✅ Passed" || echo "⚠️ Skipped")
6. Performance Tests: $([ -d "src/__tests__/performance" ] && echo "✅ Passed" || echo "⚠️ Skipped")
7. Build: ✅ Passed (${BUILD_TIME}s)

=== Log Files Generated ===
$(ls -la logs/llm_debug/local-* | awk '{print "- " $9 " (" $5 " bytes)"}')

=== Claude Code Integration Notes ===
- Progressive batching: Enabled (50 lines/batch)
- Log retention: 10 minutes
- Auto-analysis: Enabled on failures
- Build hooks: Configured for automated triggers

EOF

# Progressive batching simulation
echo -e "\n${YELLOW}🔄 Simulating progressive log batching...${NC}"
COMBINED_LOG="logs/llm_debug/pipeline-summary.log"
TOTAL_LINES=$(wc -l < "$COMBINED_LOG")
BATCH_SIZE=50
MAX_BATCHES=20

echo "Total log lines: $TOTAL_LINES"
echo "Batch configuration: $BATCH_SIZE lines per batch, max $MAX_BATCHES batches"

BATCH_COUNT=0
while [ $BATCH_COUNT -lt $MAX_BATCHES ] && [ $((BATCH_COUNT * BATCH_SIZE)) -lt $TOTAL_LINES ]; do
    BATCH_COUNT=$((BATCH_COUNT + 1))
    START_LINE=$(((BATCH_COUNT - 1) * BATCH_SIZE + 1))
    END_LINE=$((BATCH_COUNT * BATCH_SIZE))
    
    echo "Processing batch $BATCH_COUNT (lines $START_LINE-$END_LINE)"
    sed -n "${START_LINE},${END_LINE}p" "$COMBINED_LOG" > "logs/llm_debug/batch-${BATCH_COUNT}.log"
done

echo "✅ Created $BATCH_COUNT batches for Claude Code analysis"

# Final summary
echo -e "\n${GREEN}🎉 Local CI/CD Pipeline Completed Successfully!${NC}"
echo "===================================================="
log "Local CI/CD pipeline completed successfully"

echo -e "\n📊 Summary:"
echo "- All checks passed ✅"
echo "- Build time: ${BUILD_TIME}s"
echo "- Log files: $(ls logs/llm_debug/local-* | wc -l) files generated"
echo "- Claude Code batches: $BATCH_COUNT batches ready for analysis"

echo -e "\n📁 Generated artifacts:"
ls -la logs/llm_debug/

echo -e "\n${YELLOW}💡 Next steps:${NC}"
echo "1. Review logs in logs/llm_debug/ directory"
echo "2. Commit changes to trigger GitHub Actions pipeline"  
echo "3. Monitor build performance over time"
echo "4. Use /analyze-logs command for Claude Code analysis if needed"

log "Local CI/CD pipeline summary completed"