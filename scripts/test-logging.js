#!/usr/bin/env node
/* eslint-env node */

/**
 * Test script to monitor log file creation in iOS simulator
 * This script monitors the iOS simulator's Documents directories for log files
 */

const fs = require('fs');
const path = require('path');
// const { execSync } = require('child_process'); // Not used in current implementation

// iOS Simulator device ID (from our previous findings)
const DEVICE_ID = '7CE8CAA9-3458-44B7-9F53-E7686951D66B';

// Application data directories we found earlier
const APP_DIRECTORIES = [
  `/Users/<USER>/Library/Developer/CoreSimulator/Devices/${DEVICE_ID}/data/Containers/Data/Application/E239D59E-A890-42F7-9455-BE9908C27FE4/Documents`,
  `/Users/<USER>/Library/Developer/CoreSimulator/Devices/${DEVICE_ID}/data/Containers/Data/Application/B3E375FE-9557-470C-9799-12EF6C02A976/Documents`
];

console.log('🔍 FinVibe Logging Test Script');
console.log('================================');
console.log(`Device ID: ${DEVICE_ID}`);
console.log(`User: ${process.env.USER}`);
console.log('');

// Function to check for log directories and files
function checkLogFiles() {
  console.log('📁 Checking application directories...');
  
  APP_DIRECTORIES.forEach((appDir, index) => {
    console.log(`\n${index + 1}. ${appDir}`);
    
    if (!fs.existsSync(appDir)) {
      console.log('   ❌ Directory does not exist');
      return;
    }
    
    // Check for logs/llm_debug directory
    const logDebugDir = path.join(appDir, 'logs', 'llm_debug');
    console.log(`   📂 Checking: ${logDebugDir}`);
    
    if (fs.existsSync(logDebugDir)) {
      console.log('   ✅ logs/llm_debug directory exists!');
      
      // List files in the directory
      const files = fs.readdirSync(logDebugDir);
      if (files.length > 0) {
        console.log('   📝 Log files found:');
        files.forEach(file => {
          const filePath = path.join(logDebugDir, file);
          const stats = fs.statSync(filePath);
          console.log(`      - ${file} (${stats.size} bytes, ${stats.mtime.toISOString()})`);
          
          // Show first few lines of the log file
          if (stats.size > 0) {
            try {
              const content = fs.readFileSync(filePath, 'utf8');
              const lines = content.split('\n').slice(0, 3);
              console.log('        Preview:');
              lines.forEach(line => {
                if (line.trim()) {
                  console.log(`        ${line.substring(0, 80)}...`);
                }
              });
            } catch (error) {
              console.log(`        Error reading file: ${error.message}`);
            }
          }
        });
      } else {
        console.log('   📝 No log files found in directory');
      }
    } else {
      console.log('   ❌ logs/llm_debug directory does not exist');
      
      // Check what directories do exist
      try {
        const contents = fs.readdirSync(appDir);
        console.log('   📋 Contents of Documents directory:');
        contents.forEach(item => {
          const itemPath = path.join(appDir, item);
          const isDir = fs.statSync(itemPath).isDirectory();
          console.log(`      ${isDir ? '📁' : '📄'} ${item}`);
        });
      } catch (error) {
        console.log(`   ❌ Could not read directory: ${error.message}`);
      }
    }
  });
}

// Function to watch for file changes
function watchLogFiles() {
  console.log('\n👁️  Starting file watcher (press Ctrl+C to stop)...');
  
  APP_DIRECTORIES.forEach(appDir => {
    if (fs.existsSync(appDir)) {
      fs.watch(appDir, { recursive: true }, (eventType, filename) => {
        if (filename && filename.includes('log')) {
          console.log(`\n🔔 File change detected: ${eventType} - ${filename}`);
          setTimeout(() => checkLogFiles(), 1000);
        }
      });
    }
  });
}

// Main execution
console.log('Running initial check...\n');
checkLogFiles();

// Ask if user wants to start watcher
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('\n');
rl.question('Start file watcher? (y/n): ', (answer) => {
  if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
    watchLogFiles();
    console.log('File watcher started. Make some logs in the app and they should appear here.');
  } else {
    console.log('Exiting...');
    rl.close();
  }
});