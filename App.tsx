import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, Text, ActivityIndicator, StyleSheet, SafeAreaView, ScrollView } from 'react-native';
import { AccountManagementScreen } from './src/presentation/screens/accounts/AccountManagementScreen';
import { TransactionManagementScreen } from './src/presentation/screens/transactions/TransactionManagementScreen';
import { FeedbackScreen } from './src/presentation/screens/feedback/FeedbackScreen';

import { MenuScreen } from './src/presentation/screens/menu/MenuScreen';
import { DatabaseService } from './src/data/database/DatabaseService';
import { ThemeProvider, useTheme } from './src/shared/theme/ThemeProvider';
import { Card, Button } from './src/presentation/components/core';
import { FallbackIcon } from './src/presentation/components/common/FallbackIcon';

import { BottomTabNavigation, TabScreen } from './src/presentation/components/common/BottomTabNavigation';
import { useCategoryStore } from './src/shared/stores/categoryStore';
import { useAccountStore, setupEventListeners } from './src/shared/stores/accountStore';
import { TransactionService } from './src/business/services/TransactionService';
import { TransactionEntryForm } from './src/presentation/components/transactions/TransactionEntryForm';
import { Modal, TouchableWithoutFeedback } from 'react-native';
import { ExpensesPieChart } from './src/presentation/components/dashboard/ExpensesPieChart';
import { TransactionsList } from './src/presentation/components/dashboard/TransactionsList';

// Create singleton service instances to prevent repeated creation
const transactionService = new TransactionService();

const AppContent: React.FC = () => {
  const theme = useTheme();
  const [isDbReady, setIsDbReady] = useState(false);
  const [dbError, setDbError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabScreen>('dashboard');

  const [showCreateTransactionModal, setShowCreateTransactionModal] = useState(false);
  const [dashboardStats, setDashboardStats] = useState({
    totalBalance: 0,
    monthlyBalance: 0,
    recentTransactions: [] as any[],
    expensesByCategory: [] as any[],
  });
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [isLoadingDashboard, setIsLoadingDashboard] = useState(false);
  const [lastDashboardLoad, setLastDashboardLoad] = useState<number>(0);

  const { accounts, loadAccounts, lastUpdated: accountsLastUpdated, _forceUpdate } = useAccountStore();
  const { categories, loadCategories } = useCategoryStore();

  // Load dashboard data
  const loadDashboardData = useCallback(async (force: boolean = false) => {
    if (isLoadingDashboard) return; // Prevent concurrent calls
    
    // Debounce dashboard loading - don't reload if loaded within last 30 seconds (unless forced)
    const now = Date.now();
    if (!force && now - lastDashboardLoad < 30000) {
      return;
    }
    
    try {
      setIsLoadingDashboard(true);
      
      // Load accounts to get total balance
      await loadAccounts();
      
      // Load categories for recent transactions display
      await loadCategories();
      
      // Load transactions for monthly stats and recent activity
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      // Get all transactions
      const transactionResult = await transactionService.getTransactions();
      const allTransactions = transactionResult.transactions || [];
      
      // Filter for this month
      const monthlyTransactions = allTransactions.filter((transaction: any) => {
        const transactionDate = new Date(transaction.transaction_date);
        return transactionDate >= firstDayOfMonth && transactionDate <= lastDayOfMonth;
      });

      // Calculate monthly balance
      let monthlyIncome = 0;
      let monthlyExpenses = 0;
      
      monthlyTransactions.forEach((transaction: any) => {
        if (transaction.transaction_type === 'income') {
          monthlyIncome += transaction.amount;
        } else if (transaction.transaction_type === 'expense') {
          monthlyExpenses += transaction.amount;
        }
      });

      const monthlyBalance = monthlyIncome - monthlyExpenses;

      // Get recent transactions (last 5)
      const recentTransactions = allTransactions
        .sort((a: any, b: any) => new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime())
        .slice(0, 5);

      // Calculate expenses by category for pie chart - use current categories from store
      const currentCategories = categories.length > 0 ? categories : [];
      const expenseTransactions = monthlyTransactions.filter((t: any) => t.transaction_type === 'expense');
      const expensesByCategory = new Map();
      
      expenseTransactions.forEach((transaction: any) => {
        const categoryId = transaction.category_id || 0; // 0 for uncategorized
        const existing = expensesByCategory.get(categoryId) || { amount: 0, count: 0 };
        expensesByCategory.set(categoryId, {
          amount: existing.amount + transaction.amount,
          count: existing.count + 1
        });
      });

      const totalExpenses = Array.from(expensesByCategory.values()).reduce((sum, cat) => sum + cat.amount, 0);
      
      const expenseCategoryData = Array.from(expensesByCategory.entries()).map(([categoryId, data]) => {
        const category = currentCategories.find(cat => cat.id === categoryId) || {
          name: 'Uncategorized',
          icon_name: 'help-circle',
          color_code: '#999'
        };
        
        return {
          category_id: categoryId,
          category_name: category.name,
          category_icon: category.icon_name,
          category_color: category.color_code,
          total_amount: data.amount,
          percentage: totalExpenses > 0 ? (data.amount / totalExpenses) * 100 : 0
        };
      }).sort((a, b) => b.total_amount - a.total_amount);

      setDashboardStats(prev => ({
        ...prev,
        monthlyBalance,
        recentTransactions,
        expensesByCategory: expenseCategoryData,
      }));
      
      setLastDashboardLoad(Date.now());
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setIsLoadingDashboard(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadAccounts, loadCategories, isLoadingDashboard, lastDashboardLoad]);

  useEffect(() => {
    const initializeDatabase = async () => {
      try {
        const databaseService = DatabaseService.getInstance();
        await databaseService.initialize();
        setIsDbReady(true);

        // CRITICAL FIX: Ensure event listeners are set up
        setupEventListeners();

        // Load dashboard data after database is ready
        await loadDashboardData();
      } catch (error) {
        console.error('Failed to initialize database:', error);
        setDbError(error instanceof Error ? error.message : 'Database initialization failed');
      }
    };

    initializeDatabase();
  }, [loadDashboardData]);

  // Calculate total balance when accounts change
  useEffect(() => {
    if (accounts.length > 0) {
      const totalBalance = accounts
        .filter(account => account.is_active)
        .reduce((sum, account) => {
          // Credit cards and loans are liabilities - subtract from total
          if (account.type === 'credit' || account.type === 'loan') {
            return sum - account.balance;
          }
          // Assets (savings, checking, investment) add to total
          return sum + account.balance;
        }, 0);

      setDashboardStats(prev => ({
        ...prev,
        totalBalance,
      }));
    }
  }, [accounts]);

  // CRITICAL FIX: Refresh dashboard when account data is updated
  // This ensures dashboard reflects balance changes from transactions
  useEffect(() => {
    if ((accountsLastUpdated || _forceUpdate) && activeTab === 'dashboard') {
      // Force refresh dashboard data when accounts are updated
      console.log('🔄 Dashboard refresh triggered by account update');
      loadDashboardData(true);
    }
  }, [accountsLastUpdated, _forceUpdate, activeTab, loadDashboardData]);

  // Refresh dashboard data when switching to dashboard tab
  useEffect(() => {
    if (activeTab === 'dashboard' && isDbReady) {
      loadDashboardData();
    }
  }, [activeTab, loadDashboardData, isDbReady]);

  // Utility functions for recent transactions display
  const getCategoryInfo = (categoryId: number | null): { name: string; icon: string; color: string } => {
    if (!categoryId) {
      return { name: 'Uncategorized', icon: '', color: '#999' };
    }
    
    const category = categories.find(cat => cat.id === categoryId);
    return {
      name: category?.name || 'Unknown',
      icon: category?.icon_name || '',
      color: category?.color_code || '#999'
    };
  };

  const getAccountName = (accountId: number): string => {
    const account = accounts.find(acc => acc.id === accountId);
    return account?.name || 'Unknown Account';
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-IN', {
        day: '2-digit',
        month: 'short'
      });
    }
  };

  // Transaction modal handlers
  const handleCreateTransactionSuccess = () => {
    setShowCreateTransactionModal(false);
    loadDashboardData(true); // Force refresh dashboard data
  };

  const handleCreateTransactionCancel = () => {
    setShowCreateTransactionModal(false);
  };

  // Pie chart category selection handler
  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategoryId(categoryId);
  };

  // Memoized filtered transactions to prevent unnecessary recalculations
  const filteredTransactions = useMemo(() => {
    if (!selectedCategoryId) {
      return dashboardStats.recentTransactions;
    }
    
    return dashboardStats.recentTransactions.filter((transaction: any) => 
      transaction.category_id === selectedCategoryId
    );
  }, [selectedCategoryId, dashboardStats.recentTransactions]);

  // Memoized transactions title to prevent unnecessary recalculations
  const transactionsTitle = useMemo(() => {
    if (!selectedCategoryId) {
      return 'Recent Activity';
    }
    
    const category = categories.find(cat => cat.id === selectedCategoryId);
    return `${category?.name || 'Selected Category'} Transactions`;
  }, [selectedCategoryId, categories]);

  // Tab navigation handler
  const handleTabPress = (tab: TabScreen) => {
    if (tab === 'add') {
      // Show the create transaction modal
      setShowCreateTransactionModal(true);
      return;
    }



    if (tab === 'menu') {
      setActiveTab('menu');
      return;
    }

    setActiveTab(tab);
  };

  // Side menu items configuration
  const sideMenuItems = [
    {
      id: 'feedback',
      title: 'Feedback & Support',
      icon: 'message-circle',
      onPress: () => {
        setActiveTab('feedback');
      },
    },
    {
      id: 'community',
      title: 'Community',
      icon: 'users',
      onPress: () => {
        // TODO: Navigate to community screen
        console.log('Community feature coming soon');
      },
      badge: 'Soon',
      disabled: true,
    },
    {
      id: 'export',
      title: 'Export Data',
      icon: 'download',
      onPress: () => {
        // TODO: Export data functionality
        console.log('Export feature coming soon');
      },
      disabled: true,
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'settings',
      onPress: () => {
        // TODO: Navigate to settings screen
        console.log('Settings screen coming soon');
      },
      disabled: true,
    },
    {
      id: 'backup',
      title: 'Backup & Sync',
      icon: 'cloud',
      onPress: () => {
        // TODO: Backup functionality
        console.log('Backup feature coming soon');
      },
      disabled: true,
    },
    {
      id: 'about',
      title: 'About FinVibe',
      icon: 'info',
      onPress: () => {
        // TODO: About screen
        console.log('About screen coming soon');
      },
      disabled: true,
    },
  ];

  if (dbError) {
    return (
      <View style={[styles.centerContainer, { backgroundColor: theme.colors.surface }]}>
        <StatusBar style="auto" />
        <Card padding="lg" style={{ alignItems: 'center', maxWidth: 300 }}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>Database Error</Text>
          <Text style={[styles.errorDetail, { color: theme.colors.textSecondary }]}>{dbError}</Text>
        </Card>
      </View>
    );
  }

  if (!isDbReady) {
    return (
      <View style={[styles.centerContainer, { backgroundColor: theme.colors.surface }]}>
        <StatusBar style="auto" />
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>Initializing Database...</Text>
      </View>
    );
  }

  const renderDashboard = () => (
    <ScrollView style={[styles.screenContainer, { backgroundColor: theme.colors.surface }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.background, paddingTop: theme.spacing.lg }]}>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
          <View>
            <Text style={[styles.greeting, { color: theme.colors.text }]}>Hi there! 👋</Text>
            <Text style={[styles.date, { color: theme.colors.textSecondary }]}>
              Today • {new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            </Text>
          </View>
          <View style={styles.localOnlyBadge}>
            <FallbackIcon 
              icon="cloud-offline"
              name="Local Only"
              size={16}
              style={{ color: theme.colors.textSecondary }}
            />
            <Text style={[styles.localOnlyText, { color: theme.colors.textSecondary }]}>Local only</Text>
          </View>
        </View>
      </View>

      {/* Quick Stats Cards */}
      <View style={[styles.quickStats, { paddingHorizontal: theme.spacing.md }]}>
        <Card variant="elevated" style={styles.statCard}>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Total Balance</Text>
          <Text style={[styles.statValue, { color: theme.colors.text }]}>
            ₹{dashboardStats.totalBalance.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </Text>
        </Card>
        <Card variant="elevated" style={styles.statCard}>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>This Month</Text>
          <Text style={[styles.statValue, { 
            color: dashboardStats.monthlyBalance >= 0 ? theme.colors.success : theme.colors.expense 
          }]}>
            {dashboardStats.monthlyBalance >= 0 ? '+' : ''}₹{Math.abs(dashboardStats.monthlyBalance).toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </Text>
        </Card>
      </View>

      {/* Quick Actions */}
      <View style={[styles.quickActions, { paddingHorizontal: theme.spacing.md }]}>
        <Button
          title="Add Transaction"
          variant="primary"
          size="lg"
          fullWidth
          onPress={() => setActiveTab('transactions')}
          style={{ marginBottom: theme.spacing.sm }}
        />
        <View style={{ flexDirection: 'row', gap: theme.spacing.sm }}>
          <Button
            title="Accounts"
            variant="outline"
            onPress={() => handleTabPress('accounts')}
            style={{ flex: 1 }}
          />
          <Button
            title="More"
            variant="outline"
            onPress={() => handleTabPress('menu')}
            style={{ flex: 1 }}
          />
        </View>
      </View>

      {/* Expenses Pie Chart */}
      <ExpensesPieChart
        expenses={dashboardStats.expensesByCategory}
        onCategorySelect={handleCategorySelect}
        selectedCategoryId={selectedCategoryId}
      />

      {/* Transactions List */}
      <TransactionsList
        transactions={filteredTransactions}
        title={transactionsTitle}
        getCategoryInfo={getCategoryInfo}
        getAccountName={getAccountName}
        formatDate={formatDate}
        emptyMessage={selectedCategoryId ? "No transactions found for this category" : "No recent transactions"}
      />
    </ScrollView>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar style="auto" />
      


      {/* Screen Content */}
      <View style={{ flex: 1 }}>
        {activeTab === 'dashboard' && renderDashboard()}
        {activeTab === 'accounts' && <AccountManagementScreen onAccountChange={() => loadDashboardData(true)} />}
        {activeTab === 'transactions' && <TransactionManagementScreen onTransactionChange={() => loadDashboardData(true)} />}
        {activeTab === 'menu' && <MenuScreen menuItems={sideMenuItems} />}
        {activeTab === 'feedback' && <FeedbackScreen />}
      </View>

      {/* Bottom Navigation */}
      <BottomTabNavigation
        activeTab={activeTab}
        onTabPress={handleTabPress}
      />

      {/* Create Transaction Modal */}
      <Modal
        visible={showCreateTransactionModal}
        transparent={true}
        animationType="slide"
        presentationStyle="overFullScreen"
        onRequestClose={handleCreateTransactionCancel}
      >
        <TouchableWithoutFeedback onPress={handleCreateTransactionCancel}>
          <View style={styles.modalBackdrop}>
            <TouchableWithoutFeedback onPress={() => {}}>
              <View style={[styles.modalContent, { backgroundColor: theme.colors.background }]}>
                <TransactionEntryForm
                  onSuccess={handleCreateTransactionSuccess}
                  onCancel={handleCreateTransactionCancel}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </SafeAreaView>
  );
};

export default function App(): JSX.Element {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  screenContainer: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorDetail: {
    fontSize: 14,
    textAlign: 'center',
  },
  // Dashboard styles
  header: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '600',
  },
  date: {
    fontSize: 16,
    marginTop: 4,
  },

  localOnlyBadge: {
    alignItems: 'center',
    marginTop: 4,
  },
  localOnlyText: {
    fontSize: 10,
    fontWeight: '500',
    marginTop: 2,
  },
  quickStats: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
  },
  statLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  quickActions: {
    marginBottom: 24,
  },
  // Modal styles
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    height: '90%',
    width: '100%',
  },
});


