# Task Completion Checklist

## Required Steps After Completing Any Development Task

### 1. Code Quality Validation (MANDATORY)
- [ ] Run `npm run lint` - ESLint validation
- [ ] Run `npm run type-check` - TypeScript validation  
- [ ] Run `npm test` - Jest test suite
- [ ] Ensure all checks pass before considering task complete

### 2. Testing Requirements
- [ ] Maintain 80%+ code coverage
- [ ] Write unit tests for business logic
- [ ] Write integration tests for database operations
- [ ] Test performance requirements (queries < 500ms)
- [ ] Test security requirements (encryption/decryption)

### 3. Code Standards Compliance
- [ ] Follow TypeScript strict mode (no `any` types)
- [ ] Use proper interface definitions for data models
- [ ] Follow React Native functional component patterns
- [ ] Use Zustand stores for state management
- [ ] Follow database standards (parameterized queries)

### 4. Performance Validation
- [ ] Database queries complete in < 500ms
- [ ] SMS processing completes in < 2 seconds
- [ ] Memory usage stays < 100MB
- [ ] App startup stays < 3 seconds

### 5. Security Validation
- [ ] All sensitive data encrypted (AES-GCM client-side)
- [ ] No hardcoded secrets or credentials
- [ ] Proper key management via React Native Keychain
- [ ] HTTPS only for network operations

### 6. Documentation Updates
- [ ] Update relevant documentation if architecture changes
- [ ] Update type definitions if data models change
- [ ] Update migration files if database schema changes

## CRITICAL REMINDERS
- **NEVER** fake/mock implementations to make tests pass - fix the actual code
- **ALWAYS** refer to `/docs/architecture/` for detailed guidelines
- **ALWAYS** stick to versions specified in `docs/architecture/tech-stack.md`
- **ALWAYS** follow folder structure in `docs/architecture/source-tree.md`
- **ALWAYS** adhere to coding standards in `docs/architecture/coding-standards.md`

## Common Failure Points to Check
- [ ] SMS permissions handled for Android/iOS differences
- [ ] Database performance with proper indexing
- [ ] Sync conflict resolution for premium users
- [ ] ML model fallback when TensorFlow Lite fails
- [ ] Biometric authentication fallback options