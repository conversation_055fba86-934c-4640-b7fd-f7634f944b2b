# Development Guidelines and Design Patterns

## Architecture Patterns
- **Local-First**: SQLite + Cloud Sync for offline functionality
- **Zero-Knowledge**: Client-side encryption for privacy protection
- **Monorepo**: Single repository for code organization
- **Clean Architecture**: Layered separation of concerns

## Design Patterns Used
1. **Repository Pattern**: Data access abstraction (`/src/data/repositories/`)
2. **Service Pattern**: Business logic encapsulation (`/src/business/services/`)
3. **Manager Pattern**: Coordination of business operations (`/src/business/managers/`)
4. **Store Pattern**: State management with Zustand (`/src/shared/stores/`)

## Key Business Rules
- **Free Users**: Device-only storage, no cloud sync
- **Premium Users**: Encrypted cloud sync via Supabase
- **SMS Processing**: Anonymous pattern learning only
- **Performance**: < 500ms queries for 10K+ transactions
- **Privacy**: No personal data in backend logs/analytics

## Common Development Patterns

### Database Operations
```typescript
// Always use parameterized queries
db.query('SELECT * FROM transactions WHERE id = ?', [id])

// Use database transactions for related operations
db.transaction(async (trx) => {
  // Related operations here
})

// Create proper indexes for performance
CREATE INDEX idx_transactions_date ON transactions(date)
```

### State Management
```typescript
// Use Zustand stores for state
const useTransactionStore = create<TransactionStore>((set, get) => ({
  // Store implementation
}))
```

### Component Structure
```typescript
// Functional components with proper typing
interface ComponentProps {
  // Props definition
}

const Component: React.FC<ComponentProps> = ({ /* props */ }) => {
  // Component implementation
}

export default React.memo(Component) // Only for heavy components
```

### Error Handling
```typescript
// Graceful error handling with fallbacks
try {
  // Operation
} catch (error) {
  console.error('Operation failed:', error)
  // Provide fallback or user-friendly error
}
```

## Security Considerations
- All sensitive data must be encrypted before storage
- Use React Native Keychain for secure credential storage
- Implement biometric authentication with device fallback
- Never log sensitive user data
- Use HTTPS only with certificate pinning

## Performance Optimization
- Use React.memo only for heavy components
- Implement proper database indexing
- Use pagination for large datasets
- Optimize image assets for mobile
- Cache frequently accessed data

## Common Anti-Patterns to Avoid
- ❌ Using `any` type in TypeScript
- ❌ String concatenation in SQL queries
- ❌ Hardcoding secrets or credentials
- ❌ Class components (use functional components)
- ❌ Direct database access from components
- ❌ Synchronous operations in main thread