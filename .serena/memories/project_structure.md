# FinVibe Project Structure

## Root Directory Structure
```
/src/                    # Main application source (React Native mobile app)
/functions/              # Serverless cloud functions (SMS pattern learning APIs) - PLANNED
/docs/                   # Project documentation (Architecture and PRD docs)
/assets/                 # Static resources (Images, fonts, icons)
/scripts/                # Build and utility scripts (Development automation)
```

## Source Code Structure (`/src`)

### Architecture Layers
| Directory | Purpose | Architecture Layer |
|-----------|---------|-------------------|
| **`/src/presentation`** | UI Components & Screens | Presentation Layer |
| **`/src/business`** | Business Logic Services | Business Logic Layer |
| **`/src/data`** | Data Access & Storage | Data Access Layer |
| **`/src/platform`** | Platform Integrations | Platform Integration Layer |
| **`/src/shared`** | Shared Utilities | Cross-cutting Concerns |
| **`/src/__tests__`** | Comprehensive Test Suite | Testing Layer |

### Current Implementation Details

#### Testing Structure (`/src/__tests__`)
```
__tests__/
├── setup.ts                     # Test configuration and setup
├── unit/                        # Unit tests
│   ├── database/               # Database layer tests
│   ├── business/               # Business logic tests
│   ├── stores/                 # State store tests
│   ├── utils/                  # Utility function tests
│   ├── screens/                # Screen component tests
│   ├── components/             # Component tests
│   └── services/               # Service layer tests
├── integration/                # Integration tests
├── auth/                       # Authentication-specific tests
├── business/services/calculators/ # Calculator tests
├── performance/                # Performance tests
├── presentation/components/dashboard/ # Dashboard component tests
└── migrations/                 # Database migration tests
```

#### Presentation Layer (`/src/presentation`)
```
presentation/
├── screens/                    # Main application screens
│   ├── auth/                  # Authentication and security screens (IMPLEMENTED)
│   ├── accounts/              # Account management (IMPLEMENTED)
│   ├── transactions/          # Transaction management (IMPLEMENTED)
│   ├── feedback/              # User feedback screens (IMPLEMENTED)
│   ├── menu/                  # Menu and navigation screens (IMPLEMENTED)
│   ├── dashboard/             # Dashboard and overview (PLANNED)
│   ├── budget/               # Budget planning and tracking (PLANNED)
│   └── settings/             # User settings and preferences (PLANNED)
├── components/               # Reusable UI components
│   ├── core/                # Core UI components (Button, Card, etc.) (IMPLEMENTED)
│   ├── form/                # Form inputs and validation (IMPLEMENTED)
│   ├── common/              # Common UI elements and utilities (IMPLEMENTED)
│   ├── auth/                # Authentication-specific components (IMPLEMENTED)
│   ├── accounts/            # Account management components (IMPLEMENTED)
│   ├── transactions/        # Transaction-related components (IMPLEMENTED)
│   ├── feedback/            # Feedback system components (IMPLEMENTED)
│   ├── dashboard/           # Dashboard-specific components (IMPLEMENTED)
│   ├── categories/          # Category management components (IMPLEMENTED)
│   ├── charts/              # Data visualization components (PLANNED)
│   └── modals/              # Modal dialogs and overlays (PLANNED)
├── navigation/              # Navigation configuration
│   ├── AppNavigator.tsx     # Main navigation setup (PLANNED)
│   ├── TabNavigator.tsx     # Bottom tab navigation (PLANNED)
│   └── StackNavigator.tsx   # Screen stack navigation (PLANNED)
└── ErrorBoundary.tsx        # Global error boundary component (IMPLEMENTED)
```

#### Business Logic Layer (`/src/business`)
```
business/
├── services/                   # Core business services
│   ├── TransactionService.ts  # Transaction CRUD operations (IMPLEMENTED)
│   ├── AccountService.ts      # Account management (IMPLEMENTED)
│   ├── CategoryService.ts     # Category management (IMPLEMENTED)
│   ├── FeedbackService.ts     # User feedback handling (IMPLEMENTED)
│   ├── SMSParsingService.ts   # SMS parsing and processing (IMPLEMENTED)
│   ├── SystemCategoriesService.ts # System category management (IMPLEMENTED)
│   ├── CategorySuggestionService.ts # ML-based category suggestions (IMPLEMENTED)
│   ├── PerformanceService.ts  # Performance monitoring (IMPLEMENTED)
│   ├── TelemetryService.ts    # Analytics and telemetry (IMPLEMENTED)
│   ├── BuildHookService.ts    # Build integration hooks (IMPLEMENTED)
│   ├── LogAggregatorService.ts # Log collection and processing (IMPLEMENTED)
│   ├── MockAccountService.ts  # Development/testing service (IMPLEMENTED)
│   ├── BudgetService.ts       # Budget management logic (PLANNED)
│   ├── MLCategorizationService.ts # Machine learning categorization (PLANNED)
│   ├── SyncService.ts         # Cloud synchronization (PLANNED)
│   └── calculators/           # Account-specific calculation logic (IMPLEMENTED)
│       ├── BaseAccountCalculator.ts     # Base calculator interface
│       ├── AccountCalculatorFactory.ts  # Calculator factory pattern
│       ├── DefaultAccountCalculator.ts  # Standard account calculations
│       ├── CreditCardCalculator.ts      # Credit card-specific logic
│       └── LoanCalculator.ts            # Loan account calculations
├── managers/                   # Business logic managers (PLANNED)
├── models/                     # Domain models and interfaces (PLANNED)
└── validators/                 # Business rule validation (PLANNED)
```

#### Data Access Layer (`/src/data`)
```
data/
├── repositories/               # Data access repositories
│   ├── index.ts               # Repository exports (IMPLEMENTED)
│   ├── BaseRepository.ts      # Base repository with common functionality (IMPLEMENTED)
│   ├── TransactionRepository.ts # Transaction data access (IMPLEMENTED)
│   ├── AccountRepository.ts    # Account data access (IMPLEMENTED)
│   ├── CategoryRepository.ts   # Category data access (IMPLEMENTED)
│   ├── BudgetRepository.ts     # Budget data access (PLANNED)
│   └── UserRepository.ts       # User data access (PLANNED)
├── database/                   # Database configuration
│   ├── index.ts               # Database exports (IMPLEMENTED)
│   ├── DatabaseService.ts     # SQLite database setup and management (IMPLEMENTED)
│   ├── MigrationManager.ts    # Database migration management (IMPLEMENTED)
│   ├── migrations/            # Database schema migrations (15 IMPLEMENTED)
│   │   ├── 001_create_accounts.js through 015_create_feedback_sync_queue.js
│   └── schemas/               # Database schema definitions
│       ├── createTables.sql   # SQL table creation scripts (IMPLEMENTED)
│       ├── indexes.sql        # Database indexes (IMPLEMENTED)
│       └── [TypeScript schemas] # (PLANNED)
├── systemCategories.ts         # System-defined categories (IMPLEMENTED)
├── smsPatterns.ts             # SMS parsing patterns (IMPLEMENTED)
├── storage/                    # Storage management (PLANNED)
└── sync/                       # Cloud synchronization (PLANNED)
```

#### Platform Integration Layer (`/src/platform`)
```
platform/
├── sms/                        # SMS access and processing (PLANNED)
├── biometric/                  # Biometric authentication (EXTENSIVELY IMPLEMENTED)
│   ├── BiometricAuthService.ts # Fingerprint/Face ID integration
│   ├── AuthenticationManager.ts # Auth state management
│   ├── KeychainService.ts     # Secure keychain operations
│   ├── AutoLockService.ts     # Auto-lock functionality
│   ├── SecurityAuditLogger.ts # Security event logging
│   ├── PremiumGatekeeper.tsx  # Premium feature access control
│   ├── OAuthService.ts        # OAuth integration
│   ├── AppStateManager.ts     # App state monitoring
│   └── DeviceResetManager.ts  # Device reset handling
├── filesystem/                 # File system operations (PLANNED)
├── notifications/              # Push notifications (PLANNED)
└── ml/                         # Machine learning integration (PLANNED)
```

#### Shared Utilities (`/src/shared`)
```
shared/
├── types/                      # TypeScript type definitions
│   ├── index.ts               # Main type exports (IMPLEMENTED)
│   ├── feedback.ts            # Feedback system types (IMPLEMENTED)
│   ├── calculators.ts         # Account calculator types (IMPLEMENTED)
│   ├── accountMetadata.ts     # Account metadata types (IMPLEMENTED)
│   └── global.ts              # Global type definitions (IMPLEMENTED)
├── utils/                      # Utility functions (EXTENSIVELY IMPLEMENTED)
│   ├── Logger.ts              # Logging utilities
│   ├── ErrorHandler.ts        # Error handling utilities
│   ├── deviceFingerprint.ts   # Device identification
│   ├── accountValidation.ts   # Account validation functions
│   ├── transactionUtils.ts    # Transaction utility functions
│   ├── categorization.ts      # Category management utilities
│   ├── iconUtils.ts           # Icon handling utilities
│   └── [additional utilities]
├── theme/                      # Theme and styling system (IMPLEMENTED)
│   ├── index.ts               # Theme exports
│   ├── createStyles.ts        # Style creation utilities
│   └── ThemeProvider.tsx      # Theme provider component
├── stores/                     # Zustand state stores (EXTENSIVELY IMPLEMENTED)
│   ├── index.ts               # Store exports
│   ├── transactionStore.ts    # Transaction state management
│   ├── accountStore.ts        # Account state management
│   ├── categoryStore.ts       # Category state management
│   ├── feedbackStore.ts       # Feedback system state
│   ├── mockAccountStore.ts    # Mock account store for development
│   ├── authStore.ts           # Authentication state
│   └── settingsStore.ts       # User settings state
├── constants/                  # Application constants (PLANNED)
└── hooks/                      # Custom React hooks (PLANNED)
```

## Configuration Files
- **`package.json`** - Dependencies and scripts
- **`app.json`** - Expo configuration
- **`babel.config.js`** - Babel transformation
- **`tsconfig.json`** - TypeScript configuration
- **`.eslintrc.js`** - ESLint rules
- **`knexfile.js`** - Database configuration
- **`CLAUDE.md`** - Project memory and instructions

## Scripts Directory
```
scripts/
├── log-bridge-server.js        # Log processing server for development
├── test-logging.js             # Logging system tests
├── test-logger-direct.js       # Direct logger testing
└── test-ci-local.sh            # Local CI testing script
```

## Key Implementation Status
- ✅ **IMPLEMENTED**: Authentication system, Account management, Transaction handling, Feedback system, Comprehensive testing, Theme system, Calculator pattern, Database with 15 migrations
- 🔄 **PLANNED**: Navigation structure, Budget management, SMS processing, ML categorization, Cloud sync, Managers/Validators pattern, Serverless functions

## Path Aliases (TypeScript)
- `@/*` → `src/*`
- `@/presentation/*` → `src/presentation/*`
- `@/business/*` → `src/business/*`
- `@/data/*` → `src/data/*`
- `@/platform/*` → `src/platform/*`
- `@/shared/*` → `src/shared/*`