# FinVibe Project Overview

## Project Purpose
**FinVibe** is a privacy-first personal finance application designed with a local-first architecture and SMS-based transaction parsing. The app focuses on providing users with complete financial control while maintaining their privacy through zero-knowledge architecture.

## Key Features
1. **SMS Parsing** - Automated transaction extraction from bank SMS messages
2. **Offline-First** - Full functionality without internet connection
3. **ML Categorization** - Local TensorFlow Lite for transaction categorization  
4. **Family Sharing** - Real-time sync for premium users
5. **Privacy-First** - Zero-knowledge architecture with client-side encryption

## Architecture Philosophy
- **Local-First**: SQLite + SQLCipher for offline-first functionality
- **Zero-Knowledge**: Client-side encryption before cloud sync
- **Privacy-First**: No personal data in backend logs/analytics
- **Performance-Focused**: <500ms database queries, <2s SMS processing

## User Tiers
- **Free users**: Device-only storage, no cloud sync
- **Premium users**: Encrypted cloud sync via Supabase

## Core Technology Stack
- **Frontend**: React Native 0.72+ with TypeScript
- **State Management**: Zustand with persistence + React Query
- **Database**: SQLite with SQLCipher encryption + Knex.js ORM
- **Development**: Expo SDK 49+
- **Cloud**: Supabase (for premium users)
- **Security**: React Native Keychain + Expo Local Authentication
- **ML**: TensorFlow Lite for on-device processing