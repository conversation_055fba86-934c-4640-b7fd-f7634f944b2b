# Suggested Commands for FinVibe Development

## Development Commands
- `npm run start` - Start Expo development server (NOTE: Development server usually running in background)
- `npm run android` - Start Android development build
- `npm run ios` - Start iOS development build
- `npm run web` - Start web development build

## Code Quality Commands (Always run after completing tasks)
- `npm run lint` - Run ESLint validation (MUST run after code changes)
- `npm run type-check` - Run TypeScript validation (MUST run after code changes)
- `npm test` - Run Jest test suite (MUST run after code changes)

## Build Commands
- `npm run build` - Create production build
- `npm run eject` - Eject from Expo (use with caution)

## System Commands (Darwin/macOS)
- `ls` - List directory contents
- `cd <directory>` - Change directory
- `find <path> -name <pattern>` - Find files matching pattern
- `grep -r <pattern> <path>` - Search for text patterns recursively
- `cat <file>` - Display file contents
- `head -n <number> <file>` - Display first N lines of file
- `tail -n <number> <file>` - Display last N lines of file

## Git Commands
- `git status` - Check repository status
- `git add <file>` - Stage files for commit
- `git commit -m "<message>"` - Commit staged changes
- `git push` - Push commits to remote repository
- `git pull` - Pull latest changes from remote

## Database Commands (via npm scripts or manual)
- Database migrations are handled via Knex.js
- SQLite database located in device storage
- Use DatabaseService.getInstance() for database operations

## Performance Monitoring
- Database queries should complete in < 500ms
- SMS processing should complete in < 2 seconds
- App startup should be < 3 seconds on budget Android devices

## Development Notes
- IMPORTANT: Development server usually runs in background - don't restart unless necessary
- IMPORTANT: Always run `npm run lint`, `npm run type-check`, and `npm test` after making changes
- IMPORTANT: Never fake/mock implementations to make tests pass - fix the actual code
- IMPORTANT: Refer to architecture docs in `/docs/architecture/` for detailed guidelines