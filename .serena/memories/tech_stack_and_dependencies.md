# Technology Stack and Dependencies

## Core Framework
- **React Native**: 0.72+ (Cross-platform mobile development)
- **TypeScript**: Latest (Type safety and developer experience)
- **Expo SDK**: 49+ (Development tooling and platform APIs)

## State Management
- **Zustand**: Latest (Lightweight state management with persistence)
- **React Query**: v4 (Server state management and caching)
- **AsyncStorage**: Latest (Persistent storage for state)

## Database & Data
- **SQLite**: Latest (Primary local database engine)
- **SQLCipher**: Latest (Transparent encryption for SQLite)
- **Knex.js**: Latest (Query builder and migration framework)

## Security & Authentication
- **React Native Keychain**: Latest (Secure credential storage)
- **Expo Local Authentication**: Latest (Biometric authentication)
- **Crypto-js**: Latest (Client-side encryption operations)

## Machine Learning
- **TensorFlow Lite**: Latest (On-device transaction categorization)
- **React Native ML Kit**: Latest (Text extraction and pattern recognition)

## Cloud Services (Premium)
- **Supabase Database**: Latest (PostgreSQL with real-time subscriptions)
- **Supabase Edge Functions**: Latest (Serverless SMS pattern learning API)
- **Supabase Realtime**: Latest (Real-time sync for family sharing)

## Development Tools
- **Jest**: Latest (Unit and integration testing framework)
- **ESLint**: Latest (Code linting and quality)
- **Prettier**: Latest (Code formatting)
- **TypeScript**: Strict mode enabled

## Build & Deployment
- **GitHub Actions**: Latest (Automated testing and deployment)
- **EAS Build**: Latest (Expo Application Services for builds)
- **EAS Submit**: Latest (App store deployment automation)