# Code Style and Conventions

## TypeScript Standards
- **Strict Mode**: Always enabled (`"strict": true`)
- **No Any Types**: Forbidden except during migrations
- **Interface Definitions**: Required for all data models
- **Null Safety**: Explicit null handling (`string | null` not `string`)
- **Generic Types**: Use for reusable components (`Repository<T>`)

## React Native Standards
- **Functional Components**: Required (Hooks-based: `const Component = () => {}`)
- **State Management**: Zustand stores for centralized state (`useTransactionStore()`)
- **Props Typing**: Interface definitions with named interfaces (`interface ComponentProps {}`)
- **Styling**: StyleSheet API with consistent naming (`styles.container`)
- **Performance**: React.memo for optimization (heavy components only)

## Code Quality Tools
- **ESLint**: React Native + TypeScript rules
  - `prefer-const`: error
  - `no-var`: error
  - `no-unused-vars`: error
- **Prettier**: 
  - 2-space indentation
  - Single quotes
  - 100 character line width
  - Semicolons enabled
  - Trailing commas (ES5 style)

## Database Standards
- **Queries**: Parameterized only, no string concatenation
- **Migrations**: Knex.js migrations, version controlled (`20231201_create_transactions.ts`)
- **Transactions**: Database transactions for related operations (ACID compliance)
- **Indexing**: Performance-critical columns (`CREATE INDEX idx_transactions_date`)
- **Encryption**: SQLCipher for sensitive data (automatic via SQLCipher)

## Security Standards
- **Data Encryption**: All sensitive data encrypted (AES-GCM client-side)
- **Key Management**: Device keychain storage (React Native Keychain)
- **Authentication**: Biometric + device-based (Expo Local Authentication)
- **Network Security**: HTTPS only, certificate pinning (TLS 1.3 minimum)
- **Secret Management**: No hardcoded secrets (environment variables)

## Testing Standards
- **Unit Tests**: 80%+ code coverage with Jest (business logic focused)
- **Integration Tests**: Critical database operations (data flow validation)
- **Performance Tests**: Database queries < 500ms (performance regression prevention)
- **Security Tests**: Encryption/decryption flows (security validation)