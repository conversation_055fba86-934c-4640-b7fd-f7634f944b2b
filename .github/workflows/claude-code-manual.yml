name: Manual Claude Code Analysis

on:
  workflow_dispatch:
    inputs:
      analysis_type:
        description: 'Type of analysis to perform'
        required: true
        type: choice
        options:
          - 'error-analysis'
          - 'performance-debug' 
          - 'build-failure'
          - 'test-failure'
          - 'full-diagnosis'
      log_retention_hours:
        description: 'Hours of logs to analyze (1-24)'
        required: false
        default: '2'
      batch_size:
        description: 'Lines per analysis batch'
        required: false
        default: '50'

env:
  NODE_VERSION: '18.x'

jobs:
  claude-analysis:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Create comprehensive log directory
        run: |
          mkdir -p logs/llm_debug/manual-analysis
          echo "Manual Claude Code analysis initiated" | tee logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          echo "Analysis type: ${{ github.event.inputs.analysis_type }}" | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          echo "Log retention: ${{ github.event.inputs.log_retention_hours }} hours" | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          
      - name: Gather system context
        run: |
          # Collect system information for Claude Code context
          echo "=== System Context ===" | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          echo "Node version: $(node --version)" | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          echo "NPM version: $(npm --version)" | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          echo "Repository: ${{ github.repository }}" | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          echo "Branch: ${{ github.ref_name }}" | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          echo "Commit: ${{ github.sha }}" | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          
          # Package.json dependencies
          echo "=== Dependencies ===" | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          cat package.json | jq '.dependencies' | tee -a logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log
          
      - name: Run targeted analysis
        run: |
          ANALYSIS_TYPE="${{ github.event.inputs.analysis_type }}"
          SESSION_LOG="logs/llm_debug/manual-analysis/session-$(date +%Y%m%d-%H%M%S).log"
          
          echo "=== Running Analysis: $ANALYSIS_TYPE ===" | tee -a "$SESSION_LOG"
          
          case "$ANALYSIS_TYPE" in
            "error-analysis")
              echo "Performing error pattern analysis..." | tee -a "$SESSION_LOG"
              npm run lint 2>&1 | tee logs/llm_debug/manual-analysis/lint-analysis.log
              npm run type-check 2>&1 | tee logs/llm_debug/manual-analysis/typecheck-analysis.log
              ;;
              
            "performance-debug")
              echo "Performing performance analysis..." | tee -a "$SESSION_LOG"
              npm test -- --testPathPattern="performance" --verbose 2>&1 | tee logs/llm_debug/manual-analysis/performance-analysis.log
              ;;
              
            "build-failure")
              echo "Analyzing build failures..." | tee -a "$SESSION_LOG"
              npm run build 2>&1 | tee logs/llm_debug/manual-analysis/build-analysis.log || true
              ;;
              
            "test-failure")
              echo "Analyzing test failures..." | tee -a "$SESSION_LOG"
              npm test --verbose 2>&1 | tee logs/llm_debug/manual-analysis/test-analysis.log || true
              ;;
              
            "full-diagnosis")
              echo "Performing full system diagnosis..." | tee -a "$SESSION_LOG"
              npm run lint 2>&1 | tee logs/llm_debug/manual-analysis/full-lint.log || true
              npm run type-check 2>&1 | tee logs/llm_debug/manual-analysis/full-typecheck.log || true
              npm test 2>&1 | tee logs/llm_debug/manual-analysis/full-test.log || true
              npm run build 2>&1 | tee logs/llm_debug/manual-analysis/full-build.log || true
              ;;
          esac
          
          echo "Analysis completed for: $ANALYSIS_TYPE" | tee -a "$SESSION_LOG"
          
      - name: Process logs with progressive batching
        run: |
          BATCH_SIZE="${{ github.event.inputs.batch_size }}"
          MAX_BATCHES=20
          
          echo "=== Progressive Log Processing ===" | tee logs/llm_debug/manual-analysis/processing-$(date +%Y%m%d-%H%M%S).log
          echo "Batch size: $BATCH_SIZE lines" | tee -a logs/llm_debug/manual-analysis/processing-$(date +%Y%m%d-%H%M%S).log
          echo "Max batches: $MAX_BATCHES" | tee -a logs/llm_debug/manual-analysis/processing-$(date +%Y%m%d-%H%M%S).log
          
          # Combine all analysis logs
          find logs/llm_debug/manual-analysis -name "*.log" -type f | while read logfile; do
            echo "=== File: $logfile ===" >> logs/llm_debug/manual-analysis/combined-logs.txt
            cat "$logfile" >> logs/llm_debug/manual-analysis/combined-logs.txt
            echo "" >> logs/llm_debug/manual-analysis/combined-logs.txt
          done
          
          # Process in batches for Claude Code analysis
          if [ -f "logs/llm_debug/manual-analysis/combined-logs.txt" ]; then
            TOTAL_LINES=$(wc -l < logs/llm_debug/manual-analysis/combined-logs.txt)
            echo "Total lines to process: $TOTAL_LINES" | tee -a logs/llm_debug/manual-analysis/processing-$(date +%Y%m%d-%H%M%S).log
            
            BATCH_COUNT=0
            while [ $BATCH_COUNT -lt $MAX_BATCHES ] && [ $((BATCH_COUNT * BATCH_SIZE)) -lt $TOTAL_LINES ]; do
              BATCH_COUNT=$((BATCH_COUNT + 1))
              START_LINE=$(((BATCH_COUNT - 1) * BATCH_SIZE + 1))
              END_LINE=$((BATCH_COUNT * BATCH_SIZE))
              
              echo "Creating batch $BATCH_COUNT (lines $START_LINE-$END_LINE)" | tee -a logs/llm_debug/manual-analysis/processing-$(date +%Y%m%d-%H%M%S).log
              
              sed -n "${START_LINE},${END_LINE}p" logs/llm_debug/manual-analysis/combined-logs.txt > "logs/llm_debug/manual-analysis/batch-${BATCH_COUNT}.log"
              
              # Analyze batch for patterns
              ERROR_COUNT=$(grep -c "ERROR\|error\|Error\|FATAL\|FAIL" "logs/llm_debug/manual-analysis/batch-${BATCH_COUNT}.log" || echo "0")
              WARNING_COUNT=$(grep -c "WARN\|warn\|Warning\|WARNING" "logs/llm_debug/manual-analysis/batch-${BATCH_COUNT}.log" || echo "0")
              PERFORMANCE_COUNT=$(grep -c "slow\|timeout\|performance\|memory" "logs/llm_debug/manual-analysis/batch-${BATCH_COUNT}.log" || echo "0")
              
              echo "Batch $BATCH_COUNT analysis: $ERROR_COUNT errors, $WARNING_COUNT warnings, $PERFORMANCE_COUNT performance issues" | tee -a logs/llm_debug/manual-analysis/processing-$(date +%Y%m%d-%H%M%S).log
              
              if [ $ERROR_COUNT -gt 0 ] || [ $WARNING_COUNT -gt 3 ] || [ $PERFORMANCE_COUNT -gt 0 ]; then
                echo "🚨 Batch $BATCH_COUNT requires attention" | tee -a logs/llm_debug/manual-analysis/processing-$(date +%Y%m%d-%H%M%S).log
                echo "batch-${BATCH_COUNT}.log" >> logs/llm_debug/manual-analysis/priority-batches.txt
              fi
            done
            
            echo "✅ Progressive processing completed: $BATCH_COUNT batches processed" | tee -a logs/llm_debug/manual-analysis/processing-$(date +%Y%m%d-%H%M%S).log
          fi
          
      - name: Generate Claude Code analysis report
        run: |
          ANALYSIS_TYPE="${{ github.event.inputs.analysis_type }}"
          
          cat > logs/llm_debug/manual-analysis/claude-code-report.md << EOF
          # Claude Code Manual Analysis Report
          
          **Analysis Type:** $ANALYSIS_TYPE
          **Requested By:** ${{ github.actor }}
          **Timestamp:** $(date -Iseconds)
          **Repository:** ${{ github.repository }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}
          
          ## Analysis Configuration
          - Log retention: ${{ github.event.inputs.log_retention_hours }} hours
          - Batch size: ${{ github.event.inputs.batch_size }} lines
          - Max batches: 20 (1000 lines total)
          
          ## Batch Processing Summary
          $(if [ -f logs/llm_debug/manual-analysis/priority-batches.txt ]; then
            echo "### Priority Batches (Require Attention):"
            while read batch; do
              echo "- $batch"
            done < logs/llm_debug/manual-analysis/priority-batches.txt
          else
            echo "### No priority batches identified"
          fi)
          
          ## Analysis Artifacts
          - Combined logs: \`logs/llm_debug/manual-analysis/combined-logs.txt\`
          - Batch files: \`logs/llm_debug/manual-analysis/batch-*.log\`
          - Processing log: \`logs/llm_debug/manual-analysis/processing-*.log\`
          
          ## Recommended Next Steps
          1. Review priority batches for critical issues
          2. Investigate error patterns in identified batches
          3. Address performance issues if detected
          4. Consider follow-up analysis if patterns unclear
          
          ## Claude Code Integration Notes
          This analysis follows the progressive context gathering approach:
          - Logs processed in 50-line batches (configurable)
          - Maximum 1000 lines analyzed to stay within limits
          - Priority scoring based on error/warning density
          - Fail-safe processing with partial results
          
          ---
          *Generated by FinVibe Claude Code Integration*
          EOF
          
      - name: Upload comprehensive analysis results
        uses: actions/upload-artifact@v3
        with:
          name: claude-code-manual-analysis-${{ github.event.inputs.analysis_type }}
          path: logs/llm_debug/manual-analysis/
          
      - name: Summary output
        run: |
          echo "🔍 Manual Claude Code analysis completed"
          echo "Analysis type: ${{ github.event.inputs.analysis_type }}"
          echo "Artifacts uploaded with comprehensive logs and batched analysis"
          echo "Review the claude-code-report.md for detailed findings"
          
          if [ -f logs/llm_debug/manual-analysis/priority-batches.txt ]; then
            PRIORITY_COUNT=$(wc -l < logs/llm_debug/manual-analysis/priority-batches.txt)
            echo "⚠️  $PRIORITY_COUNT priority batches require attention"
          else
            echo "✅ No high-priority issues detected in analysis"
          fi