name: FinVibe CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18.x'
  EXPO_SDK_VERSION: '49.0.0'

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      cache-key: ${{ steps.cache-key.outputs.key }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Generate cache key
        id: cache-key
        run: echo "key=node-${{ hashFiles('**/package-lock.json') }}" >> $GITHUB_OUTPUT
        
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ steps.cache-key.outputs.key }}
          restore-keys: |
            node-
            
      - name: Install dependencies
        run: npm ci

  lint-and-typecheck:
    needs: setup
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Restore dependencies
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ needs.setup.outputs.cache-key }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run ESLint
        run: |
          npm run lint 2>&1 | tee logs/llm_debug/eslint-$(date +%Y%m%d-%H%M%S).log
          
      - name: Run TypeScript check
        run: |
          npm run type-check 2>&1 | tee logs/llm_debug/typescript-$(date +%Y%m%d-%H%M%S).log
          
      - name: Upload lint results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: lint-results
          path: logs/llm_debug/

  test:
    needs: setup
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        test-type: [unit, integration, performance]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Restore dependencies
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ needs.setup.outputs.cache-key }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Create logs directory
        run: mkdir -p logs/llm_debug
        
      - name: Run ${{ matrix.test-type }} tests
        run: |
          case "${{ matrix.test-type }}" in
            "unit")
              npm test -- --testPathPattern="src/__tests__/unit" --coverage --verbose 2>&1 | tee logs/llm_debug/unit-tests-$(date +%Y%m%d-%H%M%S).log
              ;;
            "integration") 
              npm test -- --testPathPattern="src/__tests__/integration" --verbose 2>&1 | tee logs/llm_debug/integration-tests-$(date +%Y%m%d-%H%M%S).log
              ;;
            "performance")
              npm test -- --testPathPattern="src/__tests__/performance" --verbose 2>&1 | tee logs/llm_debug/performance-tests-$(date +%Y%m%d-%H%M%S).log
              ;;
          esac
          
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: test-results-${{ matrix.test-type }}
          path: |
            logs/llm_debug/
            coverage/
            
      - name: Upload coverage to Codecov
        if: matrix.test-type == 'unit'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  database-tests:
    needs: setup
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Restore dependencies
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ needs.setup.outputs.cache-key }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Create logs directory
        run: mkdir -p logs/llm_debug
        
      - name: Run database migration tests
        run: |
          npm test -- --testPathPattern="MigrationManager" --verbose 2>&1 | tee logs/llm_debug/migration-tests-$(date +%Y%m%d-%H%M%S).log
          
      - name: Run database performance benchmarks
        run: |
          npm test -- --testPathPattern="database.performance" --verbose 2>&1 | tee logs/llm_debug/db-performance-$(date +%Y%m%d-%H%M%S).log
          
      - name: Performance regression check
        run: |
          echo "Checking database performance against thresholds..." | tee -a logs/llm_debug/performance-check-$(date +%Y%m%d-%H%M%S).log
          # In a real implementation, this would compare current vs baseline performance metrics
          
      - name: Upload database test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: database-test-results
          path: logs/llm_debug/

  build:
    needs: [lint-and-typecheck, test, database-tests]
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Restore dependencies
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ needs.setup.outputs.cache-key }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Create logs directory
        run: mkdir -p logs/llm_debug
        
      - name: Build application
        run: |
          START_TIME=$(date +%s)
          npm run build 2>&1 | tee logs/llm_debug/build-$(date +%Y%m%d-%H%M%S).log
          END_TIME=$(date +%s)
          BUILD_TIME=$((END_TIME - START_TIME))
          echo "Build completed in ${BUILD_TIME} seconds" | tee -a logs/llm_debug/build-$(date +%Y%m%d-%H%M%S).log
          
          # Check if build time exceeds threshold (5 seconds)
          if [ $BUILD_TIME -gt 5 ]; then
            echo "⚠️ Build time exceeded threshold (${BUILD_TIME}s > 5s)" | tee -a logs/llm_debug/build-$(date +%Y%m%d-%H%M%S).log
            echo "build_performance_warning=true" >> $GITHUB_ENV
          fi
          
      - name: Bundle size analysis
        run: |
          if [ -f "dist/bundle.js" ]; then
            BUNDLE_SIZE=$(wc -c < dist/bundle.js)
            echo "Bundle size: ${BUNDLE_SIZE} bytes" | tee -a logs/llm_debug/build-$(date +%Y%m%d-%H%M%S).log
            
            # Check bundle size threshold (1MB = 1048576 bytes)
            if [ $BUNDLE_SIZE -gt 1048576 ]; then
              echo "⚠️ Bundle size exceeds 1MB threshold" | tee -a logs/llm_debug/build-$(date +%Y%m%d-%H%M%S).log
              echo "bundle_size_warning=true" >> $GITHUB_ENV
            fi
          fi
          
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            dist/
            logs/llm_debug/

  claude-code-analysis:
    needs: [build]
    runs-on: ubuntu-latest
    if: failure() || env.build_performance_warning == 'true' || env.bundle_size_warning == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download all artifacts
        uses: actions/download-artifact@v3
        
      - name: Aggregate logs for Claude Code analysis
        run: |
          mkdir -p logs/llm_debug/combined
          
          # Combine all log files with timestamps
          find . -name "*.log" -type f | while read logfile; do
            echo "=== Processing: $logfile ===" >> logs/llm_debug/combined/all-logs-$(date +%Y%m%d-%H%M%S).log
            cat "$logfile" >> logs/llm_debug/combined/all-logs-$(date +%Y%m%d-%H%M%S).log
            echo "" >> logs/llm_debug/combined/all-logs-$(date +%Y%m%d-%H%M%S).log
          done
          
      - name: Progressive log analysis simulation
        run: |
          LOG_FILE="logs/llm_debug/combined/all-logs-$(date +%Y%m%d-%H%M%S).log"
          
          if [ -f "$LOG_FILE" ]; then
            TOTAL_LINES=$(wc -l < "$LOG_FILE")
            echo "Total log lines: $TOTAL_LINES" | tee logs/llm_debug/analysis-$(date +%Y%m%d-%H%M%S).log
            
            # Process in batches of 50 lines (as configured in CLAUDE.md)
            BATCH_SIZE=50
            MAX_BATCHES=20
            BATCH_COUNT=0
            
            while [ $BATCH_COUNT -lt $MAX_BATCHES ] && [ $((BATCH_COUNT * BATCH_SIZE)) -lt $TOTAL_LINES ]; do
              BATCH_COUNT=$((BATCH_COUNT + 1))
              START_LINE=$(((BATCH_COUNT - 1) * BATCH_SIZE + 1))
              END_LINE=$((BATCH_COUNT * BATCH_SIZE))
              
              echo "Processing batch $BATCH_COUNT (lines $START_LINE-$END_LINE)" | tee -a logs/llm_debug/analysis-$(date +%Y%m%d-%H%M%S).log
              
              # Extract batch and analyze for error patterns
              sed -n "${START_LINE},${END_LINE}p" "$LOG_FILE" > "logs/llm_debug/batch-${BATCH_COUNT}.log"
              
              # Simulate Claude Code analysis
              ERROR_COUNT=$(grep -c "ERROR\|FATAL\|FAIL" "logs/llm_debug/batch-${BATCH_COUNT}.log" || echo "0")
              WARNING_COUNT=$(grep -c "WARN\|WARNING" "logs/llm_debug/batch-${BATCH_COUNT}.log" || echo "0")
              
              if [ $ERROR_COUNT -gt 0 ] || [ $WARNING_COUNT -gt 5 ]; then
                echo "🔍 Batch $BATCH_COUNT: Found $ERROR_COUNT errors, $WARNING_COUNT warnings" | tee -a logs/llm_debug/analysis-$(date +%Y%m%d-%H%M%S).log
                echo "Recommended analysis: Review error patterns in batch $BATCH_COUNT" | tee -a logs/llm_debug/analysis-$(date +%Y%m%d-%H%M%S).log
              fi
            done
            
            echo "✅ Progressive analysis completed. Processed $BATCH_COUNT batches." | tee -a logs/llm_debug/analysis-$(date +%Y%m%d-%H%M%S).log
          fi
          
      - name: Generate error analysis report
        run: |
          cat > logs/llm_debug/claude-code-analysis-report.md << 'EOF'
          # Claude Code Analysis Report
          
          **Generated:** $(date)
          **Workflow:** ${{ github.workflow }}
          **Run:** ${{ github.run_number }}
          
          ## Analysis Summary
          
          ### Build Issues Detected:
          - Build performance: ${{ env.build_performance_warning }}
          - Bundle size warning: ${{ env.bundle_size_warning }}
          
          ### Log Analysis:
          - Total log files processed: $(find . -name "*.log" -type f | wc -l)
          - Progressive batching: Enabled (50 lines/batch, max 20 batches)
          - Error patterns: Analyzed for ERROR, FATAL, FAIL keywords
          
          ### Recommendations:
          1. Review build performance metrics if warnings present
          2. Check database query optimization for slow operations
          3. Investigate TypeScript compilation issues if present
          4. Monitor bundle size growth over time
          
          ### Manual Investigation Required:
          - Review logs/llm_debug/ directory for detailed analysis
          - Consider running performance regression tests
          - Check for dependency update impacts
          
          EOF
          
      - name: Upload Claude Code analysis results
        uses: actions/upload-artifact@v3
        with:
          name: claude-code-analysis
          path: logs/llm_debug/

  notify-completion:
    needs: [build, claude-code-analysis]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
      - name: Determine build status
        run: |
          if [ "${{ needs.build.result }}" == "success" ]; then
            echo "BUILD_STATUS=✅ SUCCESS" >> $GITHUB_ENV
            echo "BUILD_COLOR=good" >> $GITHUB_ENV
          elif [ "${{ needs.build.result }}" == "failure" ]; then
            echo "BUILD_STATUS=❌ FAILED" >> $GITHUB_ENV
            echo "BUILD_COLOR=danger" >> $GITHUB_ENV
          else
            echo "BUILD_STATUS=⚠️ WARNING" >> $GITHUB_ENV
            echo "BUILD_COLOR=warning" >> $GITHUB_ENV
          fi
          
      - name: Notify build completion
        run: |
          echo "Build completed with status: ${{ env.BUILD_STATUS }}"
          echo "Repository: ${{ github.repository }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Triggered by: ${{ github.event_name }}"
          
          # In a real implementation, this would send notifications via Slack, email, etc.
          # For now, we'll just log the notification details
          cat > build-notification.json << EOF
          {
            "status": "${{ env.BUILD_STATUS }}",
            "repository": "${{ github.repository }}",
            "branch": "${{ github.ref_name }}",
            "commit": "${{ github.sha }}",
            "run_number": "${{ github.run_number }}",
            "triggered_by": "${{ github.event_name }}",
            "timestamp": "$(date -Iseconds)",
            "claude_code_analysis": "${{ needs.claude-code-analysis.result }}",
            "artifacts_available": true
          }
          EOF
          
          echo "Build notification generated:"
          cat build-notification.json