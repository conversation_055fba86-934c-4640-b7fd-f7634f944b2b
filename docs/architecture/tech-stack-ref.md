# Tech Stack - Quick Reference

## Core Dependencies

### Frontend & Mobile
- **React Native**: 0.72+
- **TypeScript**: Latest (strict mode)
- **Expo SDK**: 49+

### State & Data
- **Zustand**: Latest (state management)
- **React Query**: v4 (API state)
- **SQLite + SQLCipher**: Latest (local encrypted DB)
- **Knex.js**: Latest (query builder/migrations)

### Security & Auth
- **React Native Keychain**: Latest (secure storage)
- **Expo Local Authentication**: Latest (biometrics)
- **Crypto-js**: Latest (client-side encryption)

### Cloud (Premium Only)
- **Supabase**: Latest (PostgreSQL + real-time + edge functions)

### Development
- **Node.js**: 18.x LTS
- **Jest**: Latest (testing)
- **ESLint + Prettier**: Latest (code quality)

## Performance Targets
- Database queries: **<500ms**
- SMS processing: **<2s**  
- App startup: **<3s**
- Memory usage: **<100MB**

## Key Patterns
- **Local-First**: SQLite + Cloud Sync
- **Zero-Knowledge**: Client-side encryption
- **Repository Pattern**: Data access abstraction
- **Serverless**: Edge Functions for processing

## Security Stack
- **Database**: SQLCipher (transparent encryption)
- **Sync**: AES-GCM (end-to-end encryption)
- **Keys**: PBKDF2 + Keychain Services
- **Auth**: Biometric + OAuth

## Testing Requirements
- **Unit**: 80%+ coverage (Jest)
- **Integration**: Critical database paths
- **Performance**: All queries <500ms
- **Security**: Encryption flow validation