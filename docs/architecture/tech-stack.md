# Technology Stack

## Core Technology Stack

| Category | Technology | Version | Purpose | Notes |
|----------|------------|---------|---------|--------|
| **Frontend Framework** | React Native | 0.72+ | Cross-platform mobile development | Primary mobile framework |
| | TypeScript | Latest | Type safety and developer experience | Language for all development |
| | Expo SDK | 49+ | Development tooling and platform APIs | Development environment |
| **State Management** | Zustand | Latest | Lightweight state management with persistence | Primary state store |
| | React Query | v4 | Server state management and caching | API state management |
| | AsyncStorage | Latest | Persistent storage for state | State persistence layer |
| **Local Database** | SQLite | Latest | Primary local database engine | Core data storage |
| | SQLCipher | Latest | Transparent encryption for SQLite | Database encryption |
| | Knex.js | Latest | Query builder and migration framework | Database ORM |
| **Machine Learning** | TensorFlow Lite | Latest | On-device transaction categorization | Local ML inference |
| | React Native ML Kit | Latest | Text extraction and pattern recognition | SMS text processing |
| **Security & Auth** | React Native Keychain | Latest | Secure credential storage | Credential management |
| | Expo Local Authentication | Latest | Biometric authentication | Fingerprint/Face ID |
| | Crypto-js | Latest | Client-side encryption operations | Data encryption |
| **Cloud Services** | Supabase Database | Latest | PostgreSQL with real-time subscriptions | Premium cloud storage |
| | Supabase Edge Functions | Latest | Serverless SMS pattern learning API | Pattern processing |
| | Supabase Realtime | Latest | Real-time sync for family sharing | Live data sync |
| **Development Tools** | React Native CLI | Latest | Development environment | Build tools |
| | Jest | Latest | Unit and integration testing framework | Testing framework |
| | ESLint | Latest | Code linting and quality | Code quality |
| | Prettier | Latest | Code formatting | Code formatting |
| **CI/CD & Deployment** | GitHub Actions | Latest | Automated testing and deployment | CI/CD pipeline |
| | EAS Build | Latest | Expo Application Services for builds | App building |
| | EAS Submit | Latest | App store deployment automation | Store deployment |
| | CodeQL | Latest | Security analysis | Security scanning |

## Performance Requirements

| Component | Target Performance | Measurement |
|-----------|-------------------|-------------|
| Database Queries | < 500ms | 10,000+ transaction queries |
| SMS Processing | < 2 seconds | Per message processing |
| App Startup | < 3 seconds | On budget Android devices |
| Memory Usage | < 100MB | Android 8+ with 2GB RAM |
| Query Aggregations | < 1 second | Monthly budget calculations |
| Sync Operations | < 5 seconds | Premium cloud sync |

## Architecture Patterns

| Pattern | Implementation | Purpose |
|---------|----------------|---------|
| Local-First | SQLite + Cloud Sync | Offline functionality |
| Zero-Knowledge | Client-side encryption | Privacy protection |
| Monorepo | Single repository | Code organization |
| Serverless | Edge Functions | Cost optimization |
| Repository Pattern | Data access abstraction | Clean architecture |
| Observer Pattern | Zustand stores | State management |

## Security Framework

| Component | Technology | Implementation |
|-----------|------------|----------------|
| Database Encryption | SQLCipher | Transparent encryption |
| Data Sync Encryption | AES-GCM | End-to-end encryption |
| Key Derivation | PBKDF2 | Password-based keys |
| Credential Storage | Keychain Services | Secure key storage |
| Authentication | Biometric + OAuth | Multi-factor auth |
| Data Anonymization | Custom algorithms | Privacy protection |

## Testing Strategy

| Test Type | Framework | Coverage Target | Purpose |
|-----------|-----------|-----------------|---------|
| Unit Tests | Jest | 80%+ | Business logic validation |
| Integration Tests | Jest + SQLite | Critical paths | Database operations |
| Performance Tests | Custom benchmarks | All queries | Speed validation |
| Security Tests | Custom tools | Encryption flows | Security validation |
| E2E Tests | Detox | Critical flows | User journey validation |

## Development Environment

| Tool | Version | Purpose |
|------|---------|---------|
| Node.js | 18.x LTS | Runtime environment |
| npm | Latest | Package management |
| Expo CLI | Latest | Development server |
| Android Studio | Latest | Android development |
| Xcode | Latest | iOS development |
| VS Code | Latest | Code editor |

## Cloud Infrastructure (Premium Only)

| Service | Provider | Purpose | Usage |
|---------|----------|---------|--------|
| Database | Supabase PostgreSQL | Encrypted data backup | Premium users |
| Real-time Sync | Supabase Realtime | Family sharing | Premium feature |
| Edge Functions | Supabase | SMS pattern learning | Anonymous processing |
| Authentication | Supabase Auth | OAuth integration | Premium accounts |
| File Storage | Supabase Storage | Receipt attachments | Premium feature |