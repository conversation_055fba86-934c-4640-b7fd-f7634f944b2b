# Essential Development Standards

> Quick reference for daily development. See full documentation for comprehensive details.

## TypeScript & React Native Standards

### ✅ Required Patterns
```typescript
// Functional components with proper typing
interface ComponentProps {
  account: Account;
  onSelect: (id: string) => void;
}

const Component: React.FC<ComponentProps> = ({ account, onSelect }) => {
  const theme = useTheme();
  const styles = createComponentStyles(theme);
  
  return (/* JSX */);
};

export default React.memo(Component); // Only for heavy components
```

### ❌ Forbidden Patterns
- `any` types (except migrations)
- Class components
- String concatenation in SQL queries
- Hardcoded secrets or credentials
- Direct database access from components

## Database Standards

### ✅ Required Patterns
```typescript
// Parameterized queries
db.query('SELECT * FROM transactions WHERE account_id = ?', [accountId]);

// Database transactions
db.transaction(async (trx) => {
  await trx('accounts').update({ balance: newBalance }).where('id', accountId);
  await trx('transactions').insert(transactionData);
});

// Knex migrations
exports.up = function(knex) {
  return knex.schema.createTable('table_name', (table) => {
    table.string('id').primary();
    table.timestamps(true, true);
  });
};
```

## Theme System Integration

### ✅ Standard Pattern
```typescript
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles, useCommonStyles } from '@/shared/theme';

const createComponentStyles = createStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.card,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  title: {
    ...theme.typography.h3,
    color: theme.colors.text,
  },
}));

const MyComponent = () => {
  const theme = useTheme();
  const commonStyles = useCommonStyles(theme);
  const styles = createComponentStyles(theme);
  
  return (
    <View style={[commonStyles.card, styles.container]}>
      <Text style={[commonStyles.textTitle, styles.title]}>Title</Text>
    </View>
  );
};
```

### Common Styles Quick Access
- **Containers**: `card`, `cardSelected`, `cardElevated`, `surface`
- **Layout**: `row`, `column`, `centered`, `flex1`, `rowSpaceBetween`
- **Buttons**: `buttonPrimary`, `buttonSecondary`, `buttonOutline`, `buttonGhost`
- **Text**: `textPrimary`, `textSecondary`, `textTitle`, `textSubtitle`
- **Forms**: `input`, `inputFocused`, `inputError`, `inputLabel`

## Tech Stack Essentials

### Core Dependencies
- **React Native**: 0.72+ with TypeScript (strict mode)
- **Expo SDK**: 49+
- **State**: Zustand + React Query v4
- **Database**: SQLite + SQLCipher + Knex.js
- **Security**: React Native Keychain + Expo Local Authentication
- **Cloud**: Supabase (PostgreSQL + real-time + edge functions)

### Performance Targets
- Database queries: **<500ms**
- SMS processing: **<2s**
- App startup: **<3s**
- Memory usage: **<100MB**

## Security Requirements

### 🔐 Encryption Standards
```typescript
// Client-side encryption before storage/sync
const encryptedData = AES.encrypt(JSON.stringify(sensitiveData), key).toString();

// Secure key storage
await Keychain.setInternetCredentials(
  'finvibe-user-key',
  username,
  encryptionKey
);

// Biometric authentication
const biometricResult = await LocalAuthentication.authenticateAsync({
  promptMessage: 'Authenticate to access your financial data',
  fallbackLabel: 'Use passcode',
});
```

### Required Security Practices
- All sensitive data encrypted with AES-GCM
- React Native Keychain for credential storage
- Biometric auth with device fallback
- HTTPS only with certificate pinning
- No secrets in code (environment variables)

## Testing Requirements

### Coverage Targets
- **Unit Tests**: 80%+ coverage with Jest
- **Integration**: Critical database operations
- **Performance**: All queries <500ms
- **Security**: Encryption flow validation

### Test Patterns
```typescript
// Unit test example
describe('TransactionService', () => {
  it('should create transaction with proper validation', async () => {
    const transaction = await TransactionService.create(validTransactionData);
    expect(transaction.id).toBeDefined();
    expect(transaction.amount).toBe(validTransactionData.amount);
  });
});

// Performance test example
it('should query 10k transactions in under 500ms', async () => {
  const start = Date.now();
  const transactions = await TransactionRepository.findByDateRange(
    startDate, endDate
  );
  const duration = Date.now() - start;
  expect(duration).toBeLessThan(500);
});
```

## Architecture Patterns

### Repository Pattern
```typescript
export class AccountRepository extends BaseRepository<Account> {
  protected tableName = 'accounts';
  
  async findByUserId(userId: string): Promise<Account[]> {
    return this.query()
      .where('user_id', userId)
      .orderBy('created_at', 'desc');
  }
}
```

### Service Pattern
```typescript
export class AccountService {
  constructor(
    private accountRepo: AccountRepository,
    private transactionRepo: TransactionRepository
  ) {}
  
  async createAccount(data: CreateAccountData): Promise<Account> {
    // Business logic and validation
    return this.accountRepo.create(validatedData);
  }
}
```

### Store Pattern (Zustand)
```typescript
interface AccountStore {
  accounts: Account[];
  loading: boolean;
  fetchAccounts: () => Promise<void>;
  createAccount: (data: CreateAccountData) => Promise<Account>;
}

export const useAccountStore = create<AccountStore>((set, get) => ({
  accounts: [],
  loading: false,
  
  fetchAccounts: async () => {
    set({ loading: true });
    const accounts = await AccountService.getAll();
    set({ accounts, loading: false });
  },
}));
```

---
> 💡 **Need More Details?** 
> - Full coding standards: `docs/architecture/coding-standards.md`
> - Complete tech stack: `docs/architecture/tech-stack.md`
> - Theme system guide: `docs/architecture/themed-components-guide.md`
> - Project structure: Ask Serena MCP for current implementation details