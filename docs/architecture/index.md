# FinVibe Technical Architecture Document

## Table of Contents

- [FinVibe Technical Architecture Document](#table-of-contents)
  - [Executive Summary](./executive-summary.md)
    - [Key Architectural Principles](./executive-summary.md#key-architectural-principles)
  - [1. System Architecture Overview](./1-system-architecture-overview.md)
    - [1.1 High-Level Architecture Diagram](./1-system-architecture-overview.md#11-high-level-architecture-diagram)
    - [1.2 Technology Stack](./1-system-architecture-overview.md#12-technology-stack)
  - [2. Data Architecture](./2-data-architecture.md)
    - [2.1 Database Design Strategy](./2-data-architecture.md#21-database-design-strategy)
    - [2.2 Data Synchronization Strategy](./2-data-architecture.md#22-data-synchronization-strategy)
  - [3. SMS Processing and ML Pipeline](./3-sms-processing-and-ml-pipeline.md)
    - [3.1 SMS Processing Architecture](./3-sms-processing-and-ml-pipeline.md#31-sms-processing-architecture)
    - [3.2 ML Categorization Strategy](./3-sms-processing-and-ml-pipeline.md#32-ml-categorization-strategy)
  - [4. Security and Encryption Architecture](./4-security-and-encryption-architecture.md)
    - [4.1 Zero-Knowledge Security Model](./4-security-and-encryption-architecture.md#41-zero-knowledge-security-model)
    - [4.2 Privacy-First Cloud Sync](./4-security-and-encryption-architecture.md#42-privacy-first-cloud-sync)
  - [5. Real-time Family Sharing Architecture](./5-real-time-family-sharing-architecture.md)
    - [5.1 Family Sharing Strategy](./5-real-time-family-sharing-architecture.md#51-family-sharing-strategy)
    - [5.2 Family Group Management](./5-real-time-family-sharing-architecture.md#52-family-group-management)
  - [6. Performance Optimization Strategy](./6-performance-optimization-strategy.md)
    - [6.1 Database Performance](./6-performance-optimization-strategy.md#61-database-performance)
    - [6.2 Memory and Caching Strategy](./6-performance-optimization-strategy.md#62-memory-and-caching-strategy)
    - [6.3 Background Processing](./6-performance-optimization-strategy.md#63-background-processing)
  - [7. Application Architecture and Data Flow](./7-application-architecture-and-data-flow.md)
    - [7.1 Layered Architecture](./7-application-architecture-and-data-flow.md#71-layered-architecture)
    - [7.2 State Management with Zustand](./7-application-architecture-and-data-flow.md#72-state-management-with-zustand)
  - [8. Cloud Services Integration](./8-cloud-services-integration.md)
    - [8.1 Supabase Architecture](./8-cloud-services-integration.md#81-supabase-architecture)
    - [8.2 Anonymous Pattern Learning](./8-cloud-services-integration.md#82-anonymous-pattern-learning)
  - [9. CI/CD Pipeline and Quality Assurance](./9-cicd-pipeline-and-quality-assurance.md)
    - [9.1 Automated Testing Strategy](./9-cicd-pipeline-and-quality-assurance.md#91-automated-testing-strategy)
    - [9.2 Claude Code Integration](./9-cicd-pipeline-and-quality-assurance.md#92-claude-code-integration)
  - [10. Deployment and Infrastructure](./10-deployment-and-infrastructure.md)
    - [10.1 Mobile App Deployment](./10-deployment-and-infrastructure.md#101-mobile-app-deployment)
    - [10.2 Cloud Infrastructure](./10-deployment-and-infrastructure.md#102-cloud-infrastructure)
  - [Implementation Roadmap](./implementation-roadmap.md)
    - [Phase 1 (Months 1-4): Core Local-First Functionality](./implementation-roadmap.md#phase-1-months-1-4-core-local-first-functionality)
    - [Phase 2 (Months 5-8): SMS Processing and ML](./implementation-roadmap.md#phase-2-months-5-8-sms-processing-and-ml)
    - [Phase 3 (Months 9-12): Premium Features](./implementation-roadmap.md#phase-3-months-9-12-premium-features)
    - [Phase 4 (Months 13-18): Optimization and Expansion](./implementation-roadmap.md#phase-4-months-13-18-optimization-and-expansion)
  - [Technical Reference Files](./technical-reference-files.md)
    - [For Development Teams:](./technical-reference-files.md#for-development-teams)
