# 4. Security and Encryption Architecture

## 4.1 Zero-Knowledge Security Model

**Client-Side Encryption:**
- AES-GCM 256-bit encryption with PBKDF2 key derivation
- Device-specific salt generation for key uniqueness
- Master key encrypted with user passphrase
- All encryption/decryption performed on device

**Local Database Security:**
- SQLCipher transparent encryption for SQLite
- Encryption keys stored in platform keychain
- Additional security pragmas for memory protection
- Row-level access control for family sharing

**Authentication Strategy:**
- Biometric authentication (Face ID/Fingerprint) as primary
- PIN/Pattern fallback for non-biometric devices
- Progressive delay on failed attempts (3 attempts → 30sec, 6 attempts → 5min)
- No cloud account required for free users

> 🔐 **Implementation Reference:** [Encryption Code Examples](./technical/implementation-examples.md#client-side-encryption)

## 4.2 Privacy-First Cloud Sync

**Premium User Cloud Sync:**
- End-to-end encryption before data leaves device
- Server cannot decrypt user financial data
- Family sharing with granular permission control
- Device authorization required for data access

**Anonymous Pattern Learning:**
- SMS patterns anonymized before cloud contribution
- No personally identifiable information shared
- Community benefits without privacy compromise

---
