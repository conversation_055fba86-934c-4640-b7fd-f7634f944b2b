# Themed Components Guide

This guide explains how to create consistent, maintainable styled components using our theme system.

> 📋 **Design Reference**: Always refer to the comprehensive [Component Specifications](../design/component-specifications.md) and [UX Specification](../design/ux-specification.md) for detailed component requirements, accessibility standards, and interaction patterns.

## Overview

Instead of using inline styles everywhere or hardcoded StyleSheet objects, we use a themed styling system that:

- Maintains consistency across the app
- Supports light/dark mode automatically  
- Makes it easy to update designs globally
- Reduces code duplication
- Improves maintainability

## Core Utilities

### `createStyles`

Creates component-specific themed styles:

```typescript
import { createStyles } from '@/shared/theme';

const createMyComponentStyles = createStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.card,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  title: {
    ...theme.typography.h3,
    color: theme.colors.text,
  },
}));

// In your component:
const styles = createMyComponentStyles(theme);
```

### `useCommonStyles`

Provides reusable styled components:

```typescript
import { useCommonStyles } from '@/shared/theme';

const MyComponent = () => {
  const theme = useTheme();
  const commonStyles = useCommonStyles(theme);
  
  return (
    <View style={commonStyles.card}>
      <Text style={commonStyles.textTitle}>Title</Text>
      <Text style={commonStyles.textSecondary}>Subtitle</Text>
    </View>
  );
};
```

## Pattern Example: AccountListItem

Here's how we refactored AccountListItem to use the themed system:

### 1. Import Theme Utilities

```typescript
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles, useCommonStyles } from '@/shared/theme';
```

### 2. Create Component Styles

```typescript
const createAccountItemStyles = createStyles((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.lg,
    marginVertical: theme.spacing.xs,
    marginHorizontal: theme.spacing.md,
    borderLeftWidth: 4,
    ...theme.shadows.sm,
  },
  accountName: {
    ...theme.typography.body,
    color: theme.colors.text,
    fontWeight: '600',
    marginBottom: 2,
  },
  // ... more styles
}));
```

### 3. Use in Component

```typescript
export const AccountListItem = ({ account, ...props }) => {
  const theme = useTheme();
  const commonStyles = useCommonStyles(theme);
  const styles = createAccountItemStyles(theme);
  
  return (
    <TouchableOpacity style={styles.container}>
      <View style={commonStyles.iconContainer}>
        {/* content */}
      </View>
    </TouchableOpacity>
  );
};
```

## Available Common Styles

### Container Styles
- `commonStyles.card` - Standard card container
- `commonStyles.cardSelected` - Selected card state  
- `commonStyles.cardElevated` - Elevated card with shadow
- `commonStyles.cardOutlined` - Outlined card variant
- `commonStyles.container` - Main app container
- `commonStyles.surface` - Surface background

### Layout Styles
- `commonStyles.row` - Horizontal flex layout
- `commonStyles.rowSpaceBetween` - Horizontal with space-between
- `commonStyles.column` - Vertical flex layout
- `commonStyles.centered` - Centered content
- `commonStyles.flex1` - flex: 1

### Button Styles (Core Variants)
- `commonStyles.buttonBase` - Base button styling
- `commonStyles.buttonPrimary` - Primary button variant
- `commonStyles.buttonSecondary` - Secondary button variant
- `commonStyles.buttonOutline` - Outline button variant
- `commonStyles.buttonGhost` - Ghost button variant
- `commonStyles.buttonDanger` - Danger button variant
- `commonStyles.buttonDisabled` - Disabled button state

### Button Size Variants
- `commonStyles.buttonSm` - Small button (40dp min height)
- `commonStyles.buttonMd` - Medium button (48dp min height) 
- `commonStyles.buttonLg` - Large button (56dp min height)

### Text Styles
- `commonStyles.textPrimary` - Primary text
- `commonStyles.textSecondary` - Secondary text
- `commonStyles.textDisabled` - Disabled text
- `commonStyles.textBold` - Bold text
- `commonStyles.textTitle` - Title text (H3 equivalent)
- `commonStyles.textSubtitle` - Subtitle text

### Form Input Styles
- `commonStyles.inputBase` - Base input styling
- `commonStyles.inputFocused` - Focused input state
- `commonStyles.inputError` - Error input state
- `commonStyles.inputDisabled` - Disabled input state
- `commonStyles.inputLabel` - Input label text

### Icon Styles
- `commonStyles.iconContainer` - Large icon container (48x48)
- `commonStyles.iconContainerSmall` - Small icon container (32x32)
- `commonStyles.iconContainerMedium` - Medium icon container (40x40)

### Status Styles
- `commonStyles.statusIndicator` - Status row layout
- `commonStyles.statusIcon` - Status icon styling
- `commonStyles.statusText` - Status text styling
- `commonStyles.statusDot` - Status dot indicator
- `commonStyles.statusBadge` - Status badge container

### Progress Styles
- `commonStyles.progressContainer` - Progress bar container
- `commonStyles.progressBar` - Progress bar fill
- `commonStyles.progressText` - Progress percentage text

### Selection Styles
- `commonStyles.selectionIndicator` - Selection checkmark container
- `commonStyles.selectionCheckmark` - Checkmark text

### Border Styles
- `commonStyles.borderLeft` - Left border accent
- `commonStyles.borderBottom` - Bottom border divider
- `commonStyles.borderRadius` - Standard border radius
- `commonStyles.borderRadiusLg` - Large border radius

### Utility Styles
- `commonStyles.marginXs/Sm/Md/Lg/Xl` - Margin utilities
- `commonStyles.paddingXs/Sm/Md/Lg/Xl` - Padding utilities  
- `commonStyles.marginBottomXs/Sm/Md` - Bottom margin utilities
- `commonStyles.successColor/errorColor/warningColor/infoColor` - Status color utilities
- `commonStyles.incomeColor/expenseColor/transferColor` - Transaction type colors

## Best Practices

### 1. Use Common Styles First
Always check if a common style exists before creating component-specific styles.

```typescript
// Good - reuse common styles
<View style={commonStyles.card}>
  <Text style={commonStyles.textTitle}>Title</Text>
</View>

// Avoid - custom inline styles
<View style={{backgroundColor: theme.colors.card, padding: 16}}>
```

### 2. Create Component-Specific Styles for Complex Components
For complex components with unique layouts, create component-specific styles:

```typescript
const createMyComplexStyles = createStyles((theme) => ({
  uniqueLayout: {
    // Complex layout specific to this component
    position: 'absolute',
    top: theme.spacing.lg,
    // ...
  }
}));
```

### 3. Combine Styles Appropriately
You can combine common styles with component-specific ones:

```typescript
<View style={[commonStyles.card, styles.specialCard]} />
<Text style={[commonStyles.textPrimary, styles.boldText]} />
```

### 4. Use Theme Values
Always use theme values instead of hardcoded values:

```typescript
// Good
backgroundColor: theme.colors.card,
padding: theme.spacing.md,
fontSize: theme.typography.body.fontSize,

// Avoid  
backgroundColor: '#ffffff',
padding: 16,
fontSize: 14,
```

### 5. Maintain Accessibility
Use the theme's accessibility features:

```typescript
// Include accessibility props
<TouchableOpacity
  style={styles.container}
  accessible={true}
  accessibilityLabel="Account item"
  accessibilityRole="button"
>
```

## Migration Strategy

When updating existing components:

1. **Import theme utilities**
2. **Replace hardcoded values** with theme values
3. **Use common styles** where possible
4. **Create component styles** for unique cases
5. **Remove old StyleSheet** definitions
6. **Test thoroughly** with both light and dark themes

This approach gives us:
- ✅ Consistent design system
- ✅ Automatic dark mode support
- ✅ Easy global design updates
- ✅ Better code maintainability
- ✅ Reduced code duplication

## Component Implementation Patterns

### Button Component Pattern
Following the [Button specifications](../design/component-specifications.md#1-button-component):

```typescript
const createButtonStyles = createStyles((theme) => ({
  // Combine common styles with component-specific ones
  primaryButton: [
    commonStyles.buttonBase,
    commonStyles.buttonPrimary,
    commonStyles.buttonMd,
  ],
  secondaryButton: [
    commonStyles.buttonBase, 
    commonStyles.buttonSecondary,
    commonStyles.buttonMd,
  ],
  // Size variants
  smallButton: [commonStyles.buttonBase, commonStyles.buttonSm],
  largeButton: [commonStyles.buttonBase, commonStyles.buttonLg],
}));
```

### Form Input Pattern
Following the [CurrencyInput specifications](../design/component-specifications.md#5-currency-input):

```typescript
const createFormStyles = createStyles((theme) => ({
  input: [
    commonStyles.inputBase,
    // Add component-specific styles here
  ],
  inputFocused: [
    commonStyles.inputBase,
    commonStyles.inputFocused,
  ],
  inputError: [
    commonStyles.inputBase, 
    commonStyles.inputError,
  ],
}));
```

### Card Component Pattern
Following the [Card specifications](../design/component-specifications.md#2-card-component):

```typescript
const createCardStyles = createStyles((theme) => ({
  defaultCard: [commonStyles.card],
  elevatedCard: [commonStyles.card, commonStyles.cardElevated],
  selectedCard: [commonStyles.card, commonStyles.cardSelected],
}));
```

## Accessibility Requirements (WCAG AA)

### Color Contrast
- Text contrast: 4.5:1 for normal text, 3:1 for large text
- Interactive elements: 3:1 contrast for focus indicators
- Never rely on color alone - always provide text/icon alternatives

```typescript
// Good - provides both color and text
<View style={[commonStyles.statusIndicator, { backgroundColor: theme.colors.error }]}>
  <Text style={commonStyles.statusText}>Over budget</Text>
</View>

// Bad - color only
<View style={{ backgroundColor: theme.colors.error }} />
```

### Touch Targets
- Minimum 48dp for all interactive elements
- 8dp spacing between adjacent targets
- Use common button styles to ensure compliance

```typescript
// Automatically compliant
<TouchableOpacity style={commonStyles.buttonMd}>
  <Text>Button Text</Text>
</TouchableOpacity>
```

### Screen Reader Support
- Proper accessibility labels and hints
- Semantic markup with accessibilityRole
- Live region announcements for dynamic content

```typescript
<TouchableOpacity
  style={commonStyles.buttonPrimary}
  accessibilityRole="button"
  accessibilityLabel="Add transaction"
  accessibilityHint="Opens transaction entry form"
>
  <Text>Add</Text>
</TouchableOpacity>
```

## Performance Considerations

### Style Optimization
- Use `createStyles` to leverage StyleSheet.create caching
- Avoid inline styles for better performance
- Combine common styles efficiently

```typescript
// Optimized - styles cached by StyleSheet.create
const styles = createMyComponentStyles(theme);
<View style={styles.container} />

// Avoid - creates new style object each render  
<View style={{ backgroundColor: theme.colors.card, padding: 16 }} />
```

### Animation Performance
- Use theme values for consistent animations
- Leverage hardware acceleration with transform and opacity
- Respect reduced motion preferences

```typescript
const animationStyles = {
  transform: [{ scale: pressed ? 0.95 : 1 }],
  opacity: disabled ? 0.6 : 1,
};
```

## Testing Components

### Component Testing Checklist
- [ ] All variants render correctly
- [ ] Theme changes apply properly
- [ ] Accessibility props are set
- [ ] Touch targets meet minimum size
- [ ] Screen reader navigation works
- [ ] Keyboard navigation functions
- [ ] Performance benchmarks pass

### Example Test Pattern
```typescript
describe('ThemedButton', () => {
  it('applies correct theme styles', () => {
    const { getByRole } = render(
      <Button title="Test" variant="primary" />,
      { wrapper: TestWrapper }
    );
    
    const button = getByRole('button');
    expect(button).toHaveStyle({ backgroundColor: theme.colors.primary });
  });
  
  it('meets accessibility requirements', () => {
    const { getByRole } = render(
      <Button title="Test" accessibilityLabel="Custom label" />
    );
    
    const button = getByRole('button');
    expect(button).toHaveAccessibilityLabel('Custom label');
    // Touch target size automatically validated by common styles
  });
});
```

## Migration Checklist

When updating existing components to use the themed system:

1. **Import theme utilities** ✅
2. **Replace hardcoded values** with theme values ✅  
3. **Use common styles** where possible ✅
4. **Create component styles** for unique cases ✅
5. **Remove old StyleSheet** definitions ✅
6. **Add accessibility props** per design specs ✅
7. **Test with both light/dark themes** ✅
8. **Verify WCAG AA compliance** ✅
9. **Performance test** on target devices ✅
10. **Update component documentation** ✅

## Next Steps

Apply this pattern to all components in priority order:
1. Core UI components (Button ✅, Card, ProgressBar, SyncStatusIndicator)
2. Form components (CurrencyInput, TextInput, DatePicker)  
3. Feature components (BudgetProgressCard, TransactionListItem ✅, AccountListItem ✅)
4. Screen components (Dashboard, Transaction Management, etc.)
5. Navigation components