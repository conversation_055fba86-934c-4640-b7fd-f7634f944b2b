# Technical Reference Files

## Architecture Reference:

- **Technology Stack:** [Complete Tech Stack](./tech-stack.md)
- **Development Standards:** [Coding Standards](./coding-standards.md)
- **Project Structure:** [Source Tree Organization](./source-tree.md)

## Implementation Reference:

- **Database Implementation:** [Database Schemas](./technical/database-schemas.md)
- **Code Examples:** [Implementation Examples](./technical/implementation-examples.md)  
- **CI/CD Setup:** [Pipeline Configuration](./technical/cicd-pipeline.md)
- **Log Processing:** [Automated Debugging](./technical/log-processing.md)

This architecture provides a comprehensive foundation for building FinVibe as a privacy-first, local-first personal finance application that scales to premium features while maintaining the core value proposition of ultra-affordable pricing through reduced infrastructure dependencies.