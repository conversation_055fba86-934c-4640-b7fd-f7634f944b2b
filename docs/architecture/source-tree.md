# Source Tree Structure

## Monorepo Organization

| Directory | Purpose | Contents | Standards |
|-----------|---------|----------|-----------|
| **`/src`** | Main application source | React Native mobile app | TypeScript + React Native |
| **`/functions`** | Serverless cloud functions | SMS pattern learning APIs | Node.js + TypeScript |
| **`/docs`** | Project documentation | Architecture and PRD docs | Markdown documentation |
| **`/assets`** | Static resources | Images, fonts, icons | Optimized mobile assets |
| **`/scripts`** | Build and utility scripts | Development automation | Shell + Node.js scripts |

## Mobile Application Structure (`/src`)

| Directory | Purpose | Components | Architecture Layer |
|-----------|---------|------------|-------------------|
| **`/src/presentation`** | UI Components & Screens | React Native components | Presentation Layer |
| **`/src/business`** | Business Logic Services | Domain services and managers | Business Logic Layer |
| **`/src/data`** | Data Access & Storage | Repositories and data sources | Data Access Layer |
| **`/src/platform`** | Platform Integrations | SMS, biometric, file system | Platform Integration Layer |
| **`/src/shared`** | Shared Utilities | Common types, constants, utils | Cross-cutting Concerns |

## Detailed Directory Structure

### Testing Structure (`/src/__tests__`)
```
__tests__/
├── setup.ts                     # Test configuration and setup
├── unit/                        # Unit tests
│   ├── database/               # Database layer tests
│   │   └── MigrationManager.test.ts
│   ├── business/               # Business logic tests
│   │   └── AccountService.test.ts
│   ├── stores/                 # State store tests
│   │   ├── accountStore.test.ts
│   │   └── categoryStore.test.ts
│   ├── utils/                  # Utility function tests
│   │   ├── transactionUtils.test.ts
│   │   └── transactionMetadataValidation.test.ts
│   ├── screens/                # Screen component tests
│   │   └── AccountManagementScreen.test.tsx
│   ├── components/             # Component tests
│   │   ├── AccountList.simple.test.tsx
│   │   ├── AccountCreationForm.test.tsx
│   │   └── AccountListItem.test.tsx
│   └── services/               # Service layer tests
│       ├── TransactionService.enhanced.test.ts
│       └── SMSParsingService.test.ts
├── integration/                # Integration tests
│   └── auth/                   # Authentication flow tests
│       └── AuthenticationFlow.test.ts
├── auth/                       # Authentication-specific tests
│   ├── KeychainService.test.ts
│   └── BiometricAuthService.test.ts
├── business/                   # Business logic tests
│   └── services/
│       └── calculators/        # Calculator tests
│           ├── AccountCalculatorFactory.test.ts
│           ├── BaseAccountCalculator.test.ts
│           ├── CreditCardCalculator.test.ts
│           └── LoanCalculator.test.ts
├── stores/                     # Store tests
│   └── transactionStore.test.ts
├── utils/                      # Utility tests
│   ├── categorization.test.ts
│   └── accountValidation.test.ts
├── feedback/                   # Feedback system tests
│   └── FeedbackService.test.ts
├── components/                 # Component tests
│   └── transactions/
│       ├── TransactionList.test.tsx
│       ├── TransactionListItem.test.tsx
│       └── TransactionEntryForm.test.tsx
├── performance/                # Performance tests
│   └── database.performance.test.ts
├── presentation/               # Presentation layer tests
│   └── components/
│       └── dashboard/
│           ├── ExpensesPieChart.test.tsx
│           └── TransactionsList.test.tsx
├── services/                   # Service tests
│   ├── CategoryService.test.ts
│   ├── TransactionService.test.ts
│   ├── SystemCategoriesService.test.ts
│   └── CategorySuggestionService.test.ts
└── migrations/                 # Database migration tests
    └── 010_add_account_metadata.test.ts
```

### Presentation Layer (`/src/presentation`)
```
presentation/
├── screens/                    # Main application screens
│   ├── auth/                  # Authentication and security screens
│   ├── accounts/              # Account management
│   ├── transactions/          # Transaction management
│   ├── feedback/              # User feedback screens
│   ├── menu/                  # Menu and navigation screens
│   ├── dashboard/             # Dashboard and overview (planned)
│   ├── budget/               # Budget planning and tracking (planned)
│   └── settings/             # User settings and preferences (planned)
├── components/               # Reusable UI components
│   ├── core/                # Core UI components (Button, Card, etc.)
│   ├── form/                # Form inputs and validation
│   ├── common/              # Common UI elements and utilities
│   ├── auth/                # Authentication-specific components
│   ├── accounts/            # Account management components
│   ├── transactions/        # Transaction-related components
│   ├── feedback/            # Feedback system components
│   ├── dashboard/           # Dashboard-specific components
│   ├── categories/          # Category management components
│   ├── charts/              # Data visualization components (planned)
│   └── modals/              # Modal dialogs and overlays (planned)
├── navigation/              # Navigation configuration
│   ├── AppNavigator.tsx     # Main navigation setup (planned)
│   ├── TabNavigator.tsx     # Bottom tab navigation (planned)
│   └── StackNavigator.tsx   # Screen stack navigation (planned)
└── ErrorBoundary.tsx        # Global error boundary component
```

### Business Logic Layer (`/src/business`)
```
business/
├── services/                   # Core business services
│   ├── TransactionService.ts  # Transaction CRUD operations
│   ├── BudgetService.ts       # Budget management logic
│   ├── AccountService.ts      # Account management
│   ├── CategoryService.ts     # Category management
│   ├── FeedbackService.ts     # User feedback handling
│   ├── SMSParsingService.ts   # SMS parsing and processing
│   ├── MLCategorizationService.ts # Machine learning categorization
│   ├── SystemCategoriesService.ts # System category management
│   ├── CategorySuggestionService.ts # ML-based category suggestions
│   ├── PerformanceService.ts  # Performance monitoring
│   ├── TelemetryService.ts    # Analytics and telemetry
│   ├── BuildHookService.ts    # Build integration hooks
│   ├── LogAggregatorService.ts # Log collection and processing
│   ├── MockAccountService.ts  # Development/testing service
│   ├── SyncService.ts         # Cloud synchronization
│   └── calculators/           # Account-specific calculation logic
│       ├── BaseAccountCalculator.ts     # Base calculator interface
│       ├── AccountCalculatorFactory.ts  # Calculator factory pattern
│       ├── DefaultAccountCalculator.ts  # Standard account calculations
│       ├── CreditCardCalculator.ts      # Credit card-specific logic
│       └── LoanCalculator.ts            # Loan account calculations
├── managers/                   # Business logic managers
│   ├── TransactionManager.ts  # Transaction business rules
│   ├── BudgetManager.ts       # Budget calculation logic
│   ├── AccountManager.ts      # Account balance management
│   └── SMSManager.ts          # SMS processing coordination
├── models/                     # Domain models and interfaces
│   ├── Transaction.ts         # Transaction entity
│   ├── Account.ts            # Account entity
│   ├── Budget.ts             # Budget entity
│   ├── Category.ts           # Category entity
│   └── User.ts               # User entity
└── validators/                 # Business rule validation
    ├── TransactionValidator.ts # Transaction validation rules
    ├── BudgetValidator.ts     # Budget validation rules
    └── AccountValidator.ts    # Account validation rules
```

### Data Access Layer (`/src/data`)
```
data/
├── repositories/               # Data access repositories
│   ├── index.ts               # Repository exports
│   ├── BaseRepository.ts      # Base repository with common functionality
│   ├── TransactionRepository.ts # Transaction data access
│   ├── AccountRepository.ts    # Account data access
│   ├── CategoryRepository.ts   # Category data access
│   ├── BudgetRepository.ts     # Budget data access (planned)
│   └── UserRepository.ts       # User data access (planned)
├── database/                   # Database configuration
│   ├── index.ts               # Database exports
│   ├── DatabaseService.ts     # SQLite database setup and management
│   ├── MigrationManager.ts    # Database migration management
│   ├── migrations/            # Database schema migrations
│   │   ├── 001_create_accounts.js
│   │   ├── 002_create_categories.js
│   │   ├── 003_create_transactions.js
│   │   ├── 004_create_budgets.js
│   │   ├── 005_create_budget_categories.js
│   │   ├── 006_create_sms_patterns.js
│   │   ├── 007_create_user_settings.js
│   │   ├── 008_create_due_dates.js
│   │   ├── 009_create_dashboard_summary.js
│   │   ├── 010_add_account_metadata.js
│   │   ├── 011_add_transaction_metadata.js
│   │   ├── 012_create_feedback_submissions.js
│   │   ├── 013_create_feature_votes.js
│   │   ├── 014_create_early_access_tokens.js
│   │   └── 015_create_feedback_sync_queue.js
│   └── schemas/               # Database schema definitions
│       ├── createTables.sql   # SQL table creation scripts
│       ├── indexes.sql        # Database indexes
│       ├── AccountSchema.ts   # Account schema (planned)
│       ├── TransactionSchema.ts # Transaction schema (planned)
│       └── BudgetSchema.ts    # Budget schema (planned)
├── systemCategories.ts         # System-defined categories
├── smsPatterns.ts             # SMS parsing patterns
├── storage/                    # Storage management (planned)
│   ├── AsyncStorageManager.ts # Local storage operations
│   ├── FileStorageManager.ts  # File system operations
│   └── CacheManager.ts        # Data caching logic
└── sync/                       # Cloud synchronization (planned)
    ├── SyncManager.ts         # Main sync coordination
    ├── ConflictResolver.ts    # Sync conflict resolution
    └── EncryptionManager.ts   # Data encryption for sync
```

### Platform Integration Layer (`/src/platform`)
```
platform/
├── sms/                        # SMS access and processing (planned)
│   ├── SMSReader.ts           # SMS permission and reading
│   ├── PatternMatcher.ts      # SMS pattern recognition
│   └── SMSPermissionManager.ts # Permission handling
├── biometric/                  # Biometric authentication
│   ├── BiometricAuthService.ts # Fingerprint/Face ID integration
│   ├── AuthenticationManager.ts # Auth state management
│   ├── KeychainService.ts     # Secure keychain operations
│   ├── AutoLockService.ts     # Auto-lock functionality
│   ├── SecurityAuditLogger.ts # Security event logging
│   ├── PremiumGatekeeper.tsx  # Premium feature access control
│   ├── OAuthService.ts        # OAuth integration
│   ├── AppStateManager.ts     # App state monitoring
│   └── DeviceResetManager.ts  # Device reset handling
├── filesystem/                 # File system operations (planned)
│   ├── FileManager.ts         # File operations
│   ├── ImageManager.ts        # Image processing
│   └── BackupManager.ts       # Data backup operations
├── notifications/              # Push notifications (planned)
│   ├── NotificationManager.ts # Notification handling
│   └── PushNotificationService.ts # Push notification setup
└── ml/                         # Machine learning integration (planned)
    ├── MLModelManager.ts      # TensorFlow Lite integration
    ├── TextExtraction.ts      # ML Kit text processing
    └── CategoryPredictor.ts   # Transaction categorization
```

### Shared Utilities (`/src/shared`)
```
shared/
├── types/                      # TypeScript type definitions
│   ├── index.ts               # Main type exports
│   ├── feedback.ts            # Feedback system types
│   ├── calculators.ts         # Account calculator types
│   ├── accountMetadata.ts     # Account metadata types
│   ├── api.ts                 # API response types (planned)
│   ├── database.ts            # Database entity types (planned)
│   ├── components.ts          # Component prop types (planned)
│   └── global.ts              # Global type definitions
├── constants/                  # Application constants (planned)
│   ├── api.ts                 # API endpoints and configs
│   ├── database.ts            # Database configuration
│   ├── storage.ts             # Storage keys and configs
│   └── app.ts                 # App-wide constants
├── utils/                      # Utility functions
│   ├── Logger.ts              # Logging utilities
│   ├── ErrorHandler.ts        # Error handling utilities
│   ├── deviceFingerprint.ts   # Device identification
│   ├── accountValidation.ts   # Account validation functions
│   ├── transactionUtils.ts    # Transaction utility functions
│   ├── transactionMetadataValidation.ts # Transaction metadata validation
│   ├── categorization.ts      # Category management utilities
│   ├── iconUtils.ts           # Icon handling utilities
│   ├── unifiedIconMapping.ts  # Icon mapping system
│   ├── formatters.ts          # Date, currency, text formatters (planned)
│   ├── validators.ts          # Input validation functions (planned)
│   ├── encryption.ts          # Encryption utility functions (planned)
│   └── performance.ts         # Performance monitoring utils (planned)
├── theme/                      # Theme and styling system
│   ├── index.ts               # Theme exports
│   ├── createStyles.ts        # Style creation utilities
│   └── ThemeProvider.tsx      # Theme provider component
├── hooks/                      # Custom React hooks (planned)
│   ├── useTransactions.ts     # Transaction management hook
│   ├── useBudgets.ts          # Budget management hook
│   ├── useAccounts.ts         # Account management hook
│   ├── useSMSProcessing.ts    # SMS processing hook
│   └── useSync.ts             # Synchronization hook
└── stores/                     # Zustand state stores
    ├── index.ts               # Store exports
    ├── transactionStore.ts    # Transaction state management
    ├── accountStore.ts        # Account state management
    ├── categoryStore.ts       # Category state management
    ├── feedbackStore.ts       # Feedback system state
    ├── mockAccountStore.ts    # Mock account store for development
    ├── authStore.ts           # Authentication state
    ├── settingsStore.ts       # User settings state
    └── budgetStore.ts         # Budget state management (planned)
```

## Serverless Functions (`/functions`)
```
functions/
├── sms-pattern-learning/       # SMS pattern learning API
│   ├── index.ts               # Main function entry point
│   ├── patternProcessor.ts    # Pattern processing logic
│   └── anonymizer.ts          # Data anonymization
├── ml-model-updates/          # ML model distribution
│   ├── index.ts               # Model update API
│   └── modelValidator.ts      # Model validation logic
└── shared/                     # Shared function utilities
    ├── types.ts               # Function type definitions
    ├── utils.ts               # Common utility functions
    └── validation.ts          # Input validation
```

## Configuration Files

| File | Purpose | Location | Maintenance |
|------|---------|----------|-------------|
| **`package.json`** | Dependencies and scripts | Root | Version management |
| **`app.json`** | Expo configuration | Root | App metadata |
| **`babel.config.js`** | Babel transformation | Root | Build configuration |
| **`metro.config.js`** | Metro bundler config | Root | Bundle optimization |
| **`tsconfig.json`** | TypeScript configuration | Root | Type checking |
| **`.eslintrc.js`** | ESLint rules | Root | Code quality |
| **`.prettierrc`** | Prettier formatting | Root | Code formatting |
| **`jest.config.js`** | Testing configuration | Root | Test execution |
| **`knexfile.js`** | Database configuration | Root | Database migrations |
| **`CLAUDE.md`** | Project memory and instructions | Root | AI-assisted development |

## Scripts Directory (`/scripts`)
```
scripts/
├── log-bridge-server.js        # Log processing server for development
├── test-logging.js             # Logging system tests
├── test-logger-direct.js       # Direct logger testing
└── test-ci-local.sh            # Local CI testing script
```

## Global Types and Configuration
```
src/types/
└── global.d.ts                 # Global TypeScript type definitions
```

## Build and Development Scripts

| Script | Purpose | Usage | Environment |
|--------|---------|--------|-------------|
| **`npm start`** | Start development server | Local development | Development |
| **`npm run build`** | Production build | Release preparation | Production |
| **`npm run test`** | Run test suite | Continuous testing | All environments |
| **`npm run lint`** | Code linting | Code quality check | Development |
| **`npm run type-check`** | TypeScript validation | Type safety check | Development |
| **`npm run prebuild`** | Expo prebuild | Native code generation | Build process |

## Asset Organization (`/assets`)
```
assets/
├── images/                     # App images and graphics
│   ├── icons/                 # App icons (various sizes)
│   ├── splash/                # Splash screen images
│   ├── onboarding/           # Onboarding flow images
│   └── illustrations/        # UI illustrations
├── fonts/                      # Custom fonts
│   ├── Inter-Regular.ttf     # Primary font family
│   ├── Inter-Bold.ttf        # Bold variant
│   └── Inter-Medium.ttf      # Medium variant
└── data/                       # Static data files
    ├── categories.json        # Default category definitions
    ├── currencies.json        # Supported currencies
    └── banks.json            # Bank SMS patterns (sanitized)
```

## Documentation Structure (`/docs`)
```
docs/
├── architecture/               # Architecture documentation
│   ├── index.md               # Architecture overview
│   ├── executive-summary.md   # Executive summary
│   ├── implementation-roadmap.md # Implementation roadmap
│   ├── tech-stack.md          # Technology stack details
│   ├── coding-standards.md    # Development standards
│   ├── source-tree.md         # This file
│   ├── themed-components-guide.md # Theme system guide
│   ├── technical-reference-files.md # Technical references
│   ├── 1-system-architecture-overview.md
│   ├── 2-data-architecture.md
│   ├── 3-sms-processing-and-ml-pipeline.md
│   ├── 4-security-and-encryption-architecture.md
│   ├── 5-real-time-family-sharing-architecture.md
│   ├── 6-performance-optimization-strategy.md
│   ├── 7-application-architecture-and-data-flow.md
│   ├── 8-cloud-services-integration.md
│   ├── 9-cicd-pipeline-and-quality-assurance.md
│   ├── 10-deployment-and-infrastructure.md
│   └── technical/             # Technical implementation details
│       ├── log-processing.md
│       ├── cicd-pipeline.md
│       ├── database-schemas.md
│       └── implementation-examples.md
├── design/                    # Design specifications
│   ├── component-specifications.md
│   └── ux-specification.md
├── features/                  # Feature specifications
│   └── enhanced-account-types-specification.md
├── stories/                   # User story documentation
│   ├── 1.1.project-setup-database-architecture.md
│   ├── 1.2.device-authentication-security.md
│   ├── 1.3.basic-account-management.md
│   ├── 1.4.manual-transaction-entry.md
│   ├── 1.5.core-categories-organization.md
│   ├── 1.6.enhanced-account-types-foundation.md
│   ├── 1.7.account-specific-balance-calculators.md
│   ├── 1.8.enhanced-transaction-types-sms-parsing.md
│   ├── 1.9.anonymous-feedback-feature-requests.md
│   └── 1.10.community-feature-request-board.md
├── prd/                       # Product requirements
│   ├── index.md               # PRD overview
│   ├── requirements.md        # Functional requirements
│   ├── technical-assumptions.md # Technical constraints
│   ├── goals-and-background-context.md
│   ├── user-interface-design-goals.md
│   ├── next-steps.md
│   ├── checklist-results-report.md
│   ├── epic-list.md
│   ├── epic-1-foundation-core-infrastructure.md
│   ├── epic-2-budget-management-financial-planning.md
│   ├── epic-3-smart-automation-sms-integration.md
│   └── epic-4-premium-conversion-data-management.md
└── guides/                    # Development guides (planned)
    ├── setup.md              # Development environment setup
    ├── testing.md            # Testing procedures
    └── deployment.md         # Deployment processes
```

This source tree structure supports the layered architecture while maintaining clear separation of concerns and enabling efficient development workflows.