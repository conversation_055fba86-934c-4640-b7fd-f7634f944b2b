# 1. System Architecture Overview

## 1.1 High-Level Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────┐
│                        FinVibe Mobile App                          │
├─────────────────────────────────────────────────────────────────────┤
│                    Presentation Layer                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────────┐ │
│  │  Dashboard  │  │Transaction  │  │   Budget    │  │  Settings  │ │
│  │   Screen    │  │Entry Screen │  │   Screen    │  │   Screen   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └────────────┘ │
├─────────────────────────────────────────────────────────────────────┤
│                     Business Logic Layer                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────────┐ │
│  │Transaction  │  │   Budget    │  │   Account   │  │SMS Parsing │ │
│  │  Manager    │  │  Manager    │  │  Manager    │  │  Manager   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └────────────┘ │
├─────────────────────────────────────────────────────────────────────┤
│                      Data Access Layer                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────────┐ │
│  │Local SQLite │  │   Zustand   │  │  TensorFlow │  │Sync Engine │ │
│  │(SQLCipher)  │  │   Store     │  │    Lite     │  │  Manager   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └────────────┘ │
├─────────────────────────────────────────────────────────────────────┤
│                    Platform Integration Layer                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────────┐ │
│  │    SMS      │  │Biometric    │  │Local File   │  │Push Notif. │ │
│  │   Access    │  │    Auth     │  │   Storage   │  │  Manager   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
                                    │
                          ┌─────────┴─────────┐
                          │   Network Layer   │
                          └─────────┬─────────┘
                                    │
┌─────────────────────────────────────────────────────────────────────┐
│                        Cloud Services (Premium Only)                │
├─────────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌────────────┐  │
│  │  Supabase   │  │   Supabase  │  │  Pattern    │  │   GitHub   │  │
│  │  Database   │  │Edge Functions│  │  Learning   │  │   Actions  │  │
│  │   + Auth    │  │   (SMS)     │  │   Service   │  │   (CI/CD)  │  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └────────────┘  │
└─────────────────────────────────────────────────────────────────────┘
```

## 1.2 Technology Stack

> 📋 **Complete Technology Stack:** [Technology Stack Reference](./tech-stack.md)

**Core Technologies:**
- **Frontend**: React Native + TypeScript + Expo SDK
- **Database**: SQLite with SQLCipher encryption  
- **State Management**: Zustand with persistence
- **Machine Learning**: TensorFlow Lite for on-device processing
- **Cloud Services**: Supabase (Premium Only)
- **Security**: React Native Keychain + Biometric Auth

---
