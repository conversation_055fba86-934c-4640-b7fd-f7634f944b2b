# 2. Data Architecture

## 2.1 Database Design Strategy

**Local-First Approach:**
- SQLite with SQLCipher for encrypted local storage
- All core functionality operates offline-first
- Cloud sync serves as encrypted backup for premium users
- Sub-500ms query performance for 10,000+ transactions

**Key Design Decisions:**
- Optimized indexing for transaction queries by date and account
- Materialized views for dashboard performance
- Conflict resolution with last-write-wins + user prompts
- Row-level security in Supabase for multi-tenant data

**Schema Organization:**
- **Core Tables**: accounts, transactions, categories, budgets
- **SMS Processing**: sms_patterns, ml_models
- **Premium Features**: family_members, user_data_sync
- **Metadata**: user_settings, due_dates

> 📋 **Detailed Schema Reference:** [Database Schemas](./technical/database-schemas.md)

## 2.2 Data Synchronization Strategy

**Conflict Resolution:**
- Last-write-wins for simple modifications
- User prompt for significant changes (>$100 transactions)
- Automatic merge for non-conflicting changes
- Account owner has final authority in family sharing

**Sync Performance:**
- Incremental sync with timestamp-based change detection
- Client-side encryption before upload
- Batch operations for efficiency
- Background sync for premium users

---
