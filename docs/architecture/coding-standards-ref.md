# Coding Standards - Quick Reference

## TypeScript Rules
- ✅ `"strict": true` always enabled
- ❌ No `any` types (except migrations)
- ✅ Interface definitions for all data models
- ✅ Explicit null handling: `string | null`
- ✅ Generic types: `Repository<T>`

## React Native Rules
- ✅ Functional components only: `const Component = () => {}`
- ✅ Zustand stores: `useTransactionStore()`
- ✅ Interface props: `interface ComponentProps {}`
- ✅ StyleSheet API: `styles.container`
- ✅ React.memo only for heavy components

## Database Rules
- ✅ Parameterized queries: `db.query('SELECT * FROM transactions WHERE id = ?', [id])`
- ✅ Knex migrations: `20231201_create_transactions.js`
- ✅ Database transactions: `db.transaction(async (trx) => {})`
- ✅ Performance indexes: `CREATE INDEX idx_transactions_date`
- ✅ SQLCipher encryption (automatic)

## Security Requirements
- 🔐 All sensitive data encrypted (AES-GCM client-side)
- 🔐 React Native Keychain for credentials
- 🔐 Biometric auth + device fallback
- 🔐 HTTPS only, certificate pinning
- 🔐 No hardcoded secrets (env variables)

## Testing Standards
- 🧪 80%+ coverage with Jest
- 🧪 Integration tests for database operations
- 🧪 Performance tests: <500ms queries
- 🧪 Security tests for encryption flows

## Anti-Patterns ❌
- `any` types in TypeScript
- String concatenation in SQL
- Hardcoded secrets
- Class components
- Direct DB access from components
- Synchronous operations in main thread