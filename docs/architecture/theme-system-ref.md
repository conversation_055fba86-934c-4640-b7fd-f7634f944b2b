# Theme System - Quick Reference

## Core Utilities

### `createStyles` Pattern
```typescript
import { createStyles } from '@/shared/theme';

const createMyStyles = createStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.card,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  title: {
    ...theme.typography.h3,
    color: theme.colors.text,
  },
}));

// Usage: const styles = createMyStyles(theme);
```

### `useCommonStyles` Pattern
```typescript
import { useCommonStyles } from '@/shared/theme';

const MyComponent = () => {
  const theme = useTheme();
  const commonStyles = useCommonStyles(theme);
  
  return (
    <View style={commonStyles.card}>
      <Text style={commonStyles.textTitle}>Title</Text>
    </View>
  );
};
```

## Common Style Categories

### Containers
- `card` - Standard card container
- `cardSelected` - Selected card state
- `cardElevated` - Elevated card with shadow
- `container` - Main app container
- `surface` - Surface background

### Layout
- `row` - Horizontal flex layout
- `rowSpaceBetween` - Horizontal with space-between
- `column` - Vertical flex layout
- `centered` - Centered content
- `flex1` - flex: 1

### Buttons
- `buttonBase` / `buttonPrimary` / `buttonSecondary`
- `buttonOutline` / `buttonGhost` / `buttonDanger`
- `buttonSm` / `buttonMd` / `buttonLg` (sizes)
- `buttonDisabled`

### Text
- `textPrimary` / `textSecondary` / `textMuted`
- `textTitle` / `textSubtitle` / `textCaption`
- `textSuccess` / `textWarning` / `textError`

### Form Elements
- `input` / `inputFocused` / `inputError`
- `inputLabel` / `inputHelperText`
- `checkbox` / `radio`

## Theme Structure Access
```typescript
// Colors
theme.colors.primary, secondary, background, card, text, etc.

// Spacing
theme.spacing.xs, sm, md, lg, xl

// Typography
theme.typography.h1, h2, h3, body, caption

// Border Radius
theme.borderRadius.sm, md, lg, xl

// Shadows
theme.shadows.sm, md, lg
```

## Component Integration Template
```typescript
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles, useCommonStyles } from '@/shared/theme';

const MyComponent = () => {
  const theme = useTheme();
  const commonStyles = useCommonStyles(theme);
  const styles = createMyStyles(theme);
  
  return (/* themed components */);
};
```