# CI/CD Pipeline Configuration

## GitHub Actions Workflow

```yaml
# .github/workflows/ci-cd.yml
name: FinVibe CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18.x'
  EXPO_VERSION: '49.x'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linter
      run: npm run lint
    
    - name: Run type check
      run: npm run type-check
    
    - name: Run unit tests
      run: npm run test:unit -- --coverage
    
    - name: Run integration tests
      run: npm run test:integration
      env:
        TEST_DATABASE_KEY: ${{ secrets.TEST_DATABASE_KEY }}
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests

  build-android:
    name: Build Android
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'adopt'
        java-version: '11'
    
    - name: Setup Android SDK
      uses: android-actions/setup-android@v2
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build Android APK
      run: |
        expo prebuild --platform android
        cd android
        ./gradlew assembleRelease
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        ANDROID_KEYSTORE_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
    
    - name: Upload APK artifact
      uses: actions/upload-artifact@v3
      with:
        name: finvibe-android-apk
        path: android/app/build/outputs/apk/release/

  build-ios:
    name: Build iOS
    runs-on: macos-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build iOS
      run: |
        expo prebuild --platform ios
        cd ios
        xcodebuild -workspace FinVibe.xcworkspace -scheme FinVibe -configuration Release
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        IOS_CERTIFICATE: ${{ secrets.IOS_CERTIFICATE }}
        IOS_PROVISIONING_PROFILE: ${{ secrets.IOS_PROVISIONING_PROFILE }}

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run dependency vulnerability scan
      run: npm audit --audit-level moderate
    
    - name: Run SAST scan
      uses: github/codeql-action/analyze@v2
      with:
        languages: typescript, javascript
    
    - name: Check for secrets in code
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD

  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run database performance tests
      run: npm run test:performance
      env:
        PERFORMANCE_DB_SIZE: 10000
    
    - name: Generate performance report
      run: npm run test:performance:report

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-android, build-ios, security-scan]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to Expo Development Build
      run: |
        npm install -g @expo/cli
        expo publish --release-channel staging
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-android, build-ios, security-scan, performance-test]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to App Stores
      run: |
        npm install -g @expo/cli eas-cli
        eas build --platform all --non-interactive
        eas submit --platform all --non-interactive
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        EAS_BUILD_PROFILE: production
```

## Performance Test Suite

```typescript
// Performance Test Suite for Database Operations
describe('Database Performance Tests', () => {
  let db: SQLiteDatabase;
  let testTransactions: Transaction[];
  
  beforeAll(async () => {
    db = await setupTestDatabase();
    testTransactions = await generateTestTransactions(10000);
  });
  
  afterAll(async () => {
    await db.close();
  });
  
  describe('Transaction Query Performance', () => {
    test('should query transactions under 500ms for 10k records', async () => {
      // Insert test transactions
      await insertTransactionsInBatches(db, testTransactions);
      
      const startTime = performance.now();
      const results = await db.executeSql(`
        SELECT * FROM transactions 
        WHERE account_id = ? 
        ORDER BY transaction_date DESC 
        LIMIT 50
      `, [1]);
      const endTime = performance.now();
      
      const queryTime = endTime - startTime;
      expect(queryTime).toBeLessThan(500);
      expect(results.rows.length).toBe(50);
    });
    
    test('should perform aggregation queries under 1 second', async () => {
      const startTime = performance.now();
      const results = await db.executeSql(`
        SELECT 
          category_id,
          SUM(amount) as total_amount,
          COUNT(*) as transaction_count
        FROM transactions 
        WHERE transaction_date >= date('now', '-30 days')
          AND transaction_type = 'expense'
        GROUP BY category_id
        ORDER BY total_amount DESC
      `);
      const endTime = performance.now();
      
      const queryTime = endTime - startTime;
      expect(queryTime).toBeLessThan(1000);
      expect(results.rows.length).toBeGreaterThan(0);
    });
  });
  
  describe('SMS Processing Performance', () => {
    test('should process SMS under 2 seconds', async () => {
      const smsMessage = generateTestSMS();
      const processor = new SMSProcessingEngine();
      
      const startTime = performance.now();
      const result = await processor.processIncomingSMS(smsMessage);
      const endTime = performance.now();
      
      const processingTime = endTime - startTime;
      expect(processingTime).toBeLessThan(2000);
      expect(result.processed).toBe(true);
    });
    
    test('should maintain processing speed with pattern cache', async () => {
      const processor = new SMSProcessingEngine();
      const testSMSMessages = generateMultipleSMS(100);
      
      const startTime = performance.now();
      for (const sms of testSMSMessages) {
        await processor.processIncomingSMS(sms);
      }
      const endTime = performance.now();
      
      const averageProcessingTime = (endTime - startTime) / testSMSMessages.length;
      expect(averageProcessingTime).toBeLessThan(500);
    });
  });
});

// Integration Test Suite for Sync Operations
describe('Cloud Sync Integration Tests', () => {
  let syncService: IncrementalSyncService;
  let testUser: TestUser;
  
  beforeEach(async () => {
    testUser = await createTestUser();
    syncService = new IncrementalSyncService(testUser);
  });
  
  afterEach(async () => {
    await cleanupTestUser(testUser);
  });
  
  test('should sync local changes to cloud', async () => {
    // Create local transactions
    const localTransactions = await createLocalTransactions(testUser, 5);
    
    // Perform sync
    const syncResult = await syncService.syncDataToCloud('transactions');
    
    expect(syncResult.success).toBe(true);
    expect(syncResult.uploadedChanges).toBe(5);
    
    // Verify data in cloud
    const cloudData = await fetchCloudData(testUser.id, 'transactions');
    expect(cloudData.length).toBe(5);
  });
  
  test('should resolve conflicts correctly', async () => {
    // Create conflicting changes
    const localTransaction = await createLocalTransaction(testUser);
    const cloudTransaction = await createCloudTransaction(testUser, localTransaction.id);
    
    // Modify both versions
    await updateLocalTransaction(localTransaction.id, { amount: 100 });
    await updateCloudTransaction(cloudTransaction.id, { amount: 200 });
    
    // Perform sync
    const syncResult = await syncService.syncDataToCloud('transactions');
    
    expect(syncResult.success).toBe(true);
    expect(syncResult.resolvedConflicts).toBe(1);
    
    // Verify conflict resolution (most recent change should win)
    const resolvedTransaction = await getTransaction(localTransaction.id);
    expect(resolvedTransaction.amount).toBe(200); // Cloud version was more recent
  });
});
```