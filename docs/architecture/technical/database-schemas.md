# Database Schemas

## Local Database Schema (SQLite + SQLCipher)

```sql
-- Core Account Management
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('checking', 'savings', 'credit', 'loan', 'investment')),
    balance DECIMAL(15,2) DEFAULT 0.00,
    currency TEXT DEFAULT 'INR',
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status TEXT DEFAULT 'local' CHECK (sync_status IN ('local', 'synced', 'pending', 'conflict'))
);

-- Transaction Storage with Optimized Indexing
CREATE TABLE transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description TEXT NOT NULL,
    category_id INTEGER,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('income', 'expense', 'transfer')),
    transaction_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sms_source TEXT, -- Original SMS if parsed
    confidence_score DECIMAL(3,2), -- ML confidence for categorization
    is_recurring BOOLEAN DEFAULT 0,
    recurring_pattern TEXT, -- JSON: {frequency, next_date, amount_variation}
    sync_status TEXT DEFAULT 'local',
    hash TEXT UNIQUE, -- For duplicate detection
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Performance Indexes for Transaction Queries
CREATE INDEX idx_transactions_account_date ON transactions(account_id, transaction_date DESC);
CREATE INDEX idx_transactions_category ON transactions(category_id);
CREATE INDEX idx_transactions_date_range ON transactions(transaction_date);
CREATE INDEX idx_transactions_sync_status ON transactions(sync_status);
CREATE INDEX idx_transactions_hash ON transactions(hash);

-- Category Management with Hierarchical Structure
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    parent_id INTEGER,
    category_type TEXT NOT NULL CHECK (category_type IN ('income', 'expense')),
    color_code TEXT DEFAULT '#4A90E2',
    icon_name TEXT DEFAULT 'category',
    is_system BOOLEAN DEFAULT 0, -- System vs user-created categories
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status TEXT DEFAULT 'local',
    FOREIGN KEY (parent_id) REFERENCES categories(id)
);

-- Budget Management
CREATE TABLE budgets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    period_type TEXT NOT NULL CHECK (period_type IN ('monthly', 'weekly', 'annual', 'custom')),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status TEXT DEFAULT 'local'
);

-- Budget Category Allocations
CREATE TABLE budget_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    budget_id INTEGER NOT NULL,
    category_id INTEGER NOT NULL,
    allocated_amount DECIMAL(15,2) NOT NULL,
    spent_amount DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    UNIQUE(budget_id, category_id)
);

-- SMS Parsing Patterns (Local Cache)
CREATE TABLE sms_patterns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bank_name TEXT NOT NULL,
    pattern_regex TEXT NOT NULL,
    extraction_fields TEXT NOT NULL, -- JSON array of field mappings
    confidence_score DECIMAL(3,2) DEFAULT 0.80,
    last_successful_parse TIMESTAMP,
    parse_count INTEGER DEFAULT 0,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ML Model Metadata
CREATE TABLE ml_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL CHECK (model_type IN ('categorization', 'recurring_detection')),
    version TEXT NOT NULL,
    model_path TEXT NOT NULL,
    accuracy_score DECIMAL(3,2),
    training_date TIMESTAMP,
    download_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1
);

-- User Preferences and Settings
CREATE TABLE user_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    data_type TEXT NOT NULL CHECK (data_type IN ('string', 'number', 'boolean', 'json')),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Family Sharing (Premium Feature)
CREATE TABLE family_members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member', 'viewer')),
    permissions TEXT NOT NULL, -- JSON: account access permissions
    invitation_status TEXT DEFAULT 'pending' CHECK (invitation_status IN ('pending', 'accepted', 'rejected', 'revoked')),
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    joined_at TIMESTAMP,
    sync_status TEXT DEFAULT 'local'
);

-- Due Date Management
CREATE TABLE due_dates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    amount DECIMAL(15,2),
    due_date DATE NOT NULL,
    category_id INTEGER,
    account_id INTEGER,
    frequency TEXT CHECK (frequency IN ('once', 'weekly', 'monthly', 'quarterly', 'yearly')),
    next_due_date DATE,
    is_paid BOOLEAN DEFAULT 0,
    reminder_days_before INTEGER DEFAULT 3,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status TEXT DEFAULT 'local',
    FOREIGN KEY (category_id) REFERENCES categories(id),
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);
```

## Cloud Sync Schema (Supabase PostgreSQL)

```sql
-- User Authentication and Encryption Keys
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    encrypted_master_key TEXT NOT NULL, -- Encrypted with user passphrase
    device_count INTEGER DEFAULT 1,
    subscription_status TEXT DEFAULT 'free' CHECK (subscription_status IN ('free', 'premium', 'family')),
    subscription_expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_sync_at TIMESTAMP
);

-- Encrypted User Data Sync
CREATE TABLE user_data_sync (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    data_type TEXT NOT NULL CHECK (data_type IN ('accounts', 'transactions', 'budgets', 'categories', 'settings')),
    encrypted_data TEXT NOT NULL, -- Client-side encrypted JSON
    data_hash TEXT NOT NULL, -- For integrity verification
    local_updated_at TIMESTAMP NOT NULL,
    server_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    device_id TEXT NOT NULL,
    sync_version INTEGER DEFAULT 1
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_data_sync ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can only see their own data" ON users
    FOR ALL USING (auth.uid() = id);

CREATE POLICY "Users can only access their own sync data" ON user_data_sync
    FOR ALL USING (auth.uid() = user_id);

-- Anonymous SMS Pattern Learning
CREATE TABLE sms_pattern_contributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bank_identifier TEXT NOT NULL, -- Anonymized bank identifier
    pattern_regex TEXT NOT NULL,
    extraction_success_rate DECIMAL(3,2) NOT NULL,
    sample_count INTEGER NOT NULL,
    country_code TEXT DEFAULT 'IN',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected'))
);

-- Family Sharing Management
CREATE TABLE family_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    owner_id UUID NOT NULL REFERENCES users(id),
    group_name TEXT NOT NULL,
    max_members INTEGER DEFAULT 6,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE family_memberships (
    id UUID PRIMARY KEY AUTOINCREMENT,
    group_id UUID NOT NULL REFERENCES family_groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member', 'viewer')),
    permissions JSONB NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(group_id, user_id)
);

-- Real-time notifications for family members
CREATE TABLE family_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    family_group_id UUID NOT NULL REFERENCES family_groups(id),
    sender_id UUID NOT NULL REFERENCES users(id),
    notification_type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days')
);
```

## Performance Optimization Queries

```sql
-- Optimized Transaction Queries for Large Datasets
-- Monthly spending summary (optimized for 10,000+ transactions)
CREATE VIEW monthly_spending_summary AS
SELECT 
    strftime('%Y-%m', transaction_date) as month,
    category_id,
    c.name as category_name,
    SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as total_expenses,
    SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as total_income,
    COUNT(*) as transaction_count
FROM transactions t
JOIN categories c ON t.category_id = c.id
WHERE transaction_date >= date('now', '-12 months')
GROUP BY strftime('%Y-%m', transaction_date), category_id;

-- Create materialized view for dashboard performance
CREATE TABLE dashboard_summary (
    id INTEGER PRIMARY KEY,
    account_id INTEGER,
    current_balance DECIMAL(15,2),
    monthly_spending DECIMAL(15,2),
    budget_utilization DECIMAL(5,2),
    last_transaction_date DATE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Trigger to maintain dashboard summary
CREATE TRIGGER update_dashboard_summary
AFTER INSERT ON transactions
BEGIN
    INSERT OR REPLACE INTO dashboard_summary (account_id, current_balance, monthly_spending, last_transaction_date)
    VALUES (
        NEW.account_id,
        (SELECT balance FROM accounts WHERE id = NEW.account_id),
        (SELECT SUM(amount) FROM transactions 
         WHERE account_id = NEW.account_id 
         AND transaction_type = 'expense' 
         AND transaction_date >= date('now', 'start of month')),
        NEW.transaction_date
    );
END;
```