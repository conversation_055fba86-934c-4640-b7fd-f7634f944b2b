# Automated Log Pipeline Architecture for FinVibe

## Introduction

This document outlines the complete automated log pipeline architecture for **FinVibe**, including the React Native application, Metro/Flipper integration, local log aggregation, and Claude-powered automated debugging. It serves as the definitive guide for implementing AI-driven error detection and automated code remediation.

This specialized architecture focuses on creating a seamless debugging workflow where development logs are automatically captured, analyzed, and acted upon by Claude <PERSON> during the development lifecycle.

**Rationale:** This unified approach eliminates manual log hunting and creates a proactive debugging system that can identify and fix issues before they become blocking problems. The local-first design ensures fast feedback loops without external dependencies.

**Key Assumptions:**
- Development is React Native focused with Metro bundler
- Flipper is available for enhanced debugging
- Local development environment with file system access
- Claude Code integration for automated analysis
- Focus on runtime errors and crash reports primarily
- 10-minute rolling log window for context efficiency

## High Level Architecture

### Technical Summary

The automated log pipeline uses a **local-first streaming architecture** with React Native Metro/Flipper as the log source, feeding into a **logs/llm_debug** folder structure for organized aggregation. Claude <PERSON> integrates via **build hooks and CLAUDE.md configuration** to perform intelligent log analysis using an **adaptive batch reading strategy** (50-line increments up to 500-1000 lines maximum). The system implements **progressive context gathering** where <PERSON> analyzes recent logs first, then expands backward through historical batches until sufficient context is achieved or the maximum threshold is reached, enabling **automated error detection and code remediation** without external infrastructure dependencies.

### Platform and Infrastructure Choice

**Platform:** Local Development Environment  
**Key Services:** File System Watchers, Metro Bundler, Flipper Debugger, Claude Code CLI  
**Deployment Host and Regions:** Developer Workstation (Local)

**Rationale:** Local-first approach eliminates network latency, ensures data privacy, and provides immediate feedback. No cloud infrastructure costs or external dependencies for core debugging workflow.

### Repository Structure

**Structure:** Monorepo with dedicated logging infrastructure  
**Monorepo Tool:** Native npm workspaces or project-specific structure  
**Package Organization:** Logs isolated in `/logs/llm_debug/` with timestamped rotating files

### High Level Architecture Diagram

```mermaid
graph TD
    RN[React Native App] --> Metro[Metro Bundler]
    RN --> Flipper[Flipper Debugger]
    
    Metro --> LogAgg[Log Stream Aggregator]
    Flipper --> LogAgg
    
    LogAgg --> LogFiles[logs/llm_debug/]
    LogFiles --> RotatedLogs[Timestamped Log Files<br/>10min retention]
    
    BuildHook[Build Hook Trigger] --> Claude[Claude Code]
    UserReport[User Error Report] --> Claude
    
    Claude --> LogReader[Adaptive Log Reader<br/>50-line batches<br/>Max 500-1000 lines]
    LogReader --> LogFiles
    
    Claude --> ErrorAnalysis[Error Analysis & Context Building]
    ErrorAnalysis --> CodeFixer[Automated Code Fixes]
    
    CodeFixer --> SourceCode[Source Code Files]
    
    subgraph "Local File System"
        LogFiles
        RotatedLogs
        SourceCode
    end
    
    subgraph "Development Tools"
        Metro
        Flipper
        Claude
    end
```

### Architectural Patterns

- **Event-Driven Streaming:** Real-time log capture from multiple development sources - _Rationale:_ Enables immediate error detection without polling delays
- **Progressive Context Gathering:** Adaptive batch reading with intelligent expansion - _Rationale:_ Balances context comprehension with token efficiency, user-defined limits prevent runaway analysis
- **Local-First Data Pipeline:** All processing within developer environment - _Rationale:_ Eliminates external dependencies, ensures data privacy, reduces latency
- **Hook-Driven Automation:** Integration with build lifecycle and user triggers - _Rationale:_ Seamless integration with existing development workflow
- **Temporal Log Rotation:** Time-based log retention with automatic cleanup - _Rationale:_ Manages disk space while maintaining sufficient debugging history
- **Fail-Safe Boundaries:** Hard limits on log analysis depth with user notification - _Rationale:_ Prevents infinite context expansion, provides clear feedback when issues require manual intervention

## Log Analysis Process

### Progressive Context Gathering Strategy

1. **Initial Analysis:** Start with last 50 lines of most recent log file
2. **Context Expansion:** If insufficient context, read previous 50 lines
3. **Iterative Deepening:** Continue expanding in 50-line batches backward
4. **Maximum Threshold:** Stop at 500-1000 lines maximum
5. **Failure Notification:** Alert user if problem cannot be identified within limits

### Adaptive Reading Algorithm

```
function analyzeLogsProgressively() {
  let currentBatch = 1
  let maxBatches = 10-20 // 500-1000 lines
  let contextFound = false
  
  while (currentBatch <= maxBatches && !contextFound) {
    logs = readLogBatch(currentBatch * 50)
    analysis = analyzeLogs(logs)
    
    if (analysis.hassufficientContext) {
      contextFound = true
      return generateFix(analysis)
    }
    
    currentBatch++
  }
  
  return "Cannot identify problem within log limits. Manual investigation required."
}
```

## Implementation Requirements

### File Structure

```
FinVibe/
├── logs/
│   └── llm_debug/
│       ├── metro-{timestamp}.log
│       ├── flipper-{timestamp}.log
│       └── combined-{timestamp}.log
├── docs/
│   └── log-processing.md
└── CLAUDE.md
```

### CLAUDE.md Configuration

The CLAUDE.md file should be configured to:
- Grant access to logs/llm_debug folder
- Define log reading permissions
- Set up build hook integration
- Configure error reporting triggers

### Integration Points

1. **Metro Bundler:** Capture build errors, warnings, and performance logs
2. **Flipper Debugger:** Stream console logs, network requests, and crash reports
3. **Build Hooks:** Trigger analysis on build failures or user request
4. **Error Reporting:** Manual trigger when user encounters issues

## Benefits

- **Proactive Debugging:** Identify issues before they become blocking
- **Context-Aware Fixes:** Progressive analysis ensures sufficient context for accurate solutions
- **Local Privacy:** No external data transmission required
- **Fast Feedback:** Immediate analysis without network delays
- **Token Efficient:** Adaptive batching prevents unnecessary context consumption
- **Fail-Safe Design:** Clear boundaries prevent infinite resource consumption