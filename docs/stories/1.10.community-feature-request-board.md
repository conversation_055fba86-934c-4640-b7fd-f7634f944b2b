# Story 1.10: Community Feature Request Board

## Status
Approved

## Story
**As an** engaged community member,
**I want** to browse, submit, and vote on feature requests through both in-app board and web portal,
**so that** I can influence the product roadmap and see transparent development progress while maintaining my privacy.

## Acceptance Criteria

### In-App Community Board:
1. **Community tab** in main navigation displaying feature requests by popularity and category
2. **Feature request categories**: UI/UX, New Features, Integrations, Performance, Bug Reports
3. **Anonymous voting system** using device-based unique identifiers (no personal data)
4. **Request submission form** with title, description, category, and impact explanation
5. **Status indicators**: Submitted, Under Review, Popular (50+ votes), Planned, In Development, Completed, Declined
6. **Search and filter functionality** by category, status, and vote count
7. **Real-time vote count updates** and community engagement metrics

### Web Portal Mirror (finvibe.com/roadmap):
8. **Public web interface** displaying same feature request data without requiring app installation
9. **Shareable individual feature URLs** for external promotion and discussion
10. **Anonymous web voting** with IP-based spam prevention and consistency with app votes
11. **Email subscription system** for feature status updates (optional, privacy-focused)
12. **RSS feed** for developers and power users to track roadmap changes

### Community Engagement Features:
13. **Manual approval system**: FinVibe team reviews all submissions before public display on board
14. **Submission queue**: Users can see their submitted requests with pending approval status
15. **Approval notifications**: Anonymous confirmation when requests are approved or feedback provided for improvements needed
16. **Comment system** for feature request clarification and community discussion (on approved requests only)
17. **Contributor recognition**: Anonymous badges for active community members ("Feature Architect", "Community Champion")
18. **Implementation celebrations**: In-app notifications when community-requested features launch

### Privacy and Moderation:
19. **Manual moderation workflow**: Team reviews for duplicates, quality, feasibility, and appropriateness before approval
20. **Duplicate consolidation**: Team can merge similar requests and redirect votes to primary request
21. **Quality standards**: Clear guidelines for request approval (specific, actionable, aligned with product vision)
22. **Rejection feedback**: Anonymous explanations for declined submissions to help users improve future requests
23. **Anonymous device fingerprinting** for vote tracking without personal data collection

### Technical Requirements:
24. **Submission queue management** with approval workflows and batch processing capabilities
25. **Serverless backend integration** using existing infrastructure for cost efficiency
26. **Offline request drafting** with sync when connection available
27. **Real-time synchronization** between app and web portal vote counts
28. **Performance optimization** for loading large numbers of requests on budget devices

## Tasks / Subtasks

- [ ] **Task 1: Supabase Community Data Architecture** (AC: 24, 25, 27)
  - [ ] Design local SQLite schema with Supabase sync capabilities
  - [ ] Create Supabase tables with Row Level Security policies
  - [ ] Implement CommunitySupabaseService for cloud operations
  - [ ] Add real-time triggers and functions for status updates
  - [ ] Create sync queue management for offline operations
  - [ ] Implement bidirectional sync between local and Supabase

- [ ] **Task 2: In-App Community Board UI** (AC: 1, 2, 6, 7)
  - [ ] Create Community tab in main navigation
  - [ ] Design feature request list with category filtering
  - [ ] Implement search and filter functionality
  - [ ] Add voting interface with real-time updates
  - [ ] Create feature request detail view
  - [ ] Implement category-based organization and display

- [ ] **Task 3: Feature Request Submission System** (AC: 4, 13, 14, 15)
  - [ ] Design feature request submission form
  - [ ] Implement request validation and quality checks
  - [ ] Create submission queue with approval workflow
  - [ ] Add approval/rejection notification system
  - [ ] Implement draft saving for offline submission
  - [ ] Create submission status tracking

- [ ] **Task 4: Anonymous Voting and Engagement** (AC: 3, 5, 17, 23)
  - [ ] Implement anonymous voting with device fingerprinting
  - [ ] Create vote aggregation and status calculation
  - [ ] Add community engagement metrics tracking
  - [ ] Implement contributor recognition system
  - [ ] Create voting fraud prevention mechanisms
  - [ ] Add implementation celebration notifications

- [ ] **Task 5: Supabase Web Portal Integration** (AC: 8, 9, 10, 11, 12)
  - [ ] Create Next.js web portal with Supabase client integration
  - [ ] Implement public web interface using Supabase RLS
  - [ ] Add anonymous web voting with Supabase Edge Functions
  - [ ] Create email subscription system using Edge Functions
  - [ ] Generate RSS feed from Supabase data
  - [ ] Implement real-time sync between app and web portal

- [ ] **Task 6: Supabase Moderation and Quality Control** (AC: 19, 20, 21, 22)
  - [ ] Create admin dashboard using Supabase Auth and RLS
  - [ ] Implement moderation Edge Functions for approval workflow
  - [ ] Add duplicate detection using Supabase functions
  - [ ] Create quality standards enforcement with automated checks
  - [ ] Build rejection feedback system with real-time notifications
  - [ ] Add batch approval processing with Supabase queries

## Dev Notes

### Previous Story Insights
**Dependencies**: 
- Requires Story 1.9 completion - feedback system foundation and device fingerprinting with Supabase integration
- Builds on anonymous voting system from Story 1.9 feedback features
- Extends Supabase integration for community-specific features
- Uses same device identification system for anonymous tracking
- Leverages existing Supabase real-time subscriptions and sync architecture

### Data Models

**Local SQLite Schema** (Offline-first storage):
```sql
-- Extended from Story 1.9 feedback system
CREATE TABLE community_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supabase_id TEXT UNIQUE, -- Sync ID from Supabase
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('ui_ux', 'new_features', 'integrations', 'performance', 'bug_reports')),
    impact_explanation TEXT NOT NULL,
    device_id TEXT NOT NULL,
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'popular', 'planned', 'in_development', 'completed', 'declined')),
    vote_count INTEGER DEFAULT 0,
    user_vote INTEGER DEFAULT 0 CHECK (user_vote IN (-1, 0, 1)), -- User's vote on this request
    comment_count INTEGER DEFAULT 0,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP,
    is_approved BOOLEAN DEFAULT 0,
    approval_feedback TEXT,
    sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed', 'conflict')),
    last_synced_at TIMESTAMP,
    version INTEGER DEFAULT 1
);

CREATE TABLE community_votes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supabase_id TEXT UNIQUE,
    request_id INTEGER NOT NULL,
    supabase_request_id TEXT,
    device_id TEXT NOT NULL,
    vote_value INTEGER NOT NULL CHECK (vote_value IN (1, -1)),
    voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status TEXT DEFAULT 'pending',
    last_synced_at TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES community_requests(id),
    UNIQUE(request_id, device_id)
);

CREATE TABLE community_comments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supabase_id TEXT UNIQUE,
    request_id INTEGER NOT NULL,
    supabase_request_id TEXT,
    device_id TEXT NOT NULL,
    comment_text TEXT NOT NULL,
    replied_to_id INTEGER,
    posted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_approved BOOLEAN DEFAULT 0,
    sync_status TEXT DEFAULT 'pending',
    last_synced_at TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES community_requests(id)
);

CREATE TABLE contributor_badges (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supabase_id TEXT UNIQUE,
    device_id TEXT NOT NULL,
    badge_type TEXT NOT NULL CHECK (badge_type IN ('feature_architect', 'community_champion', 'early_contributor')),
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    badge_reason TEXT NOT NULL,
    sync_status TEXT DEFAULT 'pending',
    last_synced_at TIMESTAMP
);

-- Sync queue for offline operations
CREATE TABLE community_sync_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
    table_name TEXT NOT NULL,
    local_id INTEGER NOT NULL,
    payload TEXT NOT NULL, -- JSON payload
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_attempt_at TIMESTAMP,
    error_message TEXT
);
```

**Supabase Tables Schema** (Cloud storage with RLS):
```sql
-- Community requests with admin approval workflow
CREATE TABLE public.community_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('ui_ux', 'new_features', 'integrations', 'performance', 'bug_reports')),
    impact_explanation TEXT NOT NULL,
    device_id TEXT NOT NULL,
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'popular', 'planned', 'in_development', 'completed', 'declined')),
    vote_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    submitted_at TIMESTAMPTZ DEFAULT NOW(),
    approved_at TIMESTAMPTZ,
    is_approved BOOLEAN DEFAULT false,
    approval_feedback TEXT,
    reviewed_by UUID REFERENCES auth.users(id),
    version INTEGER DEFAULT 1,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.community_requests ENABLE ROW LEVEL SECURITY;

-- RLS Policies for community requests
CREATE POLICY "Users can insert requests" ON public.community_requests
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view approved requests" ON public.community_requests
    FOR SELECT USING (is_approved = true);

CREATE POLICY "Users can view their own requests" ON public.community_requests
    FOR SELECT USING (device_id = current_setting('app.device_id', true));

CREATE POLICY "Admins can manage all requests" ON public.community_requests
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Community votes table
CREATE TABLE public.community_votes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id UUID REFERENCES public.community_requests(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL,
    vote_value INTEGER NOT NULL CHECK (vote_value IN (1, -1)),
    voted_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(request_id, device_id)
);

-- Community comments table
CREATE TABLE public.community_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id UUID REFERENCES public.community_requests(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL,
    comment_text TEXT NOT NULL,
    replied_to_id UUID REFERENCES public.community_comments(id),
    posted_at TIMESTAMPTZ DEFAULT NOW(),
    is_approved BOOLEAN DEFAULT false,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMPTZ
);

-- Contributor badges
CREATE TABLE public.contributor_badges (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id TEXT NOT NULL,
    badge_type TEXT NOT NULL CHECK (badge_type IN ('feature_architect', 'community_champion', 'early_contributor')),
    earned_at TIMESTAMPTZ DEFAULT NOW(),
    badge_reason TEXT NOT NULL,
    awarded_by UUID REFERENCES auth.users(id)
);

-- Real-time triggers for community updates
CREATE OR REPLACE FUNCTION notify_community_changes()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM pg_notify('community_changes', json_build_object(
        'table', TG_TABLE_NAME,
        'type', TG_OP,
        'id', NEW.id,
        'request_id', CASE WHEN TG_TABLE_NAME = 'community_votes' THEN NEW.request_id ELSE NEW.id END
    )::text);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER community_request_changes
    AFTER UPDATE ON public.community_requests
    FOR EACH ROW EXECUTE FUNCTION notify_community_changes();

CREATE TRIGGER community_vote_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.community_votes
    FOR EACH ROW EXECUTE FUNCTION notify_community_changes();

-- Function to update vote counts
CREATE OR REPLACE FUNCTION update_request_vote_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        UPDATE public.community_requests 
        SET vote_count = (
            SELECT COALESCE(SUM(vote_value), 0) 
            FROM public.community_votes 
            WHERE request_id = OLD.request_id
        )
        WHERE id = OLD.request_id;
        RETURN OLD;
    ELSE
        UPDATE public.community_requests 
        SET vote_count = (
            SELECT COALESCE(SUM(vote_value), 0) 
            FROM public.community_votes 
            WHERE request_id = NEW.request_id
        )
        WHERE id = NEW.request_id;
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_vote_counts
    AFTER INSERT OR UPDATE OR DELETE ON public.community_votes
    FOR EACH ROW EXECUTE FUNCTION update_request_vote_count();
```

**Community Business Logic**:
```typescript
// Local SQLite interfaces
interface CommunityRequest {
  id: number;
  supabaseId?: string;
  title: string;
  description: string;
  category: 'ui_ux' | 'new_features' | 'integrations' | 'performance' | 'bug_reports';
  impactExplanation: string;
  deviceId: string;
  status: 'submitted' | 'under_review' | 'popular' | 'planned' | 'in_development' | 'completed' | 'declined';
  voteCount: number;
  userVote: -1 | 0 | 1; // User's vote on this request
  commentCount: number;
  submittedAt: Date;
  approvedAt?: Date;
  isApproved: boolean;
  approvalFeedback?: string;
  syncStatus: 'pending' | 'synced' | 'failed' | 'conflict';
  lastSyncedAt?: Date;
  version: number;
}

interface CommunityVote {
  id: number;
  supabaseId?: string;
  requestId: number;
  supabaseRequestId?: string;
  deviceId: string;
  voteValue: 1 | -1;
  votedAt: Date;
  syncStatus: 'pending' | 'synced' | 'failed';
  lastSyncedAt?: Date;
}

interface CommunityComment {
  id: number;
  supabaseId?: string;
  requestId: number;
  supabaseRequestId?: string;
  deviceId: string;
  commentText: string;
  repliedToId?: number;
  postedAt: Date;
  isApproved: boolean;
  syncStatus: 'pending' | 'synced' | 'failed';
  lastSyncedAt?: Date;
}

interface ContributorBadge {
  id: number;
  supabaseId?: string;
  deviceId: string;
  badgeType: 'feature_architect' | 'community_champion' | 'early_contributor';
  earnedAt: Date;
  badgeReason: string;
  syncStatus: 'pending' | 'synced' | 'failed';
  lastSyncedAt?: Date;
}

// Supabase cloud interfaces
interface SupabaseCommunityRequest {
  id: string;
  title: string;
  description: string;
  category: 'ui_ux' | 'new_features' | 'integrations' | 'performance' | 'bug_reports';
  impact_explanation: string;
  device_id: string;
  status: 'submitted' | 'under_review' | 'popular' | 'planned' | 'in_development' | 'completed' | 'declined';
  vote_count: number;
  comment_count: number;
  submitted_at: string;
  approved_at?: string;
  is_approved: boolean;
  approval_feedback?: string;
  reviewed_by?: string;
  version: number;
  created_at: string;
  updated_at: string;
}

interface CommunityStats {
  totalRequests: number;
  popularRequests: number; // 50+ votes
  implementedRequests: number;
  userContributions: number;
  userVotes: number;
  contributorBadges: ContributorBadge[];
  syncStatus: 'idle' | 'syncing' | 'error';
  lastSyncAt?: Date;
}
```

### Component Specifications
**CommunityStore State Management** [Source: architecture/7-application-architecture-and-data-flow.md#state-management-with-zustand]:
```typescript
interface CommunityStore {
  // Local state
  requests: CommunityRequest[];
  userRequests: CommunityRequest[];
  comments: CommunityComment[];
  votes: CommunityVote[];
  badges: ContributorBadge[];
  loading: boolean;
  error: string | null;
  filters: CommunityFilters;
  stats: CommunityStats;
  
  // Sync state
  syncStatus: 'idle' | 'syncing' | 'error';
  lastSyncAt?: Date;
  pendingSyncCount: number;
  
  // Real-time state
  isConnected: boolean;
  
  // Actions
  loadCommunityRequests: (filters?: CommunityFilters) => Promise<void>;
  submitRequest: (request: Omit<CommunityRequest, 'id' | 'syncStatus' | 'version'>) => Promise<void>;
  voteOnRequest: (requestId: number, vote: 1 | -1) => Promise<void>;
  submitComment: (requestId: number, comment: string, repliedTo?: number) => Promise<void>;
  searchRequests: (query: string) => Promise<CommunityRequest[]>;
  setFilters: (filters: CommunityFilters) => void;
  loadUserContributions: () => Promise<void>;
  
  // Sync actions
  syncToSupabase: () => Promise<void>;
  enableRealTimeSync: () => void;
  disableRealTimeSync: () => void;
  handleCommunityUpdate: (update: CommunityUpdate) => void;
  retryFailedSync: () => Promise<void>;
  
  // Offline actions
  saveDraftRequest: (draft: Partial<CommunityRequest>) => void;
  loadDraftRequest: () => Partial<CommunityRequest> | null;
  clearDraftRequest: () => void;
}

interface CommunityFilters {
  category?: 'ui_ux' | 'new_features' | 'integrations' | 'performance' | 'bug_reports';
  status?: 'submitted' | 'under_review' | 'popular' | 'planned' | 'in_development' | 'completed' | 'declined';
  sortBy: 'votes' | 'recent' | 'popular' | 'status';
  sortOrder: 'asc' | 'desc';
}

interface CommunityUpdate {
  type: 'request' | 'vote' | 'comment' | 'badge';
  id: string;
  requestId?: string;
  data: any;
  timestamp: Date;
}
```

**Real-time Synchronization**:
```typescript
class CommunityRealTimeService {
  private supabaseClient: SupabaseClient;
  
  subscribeToVoteUpdates(callback: (update: VoteUpdate) => void) {
    return this.supabaseClient
      .channel('community_votes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'community_votes' }, callback)
      .subscribe();
  }
  
  subscribeToRequestUpdates(callback: (update: RequestUpdate) => void) {
    return this.supabaseClient
      .channel('community_requests')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'community_requests' }, callback)
      .subscribe();
  }
}
```

### API Specifications

**Supabase Integration Services**:
```typescript
// Community Supabase Service
class CommunitySupabaseService {
  private supabase: SupabaseClient;
  private realtimeChannel?: RealtimeChannel;
  
  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient;
  }

  // Community request operations
  async syncRequestsToCloud(localRequests: CommunityRequest[]): Promise<SyncResult[]> {
    const results: SyncResult[] = [];
    
    for (const request of localRequests) {
      if (request.syncStatus === 'pending') {
        try {
          const { data, error } = await this.supabase
            .from('community_requests')
            .upsert(this.transformRequestToSupabaseFormat(request))
            .select()
            .single();
          
          if (error) throw error;
          results.push({ localId: request.id, success: true, supabaseId: data.id });
        } catch (error) {
          results.push({ localId: request.id, success: false, error: error.message });
        }
      }
    }
    
    return results;
  }

  async getApprovedRequests(filters?: CommunityFilters): Promise<SupabaseCommunityRequest[]> {
    let query = this.supabase
      .from('community_requests')
      .select('*')
      .eq('is_approved', true);
      
    if (filters?.category) {
      query = query.eq('category', filters.category);
    }
    
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    
    if (filters?.sortBy) {
      const ascending = filters.sortOrder === 'asc';
      switch (filters.sortBy) {
        case 'votes':
          query = query.order('vote_count', { ascending });
          break;
        case 'recent':
          query = query.order('created_at', { ascending });
          break;
        case 'popular':
          query = query.order('vote_count', { ascending: false });
          break;
      }
    }
    
    const { data, error } = await query;
    if (error) throw error;
    
    return data || [];
  }

  // Voting operations
  async submitVote(requestId: string, deviceId: string, voteValue: 1 | -1): Promise<string> {
    const { data, error } = await this.supabase
      .from('community_votes')
      .upsert({
        request_id: requestId,
        device_id: deviceId,
        vote_value: voteValue
      })
      .select()
      .single();
      
    if (error) throw error;
    return data.id;
  }

  async getUserVotes(deviceId: string): Promise<{ request_id: string; vote_value: number }[]> {
    const { data, error } = await this.supabase
      .from('community_votes')
      .select('request_id, vote_value')
      .eq('device_id', deviceId);
      
    if (error) throw error;
    return data || [];
  }

  // Comments operations
  async submitComment(requestId: string, deviceId: string, commentText: string, repliedTo?: string): Promise<string> {
    const { data, error } = await this.supabase
      .from('community_comments')
      .insert({
        request_id: requestId,
        device_id: deviceId,
        comment_text: commentText,
        replied_to_id: repliedTo
      })
      .select()
      .single();
      
    if (error) throw error;
    return data.id;
  }

  async getApprovedComments(requestId: string): Promise<any[]> {
    const { data, error } = await this.supabase
      .from('community_comments')
      .select('*')
      .eq('request_id', requestId)
      .eq('is_approved', true)
      .order('posted_at', { ascending: true });
      
    if (error) throw error;
    return data || [];
  }

  // Real-time subscriptions
  subscribeToRequestUpdates(callback: (update: CommunityUpdate) => void): void {
    this.realtimeChannel = this.supabase
      .channel('community_updates')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'community_requests'
      }, (payload) => {
        callback({
          type: 'request',
          id: payload.new?.id || payload.old?.id,
          data: payload.new,
          timestamp: new Date()
        });
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'community_votes'
      }, (payload) => {
        callback({
          type: 'vote',
          id: payload.new?.id || payload.old?.id,
          requestId: payload.new?.request_id || payload.old?.request_id,
          data: payload.new,
          timestamp: new Date()
        });
      })
      .subscribe();
  }

  unsubscribeFromUpdates(): void {
    if (this.realtimeChannel) {
      this.supabase.removeChannel(this.realtimeChannel);
      this.realtimeChannel = undefined;
    }
  }

  // Web portal support
  async generateRSSFeed(): Promise<string> {
    const { data, error } = await this.supabase
      .from('community_requests')
      .select('*')
      .eq('is_approved', true)
      .in('status', ['planned', 'in_development', 'completed'])
      .order('updated_at', { ascending: false })
      .limit(50);
      
    if (error) throw error;
    
    return this.generateRSSFromData(data);
  }

  async subscribeToEmailUpdates(email: string, deviceId: string): Promise<void> {
    // Call Supabase Edge Function for email subscription
    const { error } = await this.supabase.functions.invoke('community-email-subscription', {
      body: { email, deviceId, action: 'subscribe' }
    });
    
    if (error) throw error;
  }

  private transformRequestToSupabaseFormat(request: CommunityRequest): Partial<SupabaseCommunityRequest> {
    return {
      id: request.supabaseId,
      title: request.title,
      description: request.description,
      category: request.category,
      impact_explanation: request.impactExplanation,
      device_id: request.deviceId,
      submitted_at: request.submittedAt.toISOString(),
      version: request.version
    };
  }

  private generateRSSFromData(data: any[]): string {
    // RSS XML generation logic
    const rssItems = data.map(item => `
      <item>
        <title>${item.title}</title>
        <description>${item.description}</description>
        <link>https://finvibe.com/roadmap/${item.id}</link>
        <pubDate>${new Date(item.updated_at).toUTCString()}</pubDate>
        <category>${item.category}</category>
        <guid>finvibe-request-${item.id}</guid>
      </item>
    `).join('');

    return `<?xml version="1.0" encoding="UTF-8"?>
      <rss version="2.0">
        <channel>
          <title>FinVibe Community Roadmap</title>
          <description>Latest updates on community feature requests</description>
          <link>https://finvibe.com/roadmap</link>
          ${rssItems}
        </channel>
      </rss>`;
  }
}

// Supabase Edge Functions for web portal
interface EdgeFunctionAPI {
  'community-moderation': {
    request: { requestId: string; action: 'approve' | 'reject'; feedback?: string };
    response: { success: boolean; message: string };
  };
  'community-email-subscription': {
    request: { email: string; deviceId: string; action: 'subscribe' | 'unsubscribe' };
    response: { success: boolean; subscriptionId?: string };
  };
  'community-rss-feed': {
    request: { category?: string; limit?: number };
    response: string; // RSS XML
  };
  'community-web-vote': {
    request: { requestId: string; ipAddress: string; voteValue: 1 | -1 };
    response: { success: boolean; voteId?: string; rateLimited?: boolean };
  };
}

// Local sync service
class CommunitySyncService {
  private localDb: SQLiteDatabase;
  private supabaseService: CommunitySupabaseService;
  
  async syncPendingOperations(): Promise<void> {
    // Sync pending requests
    const pendingRequests = await this.localDb.getCommunityRequestsSyncPending();
    const requestResults = await this.supabaseService.syncRequestsToCloud(pendingRequests);
    
    // Update local records with sync results
    for (const result of requestResults) {
      if (result.success) {
        await this.localDb.updateRequestSyncStatus(result.localId, 'synced', result.supabaseId);
      } else {
        await this.localDb.updateRequestSyncStatus(result.localId, 'failed');
      }
    }
    
    // Sync pending votes
    const pendingVotes = await this.localDb.getCommunityVotesSyncPending();
    for (const vote of pendingVotes) {
      try {
        const voteId = await this.supabaseService.submitVote(
          vote.supabaseRequestId!, 
          vote.deviceId, 
          vote.voteValue
        );
        await this.localDb.updateVoteSyncStatus(vote.id, 'synced', voteId);
      } catch (error) {
        await this.localDb.updateVoteSyncStatus(vote.id, 'failed');
      }
    }
  }

  async pullUpdatesFromCloud(): Promise<void> {
    const deviceId = await DeviceFingerprintService.getDeviceId();
    
    // Pull approved requests
    const approvedRequests = await this.supabaseService.getApprovedRequests();
    await this.mergeApprovedRequests(approvedRequests);
    
    // Pull user's vote history
    const userVotes = await this.supabaseService.getUserVotes(deviceId);
    await this.updateLocalVoteHistory(userVotes);
  }

  private async mergeApprovedRequests(cloudRequests: SupabaseCommunityRequest[]): Promise<void> {
    for (const cloudRequest of cloudRequests) {
      const existingLocal = await this.localDb.findRequestBySupabaseId(cloudRequest.id);
      
      if (!existingLocal) {
        // New approved request - add to local
        await this.localDb.insertCommunityRequest(this.transformFromSupabaseFormat(cloudRequest));
      } else if (cloudRequest.updated_at > existingLocal.lastSyncedAt?.toISOString()) {
        // Updated request - merge changes
        await this.localDb.updateCommunityRequestFromCloud({
          id: existingLocal.id,
          status: cloudRequest.status,
          voteCount: cloudRequest.vote_count,
          commentCount: cloudRequest.comment_count,
          version: cloudRequest.version,
          lastSyncedAt: new Date(cloudRequest.updated_at)
        });
      }
    }
  }
}
```

### File Locations
Based on layered architecture [Source: architecture/7-application-architecture-and-data-flow.md#layered-architecture]:
- **Community Services**: `src/services/community/`
- **Community Components**: `src/components/community/`
- **Community Store**: `src/stores/communityStore.ts`
- **Community Screens**: `src/screens/community/`
- **Community Types**: `src/types/community.ts`
- **Supabase Services**: `src/services/supabase/communitySupabaseService.ts`
- **Real-time Services**: `src/services/realtime/communityRealTimeService.ts`
- **Sync Services**: `src/services/sync/communitySyncService.ts`
- **Edge Functions**: `supabase/functions/community-*`
- **Web Portal**: `web/` (Next.js app with Supabase client)

### Technical Constraints
**Performance Requirements**:
- Community board loading <3 seconds for 1000+ requests from Supabase
- Real-time vote updates <1 second latency via Supabase channels
- Search response time <2 seconds with Supabase filtering
- Offline drafting and sync queue functionality with SQLite
- Optimized rendering for budget devices with progressive loading

**Privacy and Anonymity Requirements**:
- Device fingerprinting must remain anonymous
- No personal data collection without explicit opt-in
- Vote history privacy protection
- Anonymous contribution tracking

**Moderation and Quality Requirements**:
- Manual approval for all public requests using Supabase RLS
- Duplicate detection and consolidation with Edge Functions
- Quality standards enforcement with automated Supabase triggers
- Spam prevention for web portal using Edge Functions and rate limiting
- Real-time moderation notifications via Supabase channels

## Testing

### Testing Standards
**Test File Locations**: 
- Unit tests: `src/__tests__/community/`
- Integration tests: `src/__tests__/integration/community/`
- Component tests: `src/__tests__/components/community/`
- Web tests: `web/__tests__/`

**Testing Frameworks**:
- Jest for unit testing with Supabase mocks
- React Native Testing Library for component testing
- Supabase local development environment for integration testing
- Cypress for web portal testing with Supabase client
- Supabase Edge Function testing with local emulation

**Specific Testing Requirements for This Story**:
- Community request submission and approval workflow
- Anonymous voting system with fraud prevention
- Real-time synchronization between app and web portal
- Offline functionality and sync queue testing
- Moderation workflow and quality control testing
- Web portal functionality and RSS feed generation
- Performance testing with large datasets

### Key Test Scenarios
- Submit feature request and track approval status
- Test anonymous voting with duplicate prevention
- Verify real-time vote count synchronization
- Test offline request drafting and sync
- Validate moderation workflow and feedback system
- Test web portal voting and data consistency
- Performance testing with 1000+ requests
- Test contributor badge system and recognition
- Validate RSS feed generation and email subscriptions

### Integration and End-to-End Tests
- Full workflow from request submission to implementation celebration
- Cross-platform consistency between app and web portal
- Real-time updates across multiple devices
- Moderation workflow from submission to approval/rejection
- Vote synchronization and conflict resolution
- Performance under high community engagement

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-07 | 1.0 | Initial story creation | Scrum Master |

## Dev Agent Record

### Agent Model Used
*To be filled by development agent*

### Debug Log References
*To be filled by development agent*

### Completion Notes List
*To be filled by development agent*

### File List
*To be filled by development agent*

## QA Results
*To be filled by QA agent*