# Story 1.9: Anonymous Feedback and Feature Requests

## Status
Approved

## Story
**As an** engaged user,
**I want** to submit feedback and feature requests anonymously with optional reward claiming,
**so that** I can help improve the app without compromising privacy while still getting recognition for valuable contributions.

## Acceptance Criteria
1. **Anonymous feedback submission** - no personal information required for basic feedback
2. Feedback categories: Bug Report, Feature Request, General Feedback, Praise/Complaint
3. **Optional reward claiming system**:
   - Users can choose to remain completely anonymous (no rewards)
   - Users can provide minimal contact info (email/device ID) only for reward eligibility
   - Clear separation: feedback is anonymous, contact info only for reward notification
4. **Feature request voting system** - users can upvote existing requests anonymously
5. In-app feedback form with structured fields (category, description, priority level)
6. **Reward structure**:
   - Implemented feature requests: Premium upgrade credit or extended trial
   - Critical bug reports: App store gift cards or premium credits
   - Community-voted top suggestions: Public recognition in app changelog
7. Feedback submission works offline, syncs when connection available
8. **Early access delivery:** Anonymous device-based tokens for implemented feature contributors, with 2-week early access to new features
9. **Community waves:** Users with recent feedback submissions get 1-week early access to non-critical features

## Tasks / Subtasks

- [ ] **Task 1: Feedback Data Layer and Storage** (AC: 1, 7)
  - [ ] Create feedback database schema for local storage
  - [ ] Implement FeedbackService with offline-first approach
  - [ ] Create feedback sync queue for when connectivity available
  - [ ] Add feedback validation and sanitization
  - [ ] Implement device fingerprinting for anonymous tracking
  - [ ] Create feedback status tracking (submitted, synced, acknowledged)

- [ ] **Task 2: Anonymous Feedback Form** (AC: 1, 2, 5)
  - [ ] Design feedback submission form with category selection
  - [ ] Implement structured feedback fields (category, description, priority)
  - [ ] Add feedback type selection (Bug Report, Feature Request, General, Praise)
  - [ ] Create priority level selector (Low, Medium, High, Critical)
  - [ ] Implement character count and validation
  - [ ] Add attachment support for screenshots (optional)

- [ ] **Task 3: Optional Reward Claiming System** (AC: 3, 6)
  - [ ] Create reward eligibility opt-in flow
  - [ ] Implement minimal contact info collection (email/device ID only)
  - [ ] Design clear privacy separation UI
  - [ ] Create reward claim tracking system
  - [ ] Implement reward notification system
  - [ ] Add reward type definition and management

- [ ] **Task 4: Feature Request Voting System** (AC: 4)
  - [ ] Create feature request display and voting interface
  - [ ] Implement anonymous voting with device fingerprinting
  - [ ] Add vote tracking and aggregation
  - [ ] Create feature request status indicators
  - [ ] Implement voting fraud prevention
  - [ ] Add feature request search and filtering

- [ ] **Task 5: Early Access and Community Features** (AC: 8, 9)
  - [ ] Implement device-based token system for early access
  - [ ] Create early access feature flagging
  - [ ] Add community wave participation tracking
  - [ ] Implement feature rollout management
  - [ ] Create early access notification system
  - [ ] Add contributor recognition system

- [ ] **Task 6: Supabase Integration and Sync** (AC: 7)
  - [ ] Create FeedbackSupabaseService for cloud operations
  - [ ] Implement local SQLite to Supabase bidirectional sync
  - [ ] Add sync queue management for offline operations
  - [ ] Create conflict resolution with version control
  - [ ] Implement real-time status update subscriptions
  - [ ] Add Supabase Storage integration for screenshots
  - [ ] Create retry mechanism for failed sync operations

## Dev Notes

### Previous Story Insights
**Dependencies**: 
- Requires Story 1.1 completion - database architecture for local feedback storage
- Requires Story 1.2 completion - device authentication for anonymous device tracking
- Minimal cloud integration required for feedback sync and voting
- Builds foundation for Story 1.7 community features

### Data Models

**Local SQLite Schema** (Offline-first storage):
```sql
-- Local feedback storage with Supabase sync
CREATE TABLE feedback_submissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supabase_id TEXT UNIQUE, -- Sync ID from Supabase
    category TEXT NOT NULL CHECK (category IN ('bug_report', 'feature_request', 'general_feedback', 'praise_complaint')),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    priority_level TEXT NOT NULL CHECK (priority_level IN ('low', 'medium', 'high', 'critical')),
    device_id TEXT NOT NULL, -- Anonymous device fingerprint
    contact_info TEXT, -- Optional email for rewards (encrypted)
    reward_eligible BOOLEAN DEFAULT 0,
    screenshot_path TEXT,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed', 'conflict')),
    last_synced_at TIMESTAMP,
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'acknowledged', 'in_review', 'implemented', 'rejected')),
    admin_feedback TEXT, -- Feedback from FinVibe team
    version INTEGER DEFAULT 1 -- For conflict resolution
);

CREATE TABLE feature_votes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supabase_id TEXT UNIQUE,
    feature_request_id TEXT NOT NULL, -- Supabase feature request ID
    device_id TEXT NOT NULL,
    vote_value INTEGER NOT NULL CHECK (vote_value IN (1, -1)),
    voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status TEXT DEFAULT 'pending',
    last_synced_at TIMESTAMP,
    UNIQUE(feature_request_id, device_id)
);

CREATE TABLE early_access_tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supabase_id TEXT UNIQUE,
    device_id TEXT NOT NULL,
    token_type TEXT NOT NULL CHECK (token_type IN ('contributor', 'community_wave')),
    granted_for TEXT NOT NULL, -- feedback_id or feature_id
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status TEXT DEFAULT 'pending',
    is_active BOOLEAN DEFAULT 1
);

-- Sync queue for offline operations
CREATE TABLE feedback_sync_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
    table_name TEXT NOT NULL,
    local_id INTEGER NOT NULL,
    payload TEXT NOT NULL, -- JSON payload
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_attempt_at TIMESTAMP,
    error_message TEXT
);
```

**Supabase Tables Schema** (Cloud sync and team management):
```sql
-- Supabase feedback table with RLS
CREATE TABLE public.feedback_submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category TEXT NOT NULL CHECK (category IN ('bug_report', 'feature_request', 'general_feedback', 'praise_complaint')),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    priority_level TEXT NOT NULL CHECK (priority_level IN ('low', 'medium', 'high', 'critical')),
    device_id TEXT NOT NULL,
    contact_info TEXT, -- Encrypted
    reward_eligible BOOLEAN DEFAULT false,
    screenshot_url TEXT, -- Supabase Storage URL
    submitted_at TIMESTAMPTZ DEFAULT NOW(),
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'acknowledged', 'in_review', 'implemented', 'rejected')),
    admin_feedback TEXT,
    reviewed_by UUID REFERENCES auth.users(id),
    reviewed_at TIMESTAMPTZ,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.feedback_submissions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can insert their own feedback" ON public.feedback_submissions
    FOR INSERT WITH CHECK (true); -- Anonymous submissions allowed

CREATE POLICY "Users can view their own feedback" ON public.feedback_submissions
    FOR SELECT USING (device_id = current_setting('app.device_id', true));

CREATE POLICY "Admins can view and update all feedback" ON public.feedback_submissions
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Feature votes table
CREATE TABLE public.feature_votes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    feedback_id UUID REFERENCES public.feedback_submissions(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL,
    vote_value INTEGER NOT NULL CHECK (vote_value IN (1, -1)),
    voted_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(feedback_id, device_id)
);

-- Early access tokens
CREATE TABLE public.early_access_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id TEXT NOT NULL,
    token_type TEXT NOT NULL CHECK (token_type IN ('contributor', 'community_wave')),
    granted_for UUID NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Real-time triggers
CREATE OR REPLACE FUNCTION notify_feedback_changes()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM pg_notify('feedback_changes', json_build_object(
        'table', TG_TABLE_NAME,
        'type', TG_OP,
        'id', NEW.id,
        'device_id', NEW.device_id
    )::text);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER feedback_status_changes
    AFTER UPDATE OF status, admin_feedback ON public.feedback_submissions
    FOR EACH ROW EXECUTE FUNCTION notify_feedback_changes();
```

**Feedback Business Logic**:
```typescript
// Local SQLite interfaces
interface FeedbackSubmission {
  id: number;
  supabaseId?: string;
  category: 'bug_report' | 'feature_request' | 'general_feedback' | 'praise_complaint';
  title: string;
  description: string;
  priorityLevel: 'low' | 'medium' | 'high' | 'critical';
  deviceId: string;
  contactInfo?: string; // encrypted
  rewardEligible: boolean;
  screenshotPath?: string;
  submittedAt: Date;
  syncStatus: 'pending' | 'synced' | 'failed' | 'conflict';
  lastSyncedAt?: Date;
  status: 'submitted' | 'acknowledged' | 'in_review' | 'implemented' | 'rejected';
  adminFeedback?: string;
  version: number;
}

interface FeatureVote {
  id: number;
  supabaseId?: string;
  featureRequestId: string;
  deviceId: string;
  voteValue: 1 | -1;
  votedAt: Date;
  syncStatus: 'pending' | 'synced' | 'failed';
  lastSyncedAt?: Date;
}

// Supabase cloud interfaces
interface SupabaseFeedbackSubmission {
  id: string;
  category: 'bug_report' | 'feature_request' | 'general_feedback' | 'praise_complaint';
  title: string;
  description: string;
  priority_level: 'low' | 'medium' | 'high' | 'critical';
  device_id: string;
  contact_info?: string;
  reward_eligible: boolean;
  screenshot_url?: string;
  submitted_at: string;
  status: 'submitted' | 'acknowledged' | 'in_review' | 'implemented' | 'rejected';
  admin_feedback?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  version: number;
  created_at: string;
  updated_at: string;
}

interface SyncOperation {
  id: number;
  operationType: 'INSERT' | 'UPDATE' | 'DELETE';
  tableName: string;
  localId: number;
  payload: any;
  retryCount: number;
  createdAt: Date;
  lastAttemptAt?: Date;
  errorMessage?: string;
}
```

### Component Specifications
**FeedbackStore State Management** [Source: architecture/7-application-architecture-and-data-flow.md#state-management-with-zustand]:
```typescript
interface FeedbackStore {
  // Local state
  feedback: FeedbackSubmission[];
  featureRequests: FeatureVote[];
  earlyAccessTokens: EarlyAccessToken[];
  loading: boolean;
  error: string | null;
  
  // Sync state
  syncStatus: 'idle' | 'syncing' | 'error';
  lastSyncAt?: Date;
  pendingSyncCount: number;
  
  // Real-time state
  isConnected: boolean;
  
  // Actions
  submitFeedback: (feedback: Omit<FeedbackSubmission, 'id' | 'syncStatus' | 'version'>) => Promise<void>;
  loadFeedback: () => Promise<void>;
  voteOnFeature: (featureId: string, vote: 1 | -1) => Promise<void>;
  loadFeatureRequests: () => Promise<void>;
  claimEarlyAccess: (tokenType: string) => Promise<boolean>;
  
  // Sync actions
  syncToSupabase: () => Promise<void>;
  enableRealTimeSync: () => void;
  disableRealTimeSync: () => void;
  handleStatusUpdate: (update: FeedbackStatusUpdate) => void;
  retryFailedSync: () => Promise<void>;
  
  // Offline actions
  saveDraftFeedback: (draft: Partial<FeedbackSubmission>) => void;
  loadDraftFeedback: () => Partial<FeedbackSubmission> | null;
  clearDraftFeedback: () => void;
}

interface FeedbackStatusUpdate {
  id: string;
  status: 'submitted' | 'acknowledged' | 'in_review' | 'implemented' | 'rejected';
  adminFeedback?: string;
  reviewedAt?: Date;
}
```

**Device Fingerprinting System**:
```typescript
class DeviceFingerprintService {
  private deviceId: string;
  
  async generateDeviceId(): Promise<string> {
    // Generate anonymous device identifier
    // Combine device info without personal data
    // Store securely in keychain
    return sha256(deviceInfo + randomSalt);
  }
  
  async getDeviceId(): Promise<string> {
    if (!this.deviceId) {
      this.deviceId = await this.loadOrGenerateDeviceId();
    }
    return this.deviceId;
  }
}
```

### API Specifications

**Supabase Integration Services**:
```typescript
// Supabase Client Service
class FeedbackSupabaseService {
  private supabase: SupabaseClient;
  private realtimeChannel?: RealtimeChannel;
  
  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient;
  }

  // Sync operations
  async syncFeedbackToCloud(localFeedback: FeedbackSubmission[]): Promise<SyncResult[]> {
    const results: SyncResult[] = [];
    
    for (const feedback of localFeedback) {
      if (feedback.syncStatus === 'pending') {
        try {
          const { data, error } = await this.supabase
            .from('feedback_submissions')
            .upsert(this.transformToSupabaseFormat(feedback))
            .select()
            .single();
          
          if (error) throw error;
          results.push({ localId: feedback.id, success: true, supabaseId: data.id });
        } catch (error) {
          results.push({ localId: feedback.id, success: false, error: error.message });
        }
      }
    }
    
    return results;
  }

  async pullUpdatesFromCloud(deviceId: string, lastSyncAt?: Date): Promise<SupabaseFeedbackSubmission[]> {
    const query = this.supabase
      .from('feedback_submissions')
      .select('*')
      .eq('device_id', deviceId);
      
    if (lastSyncAt) {
      query.gt('updated_at', lastSyncAt.toISOString());
    }
    
    const { data, error } = await query;
    if (error) throw error;
    
    return data || [];
  }

  // Real-time subscriptions
  subscribeToStatusUpdates(deviceId: string, callback: (update: FeedbackStatusUpdate) => void): void {
    this.realtimeChannel = this.supabase
      .channel('feedback_status_updates')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'feedback_submissions',
        filter: `device_id=eq.${deviceId}`
      }, (payload) => {
        callback(this.transformToStatusUpdate(payload.new));
      })
      .subscribe();
  }

  unsubscribeFromUpdates(): void {
    if (this.realtimeChannel) {
      this.supabase.removeChannel(this.realtimeChannel);
      this.realtimeChannel = undefined;
    }
  }

  // Voting operations
  async submitVote(vote: FeatureVote): Promise<string> {
    const { data, error } = await this.supabase
      .from('feature_votes')
      .upsert({
        feedback_id: vote.featureRequestId,
        device_id: vote.deviceId,
        vote_value: vote.voteValue
      })
      .select()
      .single();
      
    if (error) throw error;
    return data.id;
  }

  // File upload for screenshots
  async uploadScreenshot(file: File, feedbackId: string): Promise<string> {
    const fileName = `feedback/${feedbackId}/${Date.now()}_screenshot.jpg`;
    
    const { data, error } = await this.supabase.storage
      .from('feedback-attachments')
      .upload(fileName, file);
      
    if (error) throw error;
    
    const { data: { publicUrl } } = this.supabase.storage
      .from('feedback-attachments')
      .getPublicUrl(fileName);
      
    return publicUrl;
  }

  private transformToSupabaseFormat(feedback: FeedbackSubmission): Partial<SupabaseFeedbackSubmission> {
    return {
      id: feedback.supabaseId,
      category: feedback.category,
      title: feedback.title,
      description: feedback.description,
      priority_level: feedback.priorityLevel,
      device_id: feedback.deviceId,
      contact_info: feedback.contactInfo,
      reward_eligible: feedback.rewardEligible,
      submitted_at: feedback.submittedAt.toISOString(),
      version: feedback.version
    };
  }

  private transformToStatusUpdate(supabaseData: any): FeedbackStatusUpdate {
    return {
      id: supabaseData.id,
      status: supabaseData.status,
      adminFeedback: supabaseData.admin_feedback,
      reviewedAt: supabaseData.reviewed_at ? new Date(supabaseData.reviewed_at) : undefined
    };
  }
}

interface SyncResult {
  localId: number;
  success: boolean;
  supabaseId?: string;
  error?: string;
}

// Local sync service
class FeedbackSyncService {
  private localDb: SQLiteDatabase;
  private supabaseService: FeedbackSupabaseService;
  private syncQueue: SyncOperation[] = [];
  
  async syncPendingOperations(): Promise<void> {
    const pendingFeedback = await this.localDb.getAllSyncPending();
    const syncResults = await this.supabaseService.syncFeedbackToCloud(pendingFeedback);
    
    // Update local records with sync results
    for (const result of syncResults) {
      if (result.success) {
        await this.localDb.updateSyncStatus(result.localId, 'synced', result.supabaseId);
      } else {
        await this.localDb.updateSyncStatus(result.localId, 'failed');
        await this.addToRetryQueue(result.localId, result.error);
      }
    }
  }

  async handleIncomingUpdates(): Promise<void> {
    const deviceId = await DeviceFingerprintService.getDeviceId();
    const lastSync = await this.localDb.getLastSyncTimestamp();
    
    const updates = await this.supabaseService.pullUpdatesFromCloud(deviceId, lastSync);
    
    for (const update of updates) {
      await this.mergeCloudUpdate(update);
    }
    
    await this.localDb.updateLastSyncTimestamp(new Date());
  }

  private async mergeCloudUpdate(cloudData: SupabaseFeedbackSubmission): Promise<void> {
    const existingLocal = await this.localDb.findBySupabaseId(cloudData.id);
    
    if (!existingLocal) {
      // New feedback from another device - should not happen for anonymous submissions
      return;
    }
    
    // Update local record with cloud changes (admin feedback, status changes)
    if (cloudData.version > existingLocal.version || cloudData.updated_at > existingLocal.lastSyncedAt?.toISOString()) {
      await this.localDb.updateFromCloud({
        id: existingLocal.id,
        status: cloudData.status,
        adminFeedback: cloudData.admin_feedback,
        version: cloudData.version,
        lastSyncedAt: new Date(cloudData.updated_at)
      });
    }
  }
}
```

### File Locations
Based on layered architecture [Source: architecture/7-application-architecture-and-data-flow.md#layered-architecture]:
- **Feedback Services**: `src/services/feedback/`
- **Feedback Components**: `src/components/feedback/`
- **Feedback Store**: `src/stores/feedbackStore.ts`
- **Feedback Screens**: `src/screens/feedback/`
- **Feedback Types**: `src/types/feedback.ts`
- **Device Utils**: `src/utils/device.ts`
- **Supabase Services**: `src/services/supabase/feedbackSupabaseService.ts`
- **Sync Services**: `src/services/sync/feedbackSyncService.ts`
- **Real-time Services**: `src/services/realtime/feedbackRealTimeService.ts`

### Technical Constraints
**Privacy Requirements**:
- No personal data collection without explicit opt-in
- Device fingerprinting must be anonymous and non-reversible
- Contact info encrypted before local storage
- Clear separation between anonymous feedback and reward eligibility

**Offline-First Requirements**:
- All feedback stored locally first in SQLite
- Background sync to Supabase when connectivity available  
- Offline voting with sync queue management
- Graceful handling of sync failures with retry mechanism
- Conflict resolution using version control
- Real-time updates when connected to Supabase

**Performance Requirements**:
- Feedback form submission <2 seconds locally
- Feature request loading <3 seconds
- Sync operation should not block UI
- Offline operation must be seamless

## Testing

### Testing Standards
**Test File Locations**: 
- Unit tests: `src/__tests__/feedback/`
- Integration tests: `src/__tests__/integration/feedback/`
- Component tests: `src/__tests__/components/feedback/`

**Testing Frameworks**:
- Jest for unit testing
- React Native Testing Library for component testing
- Mock API responses for sync testing

**Specific Testing Requirements for This Story**:
- Anonymous feedback submission without data leaks
- Device fingerprinting uniqueness and anonymity
- Offline feedback storage and sync queue functionality
- Reward eligibility opt-in and privacy separation
- Feature request voting with fraud prevention
- Early access token generation and validation
- Sync failure handling and retry mechanisms

### Key Test Scenarios
- Submit anonymous feedback without personal data
- Test reward opt-in flow with contact info collection
- Validate device fingerprinting anonymity
- Test offline feedback submission and sync
- Verify voting system with duplicate prevention
- Test early access token generation and expiry
- Validate feedback sync with server responses
- Test privacy separation between feedback and rewards

### Privacy and Security Tests
- Verify no personal data in anonymous submissions
- Test device ID anonymization and uniqueness
- Validate contact info encryption for reward eligible users
- Test data isolation between anonymous and reward systems
- Verify sync data encryption and transmission security

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-07 | 1.0 | Initial story creation | Scrum Master |

## Dev Agent Record

### Agent Model Used
Claude Sonnet 4 (claude-sonnet-4-20250514)

### Debug Log References
- TypeScript compilation and validation
- Component theming system integration
- Database migration system extension

### Completion Notes List
- ✅ **Task 1 Complete**: Database schema, migration files, and service layer implemented
- ✅ **Task 2 Complete**: Anonymous feedback form with reward claiming system implemented
- 🏗️ **Tasks 3-6**: Pending implementation (voting system, early access, Supabase integration)

### File List
**Database & Migrations:**
- `src/data/database/migrations/012_create_feedback_submissions.js`
- `src/data/database/migrations/013_create_feature_votes.js`
- `src/data/database/migrations/014_create_early_access_tokens.js`
- `src/data/database/migrations/015_create_feedback_sync_queue.js`
- Updated: `src/data/database/MigrationManager.ts`

**Types & Interfaces:**
- `src/shared/types/feedback.ts`
- Updated: `src/shared/types/index.ts`

**Services & Business Logic:**
- `src/business/services/FeedbackService.ts`
- `src/shared/utils/deviceFingerprint.ts`

**State Management:**
- `src/shared/stores/feedbackStore.ts`
- Updated: `src/shared/stores/index.ts`

**UI Components:**
- `src/presentation/components/feedback/FeedbackForm.tsx`
- `src/presentation/components/feedback/FeedbackList.tsx`
- `src/presentation/components/feedback/index.ts`

**Screens:**
- `src/presentation/screens/feedback/FeedbackScreen.tsx`
- `src/presentation/screens/feedback/index.ts`

**Theme Updates:**
- Updated: `src/shared/theme/index.ts` (added h4, h5 typography)

**Tests:**
- `src/__tests__/feedback/FeedbackService.test.ts`

## QA Results
*To be filled by QA agent*