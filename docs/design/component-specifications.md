# FinVibe Component Specifications
*Detailed Implementation Guide for React Native Components*

## Table of Contents
1. [Design System Foundation](#design-system-foundation)
2. [Core UI Components](#core-ui-components)
3. [Feature Components](#feature-components)
4. [Form Components](#form-components)
5. [Data Display Components](#data-display-components)
6. [Navigation Components](#navigation-components)
7. [Accessibility Specifications](#accessibility-specifications)
8. [Testing Requirements](#testing-requirements)

---

## Design System Foundation

### Theme Configuration
```typescript
// src/shared/theme/index.ts
export interface FinVibeTheme {
  colors: {
    // Primary colors
    primary: string;
    primaryLight: string;
    primaryDark: string;
    
    // Semantic colors
    success: string;
    warning: string;
    error: string;
    info: string;
    
    // Transaction types
    income: string;
    expense: string;
    transfer: string;
    
    // Background colors
    background: string;
    surface: string;
    card: string;
    
    // Text colors
    text: string;
    textSecondary: string;
    textDisabled: string;
    
    // Border colors
    border: string;
    borderLight: string;
    
    // Status indicators
    synced: string;
    pending: string;
    offline: string;
    conflict: string;
  };
  
  typography: {
    h1: TextStyle;
    h2: TextStyle;
    h3: TextStyle;
    body: TextStyle;
    caption: TextStyle;
    button: TextStyle;
  };
  
  spacing: {
    xs: number; // 4
    sm: number; // 8
    md: number; // 16
    lg: number; // 24
    xl: number; // 32
    xxl: number; // 48
  };
  
  borderRadius: {
    sm: number; // 4
    md: number; // 8
    lg: number; // 12
    xl: number; // 16
  };
  
  shadows: {
    sm: ViewStyle;
    md: ViewStyle;
    lg: ViewStyle;
  };
}

export const lightTheme: FinVibeTheme = {
  colors: {
    primary: '#4A90E2',
    primaryLight: '#7BB3F0',
    primaryDark: '#2E5C8A',
    
    success: '#58D68D',
    warning: '#F39C12',
    error: '#FF6B6B',
    info: '#5DADE2',
    
    income: '#58D68D',
    expense: '#FF6B6B',
    transfer: '#F39C12',
    
    background: '#FFFFFF',
    surface: '#F8F9FA',
    card: '#FFFFFF',
    
    text: '#2C3E50',
    textSecondary: '#7F8C8D',
    textDisabled: '#BDC3C7',
    
    border: '#E5E5E5',
    borderLight: '#F0F0F0',
    
    synced: '#58D68D',
    pending: '#F39C12',
    offline: '#95A5A6',
    conflict: '#FF6B6B',
  },
  typography: {
    h1: { fontSize: 32, fontWeight: '700', lineHeight: 40 },
    h2: { fontSize: 24, fontWeight: '600', lineHeight: 32 },
    h3: { fontSize: 18, fontWeight: '600', lineHeight: 24 },
    body: { fontSize: 16, fontWeight: '400', lineHeight: 24 },
    caption: { fontSize: 14, fontWeight: '400', lineHeight: 20 },
    button: { fontSize: 16, fontWeight: '600', lineHeight: 20 },
  },
  spacing: {
    xs: 4,
    sm: 8, 
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 8,
    },
  },
};
```

---

## Core UI Components

### 1. Button Component

```typescript
// src/presentation/components/core/Button.tsx
import React from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';

export interface ButtonProps {
  // Content
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  
  // Behavior
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  
  // Appearance
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
  
  // Styling
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  subtitle,
  icon,
  iconPosition = 'left',
  onPress,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  accessibilityLabel,
  accessibilityHint,
  testID,
  style,
  textStyle,
}) => {
  const theme = useTheme();
  
  const buttonStyles = [
    styles.button,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];
  
  const textStyles = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    disabled && styles.disabledText,
    textStyle,
  ];
  
  return (
    <TouchableOpacity
      style={buttonStyles}
      onPress={onPress}
      disabled={disabled || loading}
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityState={{ disabled: disabled || loading }}
      testID={testID}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator 
          color={variant === 'primary' ? theme.colors.background : theme.colors.primary}
          size="small"
        />
      ) : (
        <>
          {icon && iconPosition === 'left' && icon}
          <Text style={textStyles}>{title}</Text>
          {subtitle && <Text style={[textStyles, styles.subtitle]}>{subtitle}</Text>}
          {icon && iconPosition === 'right' && icon}
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    minHeight: 48, // WCAG AA touch target
    gap: 8,
  },
  
  // Variants
  primary: {
    backgroundColor: '#4A90E2',
  },
  secondary: {
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#4A90E2',
  },
  ghost: {
    backgroundColor: 'transparent',
  },
  danger: {
    backgroundColor: '#FF6B6B',
  },
  
  // Sizes
  sm: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    minHeight: 40,
  },
  md: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    minHeight: 48,
  },
  lg: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    minHeight: 56,
  },
  
  // States
  disabled: {
    opacity: 0.5,
  },
  fullWidth: {
    width: '100%',
  },
  
  // Text styles
  text: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  primaryText: {
    color: '#FFFFFF',
  },
  secondaryText: {
    color: '#2C3E50',
  },
  outlineText: {
    color: '#4A90E2',
  },
  ghostText: {
    color: '#4A90E2',
  },
  dangerText: {
    color: '#FFFFFF',
  },
  disabledText: {
    opacity: 0.5,
  },
  
  subtitle: {
    fontSize: 14,
    fontWeight: '400',
    opacity: 0.8,
  },
  
  // Size-specific text
  smText: {
    fontSize: 14,
  },
  mdText: {
    fontSize: 16,
  },
  lgText: {
    fontSize: 18,
  },
});
```

### 2. Card Component

```typescript
// src/presentation/components/core/Card.tsx
import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';

export interface CardProps {
  // Content
  children: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  
  // Behavior
  onPress?: () => void;
  onLongPress?: () => void;
  
  // Appearance
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: 'button' | 'none';
  testID?: string;
  
  // Styling
  style?: ViewStyle;
}

export const Card: React.FC<CardProps> = ({
  children,
  header,
  footer,
  onPress,
  onLongPress,
  variant = 'default',
  padding = 'md',
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = onPress ? 'button' : 'none',
  testID,
  style,
}) => {
  const theme = useTheme();
  
  const cardStyles = [
    styles.card,
    styles[variant],
    styles[`padding${padding.charAt(0).toUpperCase() + padding.slice(1)}`],
    style,
  ];
  
  const CardComponent = onPress ? TouchableOpacity : View;
  
  return (
    <CardComponent
      style={cardStyles}
      onPress={onPress}
      onLongPress={onLongPress}
      accessibilityRole={accessibilityRole}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      testID={testID}
      activeOpacity={onPress ? 0.7 : 1}
    >
      {header && <View style={styles.header}>{header}</View>}
      <View style={styles.content}>{children}</View>
      {footer && <View style={styles.footer}>{footer}</View>}
    </CardComponent>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
  },
  
  // Variants
  default: {
    backgroundColor: '#FFFFFF',
  },
  elevated: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  outlined: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  
  // Padding
  paddingNone: {
    padding: 0,
  },
  paddingSm: {
    padding: 8,
  },
  paddingMd: {
    padding: 16,
  },
  paddingLg: {
    padding: 24,
  },
  
  header: {
    marginBottom: 12,
  },
  content: {
    flex: 1,
  },
  footer: {
    marginTop: 12,
  },
});
```

---

## Feature Components

### 3. Budget Progress Card

```typescript
// src/presentation/components/budget/BudgetProgressCard.tsx
import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { Card } from '@/presentation/components/core/Card';
import { ProgressBar } from '@/presentation/components/core/ProgressBar';
import { useTheme } from '@/shared/theme/ThemeProvider';

export interface BudgetProgressCardProps {
  // Data
  categoryName: string;
  spent: number;
  budget: number;
  currency?: string;
  icon?: string;
  
  // Behavior
  onPress?: () => void;
  
  // Appearance
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  
  // Accessibility
  testID?: string;
  
  // Styling
  style?: ViewStyle;
}

export const BudgetProgressCard: React.FC<BudgetProgressCardProps> = ({
  categoryName,
  spent,
  budget,
  currency = '$',
  icon,
  onPress,
  size = 'md',
  showPercentage = true,
  testID,
  style,
}) => {
  const theme = useTheme();
  
  const percentage = budget > 0 ? Math.min((spent / budget) * 100, 100) : 0;
  const remaining = Math.max(budget - spent, 0);
  const isOverBudget = spent > budget;
  
  const getStatusColor = () => {
    if (isOverBudget) return theme.colors.error;
    if (percentage > 80) return theme.colors.warning;
    return theme.colors.success;
  };
  
  const getAccessibilityLabel = () => {
    const status = isOverBudget 
      ? `Over budget by ${currency}${(spent - budget).toFixed(2)}`
      : `${currency}${remaining.toFixed(2)} remaining`;
    
    return `${categoryName} budget. Spent ${currency}${spent.toFixed(2)} of ${currency}${budget.toFixed(2)}. ${status}`;
  };
  
  return (
    <Card
      onPress={onPress}
      style={[styles.card, styles[size], style]}
      accessibilityLabel={getAccessibilityLabel()}
      accessibilityHint={onPress ? "Tap to view category details" : undefined}
      testID={testID}
    >
      <View style={styles.header}>
        {icon && <Text style={styles.icon}>{icon}</Text>}
        <Text 
          style={[styles.categoryName, styles[`${size}CategoryName`]]}
          numberOfLines={1}
          accessibilityRole="header"
        >
          {categoryName}
        </Text>
      </View>
      
      <View style={styles.amounts}>
        <Text style={[styles.spent, { color: getStatusColor() }]}>
          {currency}{spent.toFixed(2)}
        </Text>
        <Text style={styles.budget}>
          /{currency}{budget.toFixed(2)}
        </Text>
      </View>
      
      <ProgressBar
        progress={percentage}
        color={getStatusColor()}
        backgroundColor={theme.colors.borderLight}
        height={size === 'sm' ? 4 : 6}
        style={styles.progressBar}
        accessibilityLabel={`${percentage.toFixed(0)}% of budget used`}
      />
      
      {showPercentage && (
        <Text 
          style={[styles.percentage, { color: getStatusColor() }]}
          accessibilityLabel={`${percentage.toFixed(0)} percent`}
        >
          {percentage.toFixed(0)}%
          {isOverBudget && (
            <Text style={styles.overBudgetText}> over budget</Text>
          )}
        </Text>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    minHeight: 120,
  },
  
  // Size variants
  sm: {
    minHeight: 100,
    padding: 12,
  },
  md: {
    minHeight: 120,
    padding: 16,
  },
  lg: {
    minHeight: 140,
    padding: 20,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  
  icon: {
    fontSize: 20,
    marginRight: 8,
  },
  
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
    flex: 1,
  },
  
  smCategoryName: {
    fontSize: 14,
  },
  mdCategoryName: {
    fontSize: 16,
  },
  lgCategoryName: {
    fontSize: 18,
  },
  
  amounts: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  
  spent: {
    fontSize: 20,
    fontWeight: '700',
  },
  
  budget: {
    fontSize: 16,
    color: '#7F8C8D',
    fontWeight: '400',
  },
  
  progressBar: {
    marginBottom: 8,
  },
  
  percentage: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'right',
  },
  
  overBudgetText: {
    fontSize: 12,
    fontWeight: '400',
    color: '#FF6B6B',
  },
});
```

### 4. Transaction List Item

```typescript
// src/presentation/components/transactions/TransactionListItem.tsx
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { Transaction } from '@/shared/types';
import { SyncStatusIndicator } from '@/presentation/components/core/SyncStatusIndicator';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { formatCurrency, formatDate } from '@/shared/utils/formatting';

export interface TransactionListItemProps {
  // Data
  transaction: Transaction;
  categoryName?: string;
  accountName?: string;
  
  // Behavior
  onPress?: (transaction: Transaction) => void;
  onLongPress?: (transaction: Transaction) => void;
  selectable?: boolean;
  selected?: boolean;
  
  // Appearance
  showAccount?: boolean;
  showCategory?: boolean;
  showDate?: boolean;
  showSyncStatus?: boolean;
  dense?: boolean;
  
  // Accessibility
  testID?: string;
}

export const TransactionListItem: React.FC<TransactionListItemProps> = ({
  transaction,
  categoryName,
  accountName,
  onPress,
  onLongPress,
  selectable = false,
  selected = false,
  showAccount = true,
  showCategory = true,
  showDate = true,
  showSyncStatus = true,
  dense = false,
  testID,
}) => {
  const theme = useTheme();
  
  const getAmountColor = () => {
    switch (transaction.transaction_type) {
      case 'income':
        return theme.colors.income;
      case 'expense':
        return theme.colors.expense;
      case 'transfer':
        return theme.colors.transfer;
      default:
        return theme.colors.text;
    }
  };
  
  const getAmountPrefix = () => {
    switch (transaction.transaction_type) {
      case 'income':
        return '+';
      case 'expense':
        return '-';
      case 'transfer':
        return '→';
      default:
        return '';
    }
  };
  
  const getAccessibilityLabel = () => {
    const parts = [
      transaction.description,
      `${getAmountPrefix()}${formatCurrency(transaction.amount)}`,
      transaction.transaction_type,
    ];
    
    if (showCategory && categoryName) {
      parts.push(`Category: ${categoryName}`);
    }
    
    if (showAccount && accountName) {
      parts.push(`Account: ${accountName}`);
    }
    
    if (showDate) {
      parts.push(`Date: ${formatDate(transaction.transaction_date)}`);
    }
    
    if (transaction.sms_source) {
      parts.push('From SMS');
    }
    
    return parts.join(', ');
  };
  
  const handlePress = () => {
    if (onPress) {
      onPress(transaction);
    }
  };
  
  const handleLongPress = () => {
    if (onLongPress) {
      onLongPress(transaction);
    } else if (selectable) {
      // Default long press behavior for selection
      Alert.alert(
        'Transaction Options',
        'What would you like to do with this transaction?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Edit', onPress: () => onPress?.(transaction) },
          { text: 'Delete', style: 'destructive' },
        ]
      );
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        dense && styles.dense,
        selected && styles.selected,
      ]}
      onPress={handlePress}
      onLongPress={handleLongPress}
      disabled={!onPress && !onLongPress}
      accessibilityLabel={getAccessibilityLabel()}
      accessibilityHint={onPress ? "Tap to edit transaction" : undefined}
      accessibilityRole="button"
      testID={testID}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        <View style={styles.leftSection}>
          <Text 
            style={styles.description}
            numberOfLines={1}
            accessibilityRole="header"
          >
            {transaction.description}
          </Text>
          
          <View style={styles.metadata}>
            {showCategory && categoryName && (
              <Text style={styles.metadataText}>{categoryName}</Text>
            )}
            
            {showAccount && accountName && (
              <Text style={styles.metadataText}>• {accountName}</Text>
            )}
            
            {showDate && (
              <Text style={styles.metadataText}>
                • {formatDate(transaction.transaction_date, 'MMM d')}
              </Text>
            )}
            
            {transaction.sms_source && (
              <Text style={styles.smsIndicator}>📱</Text>
            )}
          </View>
        </View>
        
        <View style={styles.rightSection}>
          <Text 
            style={[styles.amount, { color: getAmountColor() }]}
            accessibilityLabel={`${getAmountPrefix()}${formatCurrency(transaction.amount)}`}
          >
            {getAmountPrefix()}{formatCurrency(transaction.amount)}
          </Text>
          
          {showSyncStatus && (
            <SyncStatusIndicator
              status={transaction.sync_status}
              size="sm"
              style={styles.syncStatus}
            />
          )}
        </View>
      </View>
      
      {transaction.confidence_score && transaction.confidence_score < 0.8 && (
        <View style={styles.lowConfidenceBar} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    minHeight: 64, // WCAG AA touch target
  },
  
  dense: {
    paddingVertical: 8,
    minHeight: 56,
  },
  
  selected: {
    backgroundColor: '#E3F2FD',
  },
  
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  leftSection: {
    flex: 1,
    marginRight: 12,
  },
  
  rightSection: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  
  description: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 4,
  },
  
  metadata: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  
  metadataText: {
    fontSize: 14,
    color: '#7F8C8D',
    marginRight: 4,
  },
  
  smsIndicator: {
    fontSize: 12,
    marginLeft: 4,
  },
  
  amount: {
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'right',
  },
  
  syncStatus: {
    marginTop: 4,
  },
  
  lowConfidenceBar: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: 3,
    height: '100%',
    backgroundColor: '#F39C12',
  },
});
```

---

## Form Components

### 5. Currency Input

```typescript
// src/presentation/components/form/CurrencyInput.tsx
import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';

export interface CurrencyInputProps {
  // Value
  value: string;
  onChangeValue: (value: string) => void;
  
  // Configuration
  currency?: string;
  placeholder?: string;
  maxValue?: number;
  
  // Appearance
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'large';
  
  // Validation
  error?: string;
  required?: boolean;
  
  // Behavior
  autoFocus?: boolean;
  disabled?: boolean;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
  
  // Styling
  style?: ViewStyle;
  inputStyle?: TextStyle;
}

export const CurrencyInput: React.FC<CurrencyInputProps> = ({
  value,
  onChangeValue,
  currency = '$',
  placeholder = '0.00',
  maxValue,
  size = 'md',
  variant = 'default',
  error,
  required = false,
  autoFocus = false,
  disabled = false,
  accessibilityLabel,
  accessibilityHint,
  testID,
  style,
  inputStyle,
}) => {
  const theme = useTheme();
  const inputRef = useRef<TextInput>(null);
  const [focused, setFocused] = useState(false);
  
  const formatValue = (inputValue: string): string => {
    // Remove non-numeric characters except decimal point
    const numericValue = inputValue.replace(/[^0-9.]/g, '');
    
    // Ensure only one decimal point
    const parts = numericValue.split('.');
    if (parts.length > 2) {
      return parts[0] + '.' + parts.slice(1).join('');
    }
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
      return parts[0] + '.' + parts[1].substring(0, 2);
    }
    
    return numericValue;
  };
  
  const handleChangeText = (text: string) => {
    const formattedValue = formatValue(text);
    
    // Check max value constraint
    if (maxValue && parseFloat(formattedValue) > maxValue) {
      return;
    }
    
    onChangeValue(formattedValue);
  };
  
  const getContainerStyles = () => [
    styles.container,
    styles[size],
    styles[variant],
    focused && styles.focused,
    error && styles.error,
    disabled && styles.disabled,
    style,
  ];
  
  const getInputStyles = () => [
    styles.input,
    styles[`${size}Input`],
    styles[`${variant}Input`],
    inputStyle,
  ];
  
  const getAccessibilityLabel = () => {
    return accessibilityLabel || `Amount input, currently ${value || '0'} ${currency}`;
  };
  
  return (
    <View style={styles.wrapper}>
      <View style={getContainerStyles()}>
        <Text style={styles.currencySymbol}>{currency}</Text>
        <TextInput
          ref={inputRef}
          style={getInputStyles()}
          value={value}
          onChangeText={handleChangeText}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.textDisabled}
          keyboardType="numeric"
          returnKeyType="done"
          autoFocus={autoFocus}
          editable={!disabled}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          accessibilityLabel={getAccessibilityLabel()}
          accessibilityHint={accessibilityHint || "Enter the amount"}
          accessibilityRole="none" // Let it be treated as text input
          testID={testID}
          maxLength={12} // Reasonable limit for currency values
        />
      </View>
      
      {error && (
        <Text 
          style={styles.errorText}
          accessibilityRole="alert"
          accessibilityLiveRegion="polite"
        >
          {error}
        </Text>
      )}
      
      {maxValue && (
        <Text 
          style={styles.helpText}
          accessibilityLabel={`Maximum amount is ${maxValue} ${currency}`}
        >
          Max: {currency}{maxValue.toFixed(2)}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: 16,
  },
  
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingHorizontal: 16,
  },
  
  // Size variants
  sm: {
    paddingVertical: 8,
    minHeight: 40,
  },
  md: {
    paddingVertical: 12,
    minHeight: 48,
  },
  lg: {
    paddingVertical: 16,
    minHeight: 56,
  },
  
  // Style variants
  default: {
    backgroundColor: '#F8F9FA',
  },
  large: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    paddingVertical: 20,
    minHeight: 80,
  },
  
  // States
  focused: {
    borderColor: '#4A90E2',
    backgroundColor: '#FFFFFF',
  },
  error: {
    borderColor: '#FF6B6B',
    backgroundColor: '#FFF5F5',
  },
  disabled: {
    opacity: 0.6,
    backgroundColor: '#F5F5F5',
  },
  
  currencySymbol: {
    fontSize: 18,
    fontWeight: '600',
    color: '#7F8C8D',
    marginRight: 8,
  },
  
  input: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
    textAlign: 'left',
  },
  
  // Size-specific input styles
  smInput: {
    fontSize: 16,
  },
  mdInput: {
    fontSize: 18,
  },
  lgInput: {
    fontSize: 24,
  },
  
  // Variant-specific input styles
  defaultInput: {
    fontSize: 18,
  },
  largeInput: {
    fontSize: 32,
    fontWeight: '700',
    textAlign: 'center',
  },
  
  errorText: {
    fontSize: 14,
    color: '#FF6B6B',
    marginTop: 4,
    marginLeft: 4,
  },
  
  helpText: {
    fontSize: 12,
    color: '#7F8C8D',
    marginTop: 4,
    marginLeft: 4,
  },
});
```

---

## Data Display Components

### 6. Sync Status Indicator

```typescript
// src/presentation/components/core/SyncStatusIndicator.tsx
import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { Transaction } from '@/shared/types';
import { useTheme } from '@/shared/theme/ThemeProvider';

export interface SyncStatusIndicatorProps {
  // Data
  status: Transaction['sync_status'];
  
  // Appearance
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  variant?: 'dot' | 'badge' | 'full';
  
  // Accessibility
  testID?: string;
  
  // Styling
  style?: ViewStyle;
}

export const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({
  status,
  size = 'md',
  showLabel = false,
  variant = 'dot',
  testID,
  style,
}) => {
  const theme = useTheme();
  
  const getStatusConfig = () => {
    switch (status) {
      case 'synced':
        return {
          color: theme.colors.synced,
          label: 'Synced',
          icon: '✓',
          accessibilityLabel: 'Synced successfully',
        };
      case 'pending':
        return {
          color: theme.colors.pending,
          label: 'Syncing',
          icon: '⟳',
          accessibilityLabel: 'Sync pending',
        };
      case 'conflict':
        return {
          color: theme.colors.conflict,
          label: 'Conflict',
          icon: '⚠',
          accessibilityLabel: 'Sync conflict needs attention',
        };
      case 'local':
      default:
        return {
          color: theme.colors.offline,
          label: 'Local',
          icon: '○',
          accessibilityLabel: 'Stored locally only',
        };
    }
  };
  
  const config = getStatusConfig();
  
  const getDotSize = () => {
    switch (size) {
      case 'sm': return 6;
      case 'md': return 8;
      case 'lg': return 10;
      default: return 8;
    }
  };
  
  if (variant === 'dot') {
    return (
      <View
        style={[
          styles.dot,
          {
            width: getDotSize(),
            height: getDotSize(),
            backgroundColor: config.color,
          },
          style,
        ]}
        accessibilityLabel={config.accessibilityLabel}
        accessibilityRole="image"
        testID={testID}
      />
    );
  }
  
  if (variant === 'badge') {
    return (
      <View
        style={[
          styles.badge,
          styles[size],
          { backgroundColor: config.color },
          style,
        ]}
        accessibilityLabel={config.accessibilityLabel}
        accessibilityRole="text"
        testID={testID}
      >
        <Text style={[styles.badgeText, styles[`${size}BadgeText`]]}>
          {config.icon}
        </Text>
      </View>
    );
  }
  
  if (variant === 'full') {
    return (
      <View
        style={[styles.full, style]}
        accessibilityLabel={config.accessibilityLabel}
        accessibilityRole="text"
        testID={testID}
      >
        <View
          style={[
            styles.dot,
            {
              width: getDotSize(),
              height: getDotSize(),
              backgroundColor: config.color,
            },
          ]}
        />
        {showLabel && (
          <Text style={[styles.label, { color: config.color }]}>
            {config.label}
          </Text>
        )}
      </View>
    );
  }
  
  return null;
};

const styles = StyleSheet.create({
  dot: {
    borderRadius: 50,
  },
  
  badge: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 16,
    minHeight: 16,
  },
  
  // Badge sizes
  sm: {
    minWidth: 14,
    minHeight: 14,
  },
  md: {
    minWidth: 18,
    minHeight: 18,
  },
  lg: {
    minWidth: 22,
    minHeight: 22,
  },
  
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  
  smBadgeText: {
    fontSize: 8,
  },
  mdBadgeText: {
    fontSize: 10,
  },
  lgBadgeText: {
    fontSize: 12,
  },
  
  full: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  
  label: {
    fontSize: 12,
    fontWeight: '500',
  },
});
```

### 7. Progress Bar

```typescript
// src/presentation/components/core/ProgressBar.tsx
import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet, ViewStyle } from 'react-native';

export interface ProgressBarProps {
  // Data
  progress: number; // 0-100
  
  // Appearance
  color?: string;
  backgroundColor?: string;
  height?: number;
  borderRadius?: number;
  animated?: boolean;
  animationDuration?: number;
  
  // Accessibility
  accessibilityLabel?: string;
  testID?: string;
  
  // Styling
  style?: ViewStyle;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  color = '#4A90E2',
  backgroundColor = '#E5E5E5',
  height = 6,
  borderRadius,
  animated = true,
  animationDuration = 300,
  accessibilityLabel,
  testID,
  style,
}) => {
  const animatedWidth = useRef(new Animated.Value(0)).current;
  
  const clampedProgress = Math.max(0, Math.min(100, progress));
  const finalBorderRadius = borderRadius ?? height / 2;
  
  useEffect(() => {
    if (animated) {
      Animated.timing(animatedWidth, {
        toValue: clampedProgress,
        duration: animationDuration,
        useNativeDriver: false,
      }).start();
    } else {
      animatedWidth.setValue(clampedProgress);
    }
  }, [clampedProgress, animated, animationDuration]);
  
  return (
    <View
      style={[
        styles.container,
        {
          height,
          backgroundColor,
          borderRadius: finalBorderRadius,
        },
        style,
      ]}
      accessibilityRole="progressbar"
      accessibilityLabel={accessibilityLabel || `${clampedProgress.toFixed(0)}% progress`}
      accessibilityValue={{
        min: 0,
        max: 100,
        now: clampedProgress,
      }}
      testID={testID}
    >
      <Animated.View
        style={[
          styles.fill,
          {
            height,
            backgroundColor: color,
            borderRadius: finalBorderRadius,
            width: animatedWidth.interpolate({
              inputRange: [0, 100],
              outputRange: ['0%', '100%'],
              extrapolate: 'clamp',
            }),
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  fill: {
    position: 'absolute',
    left: 0,
    top: 0,
  },
});
```

---

## Accessibility Specifications

### WCAG AA Compliance Checklist

```typescript
// src/shared/accessibility/index.ts

export interface AccessibilityRequirements {
  // Color & Contrast (WCAG 1.4.3)
  textContrast: {
    normal: number; // 4.5:1
    large: number;  // 3:1
  };
  
  // Touch Targets (WCAG 2.5.5)
  minTouchTarget: {
    width: number;  // 48dp
    height: number; // 48dp
    spacing: number; // 8dp
  };
  
  // Focus Management (WCAG 2.4.7)
  focusIndicator: {
    minContrast: number; // 3:1
    minThickness: number; // 2px
  };
  
  // Motion & Animation (WCAG 2.3.3)
  animation: {
    respectReducedMotion: boolean;
    maxDuration: number; // 5000ms
    allowPause: boolean;
  };
}

export const accessibilityConfig: AccessibilityRequirements = {
  textContrast: {
    normal: 4.5,
    large: 3.0,
  },
  minTouchTarget: {
    width: 48,
    height: 48,
    spacing: 8,
  },
  focusIndicator: {
    minContrast: 3.0,
    minThickness: 2,
  },
  animation: {
    respectReducedMotion: true,
    maxDuration: 5000,
    allowPause: true,
  },
};

// Accessibility helper functions
export const getAccessibilityProps = (config: {
  label?: string;
  hint?: string;
  role?: string;
  state?: any;
  value?: any;
}) => {
  return {
    accessibilityLabel: config.label,
    accessibilityHint: config.hint,
    accessibilityRole: config.role as any,
    accessibilityState: config.state,
    accessibilityValue: config.value,
  };
};

export const announceForScreenReader = (message: string) => {
  // Implementation for live region announcements
  // This would integrate with React Native's AccessibilityInfo
};
```

---

## Testing Requirements

### Component Testing Template

```typescript
// src/presentation/components/__tests__/Button.test.tsx
import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import { Button } from '../core/Button';
import { ThemeProvider } from '@/shared/theme/ThemeProvider';

// Test wrapper with theme
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider>{children}</ThemeProvider>
);

describe('Button Component', () => {
  // Basic functionality
  it('renders correctly with title', () => {
    render(
      <Button title="Test Button" onPress={() => {}} />,
      { wrapper: TestWrapper }
    );
    
    expect(screen.getByText('Test Button')).toBeTruthy();
  });
  
  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    render(
      <Button title="Test Button" onPress={mockOnPress} />,
      { wrapper: TestWrapper }
    );
    
    fireEvent.press(screen.getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
  
  // Accessibility tests
  it('has correct accessibility role', () => {
    render(
      <Button title="Test Button" onPress={() => {}} />,
      { wrapper: TestWrapper }
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeTruthy();
  });
  
  it('has correct accessibility label', () => {
    render(
      <Button 
        title="Test Button" 
        onPress={() => {}} 
        accessibilityLabel="Custom label" 
      />,
      { wrapper: TestWrapper }
    );
    
    expect(screen.getByLabelText('Custom label')).toBeTruthy();
  });
  
  it('is disabled when disabled prop is true', () => {
    const mockOnPress = jest.fn();
    render(
      <Button title="Test Button" onPress={mockOnPress} disabled />,
      { wrapper: TestWrapper }
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveAccessibilityState({ disabled: true });
    
    fireEvent.press(button);
    expect(mockOnPress).not.toHaveBeenCalled();
  });
  
  // Visual regression tests
  it('matches snapshot for primary variant', () => {
    const tree = render(
      <Button title="Primary Button" onPress={() => {}} variant="primary" />,
      { wrapper: TestWrapper }
    ).toJSON();
    
    expect(tree).toMatchSnapshot();
  });
  
  // Performance tests
  it('renders quickly with large dataset', () => {
    const startTime = performance.now();
    
    render(
      <Button title="Performance Test" onPress={() => {}} />,
      { wrapper: TestWrapper }
    );
    
    const endTime = performance.now();
    expect(endTime - startTime).toBeLessThan(100); // Should render in <100ms
  });
});
```

### Integration Testing Specifications

```typescript
// src/presentation/screens/__tests__/TransactionEntry.integration.test.tsx
import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { TransactionEntryScreen } from '../transactions/TransactionEntryScreen';
import { TestProviders } from '@/shared/testing/TestProviders';

describe('Transaction Entry Integration', () => {
  it('completes full transaction entry flow', async () => {
    render(<TransactionEntryScreen />, { wrapper: TestProviders });
    
    // Enter amount
    const amountInput = screen.getByLabelText(/amount input/i);
    fireEvent.changeText(amountInput, '25.50');
    
    // Enter description
    const descriptionInput = screen.getByPlaceholderText(/what was this for/i);
    fireEvent.changeText(descriptionInput, 'Lunch at cafe');
    
    // Select category (should auto-suggest Food)
    await waitFor(() => {
      expect(screen.getByText('Food')).toBeTruthy();
    });
    
    fireEvent.press(screen.getByText('Food'));
    
    // Submit transaction
    const submitButton = screen.getByText(/add transaction/i);
    fireEvent.press(submitButton);
    
    // Verify success
    await waitFor(() => {
      expect(screen.getByText(/transaction added/i)).toBeTruthy();
    });
  });
  
  it('handles SMS parsing flow', async () => {
    const mockSMSData = {
      amount: 45.00,
      description: 'Gas Station Purchase',
      confidence: 0.95,
    };
    
    render(
      <TransactionEntryScreen initialData={mockSMSData} />, 
      { wrapper: TestProviders }
    );
    
    // Verify SMS data is pre-filled
    expect(screen.getByDisplayValue('45.00')).toBeTruthy();
    expect(screen.getByDisplayValue('Gas Station Purchase')).toBeTruthy();
    
    // Should suggest Transportation category
    await waitFor(() => {
      expect(screen.getByText('Transportation')).toBeTruthy();
    });
  });
});
```

---

## Implementation Guidelines

### Component Development Checklist

- [ ] **TypeScript Interface**: Fully typed props with proper defaults
- [ ] **Accessibility**: WCAG AA compliant with proper ARIA labels
- [ ] **Responsive Design**: Works across device sizes (320px - 768px+)
- [ ] **Theme Integration**: Uses theme colors, typography, and spacing
- [ ] **Performance**: Optimized with React.memo where appropriate
- [ ] **Error Handling**: Graceful error states and user feedback
- [ ] **Testing**: Unit tests, accessibility tests, and integration tests
- [ ] **Documentation**: Clear JSDoc comments and usage examples

### File Structure
```
src/presentation/components/
├── core/                    # Base UI components
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── ProgressBar.tsx
│   └── SyncStatusIndicator.tsx
├── form/                    # Form-specific components
│   ├── CurrencyInput.tsx
│   ├── CategorySelector.tsx
│   └── DatePicker.tsx
├── budget/                  # Budget feature components
│   ├── BudgetProgressCard.tsx
│   └── BudgetOverview.tsx
├── transactions/            # Transaction feature components
│   ├── TransactionListItem.tsx
│   ├── TransactionEntryForm.tsx
│   └── SMSParsingFlow.tsx
└── __tests__/              # Component tests
    ├── core/
    ├── form/
    └── integration/
```

This comprehensive component specification provides your development team with everything needed to implement FinVibe's user interface consistently, accessibly, and efficiently. Each component includes detailed TypeScript interfaces, styling specifications, accessibility requirements, and testing guidelines.