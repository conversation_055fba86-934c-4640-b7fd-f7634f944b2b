# Enhanced Account Types Specification: Credit Cards & Loans

**Document Version:** 1.0  
**Created:** 2025-08-12  
**Purpose:** Technical specification for implementing comprehensive Credit Card and Loan account management in FinVibe  
**Target Audience:** Product Owner, Business Analyst, Development Team  

---

## Executive Summary

FinVibe currently handles all account types using a simple balance-based model that doesn't accurately represent the unique characteristics of Credit Cards and Loans. This specification outlines the technical requirements for implementing proper Credit Card balance management (credit limits, outstanding balances, payment tracking) and Loan account management (EMI calculations, principal/interest tracking, amortization schedules).

---

## Current State Analysis

### Existing Account Model Limitations

**Current Account Interface:**
```typescript
interface Account {
  id: number;
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'loan' | 'investment';
  balance: number;  // ❌ Inadequate for credit cards and loans
  currency: string;
  is_active: boolean;
  // ... other fields
}
```

**Problems Identified:**

1. **Credit Card Issues:**
   - Single `balance` field doesn't distinguish between available credit vs outstanding balance
   - No credit limit tracking
   - Cannot calculate credit utilization ratios
   - Payment due dates and minimum payments not tracked
   - SMS parsing treats credit card transactions like bank account debits/credits

2. **Loan Account Issues:**
   - No principal amount tracking
   - Missing interest rate and EMI amount information
   - Cannot track remaining tenure or payment schedules
   - No distinction between principal and interest payments
   - Balance calculation doesn't reflect loan-specific logic

3. **Transaction Processing Issues:**
   - All transactions use generic `income`/`expense`/`transfer` types
   - SMS parsing doesn't differentiate credit card charges vs payments
   - Balance updates don't consider account-specific calculation rules

---

## Proposed Technical Solution

### 1. Enhanced Account Data Model

#### Core Account Interface Extension
```typescript
interface Account {
  id: number;
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'loan' | 'investment';
  balance: number; // Keep for backward compatibility
  currency: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  sync_status: 'local' | 'synced' | 'pending' | 'conflict';
  
  // New field for account-specific metadata
  metadata: AccountMetadata;
}
```

#### Account Metadata Interface
```typescript
interface AccountMetadata {
  // Credit Card Specific Fields
  creditLimit?: number;
  outstandingBalance?: number;
  availableCredit?: number; // Calculated field: creditLimit - outstandingBalance
  minimumPaymentDue?: number;
  paymentDueDate?: string; // ISO date format
  lastStatementDate?: string;
  creditUtilization?: number; // Calculated: (outstandingBalance / creditLimit) * 100
  
  // Loan Specific Fields
  principalAmount?: number; // Original loan amount
  currentPrincipal?: number; // Remaining principal
  interestRate?: number; // Annual interest rate percentage
  emiAmount?: number; // Monthly EMI amount
  tenure?: number; // Total tenure in months
  remainingTenure?: number; // Remaining months
  nextEmiDate?: string; // Next EMI due date
  loanStartDate?: string; // Loan disbursement date
  totalInterestPaid?: number; // Interest paid till date
  
  // Common Fields
  lastUpdated?: string;
  autoCalculate?: boolean; // Whether to auto-calculate derived fields
}
```

### 2. Enhanced Transaction Types

#### New Transaction Types
```typescript
type TransactionType = 
  | 'income' 
  | 'expense' 
  | 'transfer'
  | 'credit_payment'      // Credit card payment
  | 'credit_charge'       // Credit card purchase/charge
  | 'credit_interest'     // Credit card interest charges
  | 'credit_fee'          // Credit card fees
  | 'loan_emi'           // Loan EMI payment
  | 'loan_interest'      // Interest component of payment
  | 'loan_principal'     // Principal component of payment
  | 'loan_fee'           // Loan processing fees
  | 'loan_prepayment';   // Loan prepayment
```

#### Enhanced Transaction Interface
```typescript
interface Transaction {
  // Existing fields...
  transaction_type: TransactionType;
  
  // New fields for enhanced tracking
  transaction_metadata?: TransactionMetadata;
}

interface TransactionMetadata {
  // Credit Card specific
  creditCardDetails?: {
    merchantName?: string;
    merchantCategory?: string;
    rewardPoints?: number;
    installmentInfo?: {
      isInstallment: boolean;
      installmentNumber?: number;
      totalInstallments?: number;
    };
  };
  
  // Loan specific
  loanDetails?: {
    principalComponent?: number;
    interestComponent?: number;
    feeComponent?: number;
    remainingPrincipal?: number;
    emiNumber?: number;
  };
  
  // SMS parsing metadata
  smsDetails?: {
    originalText?: string;
    extractedFields?: Record<string, any>;
    parsingConfidence?: number;
  };
}
```

### 3. Account-Specific Balance Calculation Services

#### Service Architecture
```typescript
abstract class BaseAccountCalculator {
  abstract calculateBalance(accountId: number, transactions: Transaction[]): Promise<AccountBalance>;
  abstract validateTransaction(transaction: Transaction, account: Account): ValidationResult;
}

interface AccountBalance {
  displayBalance: number;
  actualBalance: number;
  metadata: Record<string, any>;
}
```

#### Credit Card Calculator
```typescript
class CreditCardCalculator extends BaseAccountCalculator {
  calculateBalance(accountId: number, transactions: Transaction[]): Promise<AccountBalance> {
    // Logic:
    // 1. Start with credit limit
    // 2. Subtract all charges (credit_charge, credit_interest, credit_fee)
    // 3. Add all payments (credit_payment)
    // 4. Calculate available credit = creditLimit - outstandingBalance
    // 5. Return both outstanding balance and available credit
  }
  
  calculateCreditUtilization(outstandingBalance: number, creditLimit: number): number {
    return (outstandingBalance / creditLimit) * 100;
  }
  
  calculateMinimumPayment(outstandingBalance: number): number {
    // Typically 3-5% of outstanding balance or minimum amount
    return Math.max(outstandingBalance * 0.03, 500); // ₹500 minimum
  }
}
```

#### Loan Calculator
```typescript
class LoanCalculator extends BaseAccountCalculator {
  calculateBalance(accountId: number, transactions: Transaction[]): Promise<AccountBalance> {
    // Logic:
    // 1. Start with original principal amount
    // 2. Subtract all principal payments
    // 3. Calculate remaining tenure based on payments made
    // 4. Return remaining principal as balance
  }
  
  calculateEMI(principal: number, rate: number, tenure: number): number {
    const monthlyRate = rate / 12 / 100;
    const emi = (principal * monthlyRate * Math.pow(1 + monthlyRate, tenure)) / 
                (Math.pow(1 + monthlyRate, tenure) - 1);
    return Math.round(emi * 100) / 100;
  }
  
  generateAmortizationSchedule(
    principal: number, 
    rate: number, 
    tenure: number
  ): AmortizationSchedule[] {
    // Generate month-wise principal and interest breakdown
  }
  
  splitEMIComponents(
    emiAmount: number,
    remainingPrincipal: number,
    interestRate: number
  ): { principal: number; interest: number } {
    const interestComponent = (remainingPrincipal * interestRate) / 12 / 100;
    const principalComponent = emiAmount - interestComponent;
    return { principal: principalComponent, interest: interestComponent };
  }
}
```

### 4. Enhanced SMS Parsing

#### Credit Card SMS Patterns
```typescript
const creditCardPatterns = {
  purchase: {
    patterns: [
      /(?:spent|charged|purchase).*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:transaction|txn).*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'credit_charge'
  },
  
  payment: {
    patterns: [
      /(?:payment|paid).*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i,
      /credit.*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'credit_payment'
  },
  
  availableCredit: {
    patterns: [
      /available.*?credit.*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i,
      /limit.*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i
    ]
  }
};
```

#### Loan SMS Patterns
```typescript
const loanEMIPatterns = {
  emiDeducted: {
    patterns: [
      /EMI.*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i,
      /loan.*?installment.*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'loan_emi'
  },
  
  outstandingBalance: {
    patterns: [
      /outstanding.*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i,
      /balance.*?Rs\.?\s*([0-9,]+(?:\.\d{2})?)/i
    ]
  }
};
```

### 5. Database Schema Changes

#### Account Metadata Table
```sql
CREATE TABLE account_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER NOT NULL,
    credit_limit DECIMAL(15,2),
    outstanding_balance DECIMAL(15,2),
    minimum_payment_due DECIMAL(15,2),
    payment_due_date TEXT,
    principal_amount DECIMAL(15,2),
    current_principal DECIMAL(15,2),
    interest_rate DECIMAL(5,2),
    emi_amount DECIMAL(15,2),
    tenure INTEGER,
    remaining_tenure INTEGER,
    next_emi_date TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE
);
```

#### Transaction Metadata Table
```sql
CREATE TABLE transaction_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_id INTEGER NOT NULL,
    principal_component DECIMAL(15,2),
    interest_component DECIMAL(15,2),
    fee_component DECIMAL(15,2),
    merchant_name TEXT,
    merchant_category TEXT,
    reward_points INTEGER,
    installment_info TEXT, -- JSON string
    sms_parsing_data TEXT, -- JSON string
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE
);
```

---

## Implementation Phases

### Phase 1: Foundation (Sprint 1-2)
**Scope:** Core data model and database changes
- [ ] Extend Account interface with metadata field
- [ ] Create AccountMetadata interface and implementation
- [ ] Database migration for account_metadata table
- [ ] Update AccountRepository to handle metadata
- [ ] Create account calculator service architecture
- [ ] Basic unit tests for new interfaces

**Deliverables:**
- Updated Account and Transaction type definitions
- Database migration scripts
- Basic AccountCalculator service structure

### Phase 2: Account-Specific Logic (Sprint 3-4)
**Scope:** Implement credit card and loan calculation logic
- [ ] Implement CreditCardCalculator service
- [ ] Implement LoanCalculator service
- [ ] Update AccountService to use appropriate calculators
- [ ] Enhance transaction validation for different account types
- [ ] Create EMI calculation and amortization utilities

**Deliverables:**
- Working credit card balance calculation
- Loan EMI and principal tracking
- Account-specific transaction validation

### Phase 3: Enhanced Transaction Processing (Sprint 5-6)
**Scope:** New transaction types and SMS parsing
- [ ] Add new transaction types (credit_payment, loan_emi, etc.)
- [ ] Update SMS parsing for credit card and loan patterns
- [ ] Transaction metadata handling
- [ ] Enhanced transaction creation flow
- [ ] Backward compatibility testing

**Deliverables:**
- Enhanced SMS parsing for all account types
- New transaction type processing
- Transaction metadata storage and retrieval

### Phase 4: User Interface Components (Sprint 7-8)
**Scope:** Account-specific UI components
- [ ] Credit card account view with available credit display
- [ ] Loan account view with EMI schedule
- [ ] Payment due notifications and alerts
- [ ] Credit utilization visualization
- [ ] Loan payoff calculator
- [ ] Account-specific transaction categorization in UI

**Deliverables:**
- Specialized account detail screens
- Payment tracking and alert system
- Financial health indicators

### Phase 5: Analytics and Reporting (Sprint 9-10)
**Scope:** Advanced features and insights
- [ ] Credit utilization trend analysis
- [ ] Loan amortization schedule display
- [ ] Payment history and patterns
- [ ] Financial health scoring for credit accounts
- [ ] EMI vs irregular payment comparison for loans
- [ ] Export capabilities for statements and reports

**Deliverables:**
- Credit card statement generation
- Loan payment history and projections
- Financial health insights dashboard

---

## User Experience Enhancements

### Credit Card Account View
```
┌─────────────────────────────────────┐
│ HDFC Credit Card                    │
├─────────────────────────────────────┤
│ Available Credit    ₹45,000         │
│ Credit Limit       ₹50,000          │
│ Outstanding        ₹5,000           │
│ Utilization        10%              │
├─────────────────────────────────────┤
│ Payment Due        ₹1,500           │
│ Due Date          15 Sep 2025       │
│ [Pay Now] [Set Reminder]            │
└─────────────────────────────────────┘
```

### Loan Account View
```
┌─────────────────────────────────────┐
│ Home Loan - SBI                     │
├─────────────────────────────────────┤
│ Outstanding        ₹18,50,000       │
│ Original Amount    ₹20,00,000       │
│ EMI Amount         ₹15,000          │
│ Next EMI          15 Sep 2025       │
├─────────────────────────────────────┤
│ Remaining Tenure   15 years 3 months│
│ Interest Rate      8.5% p.a.        │
│ [View Schedule] [Prepayment Calc]   │
└─────────────────────────────────────┘
```

### Smart Notifications
- **Credit Cards:** Payment due reminders, high utilization alerts, unusual spending patterns
- **Loans:** EMI due reminders, interest rate change notifications, prepayment opportunities

---

## Technical Requirements

### Performance Requirements
- Account balance calculations: < 200ms for up to 1000 transactions
- EMI schedule generation: < 500ms for 20-year loans
- SMS parsing with new patterns: < 100ms per message
- Database queries with metadata joins: < 300ms

### Security Requirements
- Encrypt sensitive financial data (credit limits, loan amounts) using AES-256
- Audit trail for all balance calculations and adjustments
- Input validation for all financial amounts and dates
- Rate limiting for SMS processing to prevent abuse

### Compatibility Requirements
- Maintain backward compatibility with existing account data
- Graceful degradation for accounts without metadata
- Support for data migration without service interruption
- API versioning for mobile app updates

---

## Risk Assessment

### High Risk
- **Data Migration Complexity:** Converting existing credit and loan accounts to new structure
  - *Mitigation:* Comprehensive migration scripts with rollback capability
- **Balance Calculation Accuracy:** Errors in financial calculations affect user trust
  - *Mitigation:* Extensive unit testing, parallel calculation validation

### Medium Risk
- **SMS Parsing Accuracy:** New patterns might not cover all bank formats
  - *Mitigation:* Iterative pattern improvement, fallback to manual categorization
- **Performance Impact:** Additional calculations and database joins
  - *Mitigation:* Database indexing, query optimization, caching strategies

### Low Risk
- **User Interface Complexity:** More detailed views might confuse users
  - *Mitigation:* Progressive disclosure, user testing, help documentation

---

## Success Metrics

### Technical Metrics
- [ ] 99.9% accuracy in balance calculations for credit cards and loans
- [ ] <300ms response time for account balance retrieval
- [ ] Zero data loss during migration
- [ ] 95% SMS parsing accuracy for credit card and loan transactions

### User Experience Metrics
- [ ] 90% user satisfaction with new account views
- [ ] 50% reduction in manual transaction categorization
- [ ] 80% adoption of payment reminder features
- [ ] 30% increase in app engagement for credit card and loan users

### Business Metrics
- [ ] 25% increase in premium feature utilization
- [ ] Improved user retention for users with credit/loan accounts
- [ ] Reduced customer support queries about balance calculations

---

## Acceptance Criteria Template

### Credit Card Account Management
```gherkin
Feature: Credit Card Balance Management
  As a credit card user
  I want to see my available credit and outstanding balance
  So that I can manage my spending and payments effectively

Scenario: Display credit card balance information
  Given I have a credit card account with ₹50,000 limit
  And I have spent ₹5,000 this month
  When I view my credit card account
  Then I should see available credit as ₹45,000
  And outstanding balance as ₹5,000
  And credit utilization as 10%

Scenario: Process credit card payment
  Given I have an outstanding balance of ₹5,000
  When I make a payment of ₹2,000
  Then my outstanding balance should be ₹3,000
  And my available credit should be ₹47,000
  And the transaction should be categorized as "credit_payment"
```

### Loan Account Management
```gherkin
Feature: Loan EMI Tracking
  As a loan borrower
  I want to track my EMI payments and remaining balance
  So that I can plan my finances and see loan progress

Scenario: Display loan account information
  Given I have a home loan of ₹20,00,000 at 8.5% for 20 years
  And I have paid 12 EMIs of ₹15,000 each
  When I view my loan account
  Then I should see remaining principal balance
  And remaining tenure as 19 years
  And next EMI date
  And EMI breakdown of principal vs interest

Scenario: Process EMI payment
  Given my next EMI is due on 15th September
  When the EMI of ₹15,000 is deducted
  Then the transaction should split into principal and interest components
  And my remaining principal should be reduced accordingly
  And next EMI date should update to 15th October
```

---

## Conclusion

This specification provides a comprehensive foundation for implementing proper Credit Card and Loan account management in FinVibe. The phased approach ensures manageable development cycles while delivering incremental value to users.

The enhanced account types will significantly improve the user experience by providing accurate financial tracking, meaningful insights, and proactive alerts for credit cards and loans. This positions FinVibe as a comprehensive personal finance management solution that understands the nuances of different financial instruments.

**Next Steps:**
1. Product Owner to prioritize features and create detailed user stories
2. Business Analyst to define acceptance criteria for each phase
3. Development Team to estimate effort and plan sprints
4. QA Team to design test scenarios for financial calculations
5. UI/UX Team to create wireframes for enhanced account views

---

**Document Status:** Ready for Story Creation  
**Review Required:** Product Owner, Technical Lead, QA Lead  
**Dependencies:** None  
**Estimated Effort:** 10 sprints (5 months)  