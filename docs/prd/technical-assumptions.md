# Technical Assumptions

## Repository Structure: Monorepo

Single repository containing mobile app, serverless cloud functions, and shared utilities. This approach supports the solo/small team development model while maintaining code consistency across minimal backend services and cross-platform mobile application.

## Service Architecture

**Local-First Hybrid Architecture**: Core functionality operates entirely offline using local SQLite storage, with optional serverless functions for anonymous SMS pattern learning and premium cloud sync. The architecture prioritizes zero marginal cost for free users while enabling scalable premium features through encrypted cloud storage (Supabase/similar).

## Testing Requirements

**Unit + Integration Testing**: Comprehensive testing pyramid including unit tests for business logic, integration tests for local database operations, and automated testing for SMS parsing accuracy across different bank formats. Manual testing procedures for device performance validation on budget hardware (Android 8+, 2GB RAM devices).

## Additional Technical Assumptions and Requests

**Cross-Platform Framework**: React Native preferred for JavaScript ecosystem familiarity and mature SQLite integration, with Flutter as alternative for superior performance on budget devices

**Local Database**: SQLCipher (encrypted SQLite) for secure on-device storage with transaction search optimization and backup/restore capabilities

**State Management**: Redux Toolkit or Zustand for predictable offline-first state management with persistence layer

**SMS Parsing Backend**: Serverless functions (Vercel/Netlify) for anonymous pattern learning API, avoiding dedicated server maintenance

**Authentication Strategy**: Device-based authentication for free users, OAuth integration for premium cloud sync users

**Performance Optimization**: Database indexing strategy for 10,000+ transactions, lazy loading for transaction history, image/attachment optimization

**Privacy Architecture**: Zero-knowledge design where backend services cannot decrypt user financial data, anonymous telemetry only for feature usage (no personal data)

**Development Tools**: React Native CLI or Expo managed workflow, Jest for testing, ESLint/Prettier for code quality, GitHub Actions for CI/CD pipeline

**Deployment Strategy**: App Store/Play Store distribution with staged rollout capabilities, feature flags for A/B testing freemium conversion flows

**Cloud Sync Infrastructure**: Supabase individual user accounts with Row Level Security for premium encrypted data sync
