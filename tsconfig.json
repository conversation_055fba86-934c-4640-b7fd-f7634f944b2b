{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitOverride": true, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "alwaysStrict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/presentation/*": ["src/presentation/*"], "@/business/*": ["src/business/*"], "@/data/*": ["src/data/*"], "@/platform/*": ["src/platform/*"], "@/shared/*": ["src/shared/*"]}}, "include": ["**/*.ts", "**/*.tsx", "src/types/global.d.ts"]}