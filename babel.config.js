module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./src'],
          extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
          alias: {
            '@': './src',
            '@/presentation': './src/presentation',
            '@/business': './src/business', 
            '@/data': './src/data',
            '@/platform': './src/platform',
            '@/shared': './src/shared'
          }
        }
      ]
    ]
  };
};