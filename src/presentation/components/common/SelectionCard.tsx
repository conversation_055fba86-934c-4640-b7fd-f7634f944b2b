import React from 'react';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { MaterialIcon } from './MaterialIcon';

interface SelectionCardProps {
  children: React.ReactNode;
  isSelected: boolean;
  onPress: () => void;
  accentColor?: string;
  disabled?: boolean;
  style?: object;
  testID?: string;
}

export const SelectionCard: React.FC<SelectionCardProps> = ({
  children,
  isSelected,
  onPress,
  accentColor,
  disabled = false,
  style,
  testID,
}) => {
  const theme = useTheme();
  
  const dynamicStyles = StyleSheet.create({
    container: {
      borderWidth: 2,
      borderColor: isSelected 
        ? accentColor || theme.colors.primary 
        : theme.colors.border,
      borderRadius: 12,
      backgroundColor: isSelected 
        ? theme.colors.surface 
        : theme.colors.card,
      ...theme.shadows.sm,
      opacity: disabled ? 0.6 : 1,
    },
    selectedOverlay: {
      position: 'absolute',
      top: 8,
      right: 8,
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: accentColor || theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      ...theme.shadows.sm,
    },
    checkmark: {
      color: theme.colors.background,
      fontSize: 16,
      fontWeight: 'bold',
    },
  });

  return (
    <TouchableOpacity
      style={[dynamicStyles.container, style]}
      onPress={onPress}
      disabled={disabled}
      testID={testID}
      activeOpacity={0.7}
    >
      {children}
      {isSelected && (
        <View style={dynamicStyles.selectedOverlay}>
          <Text style={dynamicStyles.checkmark}>✓</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

interface AccountTypeCardProps {
  icon: string;
  label: string;
  description: string;
  color: string;
  isSelected: boolean;
  onPress: () => void;
}

export const AccountTypeCard: React.FC<AccountTypeCardProps> = ({
  icon,
  label,
  description,
  color,
  isSelected,
  onPress,
}) => {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    content: {
      padding: 16,
      alignItems: 'center',
      minHeight: 120,
      flex: 1,
    },

    label: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 4,
    },
    description: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 16,
    },
  });

  return (
    <SelectionCard
      isSelected={isSelected}
      onPress={onPress}
      accentColor={color}
      style={{ flex: 1, minWidth: '45%', margin: 4 }}
    >
      <View style={styles.content}>
        <MaterialIcon name={icon} size={28} color={color} style={{ marginBottom: 8 }} />
        <Text style={styles.label}>{label}</Text>
        <Text style={styles.description}>{description}</Text>
      </View>
    </SelectionCard>
  );
};

interface CategoryCardProps {
  icon: string;
  name: string;
  subcategory?: string;
  color: string;
  isSelected: boolean;
  onPress: () => void;
}

export const CategoryCard: React.FC<CategoryCardProps> = ({
  icon,
  name,
  subcategory,
  color,
  isSelected,
  onPress,
}) => {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    content: {
      padding: 12,
      alignItems: 'center',
      minWidth: 90,
    },
    icon: {
      fontSize: 24,
      marginBottom: 6,
    },
    name: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.colors.text,
      textAlign: 'center',
      lineHeight: 14,
    },
    subcategory: {
      fontSize: 10,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: 2,
    },
  });

  return (
    <SelectionCard
      isSelected={isSelected}
      onPress={onPress}
      accentColor={color}
      style={{ margin: 4 }}
    >
      <View style={styles.content}>
        <Text style={styles.icon}>{icon}</Text>
        <Text style={styles.name}>{name}</Text>
        {subcategory && <Text style={styles.subcategory}>{subcategory}</Text>}
      </View>
    </SelectionCard>
  );
};

interface AccountCardProps {
  name: string;
  balance: number;
  currency?: string;
  isSelected: boolean;
  onPress: () => void;
}

export const AccountCard: React.FC<AccountCardProps> = ({
  name,
  balance,
  currency = '₹',
  isSelected,
  onPress,
}) => {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
    },
    accountInfo: {
      flex: 1,
    },
    accountName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    accountBalance: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
  });

  return (
    <SelectionCard
      isSelected={isSelected}
      onPress={onPress}
      style={{ marginVertical: 4 }}
    >
      <View style={styles.content}>
        <View style={styles.accountInfo}>
          <Text style={styles.accountName}>{name}</Text>
          <Text style={styles.accountBalance}>
            {currency}{balance.toFixed(2)}
          </Text>
        </View>
      </View>
    </SelectionCard>
  );
};