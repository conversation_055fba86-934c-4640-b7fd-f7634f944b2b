import React from 'react';
import { getIconComponent } from '@/shared/utils/unifiedIconMapping';

interface MaterialIconProps {
  name: string | null | undefined;
  size?: number;
  color?: string;
  style?: any;
}

/**
 * Universal Icon component that uses the unified icon mapping system
 * Supports all icon families (MaterialIcons, Ionicons, FontAwesome5, Feather)
 * Handles fallbacks gracefully for invalid icon names
 */
export const MaterialIcon: React.FC<MaterialIconProps> = ({
  name,
  size = 24,
  color = '#000000',
  style,
}) => {
  const iconComponent = getIconComponent(name);
  const IconComponent = iconComponent.component;
  
  return (
    <IconComponent
      name={iconComponent.name as any}
      size={size}
      color={color}
      style={style}
    />
  );
};