import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';
import { FallbackIcon } from './FallbackIcon';

export interface SelectorOption {
  id: string | number;
  label: string;
  description?: string;
  icon?: string;
  color?: string;
  disabled?: boolean;
}

interface FormSelectorProps {
  label?: string;
  error?: string;
  required?: boolean;
  placeholder?: string;
  options: SelectorOption[];
  selectedId?: string | number | null;
  onSelect: (id: string | number) => void;
  onCreateNew?: () => void;
  createNewLabel?: string;
  containerStyle?: any;
  labelStyle?: any;
  selectorStyle?: any;
  errorStyle?: any;
  showIcons?: boolean;
  disabled?: boolean;
}

/**
 * Reusable Selector component for dropdowns with consistent styling
 * Used throughout the application for all selection inputs
 */
export const FormSelector: React.FC<FormSelectorProps> = ({
  label,
  error,
  required = false,
  placeholder = 'Select an option',
  options,
  selectedId,
  onSelect,
  onCreateNew,
  createNewLabel = 'Create New',
  containerStyle,
  labelStyle,
  selectorStyle,
  errorStyle,
  showIcons = false,
  disabled = false,
}) => {
  const theme = useTheme();
  const [showModal, setShowModal] = useState(false);
  
  const selectedOption = options.find(option => option.id === selectedId);
  const styles = createFormSelectorStyles(theme);

  const handleSelect = (id: string | number) => {
    onSelect(id);
    setShowModal(false);
  };

  const handleCreateNew = () => {
    setShowModal(false);
    onCreateNew?.();
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.selector,
          error ? styles.selectorError : null,
          selectedOption ? { borderColor: selectedOption.color } : null,
          selectedOption ? { backgroundColor: selectedOption.color ? `${selectedOption.color}10` : theme.colors.card } : null,
          disabled && styles.selectorDisabled,
          selectorStyle
        ]}
        onPress={() => !disabled && setShowModal(true)}
        disabled={disabled}
      >
        {selectedOption ? (
          <>
            {showIcons && selectedOption.icon && (
              <View style={styles.iconContainer}>
                <FallbackIcon 
                  icon={selectedOption.icon}
                  name={selectedOption.label}
                  size={20}
                  style={{ color: selectedOption.color || theme.colors.text }}
                />
              </View>
            )}
            <Text style={[styles.selectedText, { color: theme.colors.text }]}>
              {selectedOption.label}
            </Text>
          </>
        ) : (
          <Text style={[styles.placeholderText, { color: theme.colors.textSecondary }]}>
            {placeholder}
          </Text>
        )}
        <Text style={[styles.chevron, { color: theme.colors.textSecondary }]}>⌄</Text>
      </TouchableOpacity>
      
      {error && (
        <Text style={[styles.errorText, errorStyle]}>
          {error}
        </Text>
      )}

      {/* Selection Modal */}
      <Modal
        visible={showModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1}
          onPress={() => setShowModal(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {label || 'Select Option'}
              </Text>
            </View>
            
            <ScrollView style={styles.optionsList} showsVerticalScrollIndicator={false}>
              {options.map((option) => {
                const isSelected = selectedId === option.id;
                return (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.optionItem,
                      isSelected && {
                        borderColor: option.color || theme.colors.primary,
                        backgroundColor: option.color ? `${option.color}10` : theme.colors.surface,
                      },
                      option.disabled && styles.optionDisabled
                    ]}
                    onPress={() => !option.disabled && handleSelect(option.id)}
                    disabled={option.disabled}
                  >
                    {showIcons && option.icon && (
                      <View style={styles.optionIconContainer}>
                        <FallbackIcon 
                          icon={option.icon}
                          name={option.label}
                          size={24}
                          style={{ color: option.color || theme.colors.text }}
                        />
                      </View>
                    )}
                    
                    <View style={styles.optionContent}>
                      <Text style={[
                        styles.optionLabel,
                        { color: option.disabled ? theme.colors.textDisabled : theme.colors.text }
                      ]}>
                        {option.label}
                      </Text>
                      {option.description && (
                        <Text style={[styles.optionDescription, { color: theme.colors.textSecondary }]}>
                          {option.description}
                        </Text>
                      )}
                    </View>
                    
                    {isSelected && (
                      <View style={[
                        styles.checkmarkContainer, 
                        { backgroundColor: option.color || theme.colors.primary }
                      ]}>
                        <Text style={styles.checkmark}>✓</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                );
              })}
              
              {/* Create New Option */}
              {onCreateNew && (
                <TouchableOpacity
                  style={[styles.optionItem, styles.createNewOption]}
                  onPress={handleCreateNew}
                >
                  <View style={[styles.createIcon, { backgroundColor: theme.colors.primary }]}>
                    <Text style={styles.createIconText}>+</Text>
                  </View>
                  <View style={styles.optionContent}>
                    <Text style={[styles.optionLabel, { color: theme.colors.primary }]}>
                      {createNewLabel}
                    </Text>
                    <Text style={[styles.optionDescription, { color: theme.colors.textSecondary }]}>
                      Add a new option
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const createFormSelectorStyles = createStyles((theme) => ({
  container: {
    marginBottom: theme.spacing.md,
  },
  label: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  required: {
    color: theme.colors.error,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md + 4, // Extra padding to match FormInput
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.card,
    minHeight: 48, // Consistent with FormInput
  },
  selectorError: {
    borderColor: theme.colors.error,
  },
  selectorDisabled: {
    opacity: 0.6,
    backgroundColor: theme.colors.surface,
  },
  iconContainer: {
    marginRight: theme.spacing.sm,
  },
  selectedText: {
    flex: 1,
    ...theme.typography.body,
    fontWeight: '600',
  },
  placeholderText: {
    flex: 1,
    ...theme.typography.body,
  },
  chevron: {
    fontSize: 16,
  },
  errorText: {
    color: theme.colors.error,
    ...theme.typography.caption,
    marginTop: theme.spacing.xs,
  },
  
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  optionsList: {
    padding: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.card,
  },
  optionDisabled: {
    opacity: 0.5,
  },
  optionIconContainer: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  optionContent: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  checkmarkContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  checkmark: {
    fontSize: 16,
    color: theme.colors.background,
    fontWeight: 'bold',
  },
  createNewOption: {
    borderStyle: 'dashed',
    borderColor: theme.colors.primary,
  },
  createIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  createIconText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.background,
  },
}));