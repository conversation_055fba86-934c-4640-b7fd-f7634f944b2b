import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { getIconDisplay } from '@/shared/utils/iconUtils';
import { getIconComponent } from '@/shared/utils/unifiedIconMapping';

interface FallbackIconProps {
  icon?: string | null | undefined;
  name: string;
  size?: number;
  style?: any;
}

export const FallbackIcon: React.FC<FallbackIconProps> = ({ 
  icon, 
  name, 
  size = 24,
  style 
}) => {
  const iconDisplay = getIconDisplay(icon, name);
  
  const styles = StyleSheet.create({
    emojiIcon: {
      fontSize: size,
      textAlign: 'center',
      ...style,
    },
    fallbackContainer: {
      width: size,
      height: size,
      borderRadius: size / 2, // Makes it perfectly round
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: iconDisplay.type === 'fallback' ? iconDisplay.backgroundColor : 'transparent',
      ...style,
    },
    fallbackText: {
      fontSize: size * 0.6, // 60% of container size for good proportion
      fontWeight: 'bold',
      color: iconDisplay.type === 'fallback' ? iconDisplay.textColor : 'inherit',
      textAlign: 'center',
    },
  });

  if (iconDisplay.type === 'emoji') {
    return (
      <Text style={styles.emojiIcon}>
        {iconDisplay.emoji}
      </Text>
    );
  }

  if (iconDisplay.type === 'unified_icon') {
    const iconComponent = getIconComponent(iconDisplay.iconName);
    const IconComponent = iconComponent.component;
    
    // Extract color from style object or use default
    let iconColor = '#666';
    if (style?.color) {
      iconColor = style.color;
    } else if (typeof style === 'object' && style !== null) {
      // Check if style has color as a direct property
      const flatStyle = StyleSheet.flatten(style);
      if (flatStyle?.color) {
        iconColor = flatStyle.color;
      }
    }
    
    return (
      <IconComponent 
        name={iconComponent.name as any}
        size={size}
        color={iconColor}
      />
    );
  }

  return (
    <View style={styles.fallbackContainer}>
      <Text style={styles.fallbackText}>
        {iconDisplay.letter}
      </Text>
    </View>
  );
};