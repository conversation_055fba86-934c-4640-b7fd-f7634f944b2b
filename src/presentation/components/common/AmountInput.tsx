import React from 'react';
import {
  View,
  Text,
  TextInput,
  TextInputProps,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';

interface AmountInputProps extends Omit<TextInputProps, 'keyboardType'> {
  label?: string;
  error?: string;
  required?: boolean;
  currency?: string;
  amountType?: 'income' | 'expense' | 'neutral';
  containerStyle?: any;
  labelStyle?: any;
  inputStyle?: any;
  errorStyle?: any;
}

/**
 * Reusable AmountInput component with currency symbol and proper alignment
 * Used for all monetary value inputs throughout the application
 */
export const AmountInput: React.FC<AmountInputProps> = ({
  label,
  error,
  required = false,
  currency = '₹',
  amountType = 'neutral',
  containerStyle,
  labelStyle,
  inputStyle,
  errorStyle,
  ...textInputProps
}) => {
  const theme = useTheme();
  const styles = createAmountInputStyles(theme);

  const getCurrencyColor = () => {
    switch (amountType) {
      case 'income':
        return theme.colors.success;
      case 'expense':
        return theme.colors.error;
      default:
        return theme.colors.primary;
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <View style={[
        styles.amountContainer, 
        error ? styles.inputError : null
      ]}>
        <Text style={[
          styles.currencySymbol, 
          { color: getCurrencyColor() }
        ]}>
          {currency}
        </Text>
        <TextInput
          style={[styles.amountInput, inputStyle]}
          placeholderTextColor={theme.colors.textDisabled}
          keyboardType="decimal-pad"
          {...textInputProps}
        />
      </View>
      
      {error && (
        <Text style={[styles.errorText, errorStyle]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const createAmountInputStyles = createStyles((theme) => ({
  container: {
    marginBottom: theme.spacing.md,
  },
  label: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  required: {
    color: theme.colors.error,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.card,
    minHeight: 48, // Standard input height
  },
  currencySymbol: {
    ...theme.typography.h3,
    fontWeight: 'bold',
    paddingLeft: theme.spacing.md,
    paddingRight: theme.spacing.xs,
    paddingTop: theme.spacing.md,
    paddingBottom: theme.spacing.md,
    lineHeight: undefined, // Remove lineHeight to use default baseline
    includeFontPadding: false, // Android specific - removes extra padding
  },
  amountInput: {
    flex: 1,
    paddingVertical: theme.spacing.md, // Standard padding
    paddingRight: theme.spacing.md,
    paddingLeft: 0,
    ...theme.typography.h3,
    fontWeight: '600',
    borderWidth: 0,
    color: theme.colors.text,
    lineHeight: undefined, // Remove lineHeight to use default baseline
    includeFontPadding: false, // Android specific - removes extra padding
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  errorText: {
    color: theme.colors.error,
    ...theme.typography.caption,
    marginTop: theme.spacing.xs,
  },
}));