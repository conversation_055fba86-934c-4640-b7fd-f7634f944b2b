import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Pressable,
} from 'react-native';
import { useTheme } from '../../../shared/theme/ThemeProvider';
import { FallbackIcon } from './FallbackIcon';

interface MenuItem {
  id: string;
  title: string;
  icon: string;
  onPress: () => void;
  badge?: string;
  disabled?: boolean;
}

interface SideMenuProps {
  visible: boolean;
  onClose: () => void;
  menuItems: MenuItem[];
}

export const SideMenu: React.FC<SideMenuProps> = ({
  visible,
  onClose,
  menuItems,
}) => {
  const theme = useTheme();

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        {/* Backdrop */}
        <Pressable style={styles.backdrop} onPress={onClose} />
        
        {/* Menu Content */}
        <View style={[styles.menuContainer, { backgroundColor: theme.colors.background }]}>
          <SafeAreaView style={styles.safeArea}>
            {/* Header */}
            <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
              <View style={styles.headerContent}>
                <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
                  Menu
                </Text>
                <TouchableOpacity
                  onPress={onClose}
                  style={[styles.closeButton, { backgroundColor: theme.colors.surface }]}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <FallbackIcon
                    icon="close"
                    name="Close"
                    size={20}
                    style={{ color: theme.colors.textSecondary }}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Menu Items */}
            <ScrollView style={styles.menuContent} showsVerticalScrollIndicator={false}>
              {menuItems.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[
                    styles.menuItem,
                    { borderBottomColor: theme.colors.border },
                    item.disabled && styles.disabledItem,
                  ]}
                  onPress={() => {
                    if (!item.disabled) {
                      item.onPress();
                      onClose();
                    }
                  }}
                  disabled={item.disabled}
                >
                  <View style={styles.menuItemContent}>
                    <View style={styles.menuItemLeft}>
                      <View style={[styles.iconContainer, { backgroundColor: theme.colors.surface }]}>
                        <FallbackIcon
                          icon={item.icon}
                          name={item.title}
                          size={20}
                          style={{ 
                            color: item.disabled ? theme.colors.textSecondary : theme.colors.primary 
                          }}
                        />
                      </View>
                      <Text
                        style={[
                          styles.menuItemTitle,
                          { 
                            color: item.disabled ? theme.colors.textSecondary : theme.colors.text 
                          },
                        ]}
                      >
                        {item.title}
                      </Text>
                    </View>
                    
                    <View style={styles.menuItemRight}>
                      {item.badge && (
                        <View style={[styles.badge, { backgroundColor: theme.colors.primary }]}>
                          <Text style={[styles.badgeText, { color: theme.colors.background }]}>
                            {item.badge}
                          </Text>
                        </View>
                      )}
                      <FallbackIcon
                        icon="chevron-right"
                        name="Navigate"
                        size={16}
                        style={{ 
                          color: item.disabled ? theme.colors.textSecondary : theme.colors.textSecondary,
                          opacity: 0.5 
                        }}
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Footer */}
            <View style={[styles.footer, { borderTopColor: theme.colors.border }]}>
              <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
                FinVibe v1.0.0 • Local First Finance
              </Text>
            </View>
          </SafeAreaView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    flexDirection: 'row',
  },
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  menuContainer: {
    width: 320,
    flex: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: -2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuContent: {
    flex: 1,
    paddingTop: 8,
  },
  menuItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  disabledItem: {
    opacity: 0.6,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 8,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  footer: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
  },
});