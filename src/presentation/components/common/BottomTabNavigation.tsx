import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Platform,
} from 'react-native';
import { useTheme } from '../../../shared/theme/ThemeProvider';
import { FallbackIcon } from './FallbackIcon';

export type TabScreen = 'dashboard' | 'accounts' | 'add' | 'transactions' | 'menu' | 'feedback';

interface Tab {
  id: TabScreen;
  icon: string;
  label: string;
  isCenter?: boolean;
}

interface BottomTabNavigationProps {
  activeTab: TabScreen;
  onTabPress: (tab: TabScreen) => void;
}

export const BottomTabNavigation: React.FC<BottomTabNavigationProps> = ({
  activeTab,
  onTabPress,
}) => {
  const theme = useTheme();

  const tabs: Tab[] = [
    {
      id: 'dashboard',
      icon: 'home',
      label: 'Home',
    },
    {
      id: 'accounts',
      icon: 'credit-card',
      label: 'Accounts',
    },
    {
      id: 'add',
      icon: 'plus',
      label: 'Add',
      isCenter: true,
    },
    {
      id: 'transactions',
      icon: 'list',
      label: 'Transactions',
    },
    {
      id: 'menu',
      icon: 'user',
      label: 'Menu',
    },
  ];

  const getTabStyle = (tab: Tab) => {
    const isActive = activeTab === tab.id;
    const isCenter = tab.isCenter;

    if (isCenter) {
      return [
        styles.centerTab,
        {
          backgroundColor: theme.colors.primary,
          shadowColor: theme.colors.primary,
        },
      ];
    }

    return [
      styles.tab,
      isActive && { 
        backgroundColor: `${theme.colors.primary}12`,
        transform: [{ scale: 1.05 }],
      },
    ];
  };

  const getIconStyle = (tab: Tab) => {
    const isActive = activeTab === tab.id;
    const isCenter = tab.isCenter;

    if (isCenter) {
      return { color: '#FFFFFF' }; // Pure white for + icon as shown in reference
    }

    return {
      color: isActive ? theme.colors.primary : theme.colors.textSecondary,
    };
  };


  return (
    <View style={[styles.container, { 
      backgroundColor: theme.colors.background,
      borderTopColor: theme.colors.border,
    }]}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.id}
          style={getTabStyle(tab)}
          onPress={() => onTabPress(tab.id)}
          activeOpacity={0.7}
          accessibilityLabel={tab.label}
          accessibilityRole="button"
          accessibilityHint={`Navigate to ${tab.label} screen`}
        >
          <View style={styles.tabContent}>
            <FallbackIcon
              icon={tab.icon}
              name={tab.label}
              size={tab.isCenter ? 24 : 22}
              style={getIconStyle(tab)}
            />
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: Platform.OS === 'ios' ? 70 : 60,
    paddingBottom: Platform.OS === 'ios' ? 20 : 8,
    paddingTop: 8,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    minHeight: 48,
    minWidth: 48,
  },
  centerTab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 24,
    minHeight: 48,
    minWidth: 48,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});