import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';

interface DateTimePickerProps {
  label?: string;
  error?: string;
  required?: boolean;
  value: Date;
  onChange: (date: Date) => void;
  mode?: 'date' | 'time' | 'datetime';
  minimumDate?: Date;
  maximumDate?: Date;
  disabled?: boolean;
  containerStyle?: any;
  labelStyle?: any;
  pickerStyle?: any;
  errorStyle?: any;
  format?: 'short' | 'long' | 'custom';
  customFormat?: (date: Date) => string;
}

/**
 * Reusable DateTimePicker component with consistent styling
 * Uses @react-native-community/datetimepicker for native date/time selection
 */
export const FormDateTimePicker: React.FC<DateTimePickerProps> = ({
  label,
  error,
  required = false,
  value,
  onChange,
  mode = 'date',
  minimumDate,
  maximumDate,
  disabled = false,
  containerStyle,
  labelStyle,
  pickerStyle,
  errorStyle,
  format = 'short',
  customFormat,
}) => {
  const theme = useTheme();
  const [show, setShow] = useState(false);
  const styles = createDateTimePickerStyles(theme);

  const formatDate = (date: Date): string => {
    if (customFormat) {
      return customFormat(date);
    }

    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();

    switch (mode) {
      case 'time':
        return date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
        });
      
      case 'datetime':
        if (format === 'long') {
          return date.toLocaleString('en-IN', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          });
        }
        return date.toLocaleString('en-IN', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        });
      
      case 'date':
      default:
        // Handle relative dates for today/yesterday
        if (isToday) return 'Today';
        if (isYesterday) return 'Yesterday';
        
        if (format === 'long') {
          return date.toLocaleDateString('en-IN', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
          });
        }
        
        // Default DD/MM/YYYY format
        return date.toLocaleDateString('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
        });
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShow(false);
    }
    
    if (selectedDate && event.type !== 'dismissed') {
      onChange(selectedDate);
      // Auto-close on iOS for date mode after selection
      if (Platform.OS === 'ios' && mode === 'date') {
        setShow(false);
      }
    }
    
    // Handle dismissal
    if (event.type === 'dismissed') {
      setShow(false);
    }
  };

  const showDatePicker = () => {
    if (!disabled) {
      setShow(true);
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.picker,
          error ? styles.pickerError : null,
          disabled && styles.pickerDisabled,
          pickerStyle
        ]}
        onPress={showDatePicker}
        disabled={disabled}
      >
        <Text style={[
          styles.dateText,
          { color: disabled ? theme.colors.textDisabled : theme.colors.text }
        ]}>
          📅 {formatDate(value)}
        </Text>
        {!disabled && (
          <Text style={[styles.chevron, { color: theme.colors.textSecondary }]}>⌄</Text>
        )}
      </TouchableOpacity>
      
      {error && (
        <Text style={[styles.errorText, errorStyle]}>
          {error}
        </Text>
      )}

      {show && (
        <DateTimePicker
          value={value}
          mode={mode}
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleDateChange}
          {...(minimumDate && { minimumDate })}
          {...(maximumDate && { maximumDate })}
          textColor={theme.colors.text}
          accentColor={theme.colors.primary}
        />
      )}
    </View>
  );
};

const createDateTimePickerStyles = createStyles((theme) => ({
  container: {
    marginBottom: theme.spacing.md,
  },
  label: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  required: {
    color: theme.colors.error,
  },
  picker: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md + 4, // Extra padding to match other inputs
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.card,
    minHeight: 48, // Consistent with other form inputs
  },
  pickerError: {
    borderColor: theme.colors.error,
  },
  pickerDisabled: {
    opacity: 0.6,
    backgroundColor: theme.colors.surface,
  },
  dateText: {
    ...theme.typography.body,
    fontWeight: '600',
    flex: 1,
  },
  chevron: {
    fontSize: 16,
    marginLeft: theme.spacing.sm,
  },
  errorText: {
    color: theme.colors.error,
    ...theme.typography.caption,
    marginTop: theme.spacing.xs,
  },
}));