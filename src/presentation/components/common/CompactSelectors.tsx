import React, { useState } from 'react';
import { TouchableOpacity, View, Text, StyleSheet, Modal, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { FallbackIcon } from './FallbackIcon';

interface AccountTypeOption {
  type: any;
  icon: string;
  color: string;
  label: string;
  description: string;
}

interface CompactAccountTypeSelectorProps {
  options: AccountTypeOption[];
  selectedType: any;
  onSelect: (type: any) => void;
}

export const CompactAccountTypeSelector: React.FC<CompactAccountTypeSelectorProps> = ({
  options,
  selectedType,
  onSelect,
}) => {
  // Safely get theme with fallback
  let theme;
  try {
    theme = useTheme();
  } catch (error) {
    // Fallback theme if ThemeProvider is not available
    theme = {
      colors: {
        border: '#ddd',
        card: '#fff',
        surface: '#f8f9ff',
        primary: '#4A90E2',
        text: '#333',
        textSecondary: '#666',
        background: '#fff',
      },
      spacing: {
        sm: 8,
        md: 16,
        xs: 4,
      },
      borderRadius: {
        lg: 12,
      },
      shadows: {
        sm: {},
        md: {},
      },
      typography: {
        body: { fontSize: 16 },
        caption: { fontSize: 12 },
      },
    };
  }
  const [showModal, setShowModal] = useState(false);
  
  const selectedOption = options.find(opt => opt.type === selectedType);
  
  const styles = StyleSheet.create({
    container: {
      marginBottom: 8,
    },
    selectedDisplay: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderWidth: 1,
      borderColor: selectedOption ? selectedOption.color : theme.colors.border,
      borderRadius: 8,
      backgroundColor: selectedOption ? `${selectedOption.color}10` : theme.colors.card,
    },
    selectedText: {
      flex: 1,
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    chevron: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    placeholderText: {
      flex: 1,
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: theme.colors.background,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      maxHeight: '80%',
    },
    modalHeader: {
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      alignItems: 'center',
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    optionsList: {
      padding: 16,
    },
    optionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      marginBottom: 12,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.card,
    },
    optionItemSelected: {
      borderColor: '',
      backgroundColor: '',
    },
    optionContent: {
      flex: 1,
    },
    optionLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    optionDescription: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 18,
    },
    checkmark: {
      fontSize: 20,
      color: theme.colors.background,
      fontWeight: 'bold',
    },
    checkmarkContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: 8,
    },
    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 16,
    },
    selectedIconContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 10,
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.selectedDisplay}
        onPress={() => setShowModal(true)}
      >
        {selectedOption ? (
          <>
            <View style={[styles.selectedIconContainer, { backgroundColor: `${selectedOption.color}15` }]}>
              <FallbackIcon 
                icon={selectedOption.icon}
                name={selectedOption.label}
                size={16}
                style={{ color: selectedOption.color }}
              />
            </View>
            <Text style={styles.selectedText}>{selectedOption.label}</Text>
          </>
        ) : (
          <Text style={styles.placeholderText}>Select account type</Text>
        )}
        <Text style={styles.chevron}>⌄</Text>
      </TouchableOpacity>

      <Modal
        visible={showModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1}
          onPress={() => setShowModal(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Account Type</Text>
            </View>
            <ScrollView style={styles.optionsList} showsVerticalScrollIndicator={false}>
              {options.map((option) => {
                const isSelected = selectedType === option.type;
                return (
                  <TouchableOpacity
                    key={option.type}
                    style={[
                      styles.optionItem,
                      isSelected && {
                        ...styles.optionItemSelected,
                        borderColor: option.color,
                        backgroundColor: `${option.color}10`,
                      }
                    ]}
                    onPress={() => {
                      onSelect(option.type);
                      setShowModal(false);
                    }}
                  >
                    <View style={[styles.iconContainer, { backgroundColor: `${option.color}15` }]}>
                      <FallbackIcon 
                        icon={option.icon}
                        name={option.label}
                        size={20}
                        style={{ color: option.color }}
                      />
                    </View>
                    <View style={styles.optionContent}>
                      <Text style={styles.optionLabel}>{option.label}</Text>
                      <Text style={styles.optionDescription}>
                        {option.description}
                      </Text>
                    </View>
                    {isSelected && (
                      <View style={[styles.checkmarkContainer, { backgroundColor: option.color }]}>
                        <Text style={styles.checkmark}>✓</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

// Compact Account Selector
interface AccountOption {
  id: number;
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'loan' | 'investment';
  balance: number;
}

interface CompactAccountSelectorProps {
  accounts: AccountOption[];
  selectedAccountId: number | null;
  onSelect: (accountId: number) => void;
  onCreateNew?: () => void;
}

export const CompactAccountSelector: React.FC<CompactAccountSelectorProps> = ({
  accounts,
  selectedAccountId,
  onSelect,
  onCreateNew,
}) => {
  // Safely get theme with fallback
  let theme;
  try {
    theme = useTheme();
  } catch (error) {
    // Fallback theme if ThemeProvider is not available
    theme = {
      colors: {
        border: '#ddd',
        card: '#fff',
        surface: '#f8f9ff',
        primary: '#4A90E2',
        text: '#333',
        textSecondary: '#666',
        background: '#fff',
      },
      spacing: {
        sm: 8,
        md: 16,
        xs: 4,
      },
      borderRadius: {
        lg: 12,
      },
      shadows: {
        sm: {},
        md: {},
      },
      typography: {
        body: { fontSize: 16 },
        caption: { fontSize: 12 },
      },
    };
  }
  const [showModal, setShowModal] = useState(false);
  
  const selectedAccount = accounts.find(acc => acc.id === selectedAccountId);
  
  // Account type configurations with MaterialIcons and consistent styling
  const ACCOUNT_TYPES_CONFIG = {
    checking: { icon: 'account-balance', color: '#4A90E2', label: 'Checking Account' },
    savings: { icon: 'account-balance-wallet', color: '#7ED321', label: 'Savings Account' },
    credit: { icon: 'credit-card', color: '#F5A623', label: 'Credit Card' },
    loan: { icon: 'trending-down', color: '#D0021B', label: 'Loan Account' },
    investment: { icon: 'trending-up', color: '#9013FE', label: 'Investment Account' }
  };
  
  const getAccountTypeConfig = (type: string) => {
    return ACCOUNT_TYPES_CONFIG[type as keyof typeof ACCOUNT_TYPES_CONFIG] || 
           { icon: 'account-balance-wallet', color: theme.colors.primary, label: 'Account' };
  };
  
  const selectedAccountConfig = selectedAccount ? getAccountTypeConfig(selectedAccount.type) : null;
  
  const styles = StyleSheet.create({
    container: {
      marginBottom: 8,
    },
    selectedDisplay: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderWidth: 1,
      borderColor: selectedAccount ? selectedAccountConfig?.color : theme.colors.border,
      borderRadius: 8,
      backgroundColor: selectedAccount ? `${selectedAccountConfig?.color}10` : theme.colors.card,
    },
    selectedText: {
      flex: 1,
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    chevron: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    placeholderText: {
      flex: 1,
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: theme.colors.background,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      maxHeight: '80%',
    },
    modalHeader: {
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      alignItems: 'center',
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    optionsList: {
      padding: 16,
    },
    optionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      marginBottom: 12,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.card,
    },
    optionItemSelected: {
      borderColor: '',
      backgroundColor: '',
    },
    optionContent: {
      flex: 1,
    },
    optionLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    optionDescription: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 18,
    },
    checkmark: {
      fontSize: 20,
      color: theme.colors.background,
      fontWeight: 'bold',
    },
    checkmarkContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: 8,
    },
    createNewOption: {
      borderStyle: 'dashed',
      borderColor: theme.colors.primary,
    },
    createIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 16,
    },
    createIconText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.background,
    },
    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 16,
    },
    selectedIconContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 10,
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.selectedDisplay}
        onPress={() => setShowModal(true)}
      >
        {selectedAccount ? (
          <>
            <View style={[styles.selectedIconContainer, { backgroundColor: `${selectedAccountConfig?.color}15` }]}>
              <MaterialIcons 
                name={selectedAccountConfig?.icon as any}
                size={16}
                color={selectedAccountConfig?.color}
              />
            </View>
            <Text style={styles.selectedText}>{selectedAccount.name}</Text>
          </>
        ) : (
          <Text style={styles.placeholderText}>Select account</Text>
        )}
        <Text style={styles.chevron}>⌄</Text>
      </TouchableOpacity>

      <Modal
        visible={showModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1}
          onPress={() => setShowModal(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Account</Text>
            </View>
            <ScrollView style={styles.optionsList} showsVerticalScrollIndicator={false}>
              {accounts.map((account) => {
                const isSelected = selectedAccountId === account.id;
                const accountConfig = getAccountTypeConfig(account.type);
                return (
                  <TouchableOpacity
                    key={account.id}
                    style={[
                      styles.optionItem,
                      isSelected && {
                        ...styles.optionItemSelected,
                        borderColor: accountConfig.color,
                        backgroundColor: `${accountConfig.color}10`,
                      }
                    ]}
                    onPress={() => {
                      onSelect(account.id);
                      setShowModal(false);
                    }}
                  >
                    <View style={[styles.iconContainer, { backgroundColor: `${accountConfig.color}15` }]}>
                      <MaterialIcons 
                        name={accountConfig.icon as any}
                        size={20}
                        color={accountConfig.color}
                      />
                    </View>
                    <View style={styles.optionContent}>
                      <Text style={styles.optionLabel}>{account.name}</Text>
                      <Text style={styles.optionDescription}>
                        {accountConfig.label} • ₹{account.balance.toFixed(2)}
                      </Text>
                    </View>
                    {isSelected && (
                      <View style={[styles.checkmarkContainer, { backgroundColor: accountConfig.color }]}>
                        <Text style={styles.checkmark}>✓</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                );
              })}
              
              {onCreateNew && (
                <TouchableOpacity
                  style={[styles.optionItem, styles.createNewOption]}
                  onPress={() => {
                    setShowModal(false);
                    onCreateNew();
                  }}
                >
                  <View style={[styles.createIcon, { backgroundColor: theme.colors.primary }]}>
                    <Text style={styles.createIconText}>+</Text>
                  </View>
                  <View style={styles.optionContent}>
                    <Text style={[styles.optionLabel, { color: theme.colors.primary }]}>Create New Account</Text>
                    <Text style={styles.optionDescription}>Add a new account to track transactions</Text>
                  </View>
                </TouchableOpacity>
              )}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

// Compact Category Selector
interface CategoryOption {
  id: number;
  icon_name: string;
  name: string;
  color_code: string;
}

interface CompactCategorySelectorProps {
  categories: CategoryOption[];
  selectedCategoryId: number | null;
  onSelect: (categoryId: number) => void;
  onCreateNew?: () => void;
}

export const CompactCategorySelector: React.FC<CompactCategorySelectorProps> = ({
  categories,
  selectedCategoryId,
  onSelect,
  onCreateNew,
}) => {
  // Safely get theme with fallback
  let theme;
  try {
    theme = useTheme();
  } catch (error) {
    // Fallback theme if ThemeProvider is not available
    theme = {
      colors: {
        border: '#ddd',
        card: '#fff',
        surface: '#f8f9ff',
        primary: '#4A90E2',
        text: '#333',
        textSecondary: '#666',
        background: '#fff',
      },
      spacing: {
        sm: 8,
        md: 16,
        xs: 4,
      },
      borderRadius: {
        lg: 12,
      },
      shadows: {
        sm: {},
        md: {},
      },
      typography: {
        body: { fontSize: 16 },
        caption: { fontSize: 12 },
      },
    };
  }
  const [showModal, setShowModal] = useState(false);
  
  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);
  
  const styles = StyleSheet.create({
    container: {
      marginBottom: 8,
    },
    selectedDisplay: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderWidth: 1,
      borderColor: selectedCategory ? selectedCategory.color_code : theme.colors.border,
      borderRadius: 8,
      backgroundColor: selectedCategory ? `${selectedCategory.color_code}10` : theme.colors.card,
    },
    selectedText: {
      flex: 1,
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    chevron: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    placeholderText: {
      flex: 1,
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: theme.colors.background,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      maxHeight: '80%',
    },
    modalHeader: {
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      alignItems: 'center',
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    optionsList: {
      padding: 16,
    },
    optionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      marginBottom: 12,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.card,
    },
    optionItemSelected: {
      borderColor: '',
      backgroundColor: '',
    },
    optionContent: {
      flex: 1,
    },
    optionLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    optionDescription: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 18,
    },
    checkmark: {
      fontSize: 20,
      color: theme.colors.background,
      fontWeight: 'bold',
    },
    checkmarkContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: 8,
    },
    createNewOption: {
      borderStyle: 'dashed',
      borderColor: theme.colors.primary,
    },
    createIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 16,
    },
    createIconText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.background,
    },
    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 16,
    },
    selectedIconContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 10,
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.selectedDisplay}
        onPress={() => setShowModal(true)}
      >
        {selectedCategory ? (
          <>
            <View style={[styles.selectedIconContainer, { backgroundColor: `${selectedCategory.color_code}15` }]}>
              <FallbackIcon 
                icon={selectedCategory.icon_name}
                name={selectedCategory.name}
                size={16}
                style={{ color: selectedCategory.color_code }}
              />
            </View>
            <Text style={styles.selectedText}>{selectedCategory.name}</Text>
          </>
        ) : (
          <Text style={styles.placeholderText}>Select category</Text>
        )}
        <Text style={styles.chevron}>⌄</Text>
      </TouchableOpacity>

      <Modal
        visible={showModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1}
          onPress={() => setShowModal(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Category</Text>
            </View>
            <ScrollView style={styles.optionsList} showsVerticalScrollIndicator={false}>
              {categories.map((category) => {
                const isSelected = selectedCategoryId === category.id;
                return (
                  <TouchableOpacity
                    key={category.id}
                    style={[
                      styles.optionItem,
                      isSelected && {
                        ...styles.optionItemSelected,
                        borderColor: category.color_code,
                        backgroundColor: `${category.color_code}10`,
                      }
                    ]}
                    onPress={() => {
                      onSelect(category.id);
                      setShowModal(false);
                    }}
                  >
                    <View style={[styles.iconContainer, { backgroundColor: `${category.color_code}15` }]}>
                      <FallbackIcon 
                        icon={category.icon_name}
                        name={category.name}
                        size={20}
                        style={{ color: category.color_code }}
                      />
                    </View>
                    <View style={styles.optionContent}>
                      <Text style={styles.optionLabel}>{category.name}</Text>
                      <Text style={styles.optionDescription}>
                        Category • {category.name.split(' ')[0].toLowerCase()}
                      </Text>
                    </View>
                    {isSelected && (
                      <View style={[styles.checkmarkContainer, { backgroundColor: category.color_code }]}>
                        <Text style={styles.checkmark}>✓</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                );
              })}
              
              {onCreateNew && (
                <TouchableOpacity
                  style={[styles.optionItem, styles.createNewOption]}
                  onPress={() => {
                    setShowModal(false);
                    onCreateNew();
                  }}
                >
                  <View style={[styles.createIcon, { backgroundColor: theme.colors.primary }]}>
                    <Text style={styles.createIconText}>+</Text>
                  </View>
                  <View style={styles.optionContent}>
                    <Text style={[styles.optionLabel, { color: theme.colors.primary }]}>Create New Category</Text>
                    <Text style={styles.optionDescription}>
                      Add a custom category
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};