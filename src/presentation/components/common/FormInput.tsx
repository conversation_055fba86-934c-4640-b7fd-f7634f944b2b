import React from 'react';
import {
  View,
  Text,
  TextInput,
  TextInputProps,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';

interface FormInputProps extends TextInputProps {
  label?: string;
  error?: string;
  required?: boolean;
  containerStyle?: any;
  labelStyle?: any;
  inputStyle?: any;
  errorStyle?: any;
}

/**
 * Reusable TextInput component with consistent styling and proper text clipping fix
 * Used throughout the application to maintain design consistency
 */
export const FormInput: React.FC<FormInputProps> = ({
  label,
  error,
  required = false,
  containerStyle,
  labelStyle,
  inputStyle,
  errorStyle,
  ...textInputProps
}) => {
  const theme = useTheme();
  const styles = createFormInputStyles(theme);

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <TextInput
        style={[
          styles.input,
          error ? styles.inputError : null,
          inputStyle
        ]}
        placeholderTextColor={theme.colors.textDisabled}
        {...textInputProps}
      />
      
      {error && (
        <Text style={[styles.errorText, errorStyle]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const createFormInputStyles = createStyles((theme) => ({
  container: {
    marginBottom: theme.spacing.md,
  },
  label: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  required: {
    color: theme.colors.error,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    ...theme.typography.body,
    backgroundColor: theme.colors.card,
    color: theme.colors.text,
    lineHeight: undefined, // Remove lineHeight to use default baseline
    textAlignVertical: 'center', // Center align for single line inputs
    includeFontPadding: false, // Android specific - removes extra padding
    minHeight: 48, // Standard input height
    maxHeight: 120, // Allow for multiline if needed
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  errorText: {
    color: theme.colors.error,
    ...theme.typography.caption,
    marginTop: theme.spacing.xs,
  },
}));