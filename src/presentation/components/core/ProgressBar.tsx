import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet, ViewStyle } from 'react-native';

export interface ProgressBarProps {
  // Data
  progress: number; // 0-100
  
  // Appearance
  color?: string;
  backgroundColor?: string;
  height?: number;
  borderRadius?: number;
  animated?: boolean;
  animationDuration?: number;
  
  // Accessibility
  accessibilityLabel?: string;
  testID?: string;
  
  // Styling
  style?: ViewStyle;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  color = '#4A90E2',
  backgroundColor = '#E5E5E5',
  height = 6,
  borderRadius,
  animated = true,
  animationDuration = 300,
  accessibilityLabel,
  testID,
  style,
}) => {
  const animatedWidth = useRef(new Animated.Value(0)).current;
  
  const clampedProgress = Math.max(0, Math.min(100, progress));
  const finalBorderRadius = borderRadius ?? height / 2;
  
  useEffect(() => {
    if (animated) {
      Animated.timing(animatedWidth, {
        toValue: clampedProgress,
        duration: animationDuration,
        useNativeDriver: false,
      }).start();
    } else {
      animatedWidth.setValue(clampedProgress);
    }
  }, [clampedProgress, animated, animationDuration, animatedWidth]);
  
  return (
    <View
      style={[
        styles.container,
        {
          height,
          backgroundColor,
          borderRadius: finalBorderRadius,
        },
        style,
      ]}
      accessibilityRole="progressbar"
      accessibilityLabel={accessibilityLabel || `${clampedProgress.toFixed(0)}% progress`}
      accessibilityValue={{
        min: 0,
        max: 100,
        now: clampedProgress,
      }}
      testID={testID}
    >
      <Animated.View
        style={[
          styles.fill,
          {
            height,
            backgroundColor: color,
            borderRadius: finalBorderRadius,
            width: animatedWidth.interpolate({
              inputRange: [0, 100],
              outputRange: ['0%', '100%'],
              extrapolate: 'clamp',
            }),
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  fill: {
    position: 'absolute',
    left: 0,
    top: 0,
  },
});