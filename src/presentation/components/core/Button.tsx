import React from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';

export interface ButtonProps {
  // Content
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  
  // Behavior
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  
  // Appearance
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
  
  // Styling
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  subtitle,
  icon,
  iconPosition = 'left',
  onPress,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  accessibilityLabel,
  accessibilityHint,
  testID,
  style,
  textStyle,
}) => {
  const theme = useTheme();
  
  const buttonStyles = [
    styles.button,
    {
      backgroundColor: variant === 'primary' ? theme.colors.primary : 
                      variant === 'secondary' ? theme.colors.surface : 
                      variant === 'danger' ? theme.colors.error : 'transparent',
      borderColor: variant === 'outline' ? theme.colors.primary : 
                   variant === 'secondary' ? theme.colors.border : 'transparent',
      borderWidth: variant === 'outline' || variant === 'secondary' ? 1 : 0,
      borderRadius: theme.borderRadius.md,
      minHeight: size === 'sm' ? 40 : size === 'lg' ? 56 : 48,
      paddingVertical: size === 'sm' ? 8 : size === 'lg' ? 16 : 12,
      paddingHorizontal: size === 'sm' ? 12 : size === 'lg' ? 24 : 16,
    },
    fullWidth && { width: '100%' as const },
    disabled && { opacity: 0.5 },
    style,
  ];
  
  const textStyles = [
    styles.text,
    {
      fontSize: size === 'sm' ? 14 : size === 'lg' ? 18 : 16,
      color: variant === 'primary' || variant === 'danger' ? theme.colors.background : 
             variant === 'outline' || variant === 'ghost' ? theme.colors.primary : 
             theme.colors.text,
    },
    textStyle,
  ];
  
  return (
    <TouchableOpacity
      style={buttonStyles}
      onPress={onPress}
      disabled={disabled || loading}
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityState={{ disabled: disabled || loading }}
      testID={testID}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator 
          color={variant === 'primary' || variant === 'danger' ? theme.colors.background : theme.colors.primary}
          size="small"
        />
      ) : (
        <>
          {icon && iconPosition === 'left' && icon}
          <Text style={textStyles}>{title}</Text>
          {subtitle && <Text style={[textStyles, { fontSize: (textStyles[0] as any).fontSize * 0.875, opacity: 0.8 }]}>{subtitle}</Text>}
          {icon && iconPosition === 'right' && icon}
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
});