import React from 'react';
import {
  View,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';

export interface CardProps {
  // Content
  children: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  
  // Behavior
  onPress?: () => void;
  onLongPress?: () => void;
  
  // Appearance
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: 'button' | 'none';
  testID?: string;
  
  // Styling
  style?: ViewStyle;
}

export const Card = React.memo<CardProps>(({ children, style, onPress, variant = 'default', ...props }) => {
  const theme = useTheme();
  const styles = createCardStyles(theme);

  const cardStyle = [
    styles.card,
    variant === 'elevated' && styles.elevated,
    variant === 'outlined' && styles.outlined,
    style,
  ];

  if (onPress) {
    return (
      <TouchableOpacity style={cardStyle} onPress={onPress} activeOpacity={0.7} {...props}>
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={cardStyle} {...props}>
      {children}
    </View>
  );
});

Card.displayName = 'Card';

const createCardStyles = createStyles((theme) => ({
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    ...theme.shadows.md,
  },
  elevated: {
    ...theme.shadows.lg,
  },
  outlined: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowOpacity: 0,
    elevation: 0,
  },
}));