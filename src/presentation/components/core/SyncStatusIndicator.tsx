import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';

export interface SyncStatusIndicatorProps {
  // Data
  status: 'synced' | 'pending' | 'conflict' | 'local';
  
  // Appearance
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  variant?: 'dot' | 'badge' | 'full';
  
  // Accessibility
  testID?: string;
  
  // Styling
  style?: ViewStyle;
}

export const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({
  status,
  size = 'md',
  showLabel = false,
  variant = 'dot',
  testID,
  style,
}) => {
  const theme = useTheme();
  
  const getStatusConfig = () => {
    switch (status) {
      case 'synced':
        return {
          color: theme.colors.synced,
          label: 'Synced',
          icon: '✓',
          accessibilityLabel: 'Synced successfully',
        };
      case 'pending':
        return {
          color: theme.colors.pending,
          label: 'Syncing',
          icon: '⟳',
          accessibilityLabel: 'Sync pending',
        };
      case 'conflict':
        return {
          color: theme.colors.conflict,
          label: 'Conflict',
          icon: '⚠',
          accessibilityLabel: 'Sync conflict needs attention',
        };
      case 'local':
      default:
        return {
          color: theme.colors.offline,
          label: 'Local',
          icon: '○',
          accessibilityLabel: 'Stored locally only',
        };
    }
  };
  
  const config = getStatusConfig();
  
  const getDotSize = () => {
    switch (size) {
      case 'sm': return 6;
      case 'md': return 8;
      case 'lg': return 10;
      default: return 8;
    }
  };
  
  const getBadgeSize = () => {
    switch (size) {
      case 'sm': return { minWidth: 14, minHeight: 14 };
      case 'lg': return { minWidth: 22, minHeight: 22 };
      default: return { minWidth: 18, minHeight: 18 };
    }
  };
  
  const getBadgeTextSize = () => {
    switch (size) {
      case 'sm': return 8;
      case 'lg': return 12;
      default: return 10;
    }
  };
  
  if (variant === 'dot') {
    return (
      <View
        style={[
          styles.dot,
          {
            width: getDotSize(),
            height: getDotSize(),
            backgroundColor: config.color,
          },
          style,
        ]}
        accessibilityLabel={config.accessibilityLabel}
        accessibilityRole="image"
        testID={testID}
      />
    );
  }
  
  if (variant === 'badge') {
    return (
      <View
        style={[
          styles.badge,
          getBadgeSize(),
          { backgroundColor: config.color },
          style,
        ]}
        accessibilityLabel={config.accessibilityLabel}
        accessibilityRole="text"
        testID={testID}
      >
        <Text style={[styles.badgeText, { fontSize: getBadgeTextSize() }]}>
          {config.icon}
        </Text>
      </View>
    );
  }
  
  if (variant === 'full') {
    return (
      <View
        style={[styles.full, style]}
        accessibilityLabel={config.accessibilityLabel}
        accessibilityRole="text"
        testID={testID}
      >
        <View
          style={[
            styles.dot,
            {
              width: getDotSize(),
              height: getDotSize(),
              backgroundColor: config.color,
            },
          ]}
        />
        {showLabel && (
          <Text style={[styles.label, { color: config.color }]}>
            {config.label}
          </Text>
        )}
      </View>
    );
  }
  
  return null;
};

const styles = StyleSheet.create({
  dot: {
    borderRadius: 50,
  },
  badge: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
  },
  full: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  label: {
    fontSize: 12,
    fontWeight: '500',
  },
});