import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { PieChart } from 'react-native-chart-kit';
import { useTheme } from '../../../shared/theme/ThemeProvider';
import { FallbackIcon } from '../common/FallbackIcon';
import { Card } from '../core';

interface ExpenseCategory {
  category_id: number;
  category_name: string;
  category_icon: string;
  category_color: string;
  total_amount: number;
  percentage: number;
}

interface ExpensesPieChartProps {
  expenses: ExpenseCategory[];
  onCategorySelect: (categoryId: number | null) => void;
  selectedCategoryId?: number | null;
}

const screenWidth = Dimensions.get('window').width;

export const ExpensesPieChart: React.FC<ExpensesPieChartProps> = React.memo(({
  expenses,
  onCategorySelect,
  selectedCategoryId
}) => {
  const theme = useTheme();

  if (!expenses || expenses.length === 0) {
    return (
      <Card variant="outlined" style={{ marginHorizontal: theme.spacing.md, marginBottom: theme.spacing.lg }}>
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
            No expense data available
          </Text>
        </View>
      </Card>
    );
  }

  // Memoize chart data to prevent unnecessary recalculations
  const chartData = React.useMemo(() => 
    expenses.map(expense => ({
      name: expense.category_name,
      population: expense.total_amount,
      color: expense.category_color,
      category_id: expense.category_id,
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    })), [expenses]);

  const totalAmount = React.useMemo(() => 
    expenses.reduce((sum, expense) => sum + expense.total_amount, 0), [expenses]);

  // Handle pie slice click - react-native-chart-kit doesn't support native click events
  // We'll use the legend buttons for selection instead
  const handleCategoryClick = React.useCallback((categoryId: number) => {
    // Toggle selection - if same category is clicked, deselect it
    if (selectedCategoryId === categoryId) {
      onCategorySelect(null);
    } else {
      onCategorySelect(categoryId);
    }
  }, [selectedCategoryId, onCategorySelect]);

  return (
    <Card variant="outlined" style={{ marginHorizontal: theme.spacing.md, marginBottom: theme.spacing.lg }}>
      <View style={styles.container}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Expenses by Category</Text>
        
        {/* Pie Chart */}
        <View style={styles.chartContainer}>
          <PieChart
            data={chartData}
            width={screenWidth - 80}
            height={200}
            chartConfig={{
              backgroundColor: theme.colors.surface,
              backgroundGradientFrom: theme.colors.surface,
              backgroundGradientTo: theme.colors.surface,
              color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
              strokeWidth: 2,
            }}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
            center={[10, 10]}
            absolute={false}
          />
          
          {/* Total expenses text */}
          <View style={styles.totalContainer}>
            <Text style={[styles.totalLabel, { color: theme.colors.textSecondary }]}>Total Expenses</Text>
            <Text style={[styles.totalAmount, { color: theme.colors.expense }]}>
              ₹{totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </Text>
          </View>
        </View>

        {/* Legend */}
        <View style={styles.legendContainer}>
          {expenses.map((expense) => {
            const isSelected = selectedCategoryId === expense.category_id;
            return (
              <TouchableOpacity
                key={expense.category_id}
                style={[
                  styles.legendItem,
                  isSelected && { backgroundColor: theme.colors.primary + '20' },
                  { borderColor: theme.colors.border }
                ]}
                onPress={() => handleCategoryClick(expense.category_id)}
              >
                <View style={styles.legendLeft}>
                  <View style={[styles.legendColor, { backgroundColor: expense.category_color }]} />
                  <FallbackIcon
                    icon={expense.category_icon}
                    name={expense.category_name}
                    size={16}
                    style={{ color: expense.category_color, marginRight: 8 }}
                  />
                  <Text style={[styles.legendText, { color: theme.colors.text }]} numberOfLines={1}>
                    {expense.category_name}
                  </Text>
                </View>
                <View style={styles.legendRight}>
                  <Text style={[styles.legendAmount, { color: theme.colors.expense }]}>
                    ₹{expense.total_amount.toFixed(0)}
                  </Text>
                  <Text style={[styles.legendPercentage, { color: theme.colors.textSecondary }]}>
                    {expense.percentage.toFixed(1)}%
                  </Text>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>

        {selectedCategoryId && (
          <TouchableOpacity
            style={[styles.clearButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => onCategorySelect(null)}
          >
            <Text style={[styles.clearButtonText, { color: theme.colors.surface }]}>
              Show All Categories
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </Card>
  );
});

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  totalContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  legendContainer: {
    gap: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  legendLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  legendRight: {
    alignItems: 'flex-end',
  },
  legendAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  legendPercentage: {
    fontSize: 12,
    marginTop: 2,
  },
  clearButton: {
    marginTop: 12,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});