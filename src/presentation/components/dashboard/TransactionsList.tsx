import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../../shared/theme/ThemeProvider';
import { Card } from '../core';
import { FallbackIcon } from '../common/FallbackIcon';

interface Transaction {
  id: number;
  description: string;
  amount: number;
  transaction_type: 'income' | 'expense';
  transaction_date: string;
  category_id: number | null;
  account_id: number;
}

interface CategoryInfo {
  name: string;
  icon: string;
  color: string;
}

interface TransactionsListProps {
  transactions: Transaction[];
  title: string;
  getCategoryInfo: (categoryId: number | null) => CategoryInfo;
  getAccountName: (accountId: number) => string;
  formatDate: (dateString: string) => string;
  emptyMessage?: string;
}

export const TransactionsList: React.FC<TransactionsListProps> = ({
  transactions,
  title,
  getCategoryInfo,
  getAccountName,
  formatDate,
  emptyMessage = "No transactions found"
}) => {
  const theme = useTheme();

  return (
    <View style={[styles.container, { paddingHorizontal: theme.spacing.md }]}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>{title}</Text>
      {transactions.length === 0 ? (
        <Card variant="outlined">
          <Text style={[styles.emptyState, { color: theme.colors.textSecondary }]}>
            {emptyMessage}
          </Text>
        </Card>
      ) : (
        <Card variant="outlined">
          {transactions.map((transaction: Transaction, index: number) => {
            const categoryInfo = getCategoryInfo(transaction.category_id);
            const isIncome = transaction.transaction_type === 'income';
            
            return (
              <View key={transaction.id} style={[
                styles.transactionItem,
                index < transactions.length - 1 && {
                  borderBottomWidth: 1,
                  borderBottomColor: theme.colors.border
                }
              ]}>
                {/* Category Icon */}
                <View style={[styles.categoryIcon, { 
                  borderColor: categoryInfo.color,
                  borderWidth: 2,
                  backgroundColor: theme.colors.card
                }]}>
                  <FallbackIcon 
                    icon={categoryInfo.icon}
                    name={categoryInfo.name}
                    size={16}
                    style={{ color: categoryInfo.color }}
                  />
                </View>

                {/* Transaction Details */}
                <View style={styles.transactionInfo}>
                  <Text style={[styles.transactionDesc, { color: theme.colors.text }]} numberOfLines={1}>
                    {transaction.description}
                  </Text>
                  <View style={styles.transactionMeta}>
                    <Text style={[styles.categoryName, { color: theme.colors.textSecondary }]}>
                      {categoryInfo.name}
                    </Text>
                    <Text style={[styles.accountName, { color: theme.colors.textSecondary }]}>
                      • {getAccountName(transaction.account_id)}
                    </Text>
                  </View>
                </View>

                {/* Amount and Date */}
                <View style={styles.transactionRight}>
                  <Text style={[
                    styles.transactionAmount,
                    { color: isIncome ? theme.colors.success : theme.colors.expense }
                  ]}>
                    {isIncome ? '+' : '-'}₹{Math.abs(transaction.amount).toFixed(2)}
                  </Text>
                  <Text style={[styles.transactionDate, { color: theme.colors.textSecondary }]}>
                    {formatDate(transaction.transaction_date)}
                  </Text>
                </View>
              </View>
            );
          })}
        </Card>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  emptyState: {
    textAlign: 'center',
    fontSize: 16,
    paddingVertical: 32,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
    marginRight: 8,
  },
  transactionDesc: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  transactionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryName: {
    fontSize: 12,
  },
  accountName: {
    fontSize: 12,
    marginLeft: 4,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 11,
  },
});