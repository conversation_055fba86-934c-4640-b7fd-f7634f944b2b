import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Transaction, Account } from '@/shared/types';
import { TransactionService, TransactionFilters, TransactionSearchResult } from '@/business/services/TransactionService';
import { useCategoryStore } from '@/shared/stores/categoryStore';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { MaterialIcon } from '@/presentation/components/common/MaterialIcon';
import { FallbackIcon } from '@/presentation/components/common/FallbackIcon';
// import { TransactionListItem } from './TransactionListItem'; // TODO: Remove if not needed

interface TransactionListProps {
  onTransactionPress?: (transaction: Transaction) => void;
  onTransactionLongPress?: (transaction: Transaction) => void;
  accountId?: number;
  showSearch?: boolean;
  showFilters?: boolean;
  onToggleFilters?: () => void;
  limit?: number;
  refreshTrigger?: number; // Add this prop to trigger refresh
}

interface FilterState {
  transactionType?: 'income' | 'expense' | 'transfer';
  categoryId?: number;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
}

// Singleton service instance to prevent repeated creation
let transactionServiceInstance: TransactionService | null = null;

export const TransactionList: React.FC<TransactionListProps> = ({
  onTransactionPress,
  onTransactionLongPress,
  accountId,
  showSearch = true,
  showFilters = false,
  onToggleFilters,
  limit = 20,
  refreshTrigger,
}) => {
  const theme = useTheme();
  // State
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<FilterState>({});
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [error, setError] = useState<string | null>(null);

  // Services - create singleton instance
  const transactionService = useMemo(() => {
    if (!transactionServiceInstance) {
      transactionServiceInstance = new TransactionService();
    }
    return transactionServiceInstance;
  }, []);

  // Stores
  const { categories, loadCategories } = useCategoryStore();

  // Mock data for accounts (in real app, would come from account store)
  const mockAccounts: Account[] = useMemo(() => [
    { id: 1, name: 'Main Account', type: 'checking', balance: 1000, currency: 'INR', is_active: true, created_at: '', updated_at: '', sync_status: 'local' },
    { id: 2, name: 'Savings', type: 'savings', balance: 5000, currency: 'INR', is_active: true, created_at: '', updated_at: '', sync_status: 'local' },
  ], []);

  // Load transactions
  const loadTransactions = useCallback(async (
    pageNum: number = 1, 
    isRefresh: boolean = false
  ) => {
    try {
      if (pageNum === 1 && !isRefresh) {
        setLoading(true);
      }
      setError(null);

      const transactionFilters: TransactionFilters = {
        ...(accountId && { accountId }),
        ...(searchQuery.trim() && { searchQuery: searchQuery.trim() }),
        ...filters,
      };

      const result: TransactionSearchResult = await transactionService.getTransactions(
        transactionFilters,
        pageNum,
        limit
      );

      if (pageNum === 1 || isRefresh) {
        setTransactions(result.transactions);
      } else {
        setTransactions(prev => [...prev, ...result.transactions]);
      }

      setHasMore(result.hasMore);
      setPage(pageNum);

    } catch (error) {
      console.error('Failed to load transactions:', error);
      setError(error instanceof Error ? error.message : 'Failed to load transactions');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [accountId, searchQuery, filters, limit, transactionService]);

  // Search with debounce
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout>();
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    const timeout = setTimeout(() => {
      setPage(1);
      loadTransactions(1);
    }, 500);
    
    setSearchTimeout(timeout);
  };

  // Load more transactions (pagination)
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      loadTransactions(page + 1);
    }
  };

  // Refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    setPage(1);
    loadTransactions(1, true);
  }, [loadTransactions]);

  // Filter transactions
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPage(1);
    loadTransactions(1);
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
    setPage(1);
    loadTransactions(1);
  };

  // Get account name
  const getAccountName = (accountId: number): string => {
    const account = mockAccounts.find(acc => acc.id === accountId);
    return account?.name || 'Unknown Account';
  };

  // Get category info
  const getCategoryInfo = (categoryId: number | null): { name: string; icon: string; color: string } => {
    if (!categoryId) {
      return { name: 'Uncategorized', icon: '', color: '#999' };
    }
    
    const category = categories.find(cat => cat.id === categoryId);
    return {
      name: category?.name || 'Unknown',
      icon: category?.icon_name || '',
      color: category?.color_code || '#999'
    };
  };

  // Format amount
  const formatAmount = (amount: number, type: string): string => {
    const prefix = type === 'income' ? '+₹' : '-₹';
    return `${prefix}${Math.abs(amount).toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString: string): string => {
    // Parse date string as local date to avoid timezone issues
    const [year, month, day] = dateString.split('-').map(Number);
    const date = new Date(year, month - 1, day); // month is 0-indexed
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-IN', {
        day: '2-digit',
        month: 'short'
      });
    }
  };

  // Initial load - FIXED: Removed loadTransactions dependency
  useEffect(() => {
    loadTransactions(1);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Load categories on component mount - FIXED: Only run once, not dependent on categories.length
  useEffect(() => {
    if (categories.length === 0) {
      loadCategories().catch(error => {
        console.error('Failed to load categories:', error);
      });
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Refresh transactions when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger !== undefined && refreshTrigger > 0) {
      handleRefresh();
    }
  }, [refreshTrigger, handleRefresh]);

  // Re-load transactions when dependencies change
  useEffect(() => {
    setPage(1);
    loadTransactions(1);
  }, [accountId, searchQuery, filters]); // eslint-disable-line react-hooks/exhaustive-deps

  // Render transaction item
  const renderTransactionItem = ({ item }: { item: Transaction }) => {
    const categoryInfo = getCategoryInfo(item.category_id);
    const isIncome = item.transaction_type === 'income';
    const amountColor = isIncome ? styles.incomeAmount.color : styles.expenseAmount.color;

    return (
      <TouchableOpacity
        style={styles.transactionItem}
        onPress={() => onTransactionPress?.(item)}
        onLongPress={() => onTransactionLongPress?.(item)}
        testID={`transaction-item-${item.id}`}
      >
        <View style={styles.transactionRow}>
          {/* Category Icon */}
          <View style={[styles.categoryIcon, { 
            borderColor: categoryInfo.color,
            borderWidth: 2,
            backgroundColor: theme.colors.card
          }]}>
            <FallbackIcon 
              icon={categoryInfo.icon}
              name={categoryInfo.name}
              size={20}
              style={{ color: categoryInfo.color }}
            />
          </View>

          {/* Transaction Details */}
          <View style={styles.transactionDetails}>
            <Text style={styles.transactionDescription} numberOfLines={1}>
              {item.description}
            </Text>
            <View style={styles.transactionMeta}>
              <Text style={styles.categoryName}>{categoryInfo.name}</Text>
              {!accountId && (
                <Text style={styles.accountName}> • {getAccountName(item.account_id)}</Text>
              )}
            </View>
          </View>

          {/* Amount and Date */}
          <View style={styles.transactionRight}>
            <Text style={[styles.transactionAmount, { color: amountColor }]}>
              {formatAmount(item.amount, item.transaction_type)}
            </Text>
            <Text style={styles.transactionDate}>
              {formatDate(item.transaction_date)}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Render empty state
  const renderEmptyState = () => {
    if (loading) return null;

    return (
      <View style={styles.emptyState}>
        <Text style={styles.emptyStateIcon}>📝</Text>
        <Text style={styles.emptyStateTitle}>No transactions found</Text>
        <Text style={styles.emptyStateSubtitle}>
          {searchQuery || Object.keys(filters).length > 0
            ? 'Try adjusting your search or filters'
            : 'Your transactions will appear here'
          }
        </Text>
        {(searchQuery || Object.keys(filters).length > 0) && (
          <TouchableOpacity style={styles.clearFiltersButton} onPress={clearFilters}>
            <Text style={styles.clearFiltersText}>Clear Filters</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // Render footer
  const renderFooter = () => {
    if (!hasMore) return null;

    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator color="#4A90E2" />
        <Text style={styles.loadingText}>Loading more...</Text>
      </View>
    );
  };

  // Render error
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorIcon}>⚠️</Text>
        <Text style={styles.errorTitle}>Failed to load transactions</Text>
        <Text style={styles.errorMessage}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => loadTransactions(1)}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Search Bar */}
      {showSearch && (
        <View style={[styles.searchContainer, { backgroundColor: theme.colors.background, borderBottomColor: theme.colors.border }]}>
          <TextInput
            style={[styles.searchInput, { 
              backgroundColor: theme.colors.surface, 
              color: theme.colors.text,
              borderColor: theme.colors.border 
            }]}
            placeholder="Search transactions..."
            value={searchQuery}
            onChangeText={handleSearchChange}
            placeholderTextColor={theme.colors.textSecondary}
            testID="search-input"
          />
          <View style={styles.searchActions}>
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={styles.clearSearchButton}
                onPress={() => handleSearchChange('')}
                testID="clear-search"
              >
                <Text style={[styles.clearSearchText, { color: theme.colors.textSecondary }]}>✕</Text>
              </TouchableOpacity>
            )}
            {onToggleFilters && (
              <TouchableOpacity
                style={[styles.filterButton, { 
                  backgroundColor: showFilters ? theme.colors.primary : theme.colors.surface,
                  borderColor: theme.colors.border 
                }]}
                onPress={onToggleFilters}
                testID="filter-button"
              >
                <MaterialIcon
                  name="filter-list"
                  size={20}
                  color={showFilters ? theme.colors.background : theme.colors.textSecondary}
                />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      {/* Filter Bar */}
      {showFilters && (
        <View style={[styles.filterContainer, { backgroundColor: theme.colors.background, borderBottomColor: theme.colors.border }]}>
          <Text style={styles.filterLabel}>Filters:</Text>
          <TouchableOpacity
            style={[
              styles.filterChip,
              filters.transactionType === 'expense' && styles.filterChipActive
            ]}
            onPress={() => {
              if (filters.transactionType === 'expense') {
                const { transactionType, ...restFilters } = filters;
                setFilters(restFilters);
              } else {
                handleFilterChange({ transactionType: 'expense' });
              }
            }}
          >
            <Text style={[
              styles.filterChipText,
              filters.transactionType === 'expense' && styles.filterChipTextActive
            ]}>
              Expenses
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterChip,
              filters.transactionType === 'income' && styles.filterChipActive
            ]}
            onPress={() => {
              if (filters.transactionType === 'income') {
                const { transactionType, ...restFilters } = filters;
                setFilters(restFilters);
              } else {
                handleFilterChange({ transactionType: 'income' });
              }
            }}
          >
            <Text style={[
              styles.filterChipText,
              filters.transactionType === 'income' && styles.filterChipTextActive
            ]}>
              Income
            </Text>
          </TouchableOpacity>
          {(Object.keys(filters).length > 0) && (
            <TouchableOpacity style={styles.clearFiltersChip} onPress={clearFilters}>
              <Text style={styles.clearFiltersChipText}>Clear</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Transaction List */}
      <FlatList
        data={transactions}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderTransactionItem}
        ListEmptyComponent={renderEmptyState}
        ListFooterComponent={renderFooter}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
        style={styles.transactionList}
        testID="transaction-list"
      />

      {/* Loading indicator */}
      {loading && transactions.length === 0 && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4A90E2" />
          <Text style={styles.loadingText}>Loading transactions...</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginVertical: 8,
  },
  searchInput: {
    flex: 1,
    height: 44,
    backgroundColor: '#f5f5f5',
    borderRadius: 22,
    paddingHorizontal: 16,
    paddingRight: 50, // Add padding to make room for clear button
    fontSize: 16,
    color: '#333',
  },
  searchActions: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    right: 8,
    height: 44,
  },
  clearSearchButton: {
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
  filterButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 18,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  clearSearchText: {
    fontSize: 16,
    color: '#999',
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 8,
    gap: 8,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  filterChipActive: {
    backgroundColor: '#4A90E2',
    borderColor: '#4A90E2',
  },
  filterChipText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  filterChipTextActive: {
    color: '#fff',
  },
  clearFiltersChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#ff4444',
  },
  clearFiltersChipText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500',
  },
  transactionList: {
    flex: 1,
  },
  transactionItem: {
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  transactionRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryEmoji: {
    fontSize: 18,
  },
  transactionDetails: {
    flex: 1,
    marginRight: 8,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  transactionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryName: {
    fontSize: 14,
    color: '#666',
  },
  accountName: {
    fontSize: 14,
    color: '#999',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  expenseAmount: {
    color: '#FF6B6B',
  },
  incomeAmount: {
    color: '#58D68D',
  },
  transactionDate: {
    fontSize: 12,
    color: '#999',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  clearFiltersButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#4A90E2',
  },
  clearFiltersText: {
    fontSize: 16,
    color: '#4A90E2',
    fontWeight: '600',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#4A90E2',
  },
  retryButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
});