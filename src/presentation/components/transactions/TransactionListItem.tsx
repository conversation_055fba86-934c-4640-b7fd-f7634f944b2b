import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { FallbackIcon } from '../common/FallbackIcon';
import { Transaction, Account, Category } from '@/shared/types';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { SyncStatusIndicator } from '@/presentation/components/core';

interface TransactionListItemProps {
  transaction: Transaction;
  onPress?: (transaction: Transaction) => void;
  onLongPress?: (transaction: Transaction) => void;
  showAccount?: boolean;
  accounts?: Account[];
  categories?: Category[];
}

export const TransactionListItem: React.FC<TransactionListItemProps> = ({
  transaction,
  onPress,
  onLongPress,
  showAccount = false,
  accounts = [],
  categories = [],
}) => {
  const theme = useTheme();
  // Get category info
  const getCategoryInfo = (categoryId: number | null): { name: string; icon: string; color: string } => {
    if (!categoryId) {
      return { name: 'Uncategorized', icon: '', color: '#999' };
    }
    
    const category = categories.find(cat => cat.id === categoryId);
    return {
      name: category?.name || 'Unknown',
      icon: category?.icon_name || '',
      color: category?.color_code || '#999'
    };
  };;

  // Get account name
  const getAccountName = (accountId: number): string => {
    const account = accounts.find(acc => acc.id === accountId);
    return account?.name || 'Unknown Account';
  };


  // Format date
  const formatDate = (dateString: string): string => {
    // Parse date string as local date to avoid timezone issues
    const [year, month, day] = dateString.split('-').map(Number);
    const date = new Date(year, month - 1, day); // month is 0-indexed
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-IN', {
        day: '2-digit',
        month: 'short',
        year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  // Get relative time for today's transactions
  const getRelativeTime = (dateString: string): string => {
    // Parse date string as local date to avoid timezone issues
    const [year, month, day] = dateString.split('-').map(Number);
    const date = new Date(year, month - 1, day); // month is 0-indexed
    const now = new Date();
    
    // Only show time if it's today
    if (date.toDateString() === now.toDateString()) {
      const diffMs = now.getTime() - date.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor(diffMs / (1000 * 60));

      if (diffMinutes < 60) {
        return diffMinutes <= 1 ? 'Just now' : `${diffMinutes}m ago`;
      } else if (diffHours < 24) {
        return `${diffHours}h ago`;
      }
    }
    
    return formatDate(dateString);
  };

  const categoryInfo = getCategoryInfo(transaction.category_id);
  
  // Get enhanced transaction type label
  const getTransactionTypeLabel = () => {
    switch (transaction.transaction_type) {
      case 'income':
        return 'Income';
      case 'expense':
        return 'Expense';
      case 'transfer':
        return 'Transfer';
        
      // Credit Card Types
      case 'credit_payment':
        return 'Credit Payment';
      case 'credit_charge':
        return 'Credit Purchase';
      case 'credit_interest':
        return 'Credit Interest';
      case 'credit_fee':
        return 'Credit Fee';
        
      // Loan Types
      case 'loan_emi':
        return 'Loan EMI';
      case 'loan_interest':
        return 'Loan Interest';
      case 'loan_principal':
        return 'Principal Payment';
      case 'loan_fee':
        return 'Loan Fee';
      case 'loan_prepayment':
        return 'Loan Prepayment';
        
      default:
        return transaction.transaction_type;
    }
  };
  
  // Check if this is an enhanced transaction type
  const isEnhancedTransactionType = () => {
    return ['credit_payment', 'credit_charge', 'credit_interest', 'credit_fee', 
            'loan_emi', 'loan_interest', 'loan_principal', 'loan_fee', 'loan_prepayment']
           .includes(transaction.transaction_type);
  };
  const getAmountColor = () => {
    switch (transaction.transaction_type) {
      case 'income':
        return theme.colors.income;
      case 'expense':
        return theme.colors.expense;
      case 'transfer':
        return theme.colors.transfer;
      
      // Credit Card Types
      case 'credit_payment':
        return '#7ED321'; // Green for credit payments (good)
      case 'credit_charge':
      case 'credit_interest':
      case 'credit_fee':
        return '#FF6B6B'; // Red for charges/fees (bad)
      
      // Loan Types  
      case 'loan_emi':
      case 'loan_principal':
      case 'loan_prepayment':
        return '#4ECDC4'; // Teal for loan payments (good)
      case 'loan_interest':
      case 'loan_fee':
        return '#F5A623'; // Orange for interest/fees (neutral)
        
      default:
        return theme.colors.text;
    }
  };

  const getAmountPrefix = () => {
    switch (transaction.transaction_type) {
      case 'income':
        return '+';
      case 'expense':
        return '-';
      case 'transfer':
        return '→';
      
      // Credit Card Types
      case 'credit_payment':
        return '💳+'; // Payment to credit card
      case 'credit_charge':
        return '💳-'; // Charge on credit card
      case 'credit_interest':
        return '📈-'; // Interest charge
      case 'credit_fee':
        return '💰-'; // Fee charge
        
      // Loan Types
      case 'loan_emi':
        return '🏦-'; // EMI payment
      case 'loan_interest':
        return '📊-'; // Interest component
      case 'loan_principal':
        return '🏠-'; // Principal component
      case 'loan_fee':
        return '📄-'; // Fee
      case 'loan_prepayment':
        return '⚡-'; // Prepayment
        
      default:
        return '';
    }
  };

  const getAccessibilityLabel = () => {
    const parts = [
      transaction.description,
      `${getAmountPrefix()}₹${transaction.amount}`,
      transaction.transaction_type,
      categoryInfo.name,
    ];
    
    if (showAccount) {
      parts.push(`Account: ${getAccountName(transaction.account_id)}`);
    }
    
    if (transaction.sms_source) {
      parts.push('From SMS');
    }
    
    return parts.join(', ');
  };

  return (
    <TouchableOpacity
      style={[styles.container, { 
        backgroundColor: theme.colors.card,
        borderBottomColor: theme.colors.borderLight,
      }]}
      onPress={() => onPress?.(transaction)}
      onLongPress={() => onLongPress?.(transaction)}
      testID={`transaction-item-${transaction.id}`}
      accessibilityLabel={getAccessibilityLabel()}
      accessibilityHint={onPress ? "Tap to edit transaction" : undefined}
      accessibilityRole="button"
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        <View style={styles.leftSection}>
          {/* Category Icon */}
          <View style={[styles.categoryIcon, { backgroundColor: categoryInfo.color + '20' }]}>
            <FallbackIcon 
              icon={categoryInfo.icon}
              name={categoryInfo.name}
              size={20}
            />
          </View>

          {/* Transaction Details */}
          <View style={styles.details}>
            <Text 
              style={[styles.description, { color: theme.colors.text }]} 
              numberOfLines={1}
              accessibilityRole="header"
            >
              {transaction.description}
            </Text>
            <View style={styles.metadata}>
              <Text style={[styles.categoryName, { color: theme.colors.textSecondary }]}>
                {categoryInfo.name}
              </Text>
              
              {/* Enhanced Transaction Type Label */}
              {isEnhancedTransactionType() && (
                <Text style={[styles.transactionTypeLabel, { 
                  color: getAmountColor(), 
                  backgroundColor: getAmountColor() + '15'
                }]}>
                  {getTransactionTypeLabel()}
                </Text>
              )}
              
              {showAccount && (
                <Text style={[styles.metadataText, { color: theme.colors.textSecondary }]}>
                  • {getAccountName(transaction.account_id)}
                </Text>
              )}
              <Text style={[styles.metadataText, { color: theme.colors.textSecondary }]}>
                • {getRelativeTime(transaction.transaction_date)}
              </Text>
              {transaction.sms_source && (
                <Text style={[styles.smsIndicator, { color: theme.colors.primary }]}>📱</Text>
              )}
            </View>
          </View>
        </View>

        {/* Amount and Status */}
        <View style={styles.rightSection}>
          <Text 
            style={[styles.amount, { color: getAmountColor() }]}
            accessibilityLabel={`${getAmountPrefix()}₹${transaction.amount}`}
          >
            {getAmountPrefix()}₹{transaction.amount.toFixed(2)}
          </Text>
          
          {/* Sync Status Indicator */}
          <View style={styles.statusContainer}>
            <SyncStatusIndicator
              status={transaction.sync_status || 'local'}
              size="sm"
              variant="dot"
              style={styles.syncStatus}
            />
            {transaction.is_recurring && (
              <Text style={styles.recurringIndicator}>🔄</Text>
            )}
          </View>
        </View>
      </View>

      {/* Low Confidence Warning */}
      {transaction.confidence_score && transaction.confidence_score < 0.8 && (
        <View style={styles.lowConfidenceBar} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    minHeight: 64, // WCAG AA touch target
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },

  details: {
    flex: 1,
  },
  description: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  metadata: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  categoryName: {
    fontSize: 14,
    marginRight: 4,
  },
  transactionTypeLabel: {
    fontSize: 12,
    fontWeight: '600',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 4,
    marginLeft: 4,
    textAlign: 'center',
    overflow: 'hidden',
  },
  metadataText: {
    fontSize: 14,
    marginRight: 4,
  },
  smsIndicator: {
    fontSize: 12,
    marginLeft: 4,
  },
  rightSection: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  amount: {
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'right',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 4,
  },
  syncStatus: {
    // Additional styling will come from the component
  },
  recurringIndicator: {
    fontSize: 10,
  },
  lowConfidenceBar: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: 3,
    height: '100%',
    backgroundColor: '#F39C12',
  },
});