import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { MaterialIcon } from '@/presentation/components/common/MaterialIcon';
// Using built-in components instead of external dependencies
import { Transaction, TransactionType } from '@/shared/types';
import { useAccountStore } from '@/shared/stores/accountStore';
import { useCategoryStore } from '@/shared/stores/categoryStore';
import { useTransactionStore } from '@/shared/stores/transactionStore';
import { CategorySuggestion } from '@/business/services/TransactionService';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';
import { CompactCategorySelector, CompactAccountSelector } from '@/presentation/components/common/CompactSelectors';
import { FormInput, AmountInput, FormDateTimePicker } from '@/presentation/components/common';
import { CategoryCreationForm } from '@/presentation/components/categories/CategoryCreationForm';
import { AccountCreationForm } from '@/presentation/components/accounts/AccountCreationForm';

interface TransactionEntryFormProps {
  initialTransaction?: Partial<Transaction>;
  onSuccess?: (transaction: Transaction) => void;
  onCancel?: () => void;
  isEditing?: boolean;
}

export const TransactionEntryForm: React.FC<TransactionEntryFormProps> = ({
  initialTransaction,
  onSuccess,
  onCancel,
  isEditing = false,
}) => {
  const theme = useTheme();
  
  // Form state
  const [amount, setAmount] = useState(initialTransaction?.amount?.toString() || '');
  const [description, setDescription] = useState(initialTransaction?.description || '');
  const [selectedAccountId, setSelectedAccountId] = useState<number | null>(
    initialTransaction?.account_id || null
  );
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(
    initialTransaction?.category_id || null
  );
  
  // Transfer-specific state
  const [destinationAccountId, setDestinationAccountId] = useState<number | 'external' | null>(null);
  const [transferError, setTransferError] = useState('');
  // Map enhanced transaction types to simple form types
  const mapTransactionTypeToFormType = (type?: TransactionType): TransactionType => {
    if (!type) return 'expense';
    return type;
  };;

  const [transactionType, setTransactionType] = useState<TransactionType>(
    mapTransactionTypeToFormType(initialTransaction?.transaction_type)
  );
  const [transactionDate, setTransactionDate] = useState(
    initialTransaction?.transaction_date 
      ? (() => {
          // Parse date string as local date to avoid timezone issues
          const [year, month, day] = initialTransaction.transaction_date.split('-').map(Number);
          return new Date(year, month - 1, day); // month is 0-indexed
        })()
      : new Date()
  );
  const [dateError, setDateError] = useState('');

  // Validation errors
  const [amountError, setAmountError] = useState('');
  const [descriptionError, setDescriptionError] = useState('');
  const [accountError, setAccountError] = useState('');

  // Loading and suggestions
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [categorySuggestions, setCategorySuggestions] = useState<CategorySuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Category creation
  const [showCategoryCreation, setShowCategoryCreation] = useState(false);

  // Account creation
  const [showAccountCreation, setShowAccountCreation] = useState(false);

  // Info tooltip state
  const [showInfoTooltip, setShowInfoTooltip] = useState(false);

  const { accounts } = useAccountStore();
  const { categories, loadCategories, getCategoriesByType } = useCategoryStore();
  const { createTransaction, updateTransaction } = useTransactionStore();

  const getFilteredCategories = () => {
    return getCategoriesByType(transactionType === 'income' ? 'income' : 'expense');
  };

  // Get available transaction types based on selected account
  const getAvailableTransactionTypes = (): { type: TransactionType; label: string; category: string; icon: string; color: string; description: string }[] => {
    const selectedAccount = accounts.find(acc => acc.id === selectedAccountId);
    
    const baseTypes = [
      { type: 'income' as TransactionType, label: 'Income', category: 'General', icon: 'trending-up', color: '#4CAF50', description: 'Money coming in' },
      { type: 'expense' as TransactionType, label: 'Expense', category: 'General', icon: 'trending-down', color: '#F44336', description: 'Money going out' },
      { type: 'transfer' as TransactionType, label: 'Transfer', category: 'General', icon: 'swap-horizontal', color: '#2196F3', description: 'Move between accounts' },
    ];

    if (!selectedAccount) return baseTypes;

    const enhancedTypes = [];
    
    if (selectedAccount.type === 'credit') {
      enhancedTypes.push(
        { type: 'credit_payment' as TransactionType, label: 'Payment', category: 'Credit Card', icon: 'card', color: '#4CAF50', description: 'Payment to credit card' },
        { type: 'credit_charge' as TransactionType, label: 'Purchase', category: 'Credit Card', icon: 'bag', color: '#FF9800', description: 'Credit card purchase' },
        { type: 'credit_interest' as TransactionType, label: 'Interest', category: 'Credit Card', icon: 'trending-up', color: '#F44336', description: 'Interest charges' },
        { type: 'credit_fee' as TransactionType, label: 'Fee', category: 'Credit Card', icon: 'cash', color: '#F44336', description: 'Credit card fees' }
      );
    } else if (selectedAccount.type === 'loan') {
      enhancedTypes.push(
        { type: 'loan_emi' as TransactionType, label: 'EMI Payment', category: 'Loan', icon: 'home', color: '#00BCD4', description: 'Monthly EMI payment' },
        { type: 'loan_interest' as TransactionType, label: 'Interest', category: 'Loan', icon: 'analytics', color: '#FF9800', description: 'Interest component' },
        { type: 'loan_principal' as TransactionType, label: 'Principal', category: 'Loan', icon: 'wallet', color: '#4CAF50', description: 'Principal repayment' },
        { type: 'loan_fee' as TransactionType, label: 'Fee', category: 'Loan', icon: 'receipt', color: '#F44336', description: 'Processing fees' },
        { type: 'loan_prepayment' as TransactionType, label: 'Prepayment', category: 'Loan', icon: 'flash', color: '#9C27B0', description: 'Extra payment' }
      );
    }

    return [...baseTypes, ...enhancedTypes];
  };

  // Load categories on component mount - FIXED: Only run once
  useEffect(() => {
    const initializeCategories = async () => {
      try {
        if (categories.length === 0) {
          // First try to load existing categories
          await loadCategories();
          
          // If still no categories after loading, initialize system categories
          const { categories: currentCategories } = useCategoryStore.getState();
          if (currentCategories.length === 0) {
            const { initializeSystemCategories } = useCategoryStore.getState();
            await initializeSystemCategories();
          }
        }
      } catch (error) {
        console.error('Failed to initialize categories:', error);
      }
    };

    initializeCategories();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const validateAmount = (value: string): boolean => {
    setAmountError('');
    
    if (!value.trim()) {
      setAmountError('Amount is required');
      return false;
    }
    
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) {
      setAmountError('Please enter a valid number');
      return false;
    }
    
    if (numericValue <= 0) {
      setAmountError('Amount must be greater than 0');
      return false;
    }
    
    if (numericValue > 999999999.99) {
      setAmountError('Amount is too large');
      return false;
    }
    
    return true;
  };

  const validateDescription = (value: string): boolean => {
    setDescriptionError('');
    
    if (!value.trim()) {
      setDescriptionError('Description is required');
      return false;
    }
    
    if (value.trim().length < 3) {
      setDescriptionError('Description must be at least 3 characters');
      return false;
    }
    
    if (value.trim().length > 200) {
      setDescriptionError('Description must be less than 200 characters');
      return false;
    }
    
    return true;
  };

  const validateAccount = (): boolean => {
    setAccountError('');
    
    if (!selectedAccountId) {
      setAccountError('Please select an account');
      return false;
    }
    
    const account = accounts.find(acc => acc.id === selectedAccountId);
    if (!account) {
      setAccountError('Selected account not found');
      return false;
    }
    
    if (!account.is_active) {
      setAccountError('Cannot use inactive account');
      return false;
    }
    
    return true;
  };

  const validateTransfer = (): boolean => {
    setTransferError('');
    
    if (transactionType !== 'transfer') {
      return true; // Not a transfer, no validation needed
    }
    
    if (!destinationAccountId) {
      setTransferError('Please select destination account');
      return false;
    }
    
    if (destinationAccountId !== 'external' && destinationAccountId === selectedAccountId) {
      setTransferError('Source and destination accounts cannot be the same');
      return false;
    }
    
    if (destinationAccountId !== 'external') {
      const destAccount = accounts.find(acc => acc.id === destinationAccountId);
      if (!destAccount) {
        setTransferError('Destination account not found');
        return false;
      }
      
      if (!destAccount.is_active) {
        setTransferError('Cannot transfer to inactive account');
        return false;
      }
    }
    
    return true;
  };

  const handleAmountChange = (value: string) => {
    // Allow only numbers and decimal point
    const cleanValue = value.replace(/[^0-9.]/g, '');
    
    // Prevent multiple decimal points
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      return;
    }
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
      return;
    }
    
    setAmount(cleanValue);
    if (amountError) {
      validateAmount(cleanValue);
    }
  };

  const handleDescriptionChange = (value: string) => {
    setDescription(value);
    if (descriptionError) {
      validateDescription(value);
    }
    
    // Trigger category suggestions
    if (value.length >= 3) {
      generateCategorySuggestions(value);
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const generateCategorySuggestions = (desc: string) => {
    // Simple keyword matching for demo
    const suggestions: CategorySuggestion[] = [];
    const descLower = desc.toLowerCase();
    
    const filteredCategories = getFilteredCategories();
    
    for (const category of filteredCategories) {
      const categoryWords = category.name.toLowerCase().split(' ');
      const descWords = descLower.split(' ');
      
      let matchScore = 0;
      for (const descWord of descWords) {
        if (descWord.length > 2) {
          for (const catWord of categoryWords) {
            if (catWord.includes(descWord) || descWord.includes(catWord)) {
              matchScore++;
              break;
            }
          }
        }
      }
      
      // Simple keyword patterns
      if (descLower.includes('food') || descLower.includes('restaurant') || descLower.includes('meal')) {
        if (category.name === 'Food & Dining') matchScore += 2;
      }
      if (descLower.includes('uber') || descLower.includes('taxi') || descLower.includes('gas')) {
        if (category.name === 'Transportation') matchScore += 2;
      }
      if (descLower.includes('shop') || descLower.includes('store') || descLower.includes('buy')) {
        if (category.name === 'Shopping') matchScore += 2;
      }
      if (descLower.includes('bill') || descLower.includes('electric') || descLower.includes('water')) {
        if (category.name === 'Bills & Utilities') matchScore += 2;
      }
      if (descLower.includes('salary') || descLower.includes('pay')) {
        if (category.name === 'Salary') matchScore += 2;
      }
      
      if (matchScore > 0) {
        suggestions.push({
          categoryId: category.id,
          categoryName: category.name,
          confidence: Math.min(0.9, matchScore * 0.3),
          reason: 'keyword_match'
        });
      }
    }
    
    // Sort by confidence and take top 3
    setCategorySuggestions(
      suggestions.sort((a, b) => b.confidence - a.confidence).slice(0, 3)
    );
  };

  const handleCategorySuggestionSelect = (suggestion: CategorySuggestion) => {
    setSelectedCategoryId(suggestion.categoryId);
    setShowSuggestions(false);
  };

  const handleCreateNewCategory = () => {
    setShowCategoryCreation(true);
  };

  const handleCreateNewAccount = () => {
    setShowAccountCreation(true);
  };

  const handleCategoryCreated = (newCategory: any) => {
    setSelectedCategoryId(newCategory.id);
    // Force reload of categories to include the new one
    loadCategories().catch(error => {
      console.error('Failed to reload categories:', error);
    });
  };

  const handleAccountCreated = (newAccount: any) => {
    setSelectedAccountId(newAccount.id);
    // The account store should automatically refresh, but let's ensure the UI reflects the change
    const { refreshAccounts } = useAccountStore.getState();
    refreshAccounts();
  };

  // Date change handler removed - date picker not implemented

  const handleSubmit = async () => {
    if (isSubmitting) return;
    
    // Validate all fields
    const isAmountValid = validateAmount(amount);
    const isDescriptionValid = validateDescription(description);
    const isAccountValid = validateAccount();
    const isTransferValid = validateTransfer();
    
    if (!isAmountValid || !isDescriptionValid || !isAccountValid || !isTransferValid) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      let transactionData = {
        account_id: selectedAccountId!,
        amount: parseFloat(amount),
        description: description.trim(),
        category_id: selectedCategoryId,
        transaction_type: transactionType,
        transaction_date: transactionDate.toISOString().split('T')[0],
        sms_source: null,
        confidence_score: null,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local' as const,
        hash: ''
      };

      // Add destination account for transfers
      if (transactionType === 'transfer' && destinationAccountId) {
        transactionData = {
          ...transactionData,
          // Store destination info in description for now
          description: destinationAccountId === 'external' 
            ? `${description.trim()} (Out of wallet)`
            : `${description.trim()} → ${accounts.find(acc => acc.id === destinationAccountId)?.name || 'Unknown'}`,
          // You might want to add metadata or custom fields for destination_account_id
        };
      }
      
      let resultTransaction: Transaction;
      
      if (isEditing && initialTransaction?.id) {
        // Update existing transaction
        resultTransaction = await updateTransaction(initialTransaction.id, transactionData);
      } else {
        // Create new transaction
        resultTransaction = await createTransaction(transactionData);
      }
      
      // Call onSuccess callback - let parent component handle the success message
      onSuccess?.(resultTransaction);
      
      // Reset form if creating new transaction
      if (!isEditing) {
        setAmount('');
        setDescription('');
        setSelectedCategoryId(null);
        setTransactionDate(new Date());
        setAmountError('');
        setDescriptionError('');
        setAccountError('');
        setDateError('');
      }
      
    } catch (error) {
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to save transaction'
      );
    } finally {
      setIsSubmitting(false);
    }
  };


  const styles = createTransactionEntryStyles(theme);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {isEditing ? 'Edit Transaction' : 'Add Transaction'}
        </Text>
        <Text style={styles.subtitle}>
          {isEditing ? 'Update transaction details' : 'Record your spending and income'}
        </Text>
      </View>

      {/* Transaction Type Selection */}
      <View style={styles.section}>
        <View style={styles.labelWithInfo}>
          <Text style={styles.label}>Transaction Type</Text>
          <TouchableOpacity
            style={styles.infoIconContainer}
            onPress={() => setShowInfoTooltip(!showInfoTooltip)}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <MaterialIcon 
              name="info-outline" 
              size={16} 
              color={theme.colors.textSecondary} 
            />
          </TouchableOpacity>
        </View>
        <View style={styles.transactionTypeContainer}>
          {getAvailableTransactionTypes().map((typeOption, index) => {
            const isSelected = transactionType === typeOption.type;
            return (
              <TouchableOpacity
                key={typeOption.type}
                style={[
                  styles.transactionTypeButton,
                  isSelected && styles.transactionTypeButtonActive,
                  isSelected && { borderColor: typeOption.color },
                  typeOption.category === 'Credit Card' && styles.creditCardType,
                  typeOption.category === 'Loan' && styles.loanType,
                ]}
                onPress={() => {
                  setTransactionType(typeOption.type);
                  setSelectedCategoryId(null);
                  setShowSuggestions(false);
                }}
              >
                <View style={styles.transactionTypeHeader}>
                  <MaterialIcon 
                    name={typeOption.icon}
                    size={24}
                    color={isSelected ? typeOption.color : theme.colors.textSecondary}
                    style={styles.transactionTypeIcon}
                  />
                  <View style={styles.transactionTypeInfo}>
                    <Text style={[
                      styles.transactionTypeLabel,
                      isSelected && { color: typeOption.color }
                    ]}>
                      {typeOption.label}
                    </Text>
{typeOption.category !== 'General' && (
                      <Text style={[
                        styles.transactionTypeCategory,
                        isSelected && styles.transactionTypeCategoryActive
                      ]}>
                        {typeOption.category}
                      </Text>
                    )}
                  </View>
                </View>
                <Text style={[
                  styles.transactionTypeDescription,
                  isSelected && { color: typeOption.color }
                ]}>
                  {typeOption.description}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
        {showInfoTooltip && (
          <View style={styles.infoTooltip}>
            <Text style={styles.infoTooltipText}>
              Select an account first to see account-specific transaction types
            </Text>
          </View>
        )}
      </View>

      {/* Amount Input */}
      <AmountInput
        label="Amount"
        required
        value={amount}
        onChangeText={handleAmountChange}
        placeholder="0.00"
        onBlur={() => validateAmount(amount)}
        error={amountError}
        amountType={transactionType === 'income' ? 'income' : 'expense'}
        containerStyle={styles.section}
      />

      {/* Description Input */}
      <FormInput
        label="Description"
        required
        value={description}
        onChangeText={handleDescriptionChange}
        placeholder="e.g., Lunch at restaurant, Grocery shopping"
        maxLength={200}
        onBlur={() => {
          validateDescription(description);
          setShowSuggestions(false);
        }}
        onFocus={() => {
          if (description.length >= 3) {
            setShowSuggestions(true);
          }
        }}
        error={descriptionError}
        containerStyle={[styles.section, { marginBottom: showSuggestions && categorySuggestions.length > 0 ? theme.spacing.sm : theme.spacing.md }]}
      />
        
        {/* Category Suggestions */}
        {showSuggestions && categorySuggestions.length > 0 && (
          <View style={styles.suggestionsContainer}>
            <Text style={styles.suggestionsLabel}>Suggested Categories:</Text>
            {categorySuggestions.map((suggestion, index) => (
              <TouchableOpacity
                key={index}
                style={styles.suggestionItem}
                onPress={() => handleCategorySuggestionSelect(suggestion)}
              >
                <Text style={styles.suggestionText}>{suggestion.categoryName}</Text>
                <Text style={styles.suggestionConfidence}>
                  {Math.round(suggestion.confidence * 100)}% match
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

      {/* Account Selection */}
      <View style={styles.section}>
        <Text style={styles.label}>Account *</Text>
        <CompactAccountSelector
          accounts={accounts.filter(acc => acc.is_active)}
          selectedAccountId={selectedAccountId}
          onSelect={(accountId) => {
            setSelectedAccountId(accountId);
            if (accountError) {
              validateAccount();
            }
          }}
          onCreateNew={handleCreateNewAccount}
        />
        {accountError ? <Text style={styles.errorText}>{accountError}</Text> : null}
      </View>

      {/* Transfer Destination (only shown for transfers) */}
      {transactionType === 'transfer' && (
        <View style={styles.section}>
          <Text style={styles.label}>Transfer To *</Text>
          <View style={styles.transferDestinationContainer}>
            {/* Internal accounts */}
            {accounts
              .filter(acc => acc.is_active && acc.id !== selectedAccountId)
              .map((account) => (
                <TouchableOpacity
                  key={account.id}
                  style={[
                    styles.transferDestinationOption,
                    destinationAccountId === account.id && styles.transferDestinationSelected,
                  ]}
                  onPress={() => {
                    setDestinationAccountId(account.id);
                    if (transferError) {
                      validateTransfer();
                    }
                  }}
                >
                  <View style={styles.transferDestinationInfo}>
                    <Text style={[
                      styles.transferDestinationName,
                      destinationAccountId === account.id && styles.transferDestinationNameSelected
                    ]}>
                      {account.name}
                    </Text>
                    <Text style={styles.transferDestinationType}>
                      {account.type.charAt(0).toUpperCase() + account.type.slice(1)}
                    </Text>
                  </View>
                  {destinationAccountId === account.id && (
                    <Text style={styles.selectedIndicator}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}
            
            {/* External transfer option */}
            <TouchableOpacity
              style={[
                styles.transferDestinationOption,
                styles.externalTransferOption,
                destinationAccountId === 'external' && styles.transferDestinationSelected,
              ]}
              onPress={() => {
                setDestinationAccountId('external');
                if (transferError) {
                  validateTransfer();
                }
              }}
            >
              <View style={styles.transferDestinationInfo}>
                <Text style={[
                  styles.transferDestinationName,
                  destinationAccountId === 'external' && styles.transferDestinationNameSelected
                ]}>
                  Out of Wallet
                </Text>
                <Text style={styles.transferDestinationType}>
                  External Account
                </Text>
              </View>
              {destinationAccountId === 'external' && (
                <Text style={styles.selectedIndicator}>✓</Text>
              )}
            </TouchableOpacity>
          </View>
          {transferError ? <Text style={styles.errorText}>{transferError}</Text> : null}
          <Text style={styles.helpText}>
            Select where the money is being transferred to
          </Text>
        </View>
      )}

      {/* Category Selection */}
      <View style={styles.section}>
        <Text style={styles.label}>Category</Text>
        <CompactCategorySelector
          categories={getFilteredCategories()}
          selectedCategoryId={selectedCategoryId}
          onSelect={setSelectedCategoryId}
          onCreateNew={handleCreateNewCategory}
        />
      </View>

      {/* Date Selection */}
      <FormDateTimePicker
        label="Date"
        required
        value={transactionDate}
        onChange={(date) => {
          setTransactionDate(date);
          setDateError('');
        }}
        mode="date"
        maximumDate={new Date()}
        error={dateError}
        containerStyle={styles.section}
      />

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onCancel}
          disabled={isSubmitting}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.submitButton,
            { backgroundColor: transactionType === 'income' ? theme.colors.success : theme.colors.error },
            (!amount || !description || !selectedAccountId || (transactionType === 'transfer' && !destinationAccountId) || isSubmitting) && styles.submitButtonDisabled
          ]}
          onPress={handleSubmit}
          disabled={!amount || !description || !selectedAccountId || (transactionType === 'transfer' && !destinationAccountId) || isSubmitting}
          testID="submit-button"
        >
          {isSubmitting ? (
            <ActivityIndicator color={theme.colors.background} size="small" />
          ) : (
            <Text style={styles.submitButtonText}>
              {isEditing ? 'Update Transaction' : 'Add Transaction'}
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Category Creation Modal */}
      <CategoryCreationForm
        visible={showCategoryCreation}
        onClose={() => setShowCategoryCreation(false)}
        onSuccess={handleCategoryCreated}
        categoryType={transactionType === 'income' ? 'income' : 'expense'}
      />

      {/* Account Creation Modal */}
      <Modal
        visible={showAccountCreation}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <AccountCreationForm
          onSuccess={(newAccount) => {
            handleAccountCreated(newAccount);
            setShowAccountCreation(false);
          }}
          onCancel={() => setShowAccountCreation(false)}
        />
      </Modal>
    </ScrollView>
  );
};

// Component-specific themed styles
const createTransactionEntryStyles = createStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  header: {
    marginBottom: theme.spacing.md,
  },
  title: {
    ...theme.typography.h1,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    lineHeight: 22,
  },
  section: {
    marginBottom: theme.spacing.md,
  },
  label: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    ...theme.typography.body,
    backgroundColor: theme.colors.card,
    color: theme.colors.text,
    lineHeight: theme.typography.body.lineHeight || 20,
    textAlignVertical: 'top', // Changed to top to fix clipping
    minHeight: 48, // Standard input height
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  errorText: {
    color: theme.colors.error,
    ...theme.typography.caption,
    marginTop: theme.spacing.xs,
  },
  typeToggle: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.xs,
  },
  typeButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.sm,
    alignItems: 'center',
  },
  expenseButton: {
    marginRight: theme.spacing.xs,
  },
  incomeButton: {
    marginLeft: theme.spacing.xs,
  },
  typeButtonActive: {
    backgroundColor: theme.colors.card,
    ...theme.shadows.sm,
  },
  typeButtonText: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    fontWeight: '600',
  },
  typeButtonTextActive: {
    color: theme.colors.text,
  },
  // Enhanced transaction type styles
  transactionTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
    justifyContent: 'center', // Center align the transaction type cards
  },
  transactionTypeButton: {
    borderWidth: 2,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.card,
    minWidth: '48%',
    flexGrow: 1,
    minHeight: 75, // Reduced height to save space
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  transactionTypeButtonActive: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.surface,
    ...theme.shadows.md,
  },
  transactionTypeHeader: {
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
    width: '100%',
  },
  transactionTypeIcon: {
    marginBottom: theme.spacing.xs,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  transactionTypeInfo: {
    flex: 1,
    alignItems: 'center',
    width: '100%',
  },
  transactionTypeDescription: {
    ...theme.typography.caption,
    fontSize: 11,
    color: theme.colors.textSecondary,
    lineHeight: 14,
    textAlign: 'center',
    paddingHorizontal: theme.spacing.xs,
  },
  creditCardType: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B6B',
  },
  loanType: {
    borderLeftWidth: 4,
    borderLeftColor: '#4ECDC4',
  },
  transactionTypeLabel: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
    textAlign: 'center',
    fontSize: 15,
  },
  transactionTypeLabelActive: {
    color: theme.colors.primary,
  },
  transactionTypeCategory: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    fontSize: 11,
    textAlign: 'center',
    marginBottom: 2,
  },
  transactionTypeCategoryActive: {
    color: theme.colors.primary,
  },
  helpText: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.card,
    minHeight: 48, // Standard height
  },
  currencySymbol: {
    ...theme.typography.h3,
    fontWeight: 'bold',
    paddingLeft: theme.spacing.md,
    paddingRight: theme.spacing.xs,
    color: theme.colors.primary,
    lineHeight: theme.typography.h3.lineHeight,
    textAlignVertical: 'center',
  },
  amountInput: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    paddingRight: theme.spacing.md,
    paddingLeft: 0,
    ...theme.typography.h3,
    fontWeight: '600',
    borderWidth: 0,
    color: theme.colors.text,
    lineHeight: theme.typography.h3.lineHeight,
    textAlignVertical: 'center',
  },
  accountList: {
    gap: theme.spacing.sm,
  },
  accountOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.card,
  },
  accountOptionSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.surface,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
  },
  accountBalance: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  selectedIndicator: {
    fontSize: 18,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  dateButton: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.card,
  },
  dateButtonText: {
    ...theme.typography.body,
    color: theme.colors.text,
  },
  suggestionsContainer: {
    marginTop: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
  },
  suggestionsLabel: {
    ...theme.typography.caption,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  suggestionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.xs,
  },
  suggestionText: {
    ...theme.typography.caption,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  suggestionConfidence: {
    ...theme.typography.caption,
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.xxl + theme.spacing.xl,
  },
  cancelButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
    backgroundColor: theme.colors.card,
  },
  cancelButtonText: {
    ...theme.typography.button,
    color: theme.colors.textSecondary,
  },
  submitButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  submitButtonDisabled: {
    backgroundColor: theme.colors.textDisabled,
  },
  submitButtonText: {
    ...theme.typography.button,
    color: theme.colors.background,
  },
  dateHint: {
    ...theme.typography.caption,
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
    fontStyle: 'italic',
  },
  // Transfer destination styles
  transferDestinationContainer: {
    gap: theme.spacing.sm,
  },
  transferDestinationOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.card,
    marginBottom: theme.spacing.xs,
  },
  transferDestinationSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.surface,
  },
  transferDestinationInfo: {
    flex: 1,
  },
  transferDestinationName: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
  },
  transferDestinationNameSelected: {
    color: theme.colors.primary,
  },
  transferDestinationType: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  externalTransferOption: {
    borderStyle: 'dashed',
    borderWidth: 2,
    borderColor: theme.colors.textSecondary,
  },
  // Info icon and tooltip styles
  labelWithInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  infoIconContainer: {
    marginLeft: theme.spacing.xs,
    padding: theme.spacing.xs,
  },
  infoTooltip: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginTop: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  infoTooltipText: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
    lineHeight: 16,
  },
}));