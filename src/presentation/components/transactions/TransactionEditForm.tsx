import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import { Transaction, Account, Category, TransactionType } from '@/shared/types';
import { TransactionService } from '@/business/services/TransactionService';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';
import { CompactCategorySelector, CompactAccountSelector } from '@/presentation/components/common/CompactSelectors';

interface TransactionEditFormProps {
  transaction: Transaction;
  visible: boolean;
  onSave: (updatedTransaction: Transaction) => void;
  onCancel: () => void;
  onDelete?: (transaction: Transaction) => void;
  accounts?: Account[];
  categories?: Category[];
}

export const TransactionEditForm: React.FC<TransactionEditFormProps> = ({
  transaction,
  visible,
  onSave,
  onCancel,
  onDelete,
  accounts = [],
  categories = [],
}) => {
  const theme = useTheme();
  // Form state
  const [amount, setAmount] = useState(transaction.amount.toString());
  const [description, setDescription] = useState(transaction.description);
  const [selectedAccountId, setSelectedAccountId] = useState(transaction.account_id);
  const [selectedCategoryId, setSelectedCategoryId] = useState(transaction.category_id);
  // Map enhanced transaction types to simple form types
  const mapTransactionTypeToFormType = (type: TransactionType): 'income' | 'expense' => {
    switch (type) {
      case 'income':
        return 'income';
      case 'credit_payment':
        return 'income'; // Credit card payments are money going out from user's account
      case 'expense':
      case 'transfer':
      case 'credit_charge':
      case 'credit_interest':
      case 'credit_fee':
      case 'loan_emi':
      case 'loan_interest':
      case 'loan_principal':
      case 'loan_fee':
      case 'loan_prepayment':
        return 'expense';
      default:
        return 'expense';
    }
  };

  const [transactionType, setTransactionType] = useState<'income' | 'expense'>(
    mapTransactionTypeToFormType(transaction.transaction_type)
  );
  const [transactionDate, setTransactionDate] = useState(transaction.transaction_date);

  // Validation states
  const [amountError, setAmountError] = useState('');
  const [descriptionError, setDescriptionError] = useState('');
  const [accountError, setAccountError] = useState('');

  // Loading and confirmation states
  const [isSaving, setIsSaving] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Services
  const transactionService = new TransactionService();
  
  // Styles
  const styles = createTransactionEditStyles(theme);

  // Reset form when transaction changes
  useEffect(() => {
    setAmount(transaction.amount.toString());
    setDescription(transaction.description);
    setSelectedAccountId(transaction.account_id);
    setSelectedCategoryId(transaction.category_id);
    setTransactionType(
      mapTransactionTypeToFormType(transaction.transaction_type)
    );
    setTransactionDate(transaction.transaction_date);
    
    // Clear errors
    setAmountError('');
    setDescriptionError('');
    setAccountError('');
  }, [transaction]);

  // Get filtered categories based on transaction type
  const getFilteredCategories = () => {
    return categories.filter(cat => cat.category_type === transactionType);
  };

  // Utility functions removed - not used in current implementation

  // Validation functions
  const validateAmount = (value: string): boolean => {
    setAmountError('');
    
    if (!value.trim()) {
      setAmountError('Amount is required');
      return false;
    }
    
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) {
      setAmountError('Please enter a valid number');
      return false;
    }
    
    if (numericValue <= 0) {
      setAmountError('Amount must be greater than 0');
      return false;
    }
    
    if (numericValue > *********.99) {
      setAmountError('Amount is too large');
      return false;
    }
    
    return true;
  };

  const validateDescription = (value: string): boolean => {
    setDescriptionError('');
    
    if (!value.trim()) {
      setDescriptionError('Description is required');
      return false;
    }
    
    if (value.trim().length < 3) {
      setDescriptionError('Description must be at least 3 characters');
      return false;
    }
    
    if (value.trim().length > 200) {
      setDescriptionError('Description must be less than 200 characters');
      return false;
    }
    
    return true;
  };

  const validateAccount = (): boolean => {
    setAccountError('');
    
    if (!selectedAccountId) {
      setAccountError('Please select an account');
      return false;
    }
    
    const account = accounts.find(acc => acc.id === selectedAccountId);
    if (!account) {
      setAccountError('Selected account not found');
      return false;
    }
    
    if (!account.is_active) {
      setAccountError('Cannot use inactive account');
      return false;
    }
    
    return true;
  };

  // Handle amount change
  const handleAmountChange = (value: string) => {
    // Allow only numbers and decimal point
    const cleanValue = value.replace(/[^0-9.]/g, '');
    
    // Prevent multiple decimal points
    const parts = cleanValue.split('.');
    if (parts.length > 2) return;
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) return;
    
    setAmount(cleanValue);
    if (amountError) {
      validateAmount(cleanValue);
    }
  };

  // Handle description change
  const handleDescriptionChange = (value: string) => {
    setDescription(value);
    if (descriptionError) {
      validateDescription(value);
    }
  };

  // Handle transaction type change
  const handleTransactionTypeChange = (type: 'income' | 'expense') => {
    setTransactionType(type);
    // Reset category when type changes
    setSelectedCategoryId(null);
  };

  // Handle save
  const handleSave = async () => {
    if (isSaving) return;
    
    // Validate all fields
    const isAmountValid = validateAmount(amount);
    const isDescriptionValid = validateDescription(description);
    const isAccountValid = validateAccount();
    
    if (!isAmountValid || !isDescriptionValid || !isAccountValid) {
      return;
    }
    
    // Check if anything changed
    const hasChanges = (
      parseFloat(amount) !== transaction.amount ||
      description.trim() !== transaction.description ||
      selectedAccountId !== transaction.account_id ||
      selectedCategoryId !== transaction.category_id ||
      transactionType !== transaction.transaction_type ||
      transactionDate !== transaction.transaction_date
    );

    if (!hasChanges) {
      Alert.alert('No Changes', 'No changes were made to the transaction.');
      return;
    }
    
    setIsSaving(true);
    
    try {
      const updatedData = {
        amount: parseFloat(amount),
        description: description.trim(),
        account_id: selectedAccountId,
        category_id: selectedCategoryId,
        transaction_type: transactionType,
        transaction_date: transactionDate,
        sync_status: 'local' as const
      };
      
      const updatedTransaction = await transactionService.updateTransaction(
        transaction.id,
        updatedData
      );
      
      Alert.alert(
        'Success',
        'Transaction updated successfully!',
        [{ text: 'OK', onPress: () => onSave(updatedTransaction) }]
      );
      
    } catch (error) {
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to update transaction'
      );
    } finally {
      setIsSaving(false);
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    setShowDeleteConfirm(true);
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      await transactionService.deleteTransaction(transaction.id);
      
      setShowDeleteConfirm(false);
      
      Alert.alert(
        'Success',
        'Transaction deleted successfully!',
        [{ text: 'OK', onPress: () => onDelete?.(transaction) }]
      );
      
    } catch (error) {
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to delete transaction'
      );
    }
  };

  // Handle date formatting for display
  const formatDateForDisplay = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <View style={styles.container}>
        <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.title}>Edit Transaction</Text>
            <TouchableOpacity
              style={[styles.saveButton, isSaving && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.saveButtonText}>Save</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Transaction Type Toggle */}
          <View style={styles.section}>
            <Text style={styles.label}>Transaction Type</Text>
            <View style={styles.typeToggle}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  styles.expenseButton,
                  transactionType === 'expense' && styles.typeButtonActive
                ]}
                onPress={() => handleTransactionTypeChange('expense')}
              >
                <Text style={[
                  styles.typeButtonText,
                  transactionType === 'expense' && styles.typeButtonTextActive
                ]}>
                  💸 Expense
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  styles.incomeButton,
                  transactionType === 'income' && styles.typeButtonActive
                ]}
                onPress={() => handleTransactionTypeChange('income')}
              >
                <Text style={[
                  styles.typeButtonText,
                  transactionType === 'income' && styles.typeButtonTextActive
                ]}>
                  💰 Income
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Amount Input */}
          <View style={styles.section}>
            <Text style={styles.label}>Amount *</Text>
            <View style={[styles.amountContainer, amountError ? styles.inputError : null]}>
              <Text style={[
                styles.currencySymbol,
                { color: transactionType === 'income' ? theme.colors.success : theme.colors.error }
              ]}>
                ₹
              </Text>
              <TextInput
                style={styles.amountInput}
                value={amount}
                onChangeText={handleAmountChange}
                placeholder="0.00"
                placeholderTextColor={theme.colors.textDisabled}
                keyboardType="decimal-pad"
                onBlur={() => validateAmount(amount)}
              />
            </View>
            {amountError ? <Text style={styles.errorText}>{amountError}</Text> : null}
          </View>

          {/* Description Input */}
          <View style={styles.section}>
            <Text style={styles.label}>Description *</Text>
            <TextInput
              style={[styles.input, descriptionError ? styles.inputError : null]}
              value={description}
              onChangeText={handleDescriptionChange}
              placeholder="e.g., Lunch at restaurant, Grocery shopping"
              placeholderTextColor={theme.colors.textDisabled}
              maxLength={200}
              onBlur={() => validateDescription(description)}
            />
            {descriptionError ? <Text style={styles.errorText}>{descriptionError}</Text> : null}
          </View>

          {/* Account Selection */}
          <View style={styles.section}>
            <Text style={styles.label}>Account *</Text>
            <CompactAccountSelector
              accounts={accounts.filter(acc => acc.is_active)}
              selectedAccountId={selectedAccountId}
              onSelect={(accountId) => {
                setSelectedAccountId(accountId);
                if (accountError) {
                  validateAccount();
                }
              }}
            />
            {accountError ? <Text style={styles.errorText}>{accountError}</Text> : null}
          </View>

          {/* Category Selection */}
          <View style={styles.section}>
            <Text style={styles.label}>Category</Text>
            <CompactCategorySelector
              categories={getFilteredCategories()}
              selectedCategoryId={selectedCategoryId}
              onSelect={setSelectedCategoryId}
            />
          </View>

          {/* Date Display */}
          <View style={styles.section}>
            <Text style={styles.label}>Date</Text>
            <View style={styles.dateDisplay}>
              <Text style={styles.dateText}>📅 {formatDateForDisplay(transactionDate)}</Text>
              <Text style={styles.dateHelper}>
                Transaction date cannot be changed after creation
              </Text>
            </View>
          </View>

          {/* Transaction Metadata */}
          <View style={styles.section}>
            <Text style={styles.label}>Transaction Details</Text>
            <View style={styles.metadataContainer}>
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>ID:</Text>
                <Text style={styles.metadataValue}>{transaction.id}</Text>
              </View>
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>Created:</Text>
                <Text style={styles.metadataValue}>
                  {new Date(transaction.created_at).toLocaleString('en-IN')}
                </Text>
              </View>
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>Last Updated:</Text>
                <Text style={styles.metadataValue}>
                  {new Date(transaction.updated_at).toLocaleString('en-IN')}
                </Text>
              </View>
              {transaction.sms_source && (
                <View style={styles.metadataRow}>
                  <Text style={styles.metadataLabel}>Source:</Text>
                  <Text style={styles.metadataValue}>SMS</Text>
                </View>
              )}
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>Status:</Text>
                <Text style={[styles.metadataValue, styles.statusValue]}>
                  {transaction.sync_status.toUpperCase()}
                </Text>
              </View>
            </View>
          </View>

          {/* Delete Button */}
          {onDelete && (
            <View style={styles.deleteSection}>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={handleDeleteConfirm}
                testID="delete-button"
              >
                <Text style={styles.deleteButtonText}>🗑️ Delete Transaction</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>

        {/* Delete Confirmation Modal */}
        <Modal
          visible={showDeleteConfirm}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDeleteConfirm(false)}
        >
          <View style={styles.deleteConfirmOverlay}>
            <View style={styles.deleteConfirmModal}>
              <Text style={styles.deleteConfirmTitle}>Delete Transaction?</Text>
              <Text style={styles.deleteConfirmMessage}>
                This action cannot be undone. The transaction will be permanently removed.
              </Text>
              <View style={styles.deleteConfirmButtons}>
                <TouchableOpacity
                  style={styles.deleteConfirmCancelButton}
                  onPress={() => setShowDeleteConfirm(false)}
                >
                  <Text style={styles.deleteConfirmCancelText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.deleteConfirmDeleteButton}
                  onPress={handleDelete}
                  testID="confirm-delete"
                >
                  <Text style={styles.deleteConfirmDeleteText}>Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

// Component-specific themed styles
const createTransactionEditStyles = createStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.md,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  cancelButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  cancelButtonText: {
    ...theme.typography.button,
    color: theme.colors.textSecondary,
  },
  title: {
    ...theme.typography.h2,
    color: theme.colors.text,
  },
  saveButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    minWidth: 60,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  saveButtonDisabled: {
    backgroundColor: theme.colors.textDisabled,
  },
  saveButtonText: {
    ...theme.typography.button,
    color: theme.colors.background,
  },
  section: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.surface,
  },
  label: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    ...theme.typography.body,
    backgroundColor: theme.colors.card,
    color: theme.colors.text,
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  errorText: {
    color: theme.colors.error,
    ...theme.typography.caption,
    marginTop: theme.spacing.xs,
  },
  typeToggle: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.xs,
  },
  typeButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.sm,
    alignItems: 'center',
  },
  expenseButton: {
    marginRight: theme.spacing.xs,
  },
  incomeButton: {
    marginLeft: theme.spacing.xs,
  },
  typeButtonActive: {
    backgroundColor: theme.colors.card,
    ...theme.shadows.sm,
  },
  typeButtonText: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    fontWeight: '600',
  },
  typeButtonTextActive: {
    color: theme.colors.text,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.card,
  },
  currencySymbol: {
    ...theme.typography.h3,
    fontWeight: 'bold',
    paddingLeft: theme.spacing.md,
    color: theme.colors.primary,
  },
  amountInput: {
    flex: 1,
    padding: theme.spacing.md,
    ...theme.typography.h3,
    fontWeight: '600',
    borderWidth: 0,
    color: theme.colors.text,
  },
  dateDisplay: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
  },
  dateText: {
    ...theme.typography.body,
    color: theme.colors.text,
    fontWeight: '500',
  },
  dateHelper: {
    ...theme.typography.caption,
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  metadataContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
  },
  metadataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.xs,
  },
  metadataLabel: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  metadataValue: {
    ...theme.typography.caption,
    color: theme.colors.text,
  },
  statusValue: {
    fontWeight: '600',
    color: theme.colors.primary,
  },
  deleteSection: {
    padding: theme.spacing.md,
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  deleteButtonText: {
    ...theme.typography.button,
    color: theme.colors.background,
  },
  deleteConfirmOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteConfirmModal: {
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    margin: theme.spacing.xl,
    minWidth: 300,
    ...theme.shadows.lg,
  },
  deleteConfirmTitle: {
    ...theme.typography.h3,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  deleteConfirmMessage: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: theme.spacing.xl,
  },
  deleteConfirmButtons: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  deleteConfirmCancelButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
    backgroundColor: theme.colors.card,
  },
  deleteConfirmCancelText: {
    ...theme.typography.button,
    color: theme.colors.textSecondary,
  },
  deleteConfirmDeleteButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.error,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  deleteConfirmDeleteText: {
    ...theme.typography.button,
    color: theme.colors.background,
  },
}));