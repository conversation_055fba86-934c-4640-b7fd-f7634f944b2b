import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Vibration,
  Platform,
} from 'react-native';

interface PINInputProps {
  length?: number;
  onComplete: (pin: string) => void;
  onError?: (error: string) => void;
  title?: string;
  subtitle?: string;
  showError?: boolean;
  errorMessage?: string;
  disabled?: boolean;
  autoFocus?: boolean;
  clearOnError?: boolean;
}

export const PINInput: React.FC<PINInputProps> = ({
  length = 6,
  onComplete,
  onError,
  title = 'Enter PIN',
  subtitle,
  showError = false,
  errorMessage,
  disabled = false,
  autoFocus = true,
  clearOnError = true,
}) => {
  const [pin, setPin] = useState('');
  const [showPin, setShowPin] = useState(false);
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    if (pin.length === length) {
      onComplete(pin);
    }
  }, [pin, length, onComplete]);

  useEffect(() => {
    const triggerErrorShake = () => {
      // Haptic feedback
      if (Platform.OS === 'ios') {
        Vibration.vibrate([0, 100, 50, 100]);
      } else {
        Vibration.vibrate(200);
      }

      // Shake animation
      Animated.sequence([
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 50,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: -10,
          duration: 50,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 50,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 0,
          duration: 50,
          useNativeDriver: true,
        }),
      ]).start();
    };

    if (showError && clearOnError) {
      setPin('');
      triggerErrorShake();
    }
  }, [showError, clearOnError, shakeAnimation]);

  const handlePinChange = (text: string) => {
    // Only allow numeric input
    const numericText = text.replace(/[^0-9]/g, '');
    
    // Limit to specified length
    if (numericText.length <= length) {
      setPin(numericText);
    }
  };

  const renderPinDots = () => {
    return Array.from({ length }, (_, index) => {
      const isFilled = index < pin.length;
      const isActive = index === pin.length && !disabled;
      
      return (
        <View
          key={index}
          style={[
            styles.pinDot,
            isFilled && styles.pinDotFilled,
            isActive && styles.pinDotActive,
            showError && styles.pinDotError,
          ]}
        >
          {showPin && isFilled && (
            <Text style={styles.pinDigit}>{pin[index]}</Text>
          )}
        </View>
      );
    });
  };

  const renderNumberPad = () => {
    const numbers = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['', '0', 'backspace'],
    ];

    return (
      <View style={styles.numberPad}>
        {numbers.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numberRow}>
            {row.map((number, colIndex) => {
              if (number === '') {
                return <View key={colIndex} style={styles.numberButton} />;
              }
              
              if (number === 'backspace') {
                return (
                  <TouchableOpacity
                    key={colIndex}
                    style={styles.numberButton}
                    onPress={() => setPin(prev => prev.slice(0, -1))}
                    disabled={disabled || pin.length === 0}
                  >
                    <Text style={styles.backspaceText}>⌫</Text>
                  </TouchableOpacity>
                );
              }

              return (
                <TouchableOpacity
                  key={colIndex}
                  style={styles.numberButton}
                  onPress={() => handlePinChange(pin + number)}
                  disabled={disabled || pin.length >= length}
                >
                  <Text style={styles.numberText}>{number}</Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Hidden input for hardware keyboard support */}
      <TextInput
        ref={inputRef}
        style={styles.hiddenInput}
        value={pin}
        onChangeText={handlePinChange}
        keyboardType="numeric"
        maxLength={length}
        secureTextEntry={!showPin}
        autoFocus={autoFocus}
        editable={!disabled}
      />

      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      <Animated.View
        style={[
          styles.pinContainer,
          {
            transform: [{ translateX: shakeAnimation }],
          },
        ]}
      >
        {renderPinDots()}
      </Animated.View>

      {showError && errorMessage && (
        <Text style={styles.errorText}>{errorMessage}</Text>
      )}

      <View style={styles.controls}>
        <TouchableOpacity
          style={styles.showPinButton}
          onPress={() => setShowPin(!showPin)}
        >
          <Text style={styles.showPinText}>
            {showPin ? 'Hide PIN' : 'Show PIN'}
          </Text>
        </TouchableOpacity>
      </View>

      {renderNumberPad()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
  },
  hiddenInput: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    opacity: 0,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  pinContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    gap: 16,
  },
  pinDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E5E5E7',
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  pinDotFilled: {
    borderColor: '#007AFF',
    backgroundColor: '#007AFF',
  },
  pinDotActive: {
    borderColor: '#007AFF',
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  pinDotError: {
    borderColor: '#FF3B30',
    backgroundColor: '#FF3B30',
  },
  pinDigit: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  controls: {
    marginBottom: 40,
  },
  showPinButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  showPinText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
  },
  numberPad: {
    width: '100%',
    maxWidth: 300,
  },
  numberRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  numberButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#F2F2F7',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
  numberText: {
    fontSize: 24,
    fontWeight: '400',
    color: '#000000',
  },
  backspaceText: {
    fontSize: 20,
    color: '#666666',
  },
});