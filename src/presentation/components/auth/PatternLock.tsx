import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  Vibration,
  Platform,
} from 'react-native';
import {
  PanGestureHandler,
  State,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';

interface PatternLockProps {
  onComplete: (pattern: number[]) => void;
  onError?: (error: string) => void;
  title?: string;
  subtitle?: string;
  showError?: boolean;
  errorMessage?: string;
  disabled?: boolean;
  clearOnError?: boolean;
  gridSize?: number;
  minPatternLength?: number;
}

interface DotState {
  id: number;
  x: number;
  y: number;
  selected: boolean;
  order: number;
}

const { width: screenWidth } = Dimensions.get('window');
const PATTERN_SIZE = Math.min(screenWidth - 80, 300);
const DOT_SIZE = 20;
const DOT_OUTER_SIZE = 60;

export const PatternLock: React.FC<PatternLockProps> = ({
  onComplete,
  onError,
  title = 'Draw Pattern',
  subtitle,
  showError = false,
  errorMessage,
  disabled = false,
  clearOnError = true,
  gridSize = 3,
  minPatternLength = 4,
}) => {
  const [pattern, setPattern] = useState<number[]>([]);
  const [dots, setDots] = useState<DotState[]>([]);
  const [currentPath, setCurrentPath] = useState<{ x: number; y: number }[]>([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  // Initialize dots grid
  React.useEffect(() => {
    const newDots: DotState[] = [];
    const spacing = PATTERN_SIZE / (gridSize + 1);
    const startOffset = spacing;

    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        const id = row * gridSize + col;
        newDots.push({
          id,
          x: startOffset + col * spacing,
          y: startOffset + row * spacing,
          selected: false,
          order: -1,
        });
      }
    }
    setDots(newDots);
  }, [gridSize]);

  React.useEffect(() => {
    if (pattern.length >= minPatternLength && !isDrawing) {
      onComplete(pattern);
    }
  }, [pattern, minPatternLength, isDrawing, onComplete]);

  const clearPattern = useCallback(() => {
    setPattern([]);
    setCurrentPath([]);
    setDots(prevDots =>
      prevDots.map(dot => ({
        ...dot,
        selected: false,
        order: -1,
      }))
    );
  }, []);

  React.useEffect(() => {
    const triggerErrorShake = () => {
      if (Platform.OS === 'ios') {
        Vibration.vibrate([0, 100, 50, 100]);
      } else {
        Vibration.vibrate(200);
      }

      Animated.sequence([
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 50,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: -10,
          duration: 50,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 50,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 0,
          duration: 50,
          useNativeDriver: true,
        }),
      ]).start();
    };

    if (showError && clearOnError) {
      clearPattern();
      triggerErrorShake();
    }
  }, [showError, clearOnError, clearPattern, shakeAnimation]);

  const findDotAtPosition = (x: number, y: number): DotState | null => {
    return dots.find(dot => {
      const distance = Math.sqrt(
        Math.pow(x - dot.x, 2) + Math.pow(y - dot.y, 2)
      );
      return distance <= DOT_OUTER_SIZE / 2;
    }) || null;
  };

  const handleGestureEvent = (event: PanGestureHandlerGestureEvent) => {
    if (disabled) return;

    const { translationX, translationY } = event.nativeEvent;
    const currentX = translationX;
    const currentY = translationY;

    if (isDrawing) {
      // Update current path for line drawing
      setCurrentPath(prev => [...prev.slice(0, -1), { x: currentX, y: currentY }]);

      // Check if we're over a new dot
      const dot = findDotAtPosition(currentX, currentY);
      if (dot && !dot.selected) {
        selectDot(dot);
      }
    }
  };

  const handleGestureStateChange = (event: PanGestureHandlerGestureEvent) => {
    if (disabled) return;

    const { state, translationX, translationY } = event.nativeEvent;
    const currentX = translationX;
    const currentY = translationY;

    if (state === State.BEGAN) {
      const dot = findDotAtPosition(currentX, currentY);
      if (dot) {
        setIsDrawing(true);
        selectDot(dot);
        setCurrentPath([{ x: currentX, y: currentY }]);
      }
    } else if (state === State.END || state === State.CANCELLED) {
      setIsDrawing(false);
      setCurrentPath([]);

      if (pattern.length < minPatternLength) {
        if (onError) {
          onError(`Pattern must connect at least ${minPatternLength} dots`);
        }
        setTimeout(clearPattern, 1000);
      }
    }
  };

  const selectDot = (dot: DotState) => {
    if (dot.selected) return;

    // Haptic feedback
    if (Platform.OS === 'ios') {
      Vibration.vibrate(50);
    }

    setPattern(prev => [...prev, dot.id]);
    setDots(prevDots =>
      prevDots.map(d =>
        d.id === dot.id
          ? { ...d, selected: true, order: pattern.length }
          : d
      )
    );
  };

  const renderConnectingLines = () => {
    const selectedDots = dots.filter(dot => dot.selected).sort((a, b) => a.order - b.order);
    const lines = [];

    // Draw lines between selected dots
    for (let i = 0; i < selectedDots.length - 1; i++) {
      const from = selectedDots[i];
      const to = selectedDots[i + 1];
      
      lines.push(
        <View
          key={`line-${from.id}-${to.id}`}
          style={[
            styles.connectingLine,
            {
              left: from.x,
              top: from.y,
              width: Math.sqrt(Math.pow(to.x - from.x, 2) + Math.pow(to.y - from.y, 2)),
              transform: [
                {
                  rotate: `${Math.atan2(to.y - from.y, to.x - from.x)}rad`,
                },
              ],
            },
          ]}
        />
      );
    }

    // Draw line to current finger position if drawing
    if (isDrawing && selectedDots.length > 0 && currentPath.length > 0) {
      const lastDot = selectedDots[selectedDots.length - 1];
      const currentPos = currentPath[currentPath.length - 1];
      
      lines.push(
        <View
          key="current-line"
          style={[
            styles.connectingLine,
            {
              left: lastDot.x,
              top: lastDot.y,
              width: Math.sqrt(
                Math.pow(currentPos.x - lastDot.x, 2) + Math.pow(currentPos.y - lastDot.y, 2)
              ),
              transform: [
                {
                  rotate: `${Math.atan2(currentPos.y - lastDot.y, currentPos.x - lastDot.x)}rad`,
                },
              ],
            },
          ]}
        />
      );
    }

    return lines;
  };

  const renderDots = () => {
    return dots.map(dot => (
      <View
        key={dot.id}
        style={[
          styles.dotContainer,
          {
            left: dot.x - DOT_OUTER_SIZE / 2,
            top: dot.y - DOT_OUTER_SIZE / 2,
          },
        ]}
      >
        <View
          style={[
            styles.dot,
            dot.selected && styles.dotSelected,
            showError && dot.selected && styles.dotError,
          ]}
        />
        {dot.selected && (
          <View style={styles.dotOuter} />
        )}
      </View>
    ));
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      {showError && errorMessage && (
        <Text style={styles.errorText}>{errorMessage}</Text>
      )}

      <Animated.View
        style={[
          styles.patternContainer,
          {
            transform: [{ translateX: shakeAnimation }],
          },
        ]}
      >
        <PanGestureHandler
          onGestureEvent={handleGestureEvent}
          onHandlerStateChange={handleGestureStateChange}
          enabled={!disabled}
        >
          <View style={styles.patternArea}>
            {renderConnectingLines()}
            {renderDots()}
          </View>
        </PanGestureHandler>
      </Animated.View>

      <View style={styles.footer}>
        <Text style={styles.instructionText}>
          Connect at least {minPatternLength} dots
        </Text>
        <Text style={styles.patternLength}>
          {pattern.length} / {gridSize * gridSize} dots selected
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  patternContainer: {
    marginBottom: 40,
  },
  patternArea: {
    width: PATTERN_SIZE,
    height: PATTERN_SIZE,
    position: 'relative',
  },
  dotContainer: {
    position: 'absolute',
    width: DOT_OUTER_SIZE,
    height: DOT_OUTER_SIZE,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    width: DOT_SIZE,
    height: DOT_SIZE,
    borderRadius: DOT_SIZE / 2,
    backgroundColor: '#E5E5E7',
    borderWidth: 2,
    borderColor: '#E5E5E7',
  },
  dotSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  dotError: {
    backgroundColor: '#FF3B30',
    borderColor: '#FF3B30',
  },
  dotOuter: {
    position: 'absolute',
    width: DOT_OUTER_SIZE,
    height: DOT_OUTER_SIZE,
    borderRadius: DOT_OUTER_SIZE / 2,
    borderWidth: 2,
    borderColor: 'rgba(0, 122, 255, 0.3)',
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  connectingLine: {
    position: 'absolute',
    height: 3,
    backgroundColor: '#007AFF',
    borderRadius: 1.5,
    transformOrigin: '0 50%',
  },
  footer: {
    alignItems: 'center',
  },
  instructionText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  patternLength: {
    fontSize: 12,
    color: '#999999',
  },
});