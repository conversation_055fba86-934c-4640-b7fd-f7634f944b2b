import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Category } from '@/shared/types';
import { useCategoryStore } from '@/shared/stores/categoryStore';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';
import { CATEGORY_COLORS, CATEGORY_ICONS, getDefaultIconForCategory, getDefaultColorForCategoryType } from '@/data/systemCategories';
import { FallbackIcon, FormInput } from '@/presentation/components/common';

interface CategoryCreationFormProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: (category: Category) => void;
  categoryType: 'income' | 'expense';
  parentCategory?: Category | null;
}

export const CategoryCreationForm: React.FC<CategoryCreationFormProps> = ({
  visible,
  onClose,
  onSuccess,
  categoryType,
  parentCategory,
}) => {
  const theme = useTheme();
  const { createCategory } = useCategoryStore();

  // Form state
  const [name, setName] = useState('');
  const [selectedColor, setSelectedColor] = useState(getDefaultColorForCategoryType(categoryType));
  const [selectedIcon, setSelectedIcon] = useState('ellipse-outline');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showIconPicker, setShowIconPicker] = useState(false);
  
  // Validation
  const [nameError, setNameError] = useState('');

  const validateName = (value: string): boolean => {
    setNameError('');
    
    if (!value.trim()) {
      setNameError('Category name is required');
      return false;
    }
    
    if (value.trim().length < 2) {
      setNameError('Category name must be at least 2 characters');
      return false;
    }
    
    if (value.trim().length > 50) {
      setNameError('Category name must be less than 50 characters');
      return false;
    }
    
    return true;
  };

  const handleNameChange = (value: string) => {
    setName(value);
    if (nameError) {
      validateName(value);
    }
    
    // Auto-suggest icon based on name
    if (value.trim().length > 2) {
      const suggestedIcon = getDefaultIconForCategory(value);
      if (suggestedIcon !== 'ellipse') {
        setSelectedIcon(suggestedIcon);
      }
    }
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;
    
    const isNameValid = validateName(name);
    if (!isNameValid) return;
    
    setIsSubmitting(true);
    
    try {
      const categoryData = {
        name: name.trim(),
        parent_id: parentCategory?.id || null,
        category_type: categoryType,
        color_code: selectedColor,
        icon_name: selectedIcon,
        is_system: false,
        sync_status: 'local' as const,
      };
      
      const newCategory = await createCategory(categoryData);
      
      // Reset form
      setName('');
      setSelectedColor(getDefaultColorForCategoryType(categoryType));
      setSelectedIcon('ellipse-outline');
      setNameError('');
      
      onSuccess(newCategory);
      onClose();
      
    } catch (error) {
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to create category'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      // Reset form on close
      setName('');
      setSelectedColor(getDefaultColorForCategoryType(categoryType));
      setSelectedIcon('ellipse-outline');
      setNameError('');
      onClose();
    }
  };

  const styles = createCategoryFormStyles(theme);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            New {categoryType === 'income' ? 'Income' : 'Expense'} Category
          </Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Category Preview */}
          <View style={[styles.section, { marginTop: theme.spacing.xl }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Preview</Text>
            <View style={[styles.previewContainer, { backgroundColor: theme.colors.card, borderColor: theme.colors.border }]}>
              <View style={[
                styles.previewIcon, 
                { 
                  backgroundColor: 'transparent',
                  borderColor: selectedColor,
                  borderWidth: 2
                }
              ]}>
                <FallbackIcon 
                  icon={selectedIcon} 
                  name={name || 'Category'} 
                  size={16} 
                  style={{ color: selectedColor }} 
                />
              </View>
              <Text style={[styles.previewText, { color: theme.colors.text }]}>
                {name.trim() || `${categoryType === 'income' ? 'Income' : 'Expense'} Category`}
              </Text>
              {parentCategory && (
                <Text style={[styles.previewParent, { color: theme.colors.textSecondary }]}>
                  in {parentCategory.name}
                </Text>
              )}
            </View>
          </View>

          {/* Category Name */}
          <FormInput
            label="Category Name"
            required
            value={name}
            onChangeText={handleNameChange}
            placeholder={`Enter ${categoryType} category name`}
            maxLength={50}
            autoFocus
            editable={!isSubmitting}
            error={nameError}
            containerStyle={styles.section}
          />

          {/* Color Selection */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Color</Text>
            <TouchableOpacity
              style={[
                styles.colorSelector,
                { backgroundColor: theme.colors.card, borderColor: theme.colors.border }
              ]}
              onPress={() => setShowColorPicker(true)}
              disabled={isSubmitting}
            >
              <View style={[styles.selectedColor, { backgroundColor: selectedColor }]} />
              <Text style={[styles.selectorText, { color: theme.colors.text }]}>
                {selectedColor}
              </Text>
              <Text style={[styles.chevron, { color: theme.colors.textSecondary }]}>⌄</Text>
            </TouchableOpacity>
          </View>

          {/* Icon Selection */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Icon</Text>
            <TouchableOpacity
              style={[
                styles.iconSelector,
                { backgroundColor: theme.colors.card, borderColor: theme.colors.border }
              ]}
              onPress={() => setShowIconPicker(true)}
              disabled={isSubmitting}
            >
              <View style={[
                styles.selectedIconContainer, 
                { 
                  backgroundColor: 'transparent',
                  borderColor: selectedColor,
                  borderWidth: 2
                }
              ]}>
                <FallbackIcon 
                  icon={selectedIcon} 
                  name={name || 'Category'} 
                  size={12} 
                  style={{ color: selectedColor }} 
                />
              </View>
              <Text style={[styles.selectorText, { color: theme.colors.text }]}>
                {selectedIcon}
              </Text>
              <Text style={[styles.chevron, { color: theme.colors.textSecondary }]}>⌄</Text>
            </TouchableOpacity>
          </View>

          {/* Parent Category Info */}
          {parentCategory && (
            <View style={[styles.section, styles.parentInfo, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.parentInfoLabel, { color: theme.colors.textSecondary }]}>
                Creating subcategory under:
              </Text>
              <Text style={[styles.parentInfoName, { color: theme.colors.text }]}>
                {parentCategory.name}
              </Text>
            </View>
          )}

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleClose}
              disabled={isSubmitting}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.createButton,
                (!name.trim() || isSubmitting) && styles.createButtonDisabled
              ]}
              onPress={handleSubmit}
              disabled={!name.trim() || isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator color={theme.colors.background} size="small" />
              ) : (
                <Text style={styles.createButtonText}>Create</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Color Picker Modal */}
        <Modal
          visible={showColorPicker}
          transparent
          animationType="fade"
          onRequestClose={() => setShowColorPicker(false)}
        >
          <TouchableOpacity 
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowColorPicker(false)}
          >
            <View style={[styles.pickerModal, { backgroundColor: theme.colors.background }]}>
              <Text style={[styles.pickerTitle, { color: theme.colors.text }]}>Choose Color</Text>
              <View style={styles.colorGrid}>
                {CATEGORY_COLORS.map((color, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.colorOption,
                      { backgroundColor: color },
                      selectedColor === color && styles.selectedColorOption
                    ]}
                    onPress={() => {
                      setSelectedColor(color);
                      setShowColorPicker(false);
                    }}
                  >
                    {selectedColor === color && (
                      <Text style={styles.colorCheckmark}>✓</Text>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </TouchableOpacity>
        </Modal>

        {/* Icon Picker Modal */}
        <Modal
          visible={showIconPicker}
          transparent
          animationType="fade"
          onRequestClose={() => setShowIconPicker(false)}
        >
          <TouchableOpacity 
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowIconPicker(false)}
          >
            <View style={[styles.pickerModal, { backgroundColor: theme.colors.background }]}>
              <Text style={[styles.pickerTitle, { color: theme.colors.text }]}>Choose Icon</Text>
              <ScrollView style={styles.iconScrollView}>
                <View style={styles.iconGrid}>
                  {CATEGORY_ICONS.map((icon, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.iconOption,
                        { backgroundColor: theme.colors.card, borderColor: theme.colors.border },
                        selectedIcon === icon && { borderColor: selectedColor, backgroundColor: `${selectedColor}20` }
                      ]}
                      onPress={() => {
                        setSelectedIcon(icon);
                        setShowIconPicker(false);
                      }}
                    >
                      <FallbackIcon 
                        icon={icon} 
                        name={icon} 
                        size={18} 
                        style={{ color: selectedIcon === icon ? selectedColor : '#666666' }} 
                      />
                      <Text style={[styles.iconOptionName, { color: theme.colors.textSecondary, fontSize: 10 }]}>
                        {icon}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
    </Modal>
  );
};

const createCategoryFormStyles = createStyles((theme) => ({
  container: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
  },
  title: {
    ...theme.typography.h3,
    fontWeight: '600',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
  },
  section: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    ...theme.typography.body,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
  },
  previewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
  },
  previewIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  previewIconText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  previewText: {
    ...theme.typography.body,
    fontWeight: '600',
    flex: 1,
  },
  previewParent: {
    ...theme.typography.caption,
    fontStyle: 'italic',
  },
  nameInput: {
    borderWidth: 1,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    ...theme.typography.body,
  },
  errorText: {
    ...theme.typography.caption,
    marginTop: theme.spacing.xs,
  },
  colorSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
  },
  selectedColor: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: theme.spacing.sm,
  },
  iconSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
  },
  selectedIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  selectedIconText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  selectorText: {
    ...theme.typography.body,
    flex: 1,
  },
  chevron: {
    fontSize: 16,
  },
  parentInfo: {
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  parentInfoLabel: {
    ...theme.typography.caption,
  },
  parentInfoName: {
    ...theme.typography.body,
    fontWeight: '600',
    marginTop: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pickerModal: {
    margin: theme.spacing.xl,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    maxHeight: '70%',
    width: '85%',
  },
  pickerTitle: {
    ...theme.typography.h3,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: theme.spacing.sm,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 2,
  },
  selectedColorOption: {
    transform: [{ scale: 1.2 }],
  },
  colorCheckmark: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  iconScrollView: {
    maxHeight: 300,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: theme.spacing.xs,
  },
  iconOption: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    margin: 2,
  },
  iconOptionText: {
    fontSize: 18,
  },
  iconOptionName: {
    fontSize: 8,
    marginTop: 2,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.xxl,
  },
  cancelButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
    backgroundColor: theme.colors.card,
  },
  cancelButtonText: {
    ...theme.typography.button,
    color: theme.colors.textSecondary,
    fontWeight: '600',
  },
  createButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
  },
  createButtonDisabled: {
    backgroundColor: theme.colors.textDisabled,
  },
  createButtonText: {
    ...theme.typography.button,
    color: theme.colors.background,
    fontWeight: '600',
  },
}));