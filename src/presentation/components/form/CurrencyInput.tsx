import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';

export interface CurrencyInputProps {
  // Value
  value: string;
  onChangeValue: (value: string) => void;
  
  // Configuration
  currency?: string;
  placeholder?: string;
  maxValue?: number;
  
  // Appearance
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'large';
  
  // Validation
  error?: string;
  required?: boolean;
  
  // Behavior
  autoFocus?: boolean;
  disabled?: boolean;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
  
  // Styling
  style?: ViewStyle;
  inputStyle?: TextStyle;
}

export const CurrencyInput: React.FC<CurrencyInputProps> = ({
  value,
  onChangeValue,
  currency = '$',
  placeholder = '0.00',
  maxValue,
  size = 'md',
  variant = 'default',
  error,
  required = false,
  autoFocus = false,
  disabled = false,
  accessibilityLabel,
  accessibilityHint,
  testID,
  style,
  inputStyle,
}) => {
  const theme = useTheme();
  const inputRef = useRef<TextInput>(null);
  const [focused, setFocused] = useState(false);
  
  const formatValue = (inputValue: string): string => {
    // Remove non-numeric characters except decimal point
    const numericValue = inputValue.replace(/[^0-9.]/g, '');
    
    // Ensure only one decimal point
    const parts = numericValue.split('.');
    if (parts.length > 2) {
      return parts[0] + '.' + parts.slice(1).join('');
    }
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
      return parts[0] + '.' + parts[1].substring(0, 2);
    }
    
    return numericValue;
  };
  
  const handleChangeText = (text: string) => {
    const formattedValue = formatValue(text);
    
    // Check max value constraint
    if (maxValue && parseFloat(formattedValue) > maxValue) {
      return;
    }
    
    onChangeValue(formattedValue);
  };
  
  const getContainerHeight = () => {
    if (variant === 'large') return 80;
    switch (size) {
      case 'sm': return 40;
      case 'lg': return 56;
      default: return 48;
    }
  };
  
  const getFontSize = () => {
    if (variant === 'large') return 32;
    switch (size) {
      case 'sm': return 16;
      case 'lg': return 24;
      default: return 18;
    }
  };
  
  const getAccessibilityLabel = () => {
    return accessibilityLabel || `Amount input, currently ${value || '0'} ${currency}`;
  };
  
  return (
    <View style={styles.wrapper}>
      <View
        style={[
          styles.container,
          {
            backgroundColor: focused ? theme.colors.background : variant === 'large' ? theme.colors.background : theme.colors.surface,
            borderColor: error ? theme.colors.error : focused ? theme.colors.primary : theme.colors.border,
            borderWidth: variant === 'large' ? 2 : 1,
            borderRadius: theme.borderRadius.md,
            minHeight: getContainerHeight(),
            paddingHorizontal: theme.spacing.md,
            paddingVertical: variant === 'large' ? 20 : size === 'sm' ? 8 : size === 'lg' ? 16 : 12,
            opacity: disabled ? 0.6 : 1,
          },
          ...(error ? [{
            backgroundColor: '#FFF5F5',
            borderColor: theme.colors.error,
          }] : []),
          ...(style ? [style] : []),
        ]}
      >
        <Text style={[styles.currencySymbol, { 
          fontSize: getFontSize() * 0.9,
          color: theme.colors.textSecondary,
          marginRight: theme.spacing.sm,
        }]}>
          {currency}
        </Text>
        <TextInput
          ref={inputRef}
          style={[
            styles.input,
            {
              fontSize: getFontSize(),
              color: theme.colors.text,
              textAlign: variant === 'large' ? 'center' : 'left',
              fontWeight: variant === 'large' ? '700' : '600',
            },
            inputStyle,
          ]}
          value={value}
          onChangeText={handleChangeText}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.textDisabled}
          keyboardType="numeric"
          returnKeyType="done"
          autoFocus={autoFocus}
          editable={!disabled}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          accessibilityLabel={getAccessibilityLabel()}
          accessibilityHint={accessibilityHint || "Enter the amount"}
          accessibilityRole="none" // Let it be treated as text input
          testID={testID}
          maxLength={12} // Reasonable limit for currency values
        />
      </View>
      
      {error && (
        <Text 
          style={[styles.errorText, { color: theme.colors.error, marginTop: theme.spacing.xs, marginLeft: theme.spacing.xs }]}
          accessibilityRole="alert"
          accessibilityLiveRegion="polite"
        >
          {error}
        </Text>
      )}
      
      {maxValue && (
        <Text 
          style={[styles.helpText, { 
            color: theme.colors.textSecondary, 
            marginTop: theme.spacing.xs, 
            marginLeft: theme.spacing.xs 
          }]}
          accessibilityLabel={`Maximum amount is ${maxValue} ${currency}`}
        >
          Max: {currency}{maxValue.toFixed(2)}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: 16,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySymbol: {
    fontWeight: '600',
  },
  input: {
    flex: 1,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 14,
  },
  helpText: {
    fontSize: 12,
  },
});