/**
 * Anonymous Feedback Form Component
 * Story 1.9: Anonymous Feedback and Feature Requests
 * 
 * Main feedback submission form with category selection, priority levels,
 * and optional reward claiming with privacy separation
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTheme } from '../../../shared/theme/ThemeProvider';
import { createStyles, useCommonStyles } from '../../../shared/theme';
import { useFeedbackStore } from '../../../shared/stores/feedbackStore';
import { FeedbackFormData } from '../../../shared/types/feedback';
import { FormSelector } from '../common/FormSelector';
import { Button } from '../core/Button';

interface FeedbackFormProps {
  onSubmitted?: () => void;
  onCancel?: () => void;
}

const FEEDBACK_CATEGORIES = [
  { id: 'bug_report', label: 'Bug Report', icon: '🐛', description: 'Report issues or errors' },
  { id: 'feature_request', label: 'Feature Request', icon: '💡', description: 'Suggest new features' },
  { id: 'general_feedback', label: 'General Feedback', icon: '💬', description: 'Share your thoughts' },
  { id: 'praise_complaint', label: 'Praise/Complaint', icon: '⭐', description: 'Compliments or concerns' }
] as const;

const PRIORITY_LEVELS = [
  { id: 'low', label: 'Low', color: '#4CAF50', description: 'Nice to have' },
  { id: 'medium', label: 'Medium', color: '#FF9800', description: 'Moderately important' },
  { id: 'high', label: 'High', color: '#F44336', description: 'Important issue' },
  { id: 'critical', label: 'Critical', color: '#9C27B0', description: 'Urgent problem' }
] as const;

const createFeedbackFormStyles = createStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    padding: theme.spacing.md,
    paddingBottom: theme.spacing.xl,
  },
  section: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    ...theme.typography.h4,
    color: theme.colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
  },
  sectionDescription: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.md,
  },
  input: {
    backgroundColor: theme.colors.card,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    ...theme.typography.body,
    color: theme.colors.text,
    textAlignVertical: 'top',
  },
  inputFocused: {
    borderColor: theme.colors.primary,
    borderWidth: 2,
  },
  inputError: {
    borderColor: theme.colors.error,
    borderWidth: 2,
  },
  multilineInput: {
    minHeight: 120,
    textAlignVertical: 'top',
  },
  characterCount: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    textAlign: 'right',
    marginTop: theme.spacing.xs,
  },
  characterCountLimit: {
    color: theme.colors.error,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  categoryCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: theme.colors.card,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    alignItems: 'center',
  },
  categoryCardSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: `${theme.colors.primary}15`,
    borderWidth: 2,
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: theme.spacing.xs,
  },
  categoryLabel: {
    ...theme.typography.caption,
    color: theme.colors.text,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: theme.spacing.xs,
  },
  categoryDescription: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontSize: 11,
  },
  priorityGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  priorityCard: {
    flex: 1,
    minWidth: '22%',
    backgroundColor: theme.colors.card,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
    alignItems: 'center',
  },
  priorityCardSelected: {
    borderWidth: 2,
  },
  priorityDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginBottom: theme.spacing.xs,
  },
  priorityLabel: {
    ...theme.typography.caption,
    color: theme.colors.text,
    fontWeight: '600',
    textAlign: 'center',
    fontSize: 12,
  },
  rewardSection: {
    backgroundColor: `${theme.colors.warning}15`,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    borderWidth: 1,
    borderColor: `${theme.colors.warning}30`,
  },
  rewardTitle: {
    ...theme.typography.h5,
    color: theme.colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
  },
  rewardDescription: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.md,
    lineHeight: 16,
  },
  rewardToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  rewardCheckbox: {
    width: 20,
    height: 20,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 2,
    borderColor: theme.colors.primary,
    marginRight: theme.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rewardCheckboxChecked: {
    backgroundColor: theme.colors.primary,
  },
  rewardCheckmark: {
    color: theme.colors.surface,
    fontWeight: 'bold',
  },
  rewardToggleLabel: {
    ...theme.typography.body,
    color: theme.colors.text,
    flex: 1,
  },
  contactInput: {
    marginTop: theme.spacing.sm,
  },
  privacyNotice: {
    backgroundColor: `${theme.colors.info}15`,
    borderRadius: theme.borderRadius.sm,
    padding: theme.spacing.sm,
    marginTop: theme.spacing.sm,
  },
  privacyText: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    lineHeight: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    marginTop: theme.spacing.lg,
  },
  submitButton: {
    flex: 2,
  },
  cancelButton: {
    flex: 1,
  },
  errorText: {
    ...theme.typography.caption,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  }
}));

export const FeedbackForm: React.FC<FeedbackFormProps> = ({ onSubmitted, onCancel }) => {
  const theme = useTheme();
  const commonStyles = useCommonStyles(theme);
  const styles = createFeedbackFormStyles(theme);
  
  const { 
    submitFeedback, 
    submitting, 
    error,
    draftFeedback,
    saveDraftFeedback,
    clearDraftFeedback,
    setError
  } = useFeedbackStore();

  // Form state
  const [formData, setFormData] = useState<FeedbackFormData>({
    category: 'general_feedback',
    title: '',
    description: '',
    priorityLevel: 'medium',
    rewardEligible: false,
    contactInfo: undefined,
    screenshotPath: undefined,
    ...draftFeedback
  });

  // UI state
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Auto-save draft
  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.title.trim() || formData.description.trim()) {
        saveDraftFeedback(formData);
      }
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [formData, saveDraftFeedback]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length > 2000) {
      newErrors.description = 'Description must be less than 2000 characters';
    }

    if (formData.rewardEligible && !formData.contactInfo?.trim()) {
      newErrors.contactInfo = 'Contact info required for reward eligibility';
    }

    if (formData.contactInfo && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactInfo)) {
      newErrors.contactInfo = 'Valid email address required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    setError(null);
    
    if (!validateForm()) {
      return;
    }

    try {
      await submitFeedback(formData);
      clearDraftFeedback();
      
      Alert.alert(
        'Feedback Submitted',
        'Thank you for your feedback! We\'ll review it and get back to you if needed.',
        [{ text: 'OK', onPress: onSubmitted }]
      );
    } catch (error) {
      console.error('Failed to submit feedback:', error);
    }
  };

  const updateFormData = (field: keyof FeedbackFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts fixing it
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Category Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Category</Text>
          <Text style={styles.sectionDescription}>
            What type of feedback are you sharing?
          </Text>
          
          <View style={styles.categoryGrid}>
            {FEEDBACK_CATEGORIES.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryCard,
                  formData.category === category.id && styles.categoryCardSelected
                ]}
                onPress={() => updateFormData('category', category.id)}
                accessibilityRole="button"
                accessibilityLabel={`Select ${category.label} category`}
              >
                <Text style={styles.categoryIcon}>{category.icon}</Text>
                <Text style={styles.categoryLabel}>{category.label}</Text>
                <Text style={styles.categoryDescription}>{category.description}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Title Input */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Title</Text>
          <TextInput
            style={[
              styles.input,
              focusedField === 'title' ? styles.inputFocused : undefined,
              errors.title ? styles.inputError : undefined
            ].filter(Boolean)}
            value={formData.title}
            onChangeText={(text) => updateFormData('title', text)}
            onFocus={() => setFocusedField('title')}
            onBlur={() => setFocusedField(null)}
            placeholder="Brief description of your feedback..."
            placeholderTextColor={theme.colors.textSecondary}
            maxLength={100}
            accessibilityLabel="Feedback title"
          />
          <Text style={[
            styles.characterCount,
            formData.title.length > 90 && styles.characterCountLimit
          ]}>
            {formData.title.length}/100
          </Text>
          {errors.title && <Text style={styles.errorText}>{errors.title}</Text>}
        </View>

        {/* Description Input */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.sectionDescription}>
            Provide detailed information about your feedback
          </Text>
          
          <TextInput
            style={[
              styles.input,
              styles.multilineInput,
              focusedField === 'description' ? styles.inputFocused : undefined,
              errors.description ? styles.inputError : undefined
            ].filter(Boolean)}
            value={formData.description}
            onChangeText={(text) => updateFormData('description', text)}
            onFocus={() => setFocusedField('description')}
            onBlur={() => setFocusedField(null)}
            placeholder="Tell us more about your feedback..."
            placeholderTextColor={theme.colors.textSecondary}
            multiline
            maxLength={2000}
            accessibilityLabel="Feedback description"
          />
          <Text style={[
            styles.characterCount,
            formData.description.length > 1800 && styles.characterCountLimit
          ]}>
            {formData.description.length}/2000
          </Text>
          {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
        </View>

        {/* Priority Level */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Priority</Text>
          <Text style={styles.sectionDescription}>
            How important is this feedback?
          </Text>
          
          <View style={styles.priorityGrid}>
            {PRIORITY_LEVELS.map((priority) => (
              <TouchableOpacity
                key={priority.id}
                style={[
                  styles.priorityCard,
                  formData.priorityLevel === priority.id && [
                    styles.priorityCardSelected,
                    { borderColor: priority.color }
                  ]
                ]}
                onPress={() => updateFormData('priorityLevel', priority.id)}
                accessibilityRole="button"
                accessibilityLabel={`Set priority to ${priority.label}`}
              >
                <View style={[styles.priorityDot, { backgroundColor: priority.color }]} />
                <Text style={styles.priorityLabel}>{priority.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Reward Eligibility */}
        <View style={styles.section}>
          <View style={styles.rewardSection}>
            <Text style={styles.rewardTitle}>🎁 Reward Eligibility</Text>
            <Text style={styles.rewardDescription}>
              Want to be eligible for rewards? We can notify you if your feedback leads to improvements. 
              Your feedback remains anonymous - contact info is only used for reward notifications.
            </Text>
            
            <TouchableOpacity
              style={styles.rewardToggle}
              onPress={() => updateFormData('rewardEligible', !formData.rewardEligible)}
              accessibilityRole="checkbox"
              accessibilityState={{ checked: formData.rewardEligible }}
            >
              <View style={[
                styles.rewardCheckbox,
                formData.rewardEligible && styles.rewardCheckboxChecked
              ]}>
                {formData.rewardEligible && (
                  <Text style={styles.rewardCheckmark}>✓</Text>
                )}
              </View>
              <Text style={styles.rewardToggleLabel}>
                Yes, I want to be eligible for rewards
              </Text>
            </TouchableOpacity>
            
            {formData.rewardEligible && (
              <View style={styles.contactInput}>
                <TextInput
                  style={[
                    styles.input,
                    focusedField === 'contactInfo' ? styles.inputFocused : undefined,
                    errors.contactInfo ? styles.inputError : undefined
                  ].filter(Boolean)}
                  value={formData.contactInfo}
                  onChangeText={(text) => updateFormData('contactInfo', text)}
                  onFocus={() => setFocusedField('contactInfo')}
                  onBlur={() => setFocusedField(null)}
                  placeholder="<EMAIL>"
                  placeholderTextColor={theme.colors.textSecondary}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  accessibilityLabel="Contact email for rewards"
                />
                {errors.contactInfo && <Text style={styles.errorText}>{errors.contactInfo}</Text>}
                
                <View style={styles.privacyNotice}>
                  <Text style={styles.privacyText}>
                    🔒 Privacy: Your email is encrypted and stored separately from your feedback. 
                    We only use it to contact you about rewards - never for marketing.
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Error Display */}
        {error && (
          <View style={[commonStyles.card, { backgroundColor: `${theme.colors.error}15` }]}>
            <Text style={[styles.errorText, { marginTop: 0 }]}>{error}</Text>
          </View>
        )}

        {/* Submit Buttons */}
        <View style={styles.buttonContainer}>
          {onCancel && (
            <Button
              title="Cancel"
              variant="outline"
              onPress={onCancel}
              disabled={submitting}
              style={styles.cancelButton}
            />
          )}
          
          <Button
            title={submitting ? "Submitting..." : "Submit Feedback"}
            variant="primary"
            onPress={handleSubmit}
            loading={submitting}
            disabled={submitting}
            style={styles.submitButton}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};