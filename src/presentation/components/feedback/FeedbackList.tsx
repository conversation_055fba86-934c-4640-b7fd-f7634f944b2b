/**
 * Feedback List Component
 * Story 1.9: Anonymous Feedback and Feature Requests
 * 
 * Displays user's submitted feedback with status tracking and admin responses
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useTheme } from '../../../shared/theme/ThemeProvider';
import { createStyles, useCommonStyles } from '../../../shared/theme';
import { 
  useFeedbackStore, 
  useFeedbackSubmissions, 
  useFeedbackLoading,
  useFeedbackError,
  useFeedbackSyncStatus
} from '../../../shared/stores/feedbackStore';
import { FeedbackSubmission } from '../../../shared/types/feedback';
import { Card } from '../core/Card';
import { SyncStatusIndicator } from '../core/SyncStatusIndicator';

interface FeedbackListProps {
  onFeedbackPress?: (feedback: FeedbackSubmission) => void;
  showSyncStatus?: boolean;
}

const CATEGORY_ICONS = {
  bug_report: '🐛',
  feature_request: '💡',
  general_feedback: '💬',
  praise_complaint: '⭐'
} as const;

const PRIORITY_COLORS = {
  low: '#4CAF50',
  medium: '#FF9800',
  high: '#F44336',
  critical: '#9C27B0'
} as const;

const STATUS_COLORS = {
  submitted: '#9E9E9E',
  acknowledged: '#2196F3',
  in_review: '#FF9800',
  implemented: '#4CAF50',
  rejected: '#F44336'
} as const;

const createFeedbackListStyles = createStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.card,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    ...theme.typography.h3,
    color: theme.colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  headerSubtitle: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
  },
  syncStatus: {
    marginTop: theme.spacing.sm,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: theme.spacing.md,
    paddingTop: theme.spacing.sm,
  },
  itemSeparator: {
    height: theme.spacing.sm,
  },
  feedbackCard: {
    marginBottom: 0,
  },
  feedbackHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  categoryIcon: {
    fontSize: 20,
    marginRight: theme.spacing.sm,
    marginTop: 2,
  },
  feedbackTitleRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  feedbackTitle: {
    ...theme.typography.body,
    color: theme.colors.text,
    fontWeight: '600',
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  priorityBadge: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginTop: 4,
  },
  feedbackMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
    flexWrap: 'wrap',
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
    marginRight: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  statusText: {
    ...theme.typography.caption,
    color: theme.colors.surface,
    fontWeight: '600',
    fontSize: 11,
    textTransform: 'uppercase',
  },
  dateText: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  rewardBadge: {
    backgroundColor: `${theme.colors.warning}20`,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
    marginLeft: theme.spacing.sm,
  },
  rewardText: {
    ...theme.typography.caption,
    color: theme.colors.warning,
    fontWeight: '600',
    fontSize: 11,
  },
  feedbackDescription: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
    lineHeight: 16,
  },
  adminFeedback: {
    backgroundColor: `${theme.colors.info}15`,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.info,
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    marginTop: theme.spacing.sm,
  },
  adminFeedbackTitle: {
    ...theme.typography.caption,
    color: theme.colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  adminFeedbackText: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },
  syncIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.sm,
  },
  syncText: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.xl,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: theme.spacing.md,
  },
  emptyTitle: {
    ...theme.typography.h4,
    color: theme.colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  emptyDescription: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  errorCard: {
    backgroundColor: `${theme.colors.error}15`,
    borderColor: `${theme.colors.error}30`,
    borderWidth: 1,
    marginBottom: theme.spacing.md,
  },
  errorText: {
    ...theme.typography.caption,
    color: theme.colors.error,
    textAlign: 'center',
  }
}));

export const FeedbackList: React.FC<FeedbackListProps> = ({ 
  onFeedbackPress,
  showSyncStatus = true 
}) => {
  const theme = useTheme();
  const commonStyles = useCommonStyles(theme);
  const styles = createFeedbackListStyles(theme);
  
  const { loadUserFeedback, refreshFeedback, setError } = useFeedbackStore();
  const feedback = useFeedbackSubmissions();
  const loading = useFeedbackLoading();
  const error = useFeedbackError();
  const syncStatus = useFeedbackSyncStatus();
  
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadUserFeedback();
  }, [loadUserFeedback]);

  const handleRefresh = async () => {
    setRefreshing(true);
    setError(null);
    try {
      await refreshFeedback();
    } catch (error) {
      console.error('Failed to refresh feedback:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const formatDate = (date: Date | string | undefined): string => {
    if (!date) {
      return 'Unknown date';
    }
    
    let dateObj: Date;
    if (date instanceof Date) {
      dateObj = date;
    } else {
      dateObj = new Date(date);
    }
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return 'Invalid date';
    }
    
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return dateObj.toLocaleDateString();
    }
  };

  const getStatusDisplayText = (status: FeedbackSubmission['status']): string => {
    switch (status) {
      case 'submitted': return 'Submitted';
      case 'acknowledged': return 'Acknowledged';
      case 'in_review': return 'In Review';
      case 'implemented': return 'Implemented';
      case 'rejected': return 'Closed';
      default: return status;
    }
  };

  const renderFeedbackItem = ({ item }: { item: FeedbackSubmission }) => (
    <TouchableOpacity
      onPress={() => onFeedbackPress?.(item)}
      accessibilityRole="button"
      accessibilityLabel={`Feedback: ${item.title}, Status: ${getStatusDisplayText(item.status)}`}
    >
      <Card style={styles.feedbackCard}>
        <View style={styles.feedbackHeader}>
          <Text style={styles.categoryIcon}>
            {CATEGORY_ICONS[item.category]}
          </Text>
          <View style={styles.feedbackTitleRow}>
            <Text style={styles.feedbackTitle} numberOfLines={2}>
              {item.title}
            </Text>
            <View 
              style={[
                styles.priorityBadge,
                { backgroundColor: PRIORITY_COLORS[item.priorityLevel] }
              ]} 
            />
          </View>
        </View>

        <View style={styles.feedbackMeta}>
          <View 
            style={[
              styles.statusBadge,
              { backgroundColor: STATUS_COLORS[item.status] }
            ]}
          >
            <Text style={styles.statusText}>
              {getStatusDisplayText(item.status)}
            </Text>
          </View>
          
          {item.rewardEligible && (
            <View style={styles.rewardBadge}>
              <Text style={styles.rewardText}>🎁 REWARD ELIGIBLE</Text>
            </View>
          )}
        </View>

        <Text style={styles.dateText}>
          {formatDate(item.submittedAt)}
        </Text>

        <Text style={styles.feedbackDescription} numberOfLines={3}>
          {item.description}
        </Text>

        {item.adminFeedback && (
          <View style={styles.adminFeedback}>
            <Text style={styles.adminFeedbackTitle}>
              📋 Team Response
            </Text>
            <Text style={styles.adminFeedbackText}>
              {item.adminFeedback}
            </Text>
          </View>
        )}

        {item.syncStatus !== 'synced' && (
          <View style={styles.syncIndicator}>
            <SyncStatusIndicator 
              status={item.syncStatus === 'pending' ? 'pending' : 'conflict'}
              size="sm"
            />
            <Text style={styles.syncText}>
              {item.syncStatus === 'pending' ? 'Pending sync' : 'Sync failed'}
            </Text>
          </View>
        )}
      </Card>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyIcon}>💬</Text>
      <Text style={styles.emptyTitle}>No Feedback Yet</Text>
      <Text style={styles.emptyDescription}>
        Your submitted feedback will appear here. Share your thoughts to help improve FinVibe!
      </Text>
    </View>
  );

  const renderItemSeparator = () => <View style={styles.itemSeparator} />;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Feedback</Text>
        <Text style={styles.headerSubtitle}>
          {feedback.length} feedback submissions
        </Text>
        
        {showSyncStatus && (
          <View style={styles.syncStatus}>
            <SyncStatusIndicator 
              status={syncStatus.status === 'idle' ? 'synced' : syncStatus.status === 'syncing' ? 'pending' : 'conflict'}
              size="md"
              showLabel={true}
              variant="full"
            />
          </View>
        )}
      </View>

      {/* Error Display */}
      {error && (
        <View style={[commonStyles.container, { padding: theme.spacing.md }]}>
          <Card style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
          </Card>
        </View>
      )}

      {/* Feedback List */}
      <FlatList
        style={styles.list}
        contentContainerStyle={[
          styles.listContent,
          feedback.length === 0 && { flex: 1 }
        ]}
        data={feedback}
        renderItem={renderFeedbackItem}
        keyExtractor={(item) => item.id.toString()}
        ItemSeparatorComponent={renderItemSeparator}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
            colors={[theme.colors.primary]}
          />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};