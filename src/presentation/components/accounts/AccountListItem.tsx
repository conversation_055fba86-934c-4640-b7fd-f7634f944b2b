import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Account } from '@/shared/types';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles, useCommonStyles } from '@/shared/theme';
import { MaterialIcon } from '@/presentation/components/common/MaterialIcon';
import { AccountService } from '@/business/services/AccountService';
import { AccountCalculatorFactory } from '@/business/services/calculators/AccountCalculatorFactory';

interface AccountListItemProps {
  account: Account;
  onPress?: (account: Account) => void;
  onLongPress?: (account: Account) => void;
  showBalance?: boolean;
  isSelected?: boolean;
  style?: any;
}



// Get account type config from AccountService
const getAccountTypeConfig = (type: Account['type']) => {
  const accountService = new AccountService();
  const config = accountService.getAccountTypesConfig();
  return config[type] || { icon: 'bank', color: '#4A90E2', label: 'Account' };
};

const formatCurrency = (amount: number, currency: string = 'INR'): string => {
  const symbol = currency === 'INR' ? '₹' : '$';
  const formatted = Math.abs(amount).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  return amount < 0 ? `-${symbol}${formatted}` : `${symbol}${formatted}`;
};

// Component-specific themed styles
const createAccountItemStyles = createStyles((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.lg,
    marginVertical: theme.spacing.xs,
    marginHorizontal: theme.spacing.md,
    borderLeftWidth: 4,
    ...theme.shadows.sm,
  },
  selectedContainer: {
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  inactiveContainer: {
    opacity: 0.5,
  },
  leftSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    ...theme.typography.body,
    color: theme.colors.text,
    fontWeight: '600',
    marginBottom: 2,
  },
  accountType: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  balance: {
    ...theme.typography.h3,
    marginBottom: 2,
  },
  lastUpdated: {
    ...theme.typography.caption,
    fontSize: 12,
    color: theme.colors.textDisabled,
  },
  secondaryInfo: {
    ...theme.typography.caption,
    fontSize: 11,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  progressBar: {
    width: 60,
    height: 3,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    marginBottom: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
}));

const getSyncStatusInfo = (syncStatus: Account['sync_status']) => {
  switch (syncStatus) {
    case 'synced':
      return { icon: '✓', status: 'synced', label: 'Synced' };
    case 'pending':
      return { icon: '⏳', status: 'pending', label: 'Syncing...' };
    case 'conflict':
      return { icon: '❌', status: 'conflict', label: 'Sync Conflict' };
    case 'local':
    default:
      return { icon: '📱', status: 'offline', label: 'Local Only' };
  }
};

// Enhanced balance calculation for account-specific display
const getEnhancedBalanceInfo = async (account: Account): Promise<{
  displayBalance: string;
  secondaryInfo?: string;
  balanceColor: string;
  metadata?: Record<string, any>;
}> => {
  try {
    // For credit and loan accounts, use the calculator system through AccountService
    if ((account.type === 'credit' || account.type === 'loan') && AccountCalculatorFactory.isAccountTypeSupported(account.type)) {
      try {
        const accountService = new AccountService();
        const balanceResult = await accountService.calculateAccountBalanceEnhanced(account.id);
        
        // Format the balance data according to account type
        if (account.type === 'credit') {
          const utilization = balanceResult.metadata.creditUtilization || 0;
          return {
            displayBalance: formatCurrency(balanceResult.displayBalance, account.currency || 'INR'),
            secondaryInfo: `${Math.min(utilization, 100).toFixed(1)}% used`,
            balanceColor: utilization > 80 ? '#D0021B' : utilization > 50 ? '#F5A623' : '#7ED321',
            metadata: {
              availableCredit: balanceResult.displayBalance,
              utilization,
              outstandingBalance: balanceResult.actualBalance,
              creditLimit: balanceResult.metadata.creditLimit,
              minimumPaymentDue: balanceResult.metadata.minimumPaymentDue
            }
          };
        } else if (account.type === 'loan') {
          return {
            displayBalance: formatCurrency(balanceResult.displayBalance, account.currency || 'INR'),
            secondaryInfo: balanceResult.metadata.minimumPaymentDue 
              ? `EMI: ${formatCurrency(balanceResult.metadata.minimumPaymentDue, account.currency || 'INR')}`
              : undefined,
            balanceColor: '#4ECDC4',
            metadata: {
              remainingPrincipal: balanceResult.displayBalance,
              principalAmount: balanceResult.metadata.principalAmount,
              emiAmount: balanceResult.metadata.minimumPaymentDue
            }
          };
        }
      } catch (calculatorError) {
        console.warn('Calculator failed, falling back to simple calculation:', calculatorError);
        // Fall through to fallback logic below
      }
    }
    
    // Fallback for unsupported account types or calculator failures
    // Use simple logic for credit cards based on metadata
    if (account.type === 'credit' && account.metadata?.creditLimit && typeof account.metadata.creditLimit === 'number') {
      const outstandingBalance = Math.abs(account.balance || 0);
      const availableCredit = Math.max(0, account.metadata.creditLimit - outstandingBalance);
      const utilization = account.metadata.creditLimit > 0 
        ? Math.min((outstandingBalance / account.metadata.creditLimit) * 100, 100)
        : 0;
      
      return {
        displayBalance: formatCurrency(availableCredit, account.currency || 'INR'),
        secondaryInfo: `${utilization.toFixed(1)}% used`,
        balanceColor: utilization > 80 ? '#D0021B' : utilization > 50 ? '#F5A623' : '#7ED321',
        metadata: { availableCredit, utilization, outstandingBalance }
      };
    }
    
    // Simple fallback for loan accounts
    if (account.type === 'loan' && account.metadata?.emiAmount && typeof account.metadata.emiAmount === 'number') {
      return {
        displayBalance: formatCurrency(Math.max(0, account.balance || 0), account.currency || 'INR'),
        secondaryInfo: `EMI: ${formatCurrency(account.metadata.emiAmount, account.currency || 'INR')}`,
        balanceColor: '#4ECDC4',
        metadata: { emiAmount: account.metadata.emiAmount }
      };
    }
    
    // Standard balance display for all other accounts
    return {
      displayBalance: formatCurrency(account.balance || 0, account.currency || 'INR'),
      balanceColor: (account.balance || 0) >= 0 ? '#333' : '#D0021B'
    };
  } catch (error) {
    console.warn('Failed to calculate enhanced balance:', error);
    // Ensure we always return valid string values
    return {
      displayBalance: formatCurrency(account.balance || 0, account.currency || 'INR'),
      balanceColor: (account.balance || 0) >= 0 ? '#333' : '#D0021B'
    };
  }
};

export const AccountListItem: React.FC<AccountListItemProps> = ({
  account,
  onPress,
  onLongPress,
  showBalance = true,
  isSelected = false,
  style,
}) => {
  const theme = useTheme();
  const commonStyles = useCommonStyles(theme);
  const styles = createAccountItemStyles(theme);
  const typeConfig = getAccountTypeConfig(account.type);
  const syncInfo = getSyncStatusInfo(account.sync_status);
  
  // Enhanced balance state
  const [enhancedBalance, setEnhancedBalance] = useState<{
    displayBalance: string;
    secondaryInfo?: string;
    balanceColor: string;
    metadata?: Record<string, any>;
  } | null>(null);
  
  // Load enhanced balance information
  useEffect(() => {
    const loadEnhancedBalance = async () => {
      try {
        const balanceInfo = await getEnhancedBalanceInfo(account);
        setEnhancedBalance(balanceInfo);
      } catch (error) {
        console.warn('Failed to load enhanced balance:', error);
        // Fallback to basic balance display
        setEnhancedBalance({
          displayBalance: formatCurrency(account.balance, account.currency),
          balanceColor: account.balance >= 0 ? theme.colors.text : theme.colors.error
        });
      }
    };
    
    loadEnhancedBalance();
  }, [account, theme.colors]);
  
  const handlePress = () => {
    if (onPress) {
      onPress(account);
    } else {
      // Default press behavior - show account selected alert
      Alert.alert(
        'Account Selected',
        `You selected: ${account.name}`
      );
    }
  };
  
  const handleLongPress = () => {
    if (onLongPress) {
      onLongPress(account);
    } else {
      // Default long press behavior - show account options
      Alert.alert(
        'Account Options',
        `Options for: ${account.name}`,
        [
          { text: 'Edit', onPress: () => {/* TODO: Navigate to edit */} },
          { text: 'Delete', style: 'destructive', onPress: () => {/* TODO: Handle delete */} },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };

  const getAccessibilityLabel = () => {
    const parts = [
      `${typeConfig.label} account`,
      account.name,
    ];
    
    if (showBalance) {
      parts.push(`Balance: ${formatCurrency(account.balance, account.currency)}`);
    }
    
    parts.push(`Status: ${syncInfo.label}`);
    
    if (!account.is_active) {
      parts.push('Inactive');
    }
    
    if (isSelected) {
      parts.push('Selected');
    }
    
    return parts.join(', ');
  };
  
  return (
    <TouchableOpacity
      testID="account-list-item"
      style={[
        styles.container,
        { borderLeftColor: typeConfig.color },
        isSelected && styles.selectedContainer,
        !account.is_active && styles.inactiveContainer,
        style,
      ]}
      onPress={handlePress}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
      accessible={true}
      accessibilityLabel={getAccessibilityLabel()}
      accessibilityRole="button"
      accessibilityState={{
        selected: isSelected,
        disabled: !account.is_active,
      }}
    >
      <View style={styles.leftSection}>
        <View 
          style={[
            commonStyles.iconContainer,
            { backgroundColor: `${typeConfig.color}15` }
          ]}
        >
          <MaterialIcon name={typeConfig.icon} size={20} color={typeConfig.color} />
        </View>
        
        <View style={styles.accountInfo}>
          <Text 
            style={styles.accountName} 
            numberOfLines={1}
          >
            {account.name}
          </Text>
          <Text style={styles.accountType}>
            {typeConfig.label}
          </Text>
        </View>
      </View>
      
      {showBalance && enhancedBalance && (
        <View style={styles.rightSection}>
          <Text 
            style={[
              styles.balance,
              { color: enhancedBalance.balanceColor }
            ]}
            numberOfLines={1}
          >
            {enhancedBalance.displayBalance}
          </Text>
          
          {/* Enhanced Account-Specific Information */}
          {enhancedBalance.secondaryInfo && (
            <Text style={styles.secondaryInfo}>
              {enhancedBalance.secondaryInfo}
            </Text>
          )}
          
          {/* Account Type Specific Details */}
          {account.type === 'credit' && enhancedBalance.metadata?.utilization && (
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { 
                    width: `${Math.min(enhancedBalance.metadata.utilization, 100)}%`,
                    backgroundColor: enhancedBalance.balanceColor 
                  }
                ]} 
              />
            </View>
          )}
          
          {/* Last Updated Indicator */}
          {account.updated_at && (
            <Text style={styles.lastUpdated}>
              {new Date(account.updated_at).toLocaleDateString('en-IN', {
                day: '2-digit',
                month: 'short'
              })}
            </Text>
          )}
        </View>
      )}
      
      {/* Fallback for loading or error states */}
      {showBalance && !enhancedBalance && (
        <View style={styles.rightSection}>
          <Text style={[styles.balance, { color: theme.colors.textSecondary }]}>
            Loading...
          </Text>
        </View>
      )}
      
      {/* Selection Indicator */}
      {isSelected && (
        <View style={commonStyles.selectionIndicator}>
          <Text style={commonStyles.selectionCheckmark}>
            ✓
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};


// Export additional utility functions for use in other components
export { formatCurrency, getAccountTypeConfig, getSyncStatusInfo };