import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Account, AccountMetadata } from '@/shared/types';
import { useAccountStore } from '@/shared/stores/accountStore';
import { CompactAccountTypeSelector } from '@/presentation/components/common/CompactSelectors';
import { AccountService } from '@/business/services/AccountService';
import { MaterialIcon } from '@/presentation/components/common/MaterialIcon';
import { FormDateTimePicker } from '@/presentation/components/common/DateTimePicker';

interface AccountCreationFormProps {
  onSuccess?: (account: Account) => void;
  onCancel?: () => void;
}


// Define account type descriptions for the form
const ACCOUNT_TYPE_DESCRIPTIONS = {
  checking: 'For daily transactions and expenses',
  savings: 'For saving money and earning interest',
  credit: 'For credit purchases and payments',
  loan: 'For tracking loan balances and payments',
  investment: 'For investment portfolios and assets'
};

export const AccountCreationForm: React.FC<AccountCreationFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const [name, setName] = useState('');
  const [selectedType, setSelectedType] = useState<Account['type'] | null>(null);
  const [balance, setBalance] = useState('');
  const [currency] = useState('INR'); // Fixed for now, can be made configurable
  const [nameError, setNameError] = useState('');
  const [balanceError, setBalanceError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Enhanced account metadata fields
  const [creditLimit, setCreditLimit] = useState('');
  const [paymentDueDate, setPaymentDueDate] = useState(new Date());
  const [nextEmiDate, setNextEmiDate] = useState(new Date());
  
  // Loan-specific fields
  const [principalAmount, setPrincipalAmount] = useState('');
  const [interestRate, setInterestRate] = useState('');
  const [emiAmount, setEmiAmount] = useState('');
  const [tenure, setTenure] = useState('');
  
  // Validation errors for metadata
  const [creditLimitError, setCreditLimitError] = useState('');
  const [principalError, setPrincipalError] = useState('');
  const [interestRateError, setInterestRateError] = useState('');
  const [emiError, setEmiError] = useState('');
  const [tenureError, setTenureError] = useState('');
  
  // Safely get account store with error handling
  let createAccount: (data: any) => Promise<Account>;
  let accounts: Account[];
  try {
    const accountStore = useAccountStore();
    createAccount = accountStore.createAccount;
    accounts = accountStore.accounts || [];
  } catch (error) {
    console.error('Failed to access account store:', error);
    accounts = [];
    createAccount = async () => { throw new Error('Account store not available'); };
  }

  // Create account types array from AccountService with error handling
  const ACCOUNT_TYPES = useMemo(() => {
    try {
      const accountService = new AccountService();
      const config = accountService.getAccountTypesConfig();
      
      return Object.entries(config).map(([type, typeConfig]) => ({
        type: type as Account['type'],
        icon: typeConfig.icon,
        color: typeConfig.color,
        label: typeConfig.label,
        description: ACCOUNT_TYPE_DESCRIPTIONS[type as keyof typeof ACCOUNT_TYPE_DESCRIPTIONS]
      }));
    } catch (error) {
      console.error('Failed to load account types config:', error);
      // Fallback account types if service fails
      return [
        { type: 'checking' as Account['type'], icon: 'bank', color: '#4A90E2', label: 'Checking Account', description: 'For daily transactions and expenses' },
        { type: 'savings' as Account['type'], icon: 'wallet', color: '#7ED321', label: 'Savings Account', description: 'For saving money and earning interest' },
        { type: 'credit' as Account['type'], icon: 'credit-card', color: '#F5A623', label: 'Credit Card', description: 'For credit purchases and payments' },
        { type: 'loan' as Account['type'], icon: 'trending-down', color: '#D0021B', label: 'Loan Account', description: 'For tracking loan balances and payments' },
        { type: 'investment' as Account['type'], icon: 'trending-up', color: '#9013FE', label: 'Investment Account', description: 'For investment portfolios and assets' }
      ];
    }
  }, []);

  const validateName = (value: string): boolean => {
    setNameError('');
    
    if (!value.trim()) {
      setNameError('Account name is required');
      return false;
    }
    
    if (value.trim().length < 2) {
      setNameError('Account name must be at least 2 characters');
      return false;
    }
    
    if (value.trim().length > 50) {
      setNameError('Account name must be less than 50 characters');
      return false;
    }
    
    // Check for duplicate names
    const existingAccount = accounts.find(
      account => account.name.toLowerCase() === value.trim().toLowerCase()
    );
    if (existingAccount) {
      setNameError('Account name already exists');
      return false;
    }
    
    return true;
  };

  const validateBalance = (value: string): boolean => {
    setBalanceError('');
    
    if (!value.trim()) {
      return true; // Balance is optional, defaults to 0
    }
    
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) {
      setBalanceError('Please enter a valid number');
      return false;
    }
    
    if (numericValue < -*********.99 || numericValue > *********.99) {
      setBalanceError('Balance must be between -999,999,999.99 and 999,999,999.99');
      return false;
    }
    
    return true;
  };

  const validateCreditLimit = (value: string): boolean => {
    setCreditLimitError('');
    
    if (selectedType === 'credit' && !value.trim()) {
      setCreditLimitError('Credit limit is required for credit cards');
      return false;
    }
    
    if (value.trim()) {
      const numericValue = parseFloat(value);
      if (isNaN(numericValue) || numericValue <= 0) {
        setCreditLimitError('Credit limit must be a positive number');
        return false;
      }
      
      if (numericValue > 10000000) {
        setCreditLimitError('Credit limit seems too high');
        return false;
      }
    }
    
    return true;
  };

  const validatePrincipalAmount = (value: string): boolean => {
    setPrincipalError('');
    
    if (selectedType === 'loan' && !value.trim()) {
      setPrincipalError('Principal amount is required for loans');
      return false;
    }
    
    if (value.trim()) {
      const numericValue = parseFloat(value);
      if (isNaN(numericValue) || numericValue <= 0) {
        setPrincipalError('Principal amount must be a positive number');
        return false;
      }
    }
    
    return true;
  };

  const validateInterestRate = (value: string): boolean => {
    setInterestRateError('');
    
    if (selectedType === 'loan' && !value.trim()) {
      setInterestRateError('Interest rate is required for loans');
      return false;
    }
    
    if (value.trim()) {
      const numericValue = parseFloat(value);
      if (isNaN(numericValue) || numericValue <= 0 || numericValue >= 100) {
        setInterestRateError('Interest rate must be between 0% and 100%');
        return false;
      }
    }
    
    return true;
  };

  const validateEMIAmount = (value: string): boolean => {
    setEmiError('');
    
    if (selectedType === 'loan' && !value.trim()) {
      setEmiError('EMI amount is required for loans');
      return false;
    }
    
    if (value.trim()) {
      const numericValue = parseFloat(value);
      if (isNaN(numericValue) || numericValue <= 0) {
        setEmiError('EMI amount must be a positive number');
        return false;
      }
    }
    
    return true;
  };

  const validateTenure = (value: string): boolean => {
    setTenureError('');
    
    if (selectedType === 'loan' && !value.trim()) {
      setTenureError('Tenure is required for loans');
      return false;
    }
    
    if (value.trim()) {
      const numericValue = parseInt(value);
      if (isNaN(numericValue) || numericValue <= 0 || numericValue > 360) {
        setTenureError('Tenure must be between 1 and 360 months');
        return false;
      }
    }
    
    return true;
  };

  const handleNameChange = (value: string) => {
    setName(value);
    if (nameError) {
      validateName(value);
    }
  };

  const handleBalanceChange = (value: string) => {
    // Allow only numbers, decimal point, and minus sign
    const cleanValue = value.replace(/[^0-9.-]/g, '');
    setBalance(cleanValue);
    if (balanceError) {
      validateBalance(cleanValue);
    }
  };

  const handleCreditLimitChange = (value: string) => {
    const cleanValue = value.replace(/[^0-9.]/g, '');
    setCreditLimit(cleanValue);
    if (creditLimitError) {
      validateCreditLimit(cleanValue);
    }
  };

  const handlePrincipalAmountChange = (value: string) => {
    const cleanValue = value.replace(/[^0-9.]/g, '');
    setPrincipalAmount(cleanValue);
    if (principalError) {
      validatePrincipalAmount(cleanValue);
    }
  };

  const handleInterestRateChange = (value: string) => {
    const cleanValue = value.replace(/[^0-9.]/g, '');
    setInterestRate(cleanValue);
    if (interestRateError) {
      validateInterestRate(cleanValue);
    }
  };

  const handleEMIAmountChange = (value: string) => {
    const cleanValue = value.replace(/[^0-9.]/g, '');
    setEmiAmount(cleanValue);
    if (emiError) {
      validateEMIAmount(cleanValue);
    }
  };

  const handleTenureChange = (value: string) => {
    const cleanValue = value.replace(/[^0-9]/g, '');
    setTenure(cleanValue);
    if (tenureError) {
      validateTenure(cleanValue);
    }
  };

  // Clear metadata fields when account type changes
  const handleAccountTypeChange = (accountType: Account['type']) => {
    setSelectedType(accountType);
    
    // Reset all metadata fields and errors
    setCreditLimit('');
    setPaymentDueDate(new Date());
    setPrincipalAmount('');
    setInterestRate('');
    setEmiAmount('');
    setTenure('');
    setNextEmiDate(new Date());
    setCreditLimitError('');
    setPrincipalError('');
    setInterestRateError('');
    setEmiError('');
    setTenureError('');
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;
    
    // Validate all fields
    const isNameValid = validateName(name);
    const isBalanceValid = validateBalance(balance);
    let isCreditLimitValid = true;
    let isPrincipalValid = true;
    let isInterestRateValid = true;
    let isEMIValid = true;
    let isTenureValid = true;
    
    // Validate metadata based on account type
    if (selectedType === 'credit') {
      isCreditLimitValid = validateCreditLimit(creditLimit);
    } else if (selectedType === 'loan') {
      isPrincipalValid = validatePrincipalAmount(principalAmount);
      isInterestRateValid = validateInterestRate(interestRate);
      isEMIValid = validateEMIAmount(emiAmount);
      isTenureValid = validateTenure(tenure);
    }
    
    if (!selectedType) {
      Alert.alert('Error', 'Please select an account type');
      return;
    }
    
    if (!isNameValid || !isBalanceValid || !isCreditLimitValid || 
        !isPrincipalValid || !isInterestRateValid || !isEMIValid || !isTenureValid) {
      return;
    }
    
    // Check free tier limit (mock implementation)
    const activeAccounts = accounts.filter(acc => acc.is_active);
    if (activeAccounts.length >= 5) {
      Alert.alert(
        'Account Limit Reached',
        'You have reached the maximum of 5 accounts for the free tier. Upgrade to premium to add more accounts.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Upgrade', onPress: () => {/* TODO: Navigate to upgrade screen */} }
        ]
      );
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Create metadata object if needed
      let metadata: AccountMetadata | undefined = undefined;
      if (selectedType === 'credit') {
        const creditMetadata: AccountMetadata = {
          autoCalculate: true,
        };
        if (creditLimit) creditMetadata.creditLimit = parseFloat(creditLimit);
        if (paymentDueDate) creditMetadata.paymentDueDate = paymentDueDate.toISOString();
        metadata = creditMetadata;
      } else if (selectedType === 'loan') {
        const loanMetadata: AccountMetadata = {
          autoCalculate: true,
        };
        if (principalAmount) loanMetadata.principalAmount = parseFloat(principalAmount);
        if (interestRate) loanMetadata.interestRate = parseFloat(interestRate);
        if (emiAmount) loanMetadata.emiAmount = parseFloat(emiAmount);
        if (tenure) loanMetadata.tenure = parseInt(tenure);
        if (nextEmiDate) loanMetadata.nextEmiDate = nextEmiDate.toISOString();
        metadata = loanMetadata;
      }

      const accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'> = {
        name: name.trim(),
        type: selectedType,
        balance: balance ? parseFloat(balance) : 0,
        currency,
        is_active: true,
        sync_status: 'local' as const,
        ...(metadata && { metadata }),
      };
      
      const newAccount = await createAccount(accountData);
      
      Alert.alert(
        'Success',
        `${selectedType === 'credit' ? 'Credit Card' : selectedType === 'loan' ? 'Loan Account' : 'Account'} "${newAccount.name}" created successfully!`,
        [{ text: 'OK', onPress: () => onSuccess?.(newAccount) }]
      );
      
      // Reset form
      setName('');
      setSelectedType(null);
      setBalance('');
      setNameError('');
      setBalanceError('');
      setCreditLimit('');
      setPaymentDueDate(new Date());
      setPrincipalAmount('');
      setInterestRate('');
      setEmiAmount('');
      setTenure('');
      setNextEmiDate(new Date());
      setCreditLimitError('');
      setPrincipalError('');
      setInterestRateError('');
      setEmiError('');
      setTenureError('');
      
    } catch (error) {
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to create account'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedTypeConfig = selectedType 
    ? ACCOUNT_TYPES.find(type => type.type === selectedType)
    : null;

  const renderMetadataFields = () => {
    if (selectedType === 'credit') {
      return (
        <>     
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Credit Limit * (₹)</Text>
            <TextInput
              style={[styles.input, creditLimitError ? styles.inputError : null]}
              value={creditLimit}
              onChangeText={handleCreditLimitChange}
              placeholder="e.g., 100000"
              placeholderTextColor="#999"
              keyboardType="numeric"
              onBlur={() => validateCreditLimit(creditLimit)}
            />
            {creditLimitError ? (
              <Text style={styles.errorText}>{creditLimitError}</Text>
            ) : null}
          </View>

          {/* Date picker with error handling */}
          {(() => {
            try {
              return (
                <FormDateTimePicker
                  label="Payment Due Date (Optional)"
                  value={paymentDueDate}
                  onChange={setPaymentDueDate}
                  mode="date"
                  minimumDate={new Date()}
                  containerStyle={styles.inputGroup}
                />
              );
            } catch (error) {
              console.error('FormDateTimePicker error:', error);
              return (
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Payment Due Date (Optional)</Text>
                  <Text style={styles.helperText}>Date picker temporarily unavailable</Text>
                </View>
              );
            }
          })()}
        </>
      );
    }

    if (selectedType === 'loan') {
      return (
        <>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Principal Amount * (₹)</Text>
            <TextInput
              style={[styles.input, principalError ? styles.inputError : null]}
              value={principalAmount}
              onChangeText={handlePrincipalAmountChange}
              placeholder="e.g., 500000"
              placeholderTextColor="#999"
              keyboardType="numeric"
              onBlur={() => validatePrincipalAmount(principalAmount)}
            />
            {principalError ? (
              <Text style={styles.errorText}>{principalError}</Text>
            ) : null}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Interest Rate * (%)</Text>
            <TextInput
              style={[styles.input, interestRateError ? styles.inputError : null]}
              value={interestRate}
              onChangeText={handleInterestRateChange}
              placeholder="e.g., 8.5"
              placeholderTextColor="#999"
              keyboardType="numeric"
              onBlur={() => validateInterestRate(interestRate)}
            />
            {interestRateError ? (
              <Text style={styles.errorText}>{interestRateError}</Text>
            ) : null}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>EMI Amount * (₹)</Text>
            <TextInput
              style={[styles.input, emiError ? styles.inputError : null]}
              value={emiAmount}
              onChangeText={handleEMIAmountChange}
              placeholder="e.g., 12500"
              placeholderTextColor="#999"
              keyboardType="numeric"
              onBlur={() => validateEMIAmount(emiAmount)}
            />
            {emiError ? (
              <Text style={styles.errorText}>{emiError}</Text>
            ) : null}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Tenure * (months)</Text>
            <TextInput
              style={[styles.input, tenureError ? styles.inputError : null]}
              value={tenure}
              onChangeText={handleTenureChange}
              placeholder="e.g., 240"
              placeholderTextColor="#999"
              keyboardType="numeric"
              onBlur={() => validateTenure(tenure)}
            />
            {tenureError ? (
              <Text style={styles.errorText}>{tenureError}</Text>
            ) : null}
          </View>

          {/* Date picker with error handling */}
          {(() => {
            try {
              return (
                <FormDateTimePicker
                  label="Next EMI Date (Optional)"
                  value={nextEmiDate}
                  onChange={setNextEmiDate}
                  mode="date"
                  minimumDate={new Date()}
                  containerStyle={styles.inputGroup}
                />
              );
            } catch (error) {
              console.error('FormDateTimePicker error:', error);
              return (
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Next EMI Date (Optional)</Text>
                  <Text style={styles.helperText}>Date picker temporarily unavailable</Text>
                </View>
              );
            }
          })()}
        </>
      );
    }

    return null;
  };

  try {
    return (
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Create New Account</Text>
          <Text style={styles.subtitle}>
            Add a new financial account to track your money
          </Text>
        </View>

        {/* Account Name Input */}
        <View style={styles.section}>
          <Text style={styles.label}>Account Name *</Text>
          <TextInput
            style={[styles.input, nameError ? styles.inputError : null]}
            value={name}
            onChangeText={handleNameChange}
            placeholder="e.g., Main Checking, Emergency Savings"
            placeholderTextColor="#999"
            maxLength={50}
            onBlur={() => validateName(name)}
          />
          {nameError ? <Text style={styles.errorText}>{nameError}</Text> : null}
        </View>

        {/* Account Type Selection */}
        <View style={styles.section}>
          <Text style={styles.label}>Account Type *</Text>
          <CompactAccountTypeSelector
            options={ACCOUNT_TYPES}
            selectedType={selectedType}
            onSelect={handleAccountTypeChange}
          />
        </View>

      {/* Enhanced Account Metadata Fields */}
      {renderMetadataFields()}

      {/* Initial Balance Input */}
      <View style={styles.section}>
        <Text style={styles.label}>Initial Balance</Text>
        <View style={styles.balanceContainer}>
          <Text style={styles.currencySymbol}>₹</Text>
          <TextInput
            style={[styles.balanceInput, balanceError ? styles.inputError : null]}
            value={balance}
            onChangeText={handleBalanceChange}
            placeholder="0.00"
            placeholderTextColor="#999"
            keyboardType="numeric"
            onBlur={() => validateBalance(balance)}
          />
        </View>
        {balanceError ? <Text style={styles.errorText}>{balanceError}</Text> : null}
        <Text style={styles.helperText}>
          Enter the current balance of this account (optional)
        </Text>
      </View>

      {/* Selected Type Preview */}
      {selectedTypeConfig && (
        <View style={[styles.previewCard, { borderColor: selectedTypeConfig.color }]}>
          <View style={styles.previewHeader}>
            <View style={{ backgroundColor: `${selectedTypeConfig.color}15`, borderRadius: 16, width: 32, height: 32, alignItems: 'center', justifyContent: 'center', marginRight: 12 }}>
              <MaterialIcon name={selectedTypeConfig.icon} size={20} color={selectedTypeConfig.color} />
            </View>
            <View style={styles.previewInfo}>
              <Text style={styles.previewName}>{name || 'Account Name'}</Text>
              <Text style={styles.previewType}>{selectedTypeConfig.label}</Text>
            </View>
            <Text style={styles.previewBalance}>
              ₹{balance ? parseFloat(balance).toFixed(2) : '0.00'}
            </Text>
          </View>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onCancel}
          disabled={isSubmitting}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.createButton,
            (!name || !selectedType || isSubmitting) && styles.createButtonDisabled
          ]}
          onPress={handleSubmit}
          disabled={!name || !selectedType || isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <Text style={styles.createButtonText}>Create Account</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Free Tier Info */}
      <View style={styles.tierInfo}>
        <Text style={styles.tierInfoText}>
          Free tier: {accounts.length}/5 accounts used
        </Text>
        {accounts.length >= 4 && (
          <Text style={styles.tierWarning}>
            {accounts.length === 4 
              ? 'You can create 1 more account'
              : 'Account limit reached - upgrade to add more'
            }
          </Text>
        )}
      </View>
    </ScrollView>
    );
  } catch (error) {
    console.error('AccountCreationForm render error:', error);
    // Fallback UI if component crashes
    return (
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Create New Account</Text>
          <Text style={styles.subtitle}>
            Add a new financial account to track your money
          </Text>
        </View>
        <View style={{ padding: 20, backgroundColor: '#ffebee', borderRadius: 8, margin: 20 }}>
          <Text style={{ color: '#d32f2f', fontSize: 16, fontWeight: 'bold' }}>
            Error Loading Form
          </Text>
          <Text style={{ color: '#d32f2f', marginTop: 8 }}>
            There was an issue loading the account creation form. Please try restarting the app.
          </Text>
        </View>
      </ScrollView>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  header: {
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 8,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  inputError: {
    borderColor: '#D0021B',
  },
  errorText: {
    color: '#D0021B',
    fontSize: 14,
    marginTop: 4,
  },
  helperText: {
    color: '#666',
    fontSize: 14,
    marginTop: 4,
  },
  helpText: {
    color: '#666',
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  typeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  typeOption: {
    flex: 1,
    minWidth: '45%',
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    backgroundColor: '#fff',
    borderColor: '#ddd',
  },
  typeOptionSelected: {
    backgroundColor: '#f8f9ff',
    borderWidth: 2,
  },
  typeIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  typeLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 4,
  },
  typeDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    lineHeight: 16,
  },
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  currencySymbol: {
    fontSize: 16,
    color: '#333',
    paddingLeft: 12,
    fontWeight: '600',
  },
  balanceInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    borderWidth: 0,
  },
  previewCard: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    backgroundColor: '#f8f9ff',
    marginBottom: 25,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  previewInfo: {
    flex: 1,
  },
  previewName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  previewType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  previewBalance: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  cancelButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '600',
  },
  createButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#4A90E2',
    alignItems: 'center',
  },
  createButtonDisabled: {
    backgroundColor: '#ccc',
  },
  createButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  tierInfo: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    alignItems: 'center',
  },
  tierInfoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  tierWarning: {
    fontSize: 14,
    color: '#F5A623',
    fontWeight: '600',
  },
});