import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Account } from '@/shared/types';
import { useAccountStore } from '@/shared/stores/accountStore';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';
import { AccountListItem, formatCurrency, getAccountTypeConfig } from './AccountListItem';

interface AccountListProps {
  onAccountPress?: (account: Account) => void;
  onAccountLongPress?: (account: Account) => void;
  showBalance?: boolean;
  allowSelection?: boolean;
  selectedAccountIds?: number[];
  onSelectionChange?: (selectedIds: number[]) => void;
  filterByType?: Account['type'][];
  sortBy?: 'name' | 'balance' | 'type' | 'updated_at';
  sortOrder?: 'asc' | 'desc';
  showSummary?: boolean;
  emptyStateMessage?: string;
  style?: any;
}

interface AccountSummary {
  totalBalance: number;
  accountsByType: Record<Account['type'], { count: number; balance: number }>;
  activeAccounts: number;
  lastUpdated: Date | null;
}

export const AccountList: React.FC<AccountListProps> = ({
  onAccountPress,
  onAccountLongPress,
  showBalance = true,
  allowSelection = false,
  selectedAccountIds = [],
  onSelectionChange,
  filterByType,
  sortBy = 'name',
  sortOrder = 'asc',
  showSummary = false,
  emptyStateMessage = 'No accounts found. Create your first account to get started.',
  style,
}) => {
  const theme = useTheme();
  const {
    accounts,
    loading,
    error,
    deleteAccount,
  } = useAccountStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [localSelectedIds, setLocalSelectedIds] = useState<number[]>(selectedAccountIds);

  // REMOVED: Duplicate loadAccounts() call that was causing infinite loops
  // Parent component should handle loading accounts instead

  useEffect(() => {
    setLocalSelectedIds(selectedAccountIds);
  }, [selectedAccountIds]);

  const summary = useMemo((): AccountSummary | null => {
    if (!showSummary || accounts.length === 0) return null;
    
    const activeAccounts = accounts.filter(account => account.is_active);
    const totalBalance = activeAccounts.reduce((sum, account) => sum + account.balance, 0);
    
    const accountsByType: Record<Account['type'], { count: number; balance: number }> = {
      checking: { count: 0, balance: 0 },
      savings: { count: 0, balance: 0 },
      credit: { count: 0, balance: 0 },
      loan: { count: 0, balance: 0 },
      investment: { count: 0, balance: 0 },
    };
    
    activeAccounts.forEach(account => {
      accountsByType[account.type].count += 1;
      accountsByType[account.type].balance += account.balance;
    });
    
    const lastUpdated = activeAccounts.reduce((latest, account) => {
      const accountDate = account.updated_at ? new Date(account.updated_at) : null;
      if (!accountDate) return latest;
      return !latest || accountDate > latest ? accountDate : latest;
    }, null as Date | null);
    
    return {
      totalBalance,
      accountsByType,
      activeAccounts: activeAccounts.length,
      lastUpdated,
    };
  }, [accounts, showSummary]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // REMOVED: loadAccounts() and loadAccountSummary() calls
      // Parent component should handle loading logic
      // Parent component should handle loading logic
    } catch (error) {
      console.error('Failed to refresh accounts:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getFilteredAndSortedAccounts = (): Account[] => {
    let filteredAccounts = accounts.filter(account => account.is_active);
    
    // Apply type filter
    if (filterByType && filterByType.length > 0) {
      filteredAccounts = filteredAccounts.filter(account => 
        filterByType.includes(account.type)
      );
    }
    
    // Apply sorting
    filteredAccounts.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'balance':
          comparison = a.balance - b.balance;
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'updated_at':
          const aDate = a.updated_at ? new Date(a.updated_at).getTime() : 0;
          const bDate = b.updated_at ? new Date(b.updated_at).getTime() : 0;
          comparison = aDate - bDate;
          break;
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });
    
    return filteredAccounts;
  };

  const handleAccountPress = (account: Account) => {
    if (allowSelection) {
      const newSelectedIds = localSelectedIds.includes(account.id)
        ? localSelectedIds.filter(id => id !== account.id)
        : [...localSelectedIds, account.id];
      
      setLocalSelectedIds(newSelectedIds);
      onSelectionChange?.(newSelectedIds);
    } else {
      onAccountPress?.(account);
    }
  };

  const handleAccountLongPress = (account: Account) => {
    if (onAccountLongPress) {
      onAccountLongPress(account);
    } else {
      // Default long press behavior - show account options
      Alert.alert(
        account.name,
        `Manage this ${getAccountTypeConfig(account.type).label.toLowerCase()} account`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Edit', onPress: () => {/* TODO: Navigate to edit */} },
          { 
            text: 'Delete', 
            style: 'destructive', 
            onPress: () => handleDeleteAccount(account)
          },
        ]
      );
    }
  };

  const handleDeleteAccount = (account: Account) => {
    Alert.alert(
      'Delete Account',
      `Are you sure you want to delete "${account.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteAccount(account.id);
              Alert.alert('Success', 'Account deleted successfully');
            } catch (error) {
              Alert.alert(
                'Error',
                error instanceof Error ? error.message : 'Failed to delete account'
              );
            }
          }
        },
      ]
    );
  };

  const renderSummaryCard = () => {
    if (!showSummary || !summary) return null;
    
    return (
      <View style={styles.summaryCard}>
        <View style={styles.summaryHeader}>
          <Text style={styles.summaryTitle}>Account Summary</Text>
          {summary.lastUpdated && (
            <Text style={styles.summaryLastUpdated}>
              Updated {summary.lastUpdated.toLocaleDateString('en-IN')}
            </Text>
          )}
        </View>
        
        <View style={styles.summaryStats}>
          <View style={styles.summaryStatItem}>
            <Text style={styles.summaryStatValue}>{summary.activeAccounts}</Text>
            <Text style={styles.summaryStatLabel}>Active Accounts</Text>
          </View>
          
          <View style={styles.summaryStatItem}>
            <Text style={[styles.summaryStatValue, styles.totalBalance]}>
              {formatCurrency(summary.totalBalance)}
            </Text>
            <Text style={styles.summaryStatLabel}>Total Balance</Text>
          </View>
        </View>
        
        <View style={styles.summaryTypeBreakdown}>
          {Object.entries(summary.accountsByType)
            .filter(([_, data]) => data.count > 0)
            .map(([type, data]) => {
              const config = getAccountTypeConfig(type as Account['type']);
              return (
                <View key={type} style={styles.summaryTypeItem}>
                  <Text style={styles.summaryTypeIcon}>{config.icon}</Text>
                  <View style={styles.summaryTypeInfo}>
                    <Text style={styles.summaryTypeName}>
                      {config.label} ({data.count})
                    </Text>
                    <Text style={[styles.summaryTypeBalance, { color: config.color }]}>
                      {formatCurrency(data.balance)}
                    </Text>
                  </View>
                </View>
              );
            })
          }
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>💳</Text>
      <Text style={styles.emptyStateTitle}>No Accounts</Text>
      <Text style={styles.emptyStateMessage}>{emptyStateMessage}</Text>
    </View>
  );

  const renderAccountItem = ({ item }: { item: Account }) => (
    <AccountListItem
      account={item}
      onPress={handleAccountPress}
      onLongPress={handleAccountLongPress}
      showBalance={showBalance}
      isSelected={localSelectedIds.includes(item.id)}
    />
  );

  const styles = createAccountListStyles(theme);
  const filteredAccounts = getFilteredAndSortedAccounts();

  if (loading && !refreshing) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading accounts...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={onRefresh}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {renderSummaryCard()}
      
      {filteredAccounts.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={filteredAccounts}
          renderItem={renderAccountItem}
          keyExtractor={(item) => item.id.toString()}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
        />
      )}
      
      {/* Selection Summary */}
      {allowSelection && localSelectedIds.length > 0 && (
        <View style={styles.selectionSummary}>
          <Text style={styles.selectionText}>
            {localSelectedIds.length} account{localSelectedIds.length !== 1 ? 's' : ''} selected
          </Text>
        </View>
      )}
    </View>
  );
};

// Component-specific themed styles
const createAccountListStyles = createStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    ...theme.typography.body,
    color: theme.colors.textSecondary,
  },
  errorText: {
    ...theme.typography.body,
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  retryButtonText: {
    color: theme.colors.background,
    ...theme.typography.button,
  },
  listContent: {
    paddingBottom: theme.spacing.xl,
  },
  summaryCard: {
    backgroundColor: theme.colors.card,
    margin: theme.spacing.md,
    padding: theme.spacing.xl,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.md,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  summaryTitle: {
    ...theme.typography.h3,
    color: theme.colors.text,
  },
  summaryLastUpdated: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing.xl,
  },
  summaryStatItem: {
    alignItems: 'center',
  },
  summaryStatValue: {
    ...theme.typography.h2,
    color: theme.colors.text,
  },
  totalBalance: {
    color: theme.colors.primary,
  },
  summaryStatLabel: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  summaryTypeBreakdown: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: theme.spacing.md,
  },
  summaryTypeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  summaryTypeIcon: {
    fontSize: 20,
    marginRight: theme.spacing.md,
  },
  summaryTypeInfo: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryTypeName: {
    ...theme.typography.body,
    color: theme.colors.text,
  },
  summaryTypeBalance: {
    ...theme.typography.body,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xxl,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: theme.spacing.md,
  },
  emptyStateTitle: {
    ...theme.typography.h3,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  emptyStateMessage: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  selectionSummary: {
    backgroundColor: theme.colors.primary,
    padding: theme.spacing.md,
    alignItems: 'center',
  },
  selectionText: {
    color: theme.colors.background,
    ...theme.typography.body,
    fontWeight: '600',
  },
}));