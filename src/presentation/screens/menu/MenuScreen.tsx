import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../../shared/theme/ThemeProvider';
import { FallbackIcon } from '../../components/common/FallbackIcon';

interface MenuItem {
  id: string;
  title: string;
  icon: string;
  onPress: () => void;
  badge?: string;
  disabled?: boolean;
}

interface MenuScreenProps {
  navigation?: any; // TODO: Type this properly with navigation prop types
  menuItems?: MenuItem[];
}

export const MenuScreen: React.FC<MenuScreenProps> = ({
  navigation,
  menuItems = [],
}) => {
  const theme = useTheme();

  const defaultMenuItems: MenuItem[] = [
    {
      id: 'feedback',
      title: 'Feedback & Support',
      icon: 'message-circle',
      onPress: () => {
        // TODO: Implement proper feedback screen navigation
        console.log('Feedback screen navigation - to be implemented');
      },
    },
    {
      id: 'community',
      title: 'Community',
      icon: 'users',
      onPress: () => {
        // TODO: Navigate to community screen
        console.log('Community feature coming soon');
      },
      badge: 'Soon',
      disabled: true,
    },
    {
      id: 'export',
      title: 'Export Data',
      icon: 'download',
      onPress: () => {
        // TODO: Export data functionality
        console.log('Export feature coming soon');
      },
      disabled: true,
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'settings',
      onPress: () => {
        // TODO: Navigate to settings screen
        console.log('Settings screen coming soon');
      },
      disabled: true,
    },
    {
      id: 'backup',
      title: 'Backup & Sync',
      icon: 'cloud',
      onPress: () => {
        // TODO: Backup functionality
        console.log('Backup feature coming soon');
      },
      disabled: true,
    },
    {
      id: 'about',
      title: 'About FinVibe',
      icon: 'info',
      onPress: () => {
        // TODO: About screen
        console.log('About screen coming soon');
      },
      disabled: true,
    },
  ];

  const items = menuItems.length > 0 ? menuItems : defaultMenuItems;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.background, borderBottomColor: theme.colors.border }]}>
        <View>
          <Text style={[styles.title, { color: theme.colors.text }]}>Menu</Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            Settings and more
          </Text>
        </View>
      </View>

      {/* Menu Items */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {items.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.menuItem,
              { borderBottomColor: theme.colors.border },
              item.disabled && styles.disabledItem,
            ]}
            onPress={() => {
              if (!item.disabled) {
                item.onPress();
              }
            }}
            disabled={item.disabled}
          >
            <View style={styles.menuItemContent}>
              <View style={styles.menuItemLeft}>
                <View style={[styles.iconContainer, { backgroundColor: theme.colors.surface }]}>
                  <FallbackIcon
                    icon={item.icon}
                    name={item.title}
                    size={24}
                    style={{ 
                      color: item.disabled ? theme.colors.textSecondary : theme.colors.primary 
                    }}
                  />
                </View>
                <View style={styles.menuItemTextContainer}>
                  <Text
                    style={[
                      styles.menuItemTitle,
                      { 
                        color: item.disabled ? theme.colors.textSecondary : theme.colors.text 
                      },
                    ]}
                  >
                    {item.title}
                  </Text>
                </View>
              </View>
              
              <View style={styles.menuItemRight}>
                {item.badge && (
                  <View style={[styles.badge, { backgroundColor: theme.colors.warning }]}>
                    <Text style={[styles.badgeText, { color: theme.colors.background }]}>
                      {item.badge}
                    </Text>
                  </View>
                )}
                <FallbackIcon
                  icon="chevron-right"
                  name="Navigate"
                  size={20}
                  style={{ 
                    color: theme.colors.textSecondary,
                    opacity: 0.5 
                  }}
                />
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Footer */}
      <View style={[styles.footer, { borderTopColor: theme.colors.border }]}>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          FinVibe v1.0.0 • Local First Finance
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  content: {
    flex: 1,
    paddingTop: 8,
  },
  menuItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  disabledItem: {
    opacity: 0.6,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  menuItemTextContainer: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 8,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  footer: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
  },
});