import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { BiometricAuthService } from '../../../platform/biometric/BiometricAuthService';
import { AuthMethod } from '../../../platform/biometric/AuthenticationManager';

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  action?: () => Promise<void>;
  optional?: boolean;
}

interface AuthSetupTutorialScreenProps {
  onComplete: (selectedMethod: AuthMethod) => void;
  onSkip?: () => void;
}

export const AuthSetupTutorialScreen: React.FC<AuthSetupTutorialScreenProps> = ({
  onComplete,
  onSkip,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<AuthMethod>('none');

  React.useEffect(() => {
    checkBiometricAvailability();
  }, []);

  const checkBiometricAvailability = async () => {
    try {
      const capabilities = await BiometricAuthService.detectCapabilities();
      setBiometricAvailable(capabilities.isAvailable);
      
      if (capabilities.isAvailable) {
        setSelectedMethod('biometric');
      } else {
        setSelectedMethod('pin');
      }
    } catch {
      setBiometricAvailable(false);
      setSelectedMethod('pin');
    }
  };

  const tutorialSteps: TutorialStep[] = [
    {
      id: 'intro',
      title: 'Secure Your Financial Data',
      description: 'Let\'s set up authentication to protect your financial information. This tutorial will guide you through the process.',
      icon: '🔐',
    },
    {
      id: 'understand_security',
      title: 'Why Security Matters',
      description: 'Your financial data contains sensitive information about your spending, income, and financial habits. Securing this data protects your privacy and prevents unauthorized access.',
      icon: '🛡️',
    },
    {
      id: 'local_security',
      title: 'Device-Only Protection',
      description: 'Your authentication credentials are stored securely on your device using hardware-backed security features. They never leave your device.',
      icon: '📱',
    },
    {
      id: 'choose_method',
      title: 'Choose Your Security Method',
      description: biometricAvailable 
        ? 'You can use biometric authentication (fingerprint/face) or create a secure PIN. Biometric is recommended for convenience and security.'
        : 'Your device doesn\'t support biometric authentication. We\'ll set up a secure PIN to protect your data.',
      icon: biometricAvailable ? '👆' : '🔢',
    },
    {
      id: 'test_authentication',
      title: 'Test Your Security',
      description: 'Once set up, we\'ll test your authentication method to ensure it works correctly.',
      icon: '✅',
      action: async () => {
        if (selectedMethod === 'biometric') {
          await testBiometricAuth();
        }
      },
    },
    {
      id: 'backup_options',
      title: 'Backup Options',
      description: 'If your primary authentication method fails, you can always change it in Settings. You can also reset all authentication if needed.',
      icon: '🔄',
    },
    {
      id: 'ready',
      title: 'You\'re All Set!',
      description: 'Your authentication is configured. You can now securely access your financial data. Remember, you can always change these settings later.',
      icon: '🎉',
    },
  ];

  const testBiometricAuth = async () => {
    try {
      const result = await BiometricAuthService.authenticate('Test your biometric authentication');
      
      if (result.success) {
        Alert.alert(
          'Success!',
          'Your biometric authentication is working correctly.',
          [{ text: 'Great!' }]
        );
      } else {
        Alert.alert(
          'Authentication Test Failed',
          result.error || 'Biometric authentication test failed. You can still proceed.',
          [{ text: 'Continue' }]
        );
      }
    } catch {
      Alert.alert(
        'Test Error',
        'Could not test biometric authentication. You can still proceed with setup.',
        [{ text: 'Continue' }]
      );
    }
  };

  const handleNext = async () => {
    const currentStepData = tutorialSteps[currentStep];
    
    // Execute step action if available
    if (currentStepData.action) {
      await currentStepData.action();
    }

    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(selectedMethod);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    } else {
      onComplete('none');
    }
  };

  const handleMethodChange = (method: AuthMethod) => {
    setSelectedMethod(method);
  };

  const renderMethodSelection = () => {
    if (tutorialSteps[currentStep].id !== 'choose_method') {
      return null;
    }

    return (
      <View style={styles.methodSelection}>
        <Text style={styles.methodSelectionTitle}>Available Options:</Text>
        
        {biometricAvailable && (
          <TouchableOpacity
            style={[
              styles.methodOption,
              selectedMethod === 'biometric' && styles.methodOptionSelected,
            ]}
            onPress={() => handleMethodChange('biometric')}
          >
            <View style={styles.methodOptionContent}>
              <Text style={styles.methodOptionIcon}>👆</Text>
              <View style={styles.methodOptionText}>
                <Text style={styles.methodOptionTitle}>Biometric Authentication</Text>
                <Text style={styles.methodOptionDescription}>
                  Use fingerprint or face recognition (Recommended)
                </Text>
              </View>
              <View style={[
                styles.radioButton,
                selectedMethod === 'biometric' && styles.radioButtonSelected,
              ]}>
                {selectedMethod === 'biometric' && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>
            </View>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[
            styles.methodOption,
            selectedMethod === 'pin' && styles.methodOptionSelected,
          ]}
          onPress={() => handleMethodChange('pin')}
        >
          <View style={styles.methodOptionContent}>
            <Text style={styles.methodOptionIcon}>🔢</Text>
            <View style={styles.methodOptionText}>
              <Text style={styles.methodOptionTitle}>PIN Code</Text>
              <Text style={styles.methodOptionDescription}>
                4-6 digit secure PIN that you create
              </Text>
            </View>
            <View style={[
              styles.radioButton,
              selectedMethod === 'pin' && styles.radioButtonSelected,
            ]}>
              {selectedMethod === 'pin' && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.methodOption,
            selectedMethod === 'pattern' && styles.methodOptionSelected,
          ]}
          onPress={() => handleMethodChange('pattern')}
        >
          <View style={styles.methodOptionContent}>
            <Text style={styles.methodOptionIcon}>⭕</Text>
            <View style={styles.methodOptionText}>
              <Text style={styles.methodOptionTitle}>Pattern Lock</Text>
              <Text style={styles.methodOptionDescription}>
                Draw a pattern by connecting dots
              </Text>
            </View>
            <View style={[
              styles.radioButton,
              selectedMethod === 'pattern' && styles.radioButtonSelected,
            ]}>
              {selectedMethod === 'pattern' && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderProgressIndicator = () => {
    return (
      <View style={styles.progressContainer}>
        {tutorialSteps.map((_, index) => (
          <View
            key={index}
            style={[
              styles.progressDot,
              index === currentStep && styles.progressDotActive,
              index < currentStep && styles.progressDotCompleted,
            ]}
          />
        ))}
      </View>
    );
  };

  const currentStepData = tutorialSteps[currentStep];
  const isLastStep = currentStep === tutorialSteps.length - 1;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Authentication Setup</Text>
        <Text style={styles.headerSubtitle}>
          Step {currentStep + 1} of {tutorialSteps.length}
        </Text>
        {renderProgressIndicator()}
      </View>

      {/* Content */}
      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        <View style={styles.stepContainer}>
          <View style={styles.iconContainer}>
            <Text style={styles.icon}>{currentStepData.icon}</Text>
          </View>

          <Text style={styles.title}>{currentStepData.title}</Text>
          <Text style={styles.description}>{currentStepData.description}</Text>

          {renderMethodSelection()}
        </View>
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.buttonRow}>
          {/* Skip/Back Button */}
          {currentStep === 0 ? (
            <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
              <Text style={styles.skipButtonText}>Skip Tutorial</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
          )}

          {/* Next/Complete Button */}
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Text style={styles.nextButtonText}>
              {isLastStep ? 'Complete Setup' : 'Continue'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#000000',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  progressDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#E5E5E7',
  },
  progressDotActive: {
    backgroundColor: '#007AFF',
    width: 20,
  },
  progressDotCompleted: {
    backgroundColor: '#34C759',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingBottom: 20,
  },
  stepContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  iconContainer: {
    marginBottom: 24,
  },
  icon: {
    fontSize: 64,
  },
  title: {
    fontSize: 26,
    fontWeight: '700',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  methodSelection: {
    alignSelf: 'stretch',
    marginTop: 16,
  },
  methodSelectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  methodOption: {
    borderWidth: 2,
    borderColor: '#E5E5E7',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  methodOptionSelected: {
    borderColor: '#007AFF',
    backgroundColor: 'rgba(0, 122, 255, 0.05)',
  },
  methodOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  methodOptionIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  methodOptionText: {
    flex: 1,
  },
  methodOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  methodOptionDescription: {
    fontSize: 14,
    color: '#666666',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E5E5E7',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    borderColor: '#007AFF',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#007AFF',
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
    paddingTop: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  skipButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  skipButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  backButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  backButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  nextButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    minWidth: 120,
    alignItems: 'center',
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});