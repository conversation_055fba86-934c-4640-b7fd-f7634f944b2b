import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
} from 'react-native';

interface PrivacySection {
  id: string;
  title: string;
  content: string[];
  icon: string;
  important?: boolean;
}

interface PrivacyExplanationScreenProps {
  onAccept: () => void;
  onDecline?: () => void;
  showAcceptDecline?: boolean;
}

const privacySections: PrivacySection[] = [
  {
    id: 'no_collection',
    title: 'We Don\'t Collect Your Data',
    icon: '🚫',
    content: [
      'FinVibe doesn\'t collect, store, or transmit your personal information to our servers',
      'No user accounts, email addresses, or personal identifiers are required',
      'Your financial data never leaves your device unless you choose premium cloud sync',
      'We have no access to your banking information, transaction details, or spending patterns',
    ],
    important: true,
  },
  {
    id: 'local_storage',
    title: 'Local Data Storage',
    icon: '📱',
    content: [
      'All your financial data is stored locally on your device using encrypted databases',
      'Data is protected with device-level security features like hardware encryption',
      'Your information remains private even if your device is lost (with proper lock screen)',
      'Deleting the app removes all your data permanently from the device',
    ],
  },
  {
    id: 'authentication',
    title: 'Authentication & Security',
    icon: '🔐',
    content: [
      'Biometric data (fingerprints, face scans) never leaves your device',
      'PINs and patterns are hashed and stored in your device\'s secure keychain',
      'We cannot access or reset your authentication credentials',
      'Security audit logs are stored locally and contain no personal information',
    ],
  },
  {
    id: 'sms_processing',
    title: 'SMS Transaction Processing',
    icon: '📨',
    content: [
      'SMS messages are processed locally on your device to extract transaction information',
      'We never access or store your SMS messages',
      'Only anonymized transaction patterns are used to improve our processing algorithms',
      'You control which SMS messages the app can access through system permissions',
    ],
  },
  {
    id: 'premium_features',
    title: 'Premium Cloud Sync (Optional)',
    icon: '☁️',
    content: [
      'Premium users can optionally enable encrypted cloud backup',
      'Data is encrypted on your device before being uploaded to secure cloud storage',
      'Even with cloud sync, we cannot read your financial data',
      'Cloud sync requires explicit opt-in and can be disabled at any time',
      'You can delete all cloud data from your account settings',
    ],
  },
  {
    id: 'no_tracking',
    title: 'No Tracking or Analytics',
    icon: '🕵️‍♀️',
    content: [
      'FinVibe doesn\'t use tracking pixels, analytics, or advertising networks',
      'We don\'t collect device identifiers, location data, or usage statistics',
      'No third-party advertising or marketing companies have access to your information',
      'We don\'t build user profiles or behavioral patterns for any purpose',
    ],
  },
  {
    id: 'open_source',
    title: 'Transparency & Trust',
    icon: '👁️',
    content: [
      'Our privacy practices are documented and verifiable',
      'Key security components are open-source and auditable',
      'We welcome security researchers and provide responsible disclosure channels',
      'This privacy explanation is built into the app, not hidden in lengthy terms',
    ],
  },
  {
    id: 'your_rights',
    title: 'Your Data Rights',
    icon: '⚖️',
    content: [
      'You own all your financial data - we claim no rights to it',
      'Export your data anytime in standard formats',
      'Delete all data by uninstalling the app (local) or using account settings (cloud)',
      'No vendor lock-in - your data remains accessible and portable',
      'Right to use the app completely offline without any data sharing',
    ],
  },
];

export const PrivacyExplanationScreen: React.FC<PrivacyExplanationScreenProps> = ({
  onAccept,
  onDecline,
  showAcceptDecline = false,
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const expandAllSections = () => {
    setExpandedSections(new Set(privacySections.map(section => section.id)));
  };

  const collapseAllSections = () => {
    setExpandedSections(new Set());
  };

  const renderSection = (section: PrivacySection) => {
    const isExpanded = expandedSections.has(section.id);

    return (
      <View
        key={section.id}
        style={[
          styles.sectionContainer,
          section.important && styles.importantSection,
        ]}
      >
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection(section.id)}
        >
          <View style={styles.sectionHeaderLeft}>
            <Text style={styles.sectionIcon}>{section.icon}</Text>
            <Text style={[
              styles.sectionTitle,
              section.important && styles.importantSectionTitle,
            ]}>
              {section.title}
            </Text>
          </View>
          <Text style={styles.expandIcon}>
            {isExpanded ? '▼' : '▶'}
          </Text>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.sectionContent}>
            {section.content.map((item, index) => (
              <View key={index} style={styles.contentItem}>
                <Text style={styles.contentBullet}>•</Text>
                <Text style={styles.contentText}>{item}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Privacy Matters</Text>
        <Text style={styles.headerSubtitle}>
          Understanding how FinVibe protects your financial data
        </Text>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={expandAllSections}
        >
          <Text style={styles.quickActionText}>Expand All</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={collapseAllSections}
        >
          <Text style={styles.quickActionText}>Collapse All</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Summary */}
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>In Summary</Text>
          <Text style={styles.summaryText}>
            FinVibe is designed with privacy-first principles. Your financial data stays on your device, 
            no accounts are required, and we don't collect or track your information. 
            This explanation details exactly how we protect your privacy.
          </Text>
        </View>

        {/* Sections */}
        <View style={styles.sectionsContainer}>
          {privacySections.map(renderSection)}
        </View>

        {/* Additional Information */}
        <View style={styles.additionalInfo}>
          <Text style={styles.additionalInfoTitle}>Questions about Privacy?</Text>
          <Text style={styles.additionalInfoText}>
            If you have questions about our privacy practices, you can review this information 
            anytime in the app settings. Our commitment to your privacy is fundamental to how 
            FinVibe operates.
          </Text>
        </View>
      </ScrollView>

      {/* Footer */}
      {showAcceptDecline ? (
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            By continuing, you acknowledge that you understand our privacy practices.
          </Text>
          <View style={styles.buttonRow}>
            {onDecline && (
              <TouchableOpacity style={styles.declineButton} onPress={onDecline}>
                <Text style={styles.declineButtonText}>Go Back</Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity 
              style={[styles.acceptButton, showAcceptDecline && styles.acceptButtonFlex]} 
              onPress={onAccept}
            >
              <Text style={styles.acceptButtonText}>I Understand</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.footer}>
          <TouchableOpacity style={styles.acceptButton} onPress={onAccept}>
            <Text style={styles.acceptButtonText}>Continue</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#000000',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  quickActionButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
  },
  quickActionText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingBottom: 20,
  },
  summaryContainer: {
    backgroundColor: '#E8F4FD',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  sectionsContainer: {
    gap: 12,
  },
  sectionContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    overflow: 'hidden',
  },
  importantSection: {
    borderWidth: 2,
    borderColor: '#34C759',
    backgroundColor: '#F0FDF4',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sectionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
  },
  importantSectionTitle: {
    color: '#15803D',
  },
  expandIcon: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 8,
  },
  sectionContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 0,
  },
  contentItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  contentBullet: {
    fontSize: 16,
    color: '#666666',
    marginRight: 8,
    marginTop: 1,
  },
  contentText: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  additionalInfo: {
    marginTop: 32,
    padding: 20,
    backgroundColor: '#FFF9E6',
    borderRadius: 12,
  },
  additionalInfoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#B45309',
    marginBottom: 8,
  },
  additionalInfoText: {
    fontSize: 14,
    color: '#92400E',
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
    paddingTop: 20,
    backgroundColor: '#ffffff',
  },
  footerText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  declineButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E5E7',
    alignItems: 'center',
  },
  declineButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666666',
  },
  acceptButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  acceptButtonFlex: {
    flex: 1,
  },
  acceptButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});