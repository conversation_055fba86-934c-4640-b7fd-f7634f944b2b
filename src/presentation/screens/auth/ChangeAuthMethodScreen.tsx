import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { PINInput } from '../../components/auth/PINInput';
import { PatternLock } from '../../components/auth/PatternLock';
import { AuthenticationManager, AuthMethod } from '../../../platform/biometric/AuthenticationManager';

interface ChangeAuthMethodScreenProps {
  currentMethod: AuthMethod;
  newMethod: AuthMethod;
  onComplete: (success: boolean) => void;
  onCancel: () => void;
}

type ChangeStep = 'verify_current' | 'setup_new' | 'confirm_new' | 'complete';

export const ChangeAuthMethodScreen: React.FC<ChangeAuthMethodScreenProps> = ({
  currentMethod,
  newMethod,
  onComplete,
  onCancel,
}) => {
  const [currentStep, setCurrentStep] = useState<ChangeStep>('verify_current');
  const [, setCurrentCredential] = useState<string | number[]>('');
  const [newCredential, setNewCredential] = useState<string | number[]>('');
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const getMethodDisplayName = (method: AuthMethod): string => {
    switch (method) {
      case 'biometric':
        return 'Biometric';
      case 'pin':
        return 'PIN';
      case 'pattern':
        return 'Pattern';
      default:
        return 'Unknown';
    }
  };

  const getStepTitle = (): string => {
    switch (currentStep) {
      case 'verify_current':
        return `Verify Current ${getMethodDisplayName(currentMethod)}`;
      case 'setup_new':
        return `Setup New ${getMethodDisplayName(newMethod)}`;
      case 'confirm_new':
        return `Confirm New ${getMethodDisplayName(newMethod)}`;
      case 'complete':
        return 'Security Method Changed';
      default:
        return 'Change Security Method';
    }
  };

  const getStepSubtitle = (): string => {
    switch (currentStep) {
      case 'verify_current':
        return `Enter your current ${getMethodDisplayName(currentMethod).toLowerCase()} to continue`;
      case 'setup_new':
        return `Create your new ${getMethodDisplayName(newMethod).toLowerCase()}`;
      case 'confirm_new':
        return `Confirm your new ${getMethodDisplayName(newMethod).toLowerCase()}`;
      case 'complete':
        return `Your security method has been changed to ${getMethodDisplayName(newMethod).toLowerCase()}`;
      default:
        return '';
    }
  };

  const handleCurrentMethodComplete = async (credential: string | number[]) => {
    setIsLoading(true);
    setShowError(false);

    try {
      const result = await AuthenticationManager.authenticate(currentMethod, credential);
      
      if (result.success) {
        setCurrentCredential(credential);
        setCurrentStep('setup_new');
      } else {
        setErrorMessage(result.error || 'Authentication failed');
        setShowError(true);
      }
    } catch {
      setErrorMessage('Verification failed');
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewMethodComplete = (credential: string | number[]) => {
    setNewCredential(credential);
    setCurrentStep('confirm_new');
    setShowError(false);
  };

  const handleConfirmComplete = async (credential: string | number[]) => {
    setIsLoading(true);
    setShowError(false);

    // Verify credentials match
    const credentialsMatch = 
      newMethod === 'pin' 
        ? (newCredential as string) === (credential as string)
        : newMethod === 'pattern'
        ? JSON.stringify(newCredential as number[]) === JSON.stringify(credential as number[])
        : false;

    if (!credentialsMatch) {
      setErrorMessage(`${getMethodDisplayName(newMethod)}s don't match. Try again.`);
      setShowError(true);
      setIsLoading(false);
      return;
    }

    try {
      // Setup new authentication method
      const result = await AuthenticationManager.setupAuthMethod(newMethod, credential);
      
      if (result.success) {
        setCurrentStep('complete');
        setTimeout(() => {
          onComplete(true);
        }, 2000);
      } else {
        setErrorMessage(result.error || 'Failed to setup new security method');
        setShowError(true);
      }
    } catch {
      setErrorMessage('Failed to save new security method');
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleError = (error: string) => {
    setErrorMessage(error);
    setShowError(true);
  };

  const goBackToSetup = () => {
    setCurrentStep('setup_new');
    setNewCredential('');
    setShowError(false);
  };

  const renderCurrentStep = () => {
    if (currentStep === 'verify_current') {
      if (currentMethod === 'pin') {
        return (
          <PINInput
            onComplete={handleCurrentMethodComplete}
            onError={handleError}
            showError={showError}
            errorMessage={errorMessage}
            disabled={isLoading}
            title="Enter Current PIN"
            subtitle="Verify your identity to continue"
          />
        );
      } else if (currentMethod === 'pattern') {
        return (
          <PatternLock
            onComplete={handleCurrentMethodComplete}
            onError={handleError}
            showError={showError}
            errorMessage={errorMessage}
            disabled={isLoading}
            title="Draw Current Pattern"
            subtitle="Verify your identity to continue"
          />
        );
      }
    }

    if (currentStep === 'setup_new') {
      if (newMethod === 'pin') {
        return (
          <PINInput
            onComplete={handleNewMethodComplete}
            onError={handleError}
            showError={showError}
            errorMessage={errorMessage}
            disabled={isLoading}
            title="Create New PIN"
            subtitle="Choose a secure PIN that you'll remember"
          />
        );
      } else if (newMethod === 'pattern') {
        return (
          <PatternLock
            onComplete={handleNewMethodComplete}
            onError={handleError}
            showError={showError}
            errorMessage={errorMessage}
            disabled={isLoading}
            title="Create New Pattern"
            subtitle="Draw a pattern that you'll remember"
          />
        );
      }
    }

    if (currentStep === 'confirm_new') {
      if (newMethod === 'pin') {
        return (
          <View style={styles.confirmContainer}>
            <PINInput
              onComplete={handleConfirmComplete}
              onError={handleError}
              showError={showError}
              errorMessage={errorMessage}
              disabled={isLoading}
              title="Confirm New PIN"
              subtitle="Enter your PIN again to confirm"
            />
            <TouchableOpacity style={styles.backButton} onPress={goBackToSetup}>
              <Text style={styles.backButtonText}>Back to Setup</Text>
            </TouchableOpacity>
          </View>
        );
      } else if (newMethod === 'pattern') {
        return (
          <View style={styles.confirmContainer}>
            <PatternLock
              onComplete={handleConfirmComplete}
              onError={handleError}
              showError={showError}
              errorMessage={errorMessage}
              disabled={isLoading}
              title="Confirm New Pattern"
              subtitle="Draw your pattern again to confirm"
            />
            <TouchableOpacity style={styles.backButton} onPress={goBackToSetup}>
              <Text style={styles.backButtonText}>Back to Setup</Text>
            </TouchableOpacity>
          </View>
        );
      }
    }

    if (currentStep === 'complete') {
      return (
        <View style={styles.completeContainer}>
          <Text style={styles.successIcon}>✅</Text>
          <Text style={styles.successTitle}>Security Method Changed!</Text>
          <Text style={styles.successMessage}>
            Your security method has been successfully changed to {getMethodDisplayName(newMethod).toLowerCase()}.
          </Text>
        </View>
      );
    }

    return null;
  };

  const renderProgress = () => {
    const steps = ['verify_current', 'setup_new', 'confirm_new', 'complete'];
    const currentIndex = steps.indexOf(currentStep);

    return (
      <View style={styles.progressContainer}>
        {steps.slice(0, -1).map((step, index) => (
          <View
            key={step}
            style={[
              styles.progressStep,
              index <= currentIndex && styles.progressStepActive,
              index === currentIndex && styles.progressStepCurrent,
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        {currentStep !== 'complete' && renderProgress()}
        <Text style={styles.title}>{getStepTitle()}</Text>
        <Text style={styles.subtitle}>{getStepSubtitle()}</Text>
      </View>

      <View style={styles.content}>
        {renderCurrentStep()}
      </View>

      {currentStep === 'verify_current' && (
        <View style={styles.footer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    alignItems: 'center',
    padding: 20,
    paddingBottom: 0,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    gap: 8,
  },
  progressStep: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E5E5E7',
  },
  progressStepActive: {
    backgroundColor: '#007AFF',
  },
  progressStepCurrent: {
    backgroundColor: '#34C759',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
  },
  confirmContainer: {
    flex: 1,
  },
  backButton: {
    alignSelf: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginTop: 20,
  },
  backButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
  },
  completeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  successIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 16,
  },
  successMessage: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  footer: {
    padding: 20,
  },
  cancelButton: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
});