import React, { useState } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  // Dimensions,
} from 'react-native';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';
import { Button } from '@/presentation/components/core';

// const { width: screenWidth } = Dimensions.get('window'); // Reserved for responsive design

interface OnboardingStep {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  benefits: string[];
}

interface OnboardingScreenProps {
  onComplete: () => void;
  onSkip?: () => void;
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'no_account',
    title: 'No Account Required',
    subtitle: 'Start using FinVibe immediately',
    description: 'Unlike other financial apps, FinVibe doesn\'t require you to create an account or provide personal information. Your privacy is protected from day one.',
    icon: '🚀',
    benefits: [
      'No email registration needed',
      'No personal information required',
      'Start tracking finances instantly',
      'Complete privacy protection',
    ],
  },
  {
    id: 'local_data',
    title: 'Your Data Stays Local',
    subtitle: 'All information remains on your device',
    description: 'Your financial data never leaves your device unless you choose to upgrade to premium cloud sync. This ensures maximum privacy and security.',
    icon: '📱',
    benefits: [
      'Data stored locally on your device',
      'No cloud uploads by default',
      'Works completely offline',
      'You control your information',
    ],
  },
  {
    id: 'secure_access',
    title: 'Secure Device Authentication',
    subtitle: 'Protect your data with biometric security',
    description: 'Secure your financial information with your fingerprint, face, or a secure PIN. No passwords to remember or accounts to manage.',
    icon: '🔐',
    benefits: [
      'Biometric authentication available',
      'Secure PIN protection option',
      'Quick and convenient access',
      'No passwords to remember',
    ],
  },
  {
    id: 'privacy_first',
    title: 'Privacy-First Design',
    subtitle: 'Built with your privacy in mind',
    description: 'FinVibe is designed from the ground up to protect your privacy. No tracking, no ads, no data collection - just a secure financial tracker.',
    icon: '🛡️',
    benefits: [
      'No user tracking or analytics',
      'No advertisements',
      'Zero data collection',
      'Open about our privacy practices',
    ],
  },
];

export const OnboardingScreen: React.FC<OnboardingScreenProps> = ({
  onComplete,
  onSkip,
}) => {
  const theme = useTheme();
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    } else {
      onComplete();
    }
  };

  const renderStep = (step: OnboardingStep) => {
    return (
      <View style={styles.stepContainer}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Icon */}
          <View style={styles.iconContainer}>
            <Text style={styles.icon}>{step.icon}</Text>
          </View>

          {/* Content */}
          <View style={styles.contentContainer}>
            <Text style={styles.title}>{step.title}</Text>
            <Text style={styles.subtitle}>{step.subtitle}</Text>
            <Text style={styles.description}>{step.description}</Text>

            {/* Benefits */}
            <View style={styles.benefitsContainer}>
              <Text style={styles.benefitsTitle}>Key Benefits:</Text>
              {step.benefits.map((benefit, index) => (
                <View key={index} style={styles.benefitItem}>
                  <Text style={styles.benefitBullet}>✓</Text>
                  <Text style={styles.benefitText}>{benefit}</Text>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderProgressIndicator = () => {
    return (
      <View style={styles.progressContainer}>
        {onboardingSteps.map((_, index) => (
          <View
            key={index}
            style={[
              styles.progressDot,
              index === currentStep && styles.progressDotActive,
              index < currentStep && styles.progressDotCompleted,
            ]}
          />
        ))}
      </View>
    );
  };

  const styles = createOnboardingStyles(theme);
  const currentStepData = onboardingSteps[currentStep];
  const isLastStep = currentStep === onboardingSteps.length - 1;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Welcome to FinVibe</Text>
        <Text style={styles.headerSubtitle}>
          Step {currentStep + 1} of {onboardingSteps.length}
        </Text>
        {renderProgressIndicator()}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {renderStep(currentStepData)}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.buttonRow}>
          {/* Skip/Back Button */}
          {currentStep === 0 ? (
            <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
              <Text style={styles.skipButtonText}>Skip</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
          )}

          {/* Next/Get Started Button */}
          <Button
            title={isLastStep ? 'Get Started' : 'Next'}
            onPress={handleNext}
            variant="primary"
            size="lg"
            style={{ minWidth: 120 }}
          />
        </View>

        {/* Alternative skip option */}
        {currentStep > 0 && (
          <TouchableOpacity style={styles.skipAllButton} onPress={handleSkip}>
            <Text style={styles.skipAllButtonText}>Skip remaining steps</Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
};

// Component-specific themed styles
const createOnboardingStyles = createStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.lg,
  },
  headerTitle: {
    ...theme.typography.h1,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  headerSubtitle: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xl,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.border,
  },
  progressDotActive: {
    backgroundColor: theme.colors.primary,
    width: 24,
  },
  progressDotCompleted: {
    backgroundColor: theme.colors.success,
  },
  content: {
    flex: 1,
  },
  stepContainer: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.xxl,
  },
  icon: {
    fontSize: 80,
  },
  contentContainer: {
    alignItems: 'center',
  },
  title: {
    ...theme.typography.h1,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    ...theme.typography.h3,
    color: theme.colors.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
    fontWeight: '500',
  },
  description: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xxl,
    paddingHorizontal: theme.spacing.sm,
  },
  benefitsContainer: {
    alignSelf: 'stretch',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    marginHorizontal: theme.spacing.sm,
  },
  benefitsTitle: {
    ...theme.typography.body,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  benefitBullet: {
    ...theme.typography.body,
    color: theme.colors.success,
    marginRight: theme.spacing.sm,
    fontWeight: '600',
    marginTop: 2,
  },
  benefitText: {
    flex: 1,
    ...theme.typography.caption,
    color: theme.colors.text,
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.xxl + theme.spacing.xl,
    paddingTop: theme.spacing.xl,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  skipButton: {
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
  },
  skipButtonText: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  backButton: {
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
  },
  backButtonText: {
    ...theme.typography.body,
    color: theme.colors.primary,
    fontWeight: '500',
  },

  skipAllButton: {
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
  },
  skipAllButtonText: {
    ...theme.typography.caption,
    color: theme.colors.textDisabled,
  },
}));