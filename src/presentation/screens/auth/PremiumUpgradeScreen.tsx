import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';

interface PremiumFeature {
  id: string;
  title: string;
  description: string;
  icon: string;
  highlight?: boolean;
}

interface PremiumUpgradeScreenProps {
  onUpgrade: (method: 'google' | 'apple' | 'email') => void;
  onDismiss: () => void;
  triggerFeature?: string; // Feature that triggered the upgrade prompt
}

const premiumFeatures: PremiumFeature[] = [
  {
    id: 'cloud_sync',
    title: 'Secure Cloud Backup',
    description: 'Encrypted backup of your financial data with end-to-end encryption',
    icon: '☁️',
    highlight: true,
  },
  {
    id: 'family_sharing',
    title: 'Family Sharing',
    description: 'Share budgets and expenses securely with family members',
    icon: '👨‍👩‍👧‍👦',
  },
  {
    id: 'multi_device',
    title: 'Multi-Device Access',
    description: 'Access your data across all your devices with real-time sync',
    icon: '📱',
  },
  {
    id: 'advanced_analytics',
    title: 'Advanced Analytics',
    description: 'Detailed spending insights, trends, and financial health reports',
    icon: '📊',
  },
  {
    id: 'receipt_storage',
    title: 'Receipt Storage',
    description: 'Scan and store receipt images securely in the cloud',
    icon: '🧾',
  },
  {
    id: 'priority_support',
    title: 'Priority Support',
    description: 'Get help faster with dedicated premium customer support',
    icon: '🎯',
  },
];

export const PremiumUpgradeScreen: React.FC<PremiumUpgradeScreenProps> = ({
  onUpgrade,
  onDismiss,
  triggerFeature,
}) => {
  const [selectedPlan, setSelectedPlan] = useState<'monthly' | 'yearly'>('yearly');

  const getFeatureTriggerMessage = () => {
    switch (triggerFeature) {
      case 'cloud_sync':
        return 'Enable secure cloud backup to sync your data across devices';
      case 'family_sharing':
        return 'Upgrade to premium to share budgets with family members';
      case 'receipt_storage':
        return 'Premium users can scan and store receipt images';
      case 'advanced_analytics':
        return 'Get detailed insights with premium analytics features';
      default:
        return 'Unlock powerful features to enhance your financial management';
    }
  };

  const handleUpgradeMethod = (method: 'google' | 'apple' | 'email') => {
    Alert.alert(
      'Upgrade to Premium',
      `You'll be redirected to sign up with ${method === 'google' ? 'Google' : method === 'apple' ? 'Apple' : 'email'} and complete your premium subscription.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Continue',
          onPress: () => onUpgrade(method),
        },
      ]
    );
  };

  const renderFeature = (feature: PremiumFeature) => {
    const isTriggered = feature.id === triggerFeature;

    return (
      <View
        key={feature.id}
        style={[
          styles.featureItem,
          (feature.highlight || isTriggered) && styles.highlightFeatureItem,
        ]}
      >
        <View style={styles.featureHeader}>
          <Text style={styles.featureIcon}>{feature.icon}</Text>
          <View style={styles.featureTextContainer}>
            <Text style={[
              styles.featureTitle,
              (feature.highlight || isTriggered) && styles.highlightFeatureTitle,
            ]}>
              {feature.title}
            </Text>
            <Text style={styles.featureDescription}>{feature.description}</Text>
          </View>
          {(feature.highlight || isTriggered) && (
            <View style={styles.highlightBadge}>
              <Text style={styles.highlightBadgeText}>
                {isTriggered ? 'NEEDED' : 'POPULAR'}
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderPricingPlan = (
    plan: 'monthly' | 'yearly',
    price: string,
    originalPrice?: string,
    savings?: string
  ) => {
    const isSelected = selectedPlan === plan;

    return (
      <TouchableOpacity
        style={[
          styles.pricingPlan,
          isSelected && styles.selectedPricingPlan,
        ]}
        onPress={() => setSelectedPlan(plan)}
      >
        <View style={styles.pricingPlanHeader}>
          <Text style={[
            styles.pricingPlanTitle,
            isSelected && styles.selectedPricingPlanTitle,
          ]}>
            {plan === 'monthly' ? 'Monthly' : 'Yearly'}
          </Text>
          {savings && (
            <View style={styles.savingsBadge}>
              <Text style={styles.savingsText}>Save {savings}</Text>
            </View>
          )}
        </View>
        <View style={styles.pricingPlanPriceContainer}>
          <Text style={[
            styles.pricingPlanPrice,
            isSelected && styles.selectedPricingPlanPrice,
          ]}>
            {price}
          </Text>
          {originalPrice && (
            <Text style={styles.originalPrice}>{originalPrice}</Text>
          )}
        </View>
        <View style={[
          styles.radioButton,
          isSelected && styles.selectedRadioButton,
        ]}>
          {isSelected && <View style={styles.radioButtonInner} />}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.closeButton} onPress={onDismiss}>
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Upgrade to Premium</Text>
        <Text style={styles.headerSubtitle}>{getFeatureTriggerMessage()}</Text>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Features */}
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Premium Features</Text>
          <View style={styles.featuresContainer}>
            {premiumFeatures.map(renderFeature)}
          </View>
        </View>

        {/* Pricing */}
        <View style={styles.pricingSection}>
          <Text style={styles.sectionTitle}>Choose Your Plan</Text>
          <View style={styles.pricingContainer}>
            {renderPricingPlan('monthly', '$4.99/month')}
            {renderPricingPlan('yearly', '$3.99/month', '$47.88/year', '20%')}
          </View>
        </View>

        {/* Privacy Notice */}
        <View style={styles.privacyNotice}>
          <Text style={styles.privacyNoticeTitle}>🔒 Your Privacy Remains Protected</Text>
          <Text style={styles.privacyNoticeText}>
            Even with premium features, your financial data remains encrypted and private. 
            Cloud sync uses end-to-end encryption, and we never have access to your actual financial information.
          </Text>
        </View>
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerTitle}>Sign up with:</Text>
        
        <View style={styles.authMethodsContainer}>
          <TouchableOpacity
            style={styles.authMethodButton}
            onPress={() => handleUpgradeMethod('google')}
          >
            <Text style={styles.authMethodIcon}>🔍</Text>
            <Text style={styles.authMethodText}>Google</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.authMethodButton}
            onPress={() => handleUpgradeMethod('apple')}
          >
            <Text style={styles.authMethodIcon}>🍎</Text>
            <Text style={styles.authMethodText}>Apple</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.authMethodButton}
            onPress={() => handleUpgradeMethod('email')}
          >
            <Text style={styles.authMethodIcon}>✉️</Text>
            <Text style={styles.authMethodText}>Email</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.termsText}>
          By upgrading, you agree to our Terms of Service and Privacy Policy. 
          Cancel anytime from your account settings.
        </Text>

        <TouchableOpacity style={styles.continueFreeButto} onPress={onDismiss}>
          <Text style={styles.continueFreeButtonText}>Continue with Free Version</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 8,
    paddingBottom: 24,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 8,
    right: 24,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 8,
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
  },
  featuresSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  featuresContainer: {
    gap: 12,
  },
  featureItem: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
  },
  highlightFeatureItem: {
    backgroundColor: '#E8F4FD',
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  featureHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  featureIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  highlightFeatureTitle: {
    color: '#007AFF',
  },
  featureDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  highlightBadge: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginLeft: 8,
  },
  highlightBadgeText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#ffffff',
    textTransform: 'uppercase',
  },
  pricingSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  pricingContainer: {
    gap: 12,
  },
  pricingPlan: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E5E7',
    borderRadius: 12,
    padding: 16,
  },
  selectedPricingPlan: {
    borderColor: '#007AFF',
    backgroundColor: 'rgba(0, 122, 255, 0.05)',
  },
  pricingPlanHeader: {
    flex: 1,
  },
  pricingPlanTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  selectedPricingPlanTitle: {
    color: '#007AFF',
  },
  savingsBadge: {
    alignSelf: 'flex-start',
    backgroundColor: '#34C759',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  savingsText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  pricingPlanPriceContainer: {
    alignItems: 'flex-end',
    marginRight: 16,
  },
  pricingPlanPrice: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000000',
  },
  selectedPricingPlanPrice: {
    color: '#007AFF',
  },
  originalPrice: {
    fontSize: 14,
    color: '#999999',
    textDecorationLine: 'line-through',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E5E5E7',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedRadioButton: {
    borderColor: '#007AFF',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#007AFF',
  },
  privacyNotice: {
    marginHorizontal: 24,
    marginBottom: 32,
    padding: 16,
    backgroundColor: '#F0FDF4',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#BBF7D0',
  },
  privacyNoticeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#15803D',
    marginBottom: 8,
  },
  privacyNoticeText: {
    fontSize: 12,
    color: '#166534',
    lineHeight: 18,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
    paddingTop: 20,
  },
  footerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 16,
  },
  authMethodsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 20,
  },
  authMethodButton: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
  authMethodIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  authMethodText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
  termsText: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
    lineHeight: 16,
    marginBottom: 16,
  },
  continueFreeButto: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  continueFreeButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
});