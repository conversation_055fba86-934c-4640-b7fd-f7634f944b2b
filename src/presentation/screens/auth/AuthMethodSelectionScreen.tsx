import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { BiometricAuthService, BiometricCapability } from '../../../platform/biometric/BiometricAuthService';
import { AuthMethod } from '../../../platform/biometric/AuthenticationManager';

interface AuthMethodSelectionScreenProps {
  onMethodSelected: (method: AuthMethod) => void;
  onSkip?: () => void;
  title?: string;
  subtitle?: string;
  showSkip?: boolean;
}

interface MethodOption {
  id: AuthMethod;
  title: string;
  description: string;
  icon: string;
  available: boolean;
  recommended?: boolean;
}

export const AuthMethodSelectionScreen: React.FC<AuthMethodSelectionScreenProps> = ({
  onMethodSelected,
  onSkip,
  title = 'Secure Your App',
  subtitle = 'Choose how you want to protect your financial data',
  showSkip = true,
}) => {
  const [capabilities, setCapabilities] = useState<BiometricCapability | null>(null);
  const [selectedMethod, setSelectedMethod] = useState<AuthMethod>('none');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    detectCapabilities();
  }, []);

  const detectCapabilities = async () => {
    try {
      const caps = await BiometricAuthService.detectCapabilities();
      setCapabilities(caps);
      
      // Auto-select the best available method
      if (caps.isAvailable) {
        setSelectedMethod('biometric');
      } else {
        setSelectedMethod('pin');
      }
    } catch (error) {
      console.error('Failed to detect capabilities:', error);
      setSelectedMethod('pin');
    } finally {
      setIsLoading(false);
    }
  };

  const getMethodOptions = (): MethodOption[] => {
    const biometricTypes = capabilities?.supportedTypes || [];
    const biometricNames = BiometricAuthService.getBiometricTypesDescription(biometricTypes);
    const biometricName = biometricNames.join(' or ') || 'Biometric';

    return [
      {
        id: 'biometric',
        title: `${biometricName} Authentication`,
        description: `Quick and secure access using your ${biometricName.toLowerCase()}. Your biometric data never leaves your device.`,
        icon: '🔒',
        available: capabilities?.isAvailable || false,
        recommended: capabilities?.isAvailable || false,
      },
      {
        id: 'pin',
        title: 'PIN Code',
        description: 'Secure 4-6 digit code that you create. Easy to remember and type.',
        icon: '🔢',
        available: true,
      },
      {
        id: 'pattern',
        title: 'Pattern Lock',
        description: 'Draw a pattern by connecting dots. Visual and intuitive security method.',
        icon: '⭕',
        available: true,
      },
    ];
  };

  const handleMethodSelect = (method: AuthMethod) => {
    if (!getMethodOptions().find(opt => opt.id === method)?.available) {
      return;
    }
    setSelectedMethod(method);
  };

  const handleContinue = () => {
    if (selectedMethod === 'none') {
      Alert.alert(
        'No Method Selected',
        'Please select a security method to continue.',
        [{ text: 'OK' }]
      );
      return;
    }

    const selectedOption = getMethodOptions().find(opt => opt.id === selectedMethod);
    if (!selectedOption?.available) {
      Alert.alert(
        'Method Not Available',
        'The selected security method is not available on your device. Please choose another option.',
        [{ text: 'OK' }]
      );
      return;
    }

    onMethodSelected(selectedMethod);
  };

  const renderMethodOption = (option: MethodOption) => {
    const isSelected = selectedMethod === option.id;
    const isDisabled = !option.available;

    return (
      <TouchableOpacity
        key={option.id}
        style={[
          styles.methodOption,
          isSelected && styles.methodOptionSelected,
          isDisabled && styles.methodOptionDisabled,
        ]}
        onPress={() => handleMethodSelect(option.id)}
        disabled={isDisabled}
      >
        <View style={styles.methodHeader}>
          <Text style={styles.methodIcon}>{option.icon}</Text>
          <View style={styles.methodInfo}>
            <View style={styles.methodTitleRow}>
              <Text style={[
                styles.methodTitle,
                isDisabled && styles.methodTitleDisabled,
              ]}>
                {option.title}
              </Text>
              {option.recommended && (
                <View style={styles.recommendedBadge}>
                  <Text style={styles.recommendedText}>Recommended</Text>
                </View>
              )}
            </View>
            <Text style={[
              styles.methodDescription,
              isDisabled && styles.methodDescriptionDisabled,
            ]}>
              {option.description}
            </Text>
          </View>
          <View style={[
            styles.radioButton,
            isSelected && styles.radioButtonSelected,
            isDisabled && styles.radioButtonDisabled,
          ]}>
            {isSelected && <View style={styles.radioButtonInner} />}
          </View>
        </View>
        
        {isDisabled && option.id === 'biometric' && (
          <View style={styles.unavailableInfo}>
            <Text style={styles.unavailableText}>
              {!capabilities?.hasHardware
                ? 'No biometric hardware available on this device'
                : 'No biometric credentials enrolled. Set up biometrics in device settings.'
              }
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Checking device capabilities...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subtitle}>{subtitle}</Text>
        </View>

        <View style={styles.benefitsSection}>
          <Text style={styles.benefitsTitle}>Why secure your app?</Text>
          <View style={styles.benefitsList}>
            <Text style={styles.benefitItem}>• Protect your financial data</Text>
            <Text style={styles.benefitItem}>• Keep your transactions private</Text>
            <Text style={styles.benefitItem}>• Prevent unauthorized access</Text>
            <Text style={styles.benefitItem}>• All data stays on your device</Text>
          </View>
        </View>

        <View style={styles.methodsSection}>
          <Text style={styles.methodsTitle}>Choose your security method:</Text>
          {getMethodOptions().map(renderMethodOption)}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.continueButton,
            selectedMethod === 'none' && styles.continueButtonDisabled,
          ]}
          onPress={handleContinue}
          disabled={selectedMethod === 'none'}
        >
          <Text style={[
            styles.continueButtonText,
            selectedMethod === 'none' && styles.continueButtonTextDisabled,
          ]}>
            Continue
          </Text>
        </TouchableOpacity>

        {showSkip && onSkip && (
          <TouchableOpacity style={styles.skipButton} onPress={onSkip}>
            <Text style={styles.skipButtonText}>Skip for now</Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  benefitsSection: {
    marginBottom: 32,
    padding: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  benefitsList: {
    gap: 8,
  },
  benefitItem: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  methodsSection: {
    marginBottom: 32,
  },
  methodsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  methodOption: {
    borderWidth: 2,
    borderColor: '#E5E5E7',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  methodOptionSelected: {
    borderColor: '#007AFF',
    backgroundColor: 'rgba(0, 122, 255, 0.05)',
  },
  methodOptionDisabled: {
    opacity: 0.5,
    backgroundColor: '#F8F9FA',
  },
  methodHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  methodIcon: {
    fontSize: 24,
    marginRight: 12,
    marginTop: 2,
  },
  methodInfo: {
    flex: 1,
    marginRight: 12,
  },
  methodTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  methodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginRight: 8,
  },
  methodTitleDisabled: {
    color: '#999999',
  },
  recommendedBadge: {
    backgroundColor: '#34C759',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  recommendedText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
    textTransform: 'uppercase',
  },
  methodDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  methodDescriptionDisabled: {
    color: '#999999',
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E5E7',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },
  radioButtonSelected: {
    borderColor: '#007AFF',
  },
  radioButtonDisabled: {
    borderColor: '#CCCCCC',
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  unavailableInfo: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#FFF3CD',
    borderRadius: 8,
  },
  unavailableText: {
    fontSize: 12,
    color: '#856404',
    lineHeight: 16,
  },
  footer: {
    padding: 20,
    paddingBottom: 40,
  },
  continueButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  continueButtonDisabled: {
    backgroundColor: '#E5E5E7',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  continueButtonTextDisabled: {
    color: '#999999',
  },
  skipButton: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  skipButtonText: {
    fontSize: 16,
    color: '#666666',
  },
});