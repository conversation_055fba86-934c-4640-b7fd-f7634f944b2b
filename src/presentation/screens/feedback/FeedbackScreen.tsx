/**
 * Feedback Screen
 * Story 1.9: Anonymous Feedback and Feature Requests
 * 
 * Main screen for feedback management - includes form and list tabs
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
} from 'react-native';
import { useTheme } from '../../../shared/theme/ThemeProvider';
import { createStyles, useCommonStyles } from '../../../shared/theme';
import { FeedbackForm } from '../../components/feedback/FeedbackForm';
import { FeedbackList } from '../../components/feedback/FeedbackList';
import { FeedbackSubmission } from '../../../shared/types/feedback';
import { Button } from '../../components/core/Button';

interface FeedbackScreenProps {
  navigation?: any; // For navigation if needed
}

const createFeedbackScreenStyles = createStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    backgroundColor: theme.colors.card,
    paddingTop: theme.spacing.lg,
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    ...theme.typography.h2,
    color: theme.colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
  },
  headerDescription: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.md,
    lineHeight: 20,
  },

  content: {
    flex: 1,
  },
  fabContainer: {
    position: 'absolute',
    bottom: theme.spacing.lg,
    right: theme.spacing.md,
  },
  fab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    ...theme.shadows.md,
  },
  fabText: {
    fontSize: 24,
    color: theme.colors.surface,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: theme.borderRadius.xl,
    borderTopRightRadius: theme.borderRadius.xl,
    maxHeight: '90%',
  },
  modalHeader: {
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    alignItems: 'center',
  },
  modalTitle: {
    ...theme.typography.h3,
    color: theme.colors.text,
    fontWeight: '600',
  },
  modalDragIndicator: {
    width: 40,
    height: 4,
    borderRadius: 2,
    backgroundColor: theme.colors.border,
    marginBottom: theme.spacing.sm,
  },
  feedbackDetailModal: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  detailContent: {
    padding: theme.spacing.md,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  detailCategoryIcon: {
    fontSize: 24,
    marginRight: theme.spacing.sm,
    marginTop: 2,
  },
  detailTitle: {
    ...theme.typography.h3,
    color: theme.colors.text,
    fontWeight: '600',
    flex: 1,
  },
  detailDescription: {
    ...theme.typography.body,
    color: theme.colors.text,
    lineHeight: 22,
    marginBottom: theme.spacing.md,
  },
  detailMeta: {
    backgroundColor: theme.colors.card,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
  },
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  metaLabel: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    fontWeight: '600',
  },
  metaValue: {
    ...theme.typography.caption,
    color: theme.colors.text,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  statusText: {
    ...theme.typography.caption,
    color: theme.colors.surface,
    fontWeight: '600',
    fontSize: 11,
    textTransform: 'uppercase',
  }
}));

const CATEGORY_ICONS = {
  bug_report: '🐛',
  feature_request: '💡',
  general_feedback: '💬',
  praise_complaint: '⭐'
} as const;

const STATUS_COLORS = {
  submitted: '#9E9E9E',
  acknowledged: '#2196F3',
  in_review: '#FF9800',
  implemented: '#4CAF50',
  rejected: '#F44336'
} as const;

export const FeedbackScreen: React.FC<FeedbackScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const commonStyles = useCommonStyles(theme);
  const styles = createFeedbackScreenStyles(theme);
  
  const [showForm, setShowForm] = useState(false);
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackSubmission | null>(null);

  const handleFeedbackPress = (feedback: FeedbackSubmission) => {
    setSelectedFeedback(feedback);
  };

  const handleFormSubmitted = () => {
    setShowForm(false);
  };

  const getStatusDisplayText = (status: FeedbackSubmission['status']): string => {
    switch (status) {
      case 'submitted': return 'Submitted';
      case 'acknowledged': return 'Acknowledged';
      case 'in_review': return 'In Review';
      case 'implemented': return 'Implemented';
      case 'rejected': return 'Closed';
      default: return status;
    }
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <View style={styles.container}>
      {/* Content */}
      <View style={styles.content}>
        <FeedbackList
          onFeedbackPress={handleFeedbackPress}
          showSyncStatus={true}
        />
      </View>

      {/* Floating Action Button */}
      <View style={styles.fabContainer}>
        <TouchableOpacity
          style={styles.fab}
          onPress={() => setShowForm(true)}
          accessibilityRole="button"
          accessibilityLabel="Add new feedback"
        >
          <Text style={styles.fabText}>+</Text>
        </TouchableOpacity>
      </View>

      {/* Feedback Form Modal */}
      <Modal
        visible={showForm}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowForm(false)}
      >
        <View style={styles.feedbackDetailModal}>
          <View style={styles.modalHeader}>
            <View style={styles.modalDragIndicator} />
            <Text style={styles.modalTitle}>Submit Feedback</Text>
          </View>
          
          <FeedbackForm
            onSubmitted={handleFormSubmitted}
            onCancel={() => setShowForm(false)}
          />
        </View>
      </Modal>

      {/* Feedback Detail Modal */}
      <Modal
        visible={!!selectedFeedback}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setSelectedFeedback(null)}
      >
        {selectedFeedback && (
          <View style={styles.feedbackDetailModal}>
            <View style={styles.modalHeader}>
              <View style={styles.modalDragIndicator} />
              <Text style={styles.modalTitle}>Feedback Details</Text>
            </View>

            <View style={styles.detailContent}>
              <View style={styles.detailHeader}>
                <Text style={styles.detailCategoryIcon}>
                  {CATEGORY_ICONS[selectedFeedback.category]}
                </Text>
                <Text style={styles.detailTitle}>
                  {selectedFeedback.title}
                </Text>
              </View>

              <Text style={styles.detailDescription}>
                {selectedFeedback.description}
              </Text>

              <View style={styles.detailMeta}>
                <View style={styles.metaRow}>
                  <Text style={styles.metaLabel}>Status</Text>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: STATUS_COLORS[selectedFeedback.status] }
                  ]}>
                    <Text style={styles.statusText}>
                      {getStatusDisplayText(selectedFeedback.status)}
                    </Text>
                  </View>
                </View>

                <View style={styles.metaRow}>
                  <Text style={styles.metaLabel}>Priority</Text>
                  <Text style={[styles.metaValue, { textTransform: 'capitalize' }]}>
                    {selectedFeedback.priorityLevel}
                  </Text>
                </View>

                <View style={styles.metaRow}>
                  <Text style={styles.metaLabel}>Submitted</Text>
                  <Text style={styles.metaValue}>
                    {formatDate(selectedFeedback.submittedAt)}
                  </Text>
                </View>

                {selectedFeedback.rewardEligible && (
                  <View style={styles.metaRow}>
                    <Text style={styles.metaLabel}>Reward Eligible</Text>
                    <Text style={[styles.metaValue, { color: theme.colors.warning }]}>
                      Yes 🎁
                    </Text>
                  </View>
                )}
              </View>

              {selectedFeedback.adminFeedback && (
                <View style={[commonStyles.card, { backgroundColor: `${theme.colors.info}15` }]}>
                  <Text style={[styles.metaLabel, { marginBottom: theme.spacing.sm }]}>
                    📋 Team Response
                  </Text>
                  <Text style={styles.detailDescription}>
                    {selectedFeedback.adminFeedback}
                  </Text>
                </View>
              )}

              <Button
                title="Close"
                variant="outline"
                onPress={() => setSelectedFeedback(null)}
                style={{ marginTop: theme.spacing.lg }}
              />
            </View>
          </View>
        )}
      </Modal>
    </View>
  );
};