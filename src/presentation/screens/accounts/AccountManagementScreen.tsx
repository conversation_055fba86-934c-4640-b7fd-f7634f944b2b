import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Alert,
  Modal,
  SafeAreaView,
  StatusBar,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { Account } from '@/shared/types';
import { useAccountStore } from '@/shared/stores/accountStore';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { createStyles } from '@/shared/theme';
import { AccountListItem, AccountCreationForm } from '@/presentation/components/accounts';

interface AccountManagementScreenProps {
  navigation?: any; // TODO: Type this properly with navigation prop types
  onAccountChange?: () => void; // Callback for when accounts are added/updated/deleted
}

export const AccountManagementScreen: React.FC<AccountManagementScreenProps> = ({
  navigation,
  onAccountChange,
}) => {
  const theme = useTheme();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [, setSelectedAccount] = useState<Account | null>(null);
  const [sortBy] = useState<'name' | 'balance' | 'type' | 'updated_at'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  
  const {
    accounts,
    loading,
    error,
    loadAccounts,
    deleteAccount,
    transferBalance,
  } = useAccountStore();

  useEffect(() => {
    loadAccounts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run once on mount

  const handleAccountPress = (account: Account) => {
    setSelectedAccount(account);
    // TODO: Navigate to account details screen
    // navigation?.navigate('AccountDetails', { accountId: account.id });
  };

  const handleAccountLongPress = (account: Account) => {
    Alert.alert(
      account.name,
      'Choose an action',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Edit', onPress: () => handleEditAccount(account) },
        { text: 'Transfer Balance', onPress: () => handleTransferBalance(account) },
        { 
          text: 'Delete', 
          style: 'destructive', 
          onPress: () => handleDeleteAccount(account) 
        },
      ]
    );
  };

  const handleEditAccount = (account: Account) => {
    // TODO: Navigate to edit account screen or show edit modal
    Alert.alert('Edit Account', `Edit functionality for "${account.name}" will be implemented soon.`);
  };

  const handleTransferBalance = (account: Account) => {
    if (accounts.length < 2) {
      Alert.alert('Transfer Not Available', 'You need at least 2 accounts to transfer balance.');
      return;
    }

    const otherAccounts = accounts.filter(acc => acc.id !== account.id && acc.is_active);
    
    Alert.alert(
      'Transfer Balance',
      `Transfer balance from "${account.name}" to another account?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Choose Account', 
          onPress: () => showTransferOptions(account, otherAccounts)
        },
      ]
    );
  };

  const showTransferOptions = (fromAccount: Account, toAccounts: Account[]) => {
    const buttons: {text: string, onPress?: () => void, style?: 'cancel' | 'destructive'}[] = toAccounts.map(account => ({
      text: `${account.name} (${account.type})`,
      onPress: () => confirmTransfer(fromAccount, account)
    }));
    
    buttons.push({ text: 'Cancel', style: 'cancel' });
    
    Alert.alert(
      'Select Destination Account',
      `Transfer ₹${fromAccount.balance.toFixed(2)} from "${fromAccount.name}" to:`,
      buttons
    );
  };

  const confirmTransfer = (fromAccount: Account, toAccount: Account) => {
    Alert.alert(
      'Confirm Transfer',
      `Transfer ₹${fromAccount.balance.toFixed(2)} from "${fromAccount.name}" to "${toAccount.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Transfer',
          onPress: async () => {
            try {
              await transferBalance(fromAccount.id, toAccount.id, fromAccount.balance);
              onAccountChange?.(); // Notify parent component
              Alert.alert('Success', 'Balance transferred successfully!');
            } catch (error) {
              Alert.alert(
                'Transfer Failed',
                error instanceof Error ? error.message : 'Failed to transfer balance'
              );
            }
          }
        },
      ]
    );
  };

  const handleDeleteAccount = (account: Account) => {
    Alert.alert(
      'Delete Account',
      `Are you sure you want to delete "${account.name}"?\n\nThis will permanently remove the account and all its data.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => confirmDeleteAccount(account)
        },
      ]
    );
  };

  const confirmDeleteAccount = async (account: Account) => {
    try {
      // If account has balance, offer to transfer it
      if (account.balance !== 0 && accounts.length > 1) {
        const otherAccounts = accounts.filter(acc => acc.id !== account.id && acc.is_active);
        
        if (otherAccounts.length > 0) {
          Alert.alert(
            'Account Has Balance',
            `"${account.name}" has a balance of ₹${account.balance.toFixed(2)}. Would you like to transfer it to another account before deleting?`,
            [
              { 
                text: 'Delete Without Transfer', 
                style: 'destructive',
                onPress: () => performDelete(account.id)
              },
              { text: 'Cancel', style: 'cancel' },
              { 
                text: 'Transfer First', 
                onPress: () => showTransferOptions(account, otherAccounts)
              },
            ]
          );
          return;
        }
      }
      
      await performDelete(account.id);
    } catch (error) {
      Alert.alert(
        'Delete Failed',
        error instanceof Error ? error.message : 'Failed to delete account'
      );
    }
  };

  const performDelete = async (accountId: number) => {
    try {
      await deleteAccount(accountId);
      // Refresh the account list to reflect the deletion
      await loadAccounts();
      onAccountChange?.(); // Notify parent component
      Alert.alert('Success', 'Account deleted successfully!');
    } catch (error) {
      Alert.alert(
        'Delete Failed',
        error instanceof Error ? error.message : 'Failed to delete account'
      );
    }
  };

  const handleCreateSuccess = async (account: Account) => {
    setShowCreateForm(false);
    // Refresh the account list to show the newly created account
    await loadAccounts();
    onAccountChange?.(); // Notify parent component
    // Optionally navigate to the new account details
    // navigation?.navigate('AccountDetails', { accountId: account.id });
  };

  const toggleSortOrder = () => {
    setSortOrder(current => current === 'asc' ? 'desc' : 'asc');
  };

  // Sort accounts based on current sort criteria
  const getSortedAccounts = () => {
    const activeAccounts = accounts.filter(acc => acc.is_active);
    
    return activeAccounts.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'balance':
          comparison = a.balance - b.balance;
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'updated_at':
          comparison = new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime();
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <Text style={styles.title}>Accounts</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowCreateForm(true)}
        >
          <Text style={styles.addButtonText}>+ Add</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.headerControls}>
        <TouchableOpacity
          style={styles.sortButton}
          onPress={toggleSortOrder}
        >
          <Text style={styles.sortButtonText}>
            Sort: {sortBy} {sortOrder === 'asc' ? '↑' : '↓'}
          </Text>
        </TouchableOpacity>
        
        <Text style={styles.accountCount}>
          {getSortedAccounts().length} accounts
        </Text>
      </View>
    </View>
  );

  const renderCreateModal = () => (
    <Modal
      visible={showCreateForm}
      transparent={true}
      animationType="slide"
      presentationStyle="overFullScreen"
      onRequestClose={() => setShowCreateForm(false)}
    >
      <TouchableWithoutFeedback onPress={() => setShowCreateForm(false)}>
        <View style={styles.modalBackdrop}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <View style={styles.modalContent}>
              <AccountCreationForm
                onSuccess={handleCreateSuccess}
                onCancel={() => setShowCreateForm(false)}
              />
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );

  const styles = createAccountManagementStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar 
        barStyle="dark-content" 
        backgroundColor={theme.colors.card} 
      />
      
      {renderHeader()}
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading accounts...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error: {error}</Text>
        </View>
      ) : getSortedAccounts().length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            No accounts found. Create your first account to start managing your finances.
          </Text>
        </View>
      ) : (
        <FlatList
          data={getSortedAccounts()}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <AccountListItem
              account={item}
              onPress={handleAccountPress}
              onLongPress={handleAccountLongPress}
              showBalance={true}
            />
          )}
          style={styles.accountList}
        />
      )}
      
      {/* Floating Add Button */}
      <View style={styles.fab}>
        <TouchableOpacity
          style={[styles.fabButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setShowCreateForm(true)}
          accessibilityLabel="Add new account"
          accessibilityHint="Opens account creation form"
        >
          <Text style={[styles.fabText, { color: theme.colors.background }]}>+</Text>
        </TouchableOpacity>
      </View>
      
      {renderCreateModal()}
    </SafeAreaView>
  );
};

// Component-specific themed styles
const createAccountManagementStyles = createStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    backgroundColor: theme.colors.card,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    ...theme.shadows.sm,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  title: {
    ...theme.typography.h1,
    color: theme.colors.text,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    ...theme.shadows.sm,
  },
  addButtonText: {
    color: theme.colors.background,
    ...theme.typography.button,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  fabText: {
    fontSize: 24,
    fontWeight: '600',
  },
  headerControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sortButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  sortButtonText: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  accountCount: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
  },
  accountList: {
    flex: 1,
    paddingTop: theme.spacing.sm,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  loadingText: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.sm,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  errorText: {
    ...theme.typography.body,
    color: theme.colors.error,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xxl,
  },
  emptyText: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    height: '90%',
    width: '100%',
    ...theme.shadows.lg,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    ...theme.shadows.sm,
  },
  modalCloseButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  modalCloseText: {
    ...theme.typography.body,
    color: theme.colors.primary,
    fontWeight: '600',
  },
}));