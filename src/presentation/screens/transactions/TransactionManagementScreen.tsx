import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Modal,
  SafeAreaView,
  <PERSON>ert,
  ScrollView,
} from 'react-native';
import { Transaction } from '@/shared/types';
import { TransactionService } from '@/business/services/TransactionService';
import { TransactionList } from '@/presentation/components/transactions/TransactionList';
import { TransactionEntryForm } from '@/presentation/components/transactions/TransactionEntryForm';
import { TransactionEditForm } from '@/presentation/components/transactions/TransactionEditForm';
import { useAccountStore } from '@/shared/stores/accountStore';
import { useCategoryStore } from '@/shared/stores/categoryStore';
import { useTheme } from '@/shared/theme/ThemeProvider';
import { Button, Card } from '@/presentation/components/core';

interface TransactionManagementScreenProps {
  navigation?: any; // TODO: Type this properly with navigation prop types
  onTransactionChange?: () => void; // Callback for when transactions are added/updated/deleted
}

export const TransactionManagementScreen: React.FC<TransactionManagementScreenProps> = ({
  navigation,
  onTransactionChange,
}) => {
  const theme = useTheme();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState<Transaction | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [statsData, setStatsData] = useState({
    totalExpenses: 0,
    totalIncome: 0,
    monthlyBalance: 0,
    transactionCount: 0,
  });

  const { accounts } = useAccountStore();
  const { categories } = useCategoryStore();

  // Load stats data
  useEffect(() => {
    const loadStats = async () => {
      try {
        const transactionService = new TransactionService();
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        // Get all transactions (set high limit to get all transactions)
        const transactionResult = await transactionService.getTransactions({}, 1, 10000);
        const allTransactions = transactionResult.transactions || [];
        
        // Filter by current month for monthly stats
        const monthlyTransactions = allTransactions.filter((transaction: any) => {
          try {
            const transactionDate = new Date(transaction.transaction_date);
            // Ensure valid date and within month range
            if (isNaN(transactionDate.getTime())) {
              console.warn('Invalid transaction date:', transaction.transaction_date);
              return false;
            }
            return transactionDate >= firstDayOfMonth && transactionDate <= lastDayOfMonth;
          } catch (error) {
            console.warn('Error parsing transaction date:', transaction.transaction_date, error);
            return false;
          }
        });
        
        // Calculate all-time stats for total records
        const allTimeStats = {
          totalIncome: 0,
          totalExpenses: 0,
          totalCount: allTransactions.length
        };
        
        allTransactions.forEach((transaction: any) => {
          // Ensure transaction has valid amount and type
          const amount = Number(transaction.amount) || 0;
          const type = String(transaction.transaction_type || '').toLowerCase().trim();
          
          if (type === 'income') {
            allTimeStats.totalIncome += amount;
          } else if (type === 'expense') {
            allTimeStats.totalExpenses += amount;
          }
        });

        // Calculate monthly stats
        let totalIncome = 0;
        let totalExpenses = 0;

        monthlyTransactions.forEach((transaction: any) => {
          // Ensure transaction has valid amount and type
          const amount = Number(transaction.amount) || 0;
          const type = String(transaction.transaction_type || '').toLowerCase().trim();
          
          if (type === 'income') {
            totalIncome += amount;
          } else if (type === 'expense') {
            totalExpenses += amount;
          }
        });

        const monthlyBalance = totalIncome - totalExpenses;

        setStatsData({
          totalIncome: Math.round(totalIncome),
          totalExpenses: Math.round(totalExpenses),
          monthlyBalance: Math.round(monthlyBalance),
          transactionCount: allTimeStats.totalCount, // Use all-time count for "Total Records"
        });
      } catch (error) {
        console.error('Error loading stats:', error);
        // Keep default values on error
      }
    };

    loadStats();
  }, [refreshKey]); // Reload when refreshKey changes

  const handleTransactionPress = useCallback((transaction: Transaction) => {
    setEditingTransaction(transaction);
  }, []);

  const handleDeleteTransaction = useCallback(async (transaction: Transaction) => {
    try {
      // Use the transaction service to delete the transaction
      const transactionService = new TransactionService();
      const success = await transactionService.deleteTransaction(transaction.id);
      
      if (success) {
        // Force refresh of the transaction list
        setRefreshKey(prev => prev + 1);
        onTransactionChange?.(); // Notify parent component
        Alert.alert('Success', 'Transaction deleted successfully');
      } else {
        throw new Error('Delete operation returned false');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to delete transaction');
      console.error('Error deleting transaction:', error);
    }
  }, [onTransactionChange]);

  const confirmDelete = useCallback((transaction: Transaction) => {
    Alert.alert(
      'Delete Transaction',
      `Are you sure you want to delete "${transaction.description}" for ₹${transaction.amount}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => handleDeleteTransaction(transaction),
        },
      ]
    );
  }, [handleDeleteTransaction]);

  const handleTransactionLongPress = useCallback((transaction: Transaction) => {
    Alert.alert(
      'Transaction Options',
      `What would you like to do with "${transaction.description}"?`,
      [
        { text: 'Edit', onPress: () => setEditingTransaction(transaction) },
        { text: 'Delete', style: 'destructive', onPress: () => confirmDelete(transaction) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  }, [confirmDelete]);

  const handleCreateSuccess = (transaction: Transaction) => {
    setShowCreateForm(false);
    setRefreshKey(prev => prev + 1); // Force TransactionList to refresh
    onTransactionChange?.(); // Notify parent component
    Alert.alert('Success', 'Transaction created successfully');
  };

  const handleEditSuccess = (transaction: Transaction) => {
    setEditingTransaction(null);
    onTransactionChange?.(); // Notify parent component
    Alert.alert('Success', 'Transaction updated successfully');
  };

  const handleEditDelete = (transaction: Transaction) => {
    setEditingTransaction(null);
    setRefreshKey(prev => prev + 1); // Force TransactionList to refresh
    onTransactionChange?.(); // Notify parent component
    Alert.alert('Success', 'Transaction deleted successfully');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Modern Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.background, borderBottomColor: theme.colors.border }]}>
        <View>
          <Text style={[styles.title, { color: theme.colors.text }]}>Transactions</Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            Manage your financial records
          </Text>
        </View>
        <View style={styles.headerActions}>
          <Button
            title="+ Add"
            variant="primary"
            size="sm"
            onPress={() => setShowCreateForm(true)}
          />
        </View>
      </View>

      {/* Stats Cards */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.statsContainer}
        style={styles.statsScrollView}
      >
        <Card style={{...styles.statCard, marginLeft: theme.spacing.xl}}>
          <Text style={{...styles.statLabel, color: theme.colors.textSecondary}}>
            This Month
          </Text>
          <Text style={{...styles.statValue, color: theme.colors.success}}>
            +₹{statsData.totalIncome.toLocaleString()}
          </Text>
          <Text style={{...styles.statSubtext, color: theme.colors.textSecondary}}>
            Income
          </Text>
        </Card>

        <Card style={styles.statCard}>
          <Text style={{...styles.statLabel, color: theme.colors.textSecondary}}>
            This Month
          </Text>
          <Text style={{...styles.statValue, color: theme.colors.error}}>
            -₹{statsData.totalExpenses.toLocaleString()}
          </Text>
          <Text style={{...styles.statSubtext, color: theme.colors.textSecondary}}>
            Expenses
          </Text>
        </Card>

        <Card style={styles.statCard}>
          <Text style={{...styles.statLabel, color: theme.colors.textSecondary}}>
            Net Balance
          </Text>
          <Text style={{...styles.statValue, 
            color: statsData.monthlyBalance >= 0 ? theme.colors.success : theme.colors.error 
          }}>
            {statsData.monthlyBalance >= 0 ? '+' : ''}₹{statsData.monthlyBalance.toLocaleString()}
          </Text>
          <Text style={{...styles.statSubtext, color: theme.colors.textSecondary}}>
            This Month
          </Text>
        </Card>

        <Card style={{...styles.statCard, marginRight: theme.spacing.xl}}>
          <Text style={{...styles.statLabel, color: theme.colors.textSecondary}}>
            Total Records
          </Text>
          <Text style={{...styles.statValue, color: theme.colors.primary}}>
            {statsData.transactionCount}
          </Text>
          <Text style={{...styles.statSubtext, color: theme.colors.textSecondary}}>
            Transactions
          </Text>
        </Card>
      </ScrollView>

      {/* Transaction List - Simple direct layout */}
      <TransactionList
        onTransactionPress={handleTransactionPress}
        onTransactionLongPress={handleTransactionLongPress}
        refreshTrigger={refreshKey}
        showSearch={true}
        showFilters={showFilters}
        onToggleFilters={() => setShowFilters(!showFilters)}
      />

      {/* Floating Add Button */}
      <View style={styles.fab}>
        <TouchableOpacity
          style={[styles.fabButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setShowCreateForm(true)}
          accessibilityLabel="Add new transaction"
          accessibilityHint="Opens transaction entry form"
        >
          <Text style={[styles.fabText, { color: theme.colors.background }]}>+</Text>
        </TouchableOpacity>
      </View>

      {/* Create Transaction Modal */}
      <Modal
        visible={showCreateForm}
        transparent={true}
        animationType="slide"
        presentationStyle="overFullScreen"
        onRequestClose={() => setShowCreateForm(false)}
      >
        <TouchableWithoutFeedback onPress={() => setShowCreateForm(false)}>
          <View style={styles.modalBackdrop}>
            <TouchableWithoutFeedback onPress={() => {}}>
              <View style={[styles.modalContent, { backgroundColor: theme.colors.background }]}>
                <TransactionEntryForm
                  onSuccess={handleCreateSuccess}
                  onCancel={() => setShowCreateForm(false)}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* Edit Transaction Modal */}
      {editingTransaction && (
        <TransactionEditForm
          transaction={editingTransaction}
          visible={!!editingTransaction}
          onSave={handleEditSuccess}
          onCancel={() => setEditingTransaction(null)}
          onDelete={handleEditDelete}
          accounts={accounts}
          categories={categories}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statsScrollView: {
    maxHeight: 120,
    minHeight: 120,
  },
  statsContainer: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  statCard: {
    minWidth: 140,
    marginHorizontal: 8,
    paddingVertical: 16,
    paddingHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 2,
  },
  statSubtext: {
    fontSize: 11,
    fontWeight: '400',
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  fabText: {
    fontSize: 24,
    fontWeight: '600',
  },
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    height: '90%',
    width: '100%',
  },

});