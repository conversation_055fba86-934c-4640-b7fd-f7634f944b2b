import React from 'react';
// import { useAuthStore } from '../../shared/stores/authStore';
import { OAuthCredentials } from './OAuthService';

/* eslint-disable no-unused-vars */
export enum PremiumFeature {
  CLOUD_SYNC = 'cloud_sync',
  FAMILY_SHARING = 'family_sharing',
  MULTI_DEVICE = 'multi_device',
  ADVANCED_ANALYTICS = 'advanced_analytics',
  RECEIPT_STORAGE = 'receipt_storage',
  PRIORITY_SUPPORT = 'priority_support',
  UNLIMITED_ACCOUNTS = 'unlimited_accounts',
  EXPORT_DATA = 'export_data',
  CUSTOM_CATEGORIES = 'custom_categories',
  BUDGET_ALERTS = 'budget_alerts',
}
/* eslint-enable no-unused-vars */

export interface PremiumSubscription {
  isActive: boolean;
  plan: 'monthly' | 'yearly' | null;
  expiresAt: number | null;
  features: PremiumFeature[];
  trialEndsAt: number | null;
  linkedAccount?: OAuthCredentials;
}

export interface FeatureLimit {
  current: number;
  limit: number;
  unlimited: boolean;
}

export class PremiumGatekeeper {
  private static instance: PremiumGatekeeper;
  private subscription: PremiumSubscription;

  private constructor() {
    this.subscription = {
      isActive: false,
      plan: null,
      expiresAt: null,
      features: [],
      trialEndsAt: null,
    };
  }

  public static getInstance(): PremiumGatekeeper {
    if (!PremiumGatekeeper.instance) {
      PremiumGatekeeper.instance = new PremiumGatekeeper();
    }
    return PremiumGatekeeper.instance;
  }

  /**
   * Check if user has access to a premium feature
   */
  public hasFeatureAccess(feature: PremiumFeature): boolean {
    // Check if subscription is active
    if (!this.subscription.isActive) {
      return false;
    }

    // Check if subscription has expired
    if (this.subscription.expiresAt && Date.now() > this.subscription.expiresAt) {
      return false;
    }

    // Check if feature is included in current plan
    return this.subscription.features.includes(feature);
  }

  /**
   * Check if user is on a trial period
   */
  public isOnTrial(): boolean {
    if (!this.subscription.trialEndsAt) {
      return false;
    }
    return Date.now() < this.subscription.trialEndsAt;
  }

  /**
   * Get remaining trial days
   */
  public getTrialDaysRemaining(): number {
    if (!this.subscription.trialEndsAt) {
      return 0;
    }
    
    const remaining = this.subscription.trialEndsAt - Date.now();
    return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)));
  }

  /**
   * Get feature limits for free users
   */
  public getFeatureLimit(feature: string): FeatureLimit {
    const limits: Record<string, FeatureLimit> = {
      accounts: {
        current: 0, // Should be fetched from actual data
        limit: 3,
        unlimited: this.subscription.isActive,
      },
      categories: {
        current: 0, // Should be fetched from actual data
        limit: 10,
        unlimited: this.hasFeatureAccess(PremiumFeature.CUSTOM_CATEGORIES),
      },
      transactions_per_month: {
        current: 0, // Should be fetched from actual data
        limit: 1000,
        unlimited: this.subscription.isActive,
      },
      receipt_storage: {
        current: 0, // Should be fetched from actual data
        limit: 0, // Not available for free users
        unlimited: this.hasFeatureAccess(PremiumFeature.RECEIPT_STORAGE),
      },
    };

    return limits[feature] || { current: 0, limit: 0, unlimited: false };
  }

  /**
   * Get upgrade prompt message for a specific feature
   */
  public getUpgradeMessage(feature: PremiumFeature): string {
    const messages: Record<PremiumFeature, string> = {
      [PremiumFeature.CLOUD_SYNC]: 'Upgrade to Premium to sync your data across devices with secure cloud backup.',
      [PremiumFeature.FAMILY_SHARING]: 'Share budgets and expenses with family members with Premium.',
      [PremiumFeature.MULTI_DEVICE]: 'Access your data on multiple devices with Premium cloud sync.',
      [PremiumFeature.ADVANCED_ANALYTICS]: 'Get detailed spending insights and financial reports with Premium.',
      [PremiumFeature.RECEIPT_STORAGE]: 'Scan and store receipt images securely with Premium.',
      [PremiumFeature.PRIORITY_SUPPORT]: 'Get faster support response times with Premium.',
      [PremiumFeature.UNLIMITED_ACCOUNTS]: 'Track unlimited bank accounts with Premium.',
      [PremiumFeature.EXPORT_DATA]: 'Export your financial data in multiple formats with Premium.',
      [PremiumFeature.CUSTOM_CATEGORIES]: 'Create unlimited custom categories with Premium.',
      [PremiumFeature.BUDGET_ALERTS]: 'Get smart budget alerts and notifications with Premium.',
    };

    return messages[feature] || 'This feature is available with Premium subscription.';
  }

  /**
   * Set premium subscription status
   */
  public setSubscription(subscription: Partial<PremiumSubscription>): void {
    this.subscription = {
      ...this.subscription,
      ...subscription,
    };
  }

  /**
   * Activate premium subscription
   */
  public activateSubscription(
    plan: 'monthly' | 'yearly',
    expiresAt: number,
    linkedAccount?: OAuthCredentials
  ): void {
    const allFeatures = Object.values(PremiumFeature);
    
    const subscription: PremiumSubscription = {
      isActive: true,
      plan,
      expiresAt,
      features: allFeatures,
      trialEndsAt: null, // Clear trial when subscription is active
    };
    if (linkedAccount) {
      subscription.linkedAccount = linkedAccount;
    }
    this.subscription = subscription;
  }

  /**
   * Start trial period
   */
  public startTrial(durationDays: number = 7): void {
    const trialEndsAt = Date.now() + (durationDays * 24 * 60 * 60 * 1000);
    const allFeatures = Object.values(PremiumFeature);
    
    this.subscription = {
      isActive: true,
      plan: null,
      expiresAt: trialEndsAt,
      features: allFeatures,
      trialEndsAt,
    };
  }

  /**
   * Cancel subscription
   */
  public cancelSubscription(): void {
    this.subscription = {
      isActive: false,
      plan: null,
      expiresAt: null,
      features: [],
      trialEndsAt: null,
    };
  }

  /**
   * Get current subscription status
   */
  public getSubscription(): PremiumSubscription {
    return { ...this.subscription };
  }

  /**
   * Check if subscription needs renewal
   */
  public needsRenewal(): boolean {
    if (!this.subscription.isActive || !this.subscription.expiresAt) {
      return false;
    }

    // Check if expiring within 7 days
    const sevenDaysFromNow = Date.now() + (7 * 24 * 60 * 60 * 1000);
    return this.subscription.expiresAt <= sevenDaysFromNow;
  }

  /**
   * Get days until expiration
   */
  public getDaysUntilExpiration(): number {
    if (!this.subscription.expiresAt) {
      return Infinity;
    }

    const remaining = this.subscription.expiresAt - Date.now();
    return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)));
  }

  /**
   * Validate feature usage against limits
   */
  public validateFeatureUsage(feature: string, currentUsage: number): {
    allowed: boolean;
    limit: number;
    remaining: number;
    unlimited: boolean;
  } {
    const limit = this.getFeatureLimit(feature);
    
    return {
      allowed: limit.unlimited || currentUsage < limit.limit,
      limit: limit.limit,
      remaining: limit.unlimited ? Infinity : Math.max(0, limit.limit - currentUsage),
      unlimited: limit.unlimited,
    };
  }

  /**
   * Get feature availability status
   */
  public getFeatureStatus(feature: PremiumFeature): {
    available: boolean;
    reason?: string;
    upgradeRequired: boolean;
  } {
    if (this.hasFeatureAccess(feature)) {
      return { available: true, upgradeRequired: false };
    }

    if (!this.subscription.isActive) {
      return {
        available: false,
        reason: 'Premium subscription required',
        upgradeRequired: true,
      };
    }

    if (this.subscription.expiresAt && Date.now() > this.subscription.expiresAt) {
      return {
        available: false,
        reason: 'Subscription has expired',
        upgradeRequired: true,
      };
    }

    return {
      available: false,
      reason: 'Feature not included in current plan',
      upgradeRequired: true,
    };
  }
}

// Hook for using PremiumGatekeeper in React components
export const usePremiumGatekeeper = () => {
  const gatekeeper = PremiumGatekeeper.getInstance();
  
  // Get subscription from auth store if available
  React.useEffect(() => {
    // This would typically sync with persisted subscription data
    // For now, we'll assume free tier
  }, []);

  return {
    hasFeatureAccess: gatekeeper.hasFeatureAccess.bind(gatekeeper),
    isOnTrial: gatekeeper.isOnTrial.bind(gatekeeper),
    getTrialDaysRemaining: gatekeeper.getTrialDaysRemaining.bind(gatekeeper),
    getFeatureLimit: gatekeeper.getFeatureLimit.bind(gatekeeper),
    getUpgradeMessage: gatekeeper.getUpgradeMessage.bind(gatekeeper),
    getSubscription: gatekeeper.getSubscription.bind(gatekeeper),
    needsRenewal: gatekeeper.needsRenewal.bind(gatekeeper),
    getDaysUntilExpiration: gatekeeper.getDaysUntilExpiration.bind(gatekeeper),
    validateFeatureUsage: gatekeeper.validateFeatureUsage.bind(gatekeeper),
    getFeatureStatus: gatekeeper.getFeatureStatus.bind(gatekeeper),
    activateSubscription: gatekeeper.activateSubscription.bind(gatekeeper),
    startTrial: gatekeeper.startTrial.bind(gatekeeper),
    cancelSubscription: gatekeeper.cancelSubscription.bind(gatekeeper),
  };
};

// Helper component for feature gating
export const PremiumFeatureGate: React.FC<{
  feature: PremiumFeature;
  children: React.ReactNode;
  onUpgradeRequired?: () => void;
  fallback?: React.ReactNode;
}> = ({ feature, children, onUpgradeRequired, fallback }) => {
  const { hasFeatureAccess } = usePremiumGatekeeper();

  if (hasFeatureAccess(feature)) {
    return <>{children}</>;
  }

  if (onUpgradeRequired) {
    onUpgradeRequired();
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  return null;
};