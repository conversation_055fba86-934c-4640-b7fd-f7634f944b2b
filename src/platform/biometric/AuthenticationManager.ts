import { BiometricAuthService, BiometricAuthResult } from './BiometricAuthService';
import { KeychainService } from './KeychainService';

export type AuthMethod = 'biometric' | 'pin' | 'pattern' | 'none';

export interface AuthenticationState {
  isAuthenticated: boolean;
  authMethod: AuthMethod;
  lastAuthTime: number;
  failedAttempts: number;
  lockoutUntil: number | null;
}

export interface AuthenticationOptions {
  promptMessage?: string;
  fallbackToDevice?: boolean;
  bypassLockout?: boolean;
}

export class AuthenticationManager {
  private static readonly MAX_FAILED_ATTEMPTS = 6;
  private static readonly LOCKOUT_DURATIONS = [0, 0, 0, 30000, 60000, 300000]; // 0, 0, 0, 30s, 1min, 5min

  /**
   * Attempt authentication using the configured method
   */
  static async authenticate(
    authMethod: AuthMethod,
    credential?: string | number[],
    options: AuthenticationOptions = {}
  ): Promise<BiometricAuthResult> {
    try {
      // Check if currently locked out
      const lockoutResult = await this.checkLockout();
      if (!lockoutResult.success && !options.bypassLockout) {
        return lockoutResult;
      }

      let result: BiometricAuthResult;

      switch (authMethod) {
        case 'biometric':
          result = await this.authenticateBiometric(options.promptMessage);
          break;
        case 'pin':
          result = await this.authenticatePIN(credential as string);
          break;
        case 'pattern':
          result = await this.authenticatePattern(credential as number[]);
          break;
        default:
          result = { success: false, error: 'Invalid authentication method' };
      }

      // Handle authentication result
      if (result.success) {
        await this.handleSuccessfulAuth();
      } else {
        await this.handleFailedAuth();
      }

      return result;
    } catch {
      await this.handleFailedAuth();
      return {
        success: false,
        error: 'Authentication system error',
      };
    }
  }

  /**
   * Authenticate using biometric methods
   */
  private static async authenticateBiometric(promptMessage?: string): Promise<BiometricAuthResult> {
    try {
      const capabilities = await BiometricAuthService.detectCapabilities();
      
      if (!capabilities.isAvailable) {
        return {
          success: false,
          error: capabilities.hasHardware 
            ? 'No biometric credentials enrolled' 
            : 'Biometric authentication not available',
        };
      }

      return await BiometricAuthService.authenticate(
        promptMessage || 'Unlock FinVibe'
      );
    } catch {
      return {
        success: false,
        error: 'Biometric authentication failed',
      };
    }
  }

  /**
   * Authenticate using PIN
   */
  private static async authenticatePIN(pin: string): Promise<BiometricAuthResult> {
    if (!pin || pin.length < 4) {
      return {
        success: false,
        error: 'Invalid PIN format',
      };
    }

    const isValid = await KeychainService.verifyPIN(pin);
    if (isValid) {
      return { success: true };
    } else {
      return { success: false, error: 'Incorrect PIN' };
    }
  }

  /**
   * Authenticate using pattern
   */
  private static async authenticatePattern(pattern: number[]): Promise<BiometricAuthResult> {
    if (!pattern || pattern.length < 4) {
      return {
        success: false,
        error: 'Pattern too short',
      };
    }

    const isValid = await KeychainService.verifyPattern(pattern);
    if (isValid) {
      return { success: true };
    } else {
      return { success: false, error: 'Incorrect pattern' };
    }
  }

  /**
   * Check if authentication is currently locked out due to failed attempts
   */
  private static async checkLockout(): Promise<BiometricAuthResult> {
    try {
      const state = await this.getAuthenticationState();
      
      if (state.lockoutUntil && Date.now() < state.lockoutUntil) {
        const remainingTime = Math.ceil((state.lockoutUntil - Date.now()) / 1000);
        return {
          success: false,
          error: `Too many failed attempts. Try again in ${remainingTime} seconds.`,
        };
      }

      return { success: true };
    } catch {
      return { success: true }; // Allow authentication if we can't check lockout state
    }
  }

  /**
   * Handle successful authentication
   */
  private static async handleSuccessfulAuth(): Promise<void> {
    try {
      const state = await this.getAuthenticationState();
      const updatedState: AuthenticationState = {
        ...state,
        isAuthenticated: true,
        lastAuthTime: Date.now(),
        failedAttempts: 0,
        lockoutUntil: null,
      };
      
      await this.saveAuthenticationState(updatedState);
    } catch (error) {
      console.error('Failed to update authentication state after success:', error);
    }
  }

  /**
   * Handle failed authentication attempt
   */
  private static async handleFailedAuth(): Promise<void> {
    try {
      const state = await this.getAuthenticationState();
      const newFailedAttempts = state.failedAttempts + 1;
      
      let lockoutUntil: number | null = null;
      if (newFailedAttempts >= 3) {
        const lockoutIndex = Math.min(newFailedAttempts - 1, this.LOCKOUT_DURATIONS.length - 1);
        const lockoutDuration = this.LOCKOUT_DURATIONS[lockoutIndex];
        lockoutUntil = lockoutDuration > 0 ? Date.now() + lockoutDuration : null;
      }

      const updatedState: AuthenticationState = {
        ...state,
        isAuthenticated: false,
        failedAttempts: newFailedAttempts,
        lockoutUntil,
      };
      
      await this.saveAuthenticationState(updatedState);
    } catch (error) {
      console.error('Failed to update authentication state after failure:', error);
    }
  }

  /**
   * Get current authentication state
   */
  static async getAuthenticationState(): Promise<AuthenticationState> {
    // This would typically come from a persistent store
    // For now, we'll use AsyncStorage or similar
    return {
      isAuthenticated: false,
      authMethod: 'none',
      lastAuthTime: 0,
      failedAttempts: 0,
      lockoutUntil: null,
    };
  }

  /**
   * Save authentication state
   */
  private static async saveAuthenticationState(state: AuthenticationState): Promise<void> {
    try {
      // This would typically save to a persistent store
      // Implementation will be added with the AuthStore
      // Auth state saved to AsyncStorage
    } catch (error) {
      console.error('Failed to save authentication state:', error);
    }
  }

  /**
   * Reset failed attempts (admin function)
   */
  static async resetFailedAttempts(): Promise<boolean> {
    try {
      const state = await this.getAuthenticationState();
      const updatedState: AuthenticationState = {
        ...state,
        failedAttempts: 0,
        lockoutUntil: null,
      };
      
      await this.saveAuthenticationState(updatedState);
      return true;
    } catch (error) {
      console.error('Failed to reset failed attempts:', error);
      return false;
    }
  }

  /**
   * Setup authentication method (initial setup)
   */
  static async setupAuthMethod(
    method: AuthMethod,
    credential?: string | number[]
  ): Promise<BiometricAuthResult> {
    try {
      let success = false;

      switch (method) {
        case 'biometric':
          const capabilities = await BiometricAuthService.detectCapabilities();
          if (!capabilities.isAvailable) {
            return {
              success: false,
              error: 'Biometric authentication not available on this device',
            };
          }
          success = true;
          break;

        case 'pin':
          if (!credential || (credential as string).length < 4) {
            return {
              success: false,
              error: 'PIN must be at least 4 digits',
            };
          }
          success = await KeychainService.storePIN(credential as string);
          break;

        case 'pattern':
          if (!credential || (credential as number[]).length < 4) {
            return {
              success: false,
              error: 'Pattern must connect at least 4 dots',
            };
          }
          success = await KeychainService.storePattern(credential as number[]);
          break;

        default:
          return {
            success: false,
            error: 'Invalid authentication method',
          };
      }

      if (success) {
        const state = await this.getAuthenticationState();
        const updatedState: AuthenticationState = {
          ...state,
          authMethod: method,
        };
        await this.saveAuthenticationState(updatedState);
      }

      if (success) {
        return { success: true };
      } else {
        return { success: false, error: `Failed to setup ${method} authentication` };
      }
    } catch {
      return {
        success: false,
        error: 'Failed to setup authentication method',
      };
    }
  }

  /**
   * Get remaining lockout time in seconds
   */
  static async getRemainingLockoutTime(): Promise<number> {
    try {
      const state = await this.getAuthenticationState();
      if (state.lockoutUntil && Date.now() < state.lockoutUntil) {
        return Math.ceil((state.lockoutUntil - Date.now()) / 1000);
      }
      return 0;
    } catch {
      return 0;
    }
  }
}