import * as SecureStore from 'expo-secure-store';
// CryptoJS removed - using expo-crypto instead

export interface KeychainOptions {
  service?: string;
  accessGroup?: string;
  touchID?: boolean;
  showModal?: boolean;
  kPromptMessage?: string;
}

export interface SecureCredentials {
  username: string;
  password: string;
}

export class KeychainService {
  private static readonly DEFAULT_SERVICE = 'FinVibeAuth';
  private static readonly PIN_KEY = 'user_pin';
  private static readonly PATTERN_KEY = 'user_pattern';
  private static readonly DATABASE_KEY = 'database_encryption_key';

  /**
   * Store credentials securely in device keychain
   */
  static async setCredentials(
    username: string,
    password: string,
    options: KeychainOptions = {}
  ): Promise<boolean> {
    try {
      // Use Expo SecureStore instead of react-native-keychain
      const service = options.service || this.DEFAULT_SERVICE;
      const credentials = JSON.stringify({ username, password });
      
      await SecureStore.setItemAsync(service, credentials);
      return true;
    } catch (error) {
      console.error('SecureStore setCredentials error:', error);
      return false;
    }
  }

  /**
   * Retrieve credentials from device keychain
   */
  static async getCredentials(
    options: KeychainOptions = {}
  ): Promise<SecureCredentials | null> {
    try {
      const service = options.service || this.DEFAULT_SERVICE;
      const credentialsJson = await SecureStore.getItemAsync(service);
      
      if (credentialsJson) {
        const credentials = JSON.parse(credentialsJson);
        if (credentials && credentials.username && credentials.password) {
          return {
            username: credentials.username,
            password: credentials.password,
          };
        }
      }
      
      return null;
    } catch (error) {
      console.error('SecureStore getCredentials error:', error);
      return null;
    }
  }

  /**
   * Remove credentials from device keychain
   */
  static async removeCredentials(service?: string): Promise<boolean> {
    try {
      await SecureStore.deleteItemAsync(service || this.DEFAULT_SERVICE);
      return true;
    } catch (error) {
      console.error('SecureStore removeCredentials error:', error);
      return false;
    }
  }

  /**
   * Store hashed PIN securely
   */
  static async storePIN(pin: string): Promise<boolean> {
    try {
      const Crypto = require('expo-crypto');
      const hashedPin = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        pin
      );
      return await this.setCredentials(this.PIN_KEY, hashedPin, {
        service: this.PIN_KEY,
      });
    } catch (error) {
      console.error('Store PIN error:', error);
      return false;
    }
  }

  /**
   * Verify PIN against stored hash
   */
  static async verifyPIN(pin: string): Promise<boolean> {
    try {
      const credentials = await this.getCredentials({
        service: this.PIN_KEY,
      });
      if (!credentials || credentials.username !== this.PIN_KEY) {
        return false;
      }

      const Crypto = require('expo-crypto');
      const hashedPin = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        pin
      );
      return credentials.password === hashedPin;
    } catch (error) {
      console.error('Verify PIN error:', error);
      return false;
    }
  }

  /**
   * Store pattern hash securely
   */
  static async storePattern(pattern: number[]): Promise<boolean> {
    try {
      const patternString = pattern.join(',');
      const Crypto = require('expo-crypto');
      const hashedPattern = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        patternString
      );
      return await this.setCredentials(this.PATTERN_KEY, hashedPattern, {
        service: this.PATTERN_KEY,
      });
    } catch (error) {
      console.error('Store pattern error:', error);
      return false;
    }
  }

  /**
   * Verify pattern against stored hash
   */
  static async verifyPattern(pattern: number[]): Promise<boolean> {
    try {
      const credentials = await this.getCredentials({
        service: this.PATTERN_KEY,
      });
      if (!credentials || credentials.username !== this.PATTERN_KEY) {
        return false;
      }

      const patternString = pattern.join(',');
      const Crypto = require('expo-crypto');
      const hashedPattern = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        patternString
      );
      return credentials.password === hashedPattern;
    } catch (error) {
      console.error('Verify pattern error:', error);
      return false;
    }
  }

  /**
   * Generate and store database encryption key
   */
  static async generateDatabaseKey(): Promise<string | null> {
    try {
      const Crypto = require('expo-crypto');
      // Generate a random 32-byte key and convert to hex string
      const randomBytes = await Crypto.getRandomBytesAsync(32);
      const key = randomBytes.map((b: number) => b.toString(16).padStart(2, '0')).join('');
      
      const stored = await this.setCredentials(this.DATABASE_KEY, key, {
        service: this.DATABASE_KEY,
        touchID: true,
        kPromptMessage: 'Unlock your financial data',
      });
      
      return stored ? key : null;
    } catch (error) {
      console.error('Generate database key error:', error);
      return null;
    }
  }

  /**
   * Retrieve database encryption key
   */
  static async getDatabaseKey(): Promise<string | null> {
    try {
      const credentials = await this.getCredentials({
        service: this.DATABASE_KEY,
        kPromptMessage: 'Unlock your financial data',
      });
      
      if (credentials && credentials.username === this.DATABASE_KEY) {
        return credentials.password;
      }
      
      return null;
    } catch (error) {
      console.error('Get database key error:', error);
      return null;
    }
  }

  /**
   * Check if keychain is available on the device
   */
  static async isKeychainAvailable(): Promise<boolean> {
    try {
      // For Expo SecureStore, we can always assume it's available
      // as it handles the platform differences internally
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get supported biometry types from keychain
   */
  static async getSupportedBiometryType(): Promise<string | null> {
    try {
      // For now, return null as SecureStore doesn't provide biometry type info
      // This would need to be implemented with expo-local-authentication if needed
      return null;
    } catch {
      return null;
    }
  }

  /**
   * Clear all stored credentials for the app
   */
  static async clearAllCredentials(): Promise<boolean> {
    try {
      const services = [
        this.DEFAULT_SERVICE,
        this.PIN_KEY,
        this.PATTERN_KEY,
        this.DATABASE_KEY,
      ];

      const results = await Promise.all(
        services.map(service => this.removeCredentials(service))
      );

      return results.every(result => result === true);
    } catch (error) {
      console.error('Clear all credentials error:', error);
      return false;
    }
  }
}