import { Alert } from 'react-native';
import { KeychainService } from './KeychainService';
import { useAuthStore } from '../../shared/stores/authStore';

export interface ResetOptions {
  clearAuthData?: boolean;
  clearAppData?: boolean;
  showConfirmation?: boolean;
}

export class DeviceResetManager {
  private static instance: DeviceResetManager;

  private constructor() {}

  public static getInstance(): DeviceResetManager {
    if (!DeviceResetManager.instance) {
      DeviceResetManager.instance = new DeviceResetManager();
    }
    return DeviceResetManager.instance;
  }

  /**
   * Show device reset confirmation dialog
   */
  public showResetConfirmation(onConfirm: () => void, onCancel?: () => void): void {
    Alert.alert(
      'Reset Device Authentication',
      'This will remove all authentication data and reset the app to its initial state. You will need to set up authentication again.\n\nYour financial data will remain safe but you\'ll need to reconfigure your security settings.\n\nThis action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: onCancel,
        },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Are you sure?',
              'This will permanently reset your authentication settings.',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Yes, Reset',
                  style: 'destructive',
                  onPress: onConfirm,
                },
              ]
            );
          },
        },
      ]
    );
  }

  /**
   * Perform device reset
   */
  public async performReset(options: ResetOptions = {}): Promise<boolean> {
    try {
      const {
        clearAuthData = true,
        clearAppData = false,
        showConfirmation = true,
      } = options;

      if (showConfirmation) {
        return new Promise<boolean>((resolve) => {
          this.showResetConfirmation(
            async () => {
              const success = await this.executeReset(clearAuthData, clearAppData);
              resolve(success);
            },
            () => resolve(false)
          );
        });
      }

      return await this.executeReset(clearAuthData, clearAppData);
    } catch (error) {
      console.error('Device reset failed:', error);
      return false;
    }
  }

  /**
   * Execute the actual reset process
   */
  private async executeReset(clearAuthData: boolean, clearAppData: boolean): Promise<boolean> {
    try {
      const results: boolean[] = [];

      // Clear authentication data
      if (clearAuthData) {
        const authClearResult = await this.clearAuthenticationData();
        results.push(authClearResult);
      }

      // Clear app data (if requested)
      if (clearAppData) {
        const appDataResult = await this.clearApplicationData();
        results.push(appDataResult);
      }

      // Reset auth store
      const authStore = useAuthStore.getState();
      await authStore.reset();

      const success = results.every(result => result === true);
      
      if (success) {
        this.showResetSuccess();
      } else {
        this.showResetPartialFailure();
      }

      return success;
    } catch (error) {
      console.error('Reset execution failed:', error);
      this.showResetFailure();
      return false;
    }
  }

  /**
   * Clear all authentication-related data
   */
  private async clearAuthenticationData(): Promise<boolean> {
    try {
      // Clear keychain data
      const keychainResult = await KeychainService.clearAllCredentials();
      
      // Clear failed attempts and lockout state
      const authStore = useAuthStore.getState();
      authStore.resetFailedAttempts();

      return keychainResult;
    } catch (error) {
      console.error('Failed to clear authentication data:', error);
      return false;
    }
  }

  /**
   * Clear application data (to be implemented based on app requirements)
   */
  private async clearApplicationData(): Promise<boolean> {
    try {
      // This would clear app-specific data like:
      // - Database records
      // - Cached data
      // - Settings
      // - Temporary files
      // Implementation depends on specific app requirements
      
      console.log('Application data clearing not implemented yet');
      return true;
    } catch (error) {
      console.error('Failed to clear application data:', error);
      return false;
    }
  }

  /**
   * Check if reset is recommended based on failed attempts
   */
  public shouldRecommendReset(): boolean {
    const authStore = useAuthStore.getState();
    return authStore.failedAttempts >= authStore.maxFailedAttempts;
  }

  /**
   * Get reset recommendation message
   */
  public getResetRecommendationMessage(): string {
    return 'You have reached the maximum number of failed authentication attempts. For security reasons, we recommend resetting your authentication settings.';
  }

  /**
   * Show reset success message
   */
  private showResetSuccess(): void {
    Alert.alert(
      'Reset Complete',
      'Your authentication settings have been reset successfully. You can now set up authentication again.',
      [{ text: 'OK' }]
    );
  }

  /**
   * Show partial failure message
   */
  private showResetPartialFailure(): void {
    Alert.alert(
      'Reset Partially Complete',
      'Some authentication data may not have been cleared completely. You may need to restart the app or contact support.',
      [{ text: 'OK' }]
    );
  }

  /**
   * Show reset failure message
   */
  private showResetFailure(): void {
    Alert.alert(
      'Reset Failed',
      'Unable to reset authentication data. Please restart the app or contact support if the problem persists.',
      [{ text: 'OK' }]
    );
  }

  /**
   * Emergency reset - bypass all confirmations
   */
  public async emergencyReset(): Promise<boolean> {
    try {
      console.log('Performing emergency reset...');
      
      const success = await this.executeReset(true, false);
      
      if (success) {
        Alert.alert(
          'Emergency Reset Complete',
          'All authentication data has been cleared. Please restart the app.',
          [{ text: 'OK' }]
        );
      }
      
      return success;
    } catch (error) {
      console.error('Emergency reset failed:', error);
      return false;
    }
  }

  /**
   * Show emergency reset option
   */
  public showEmergencyResetOption(): void {
    Alert.alert(
      'Emergency Reset',
      'This will immediately clear all authentication data without confirmation. Use only if normal reset is not working.\n\nThis action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Emergency Reset',
          style: 'destructive',
          onPress: () => this.emergencyReset(),
        },
      ]
    );
  }
}

// Hook for using DeviceResetManager in React components
export const useDeviceReset = () => {
  const resetManager = DeviceResetManager.getInstance();
  const authStore = useAuthStore();

  return {
    performReset: resetManager.performReset.bind(resetManager),
    emergencyReset: resetManager.emergencyReset.bind(resetManager),
    showResetConfirmation: resetManager.showResetConfirmation.bind(resetManager),
    showEmergencyResetOption: resetManager.showEmergencyResetOption.bind(resetManager),
    shouldRecommendReset: resetManager.shouldRecommendReset.bind(resetManager),
    getResetRecommendationMessage: resetManager.getResetRecommendationMessage.bind(resetManager),
    isResetRecommended: authStore.failedAttempts >= authStore.maxFailedAttempts,
  };
};