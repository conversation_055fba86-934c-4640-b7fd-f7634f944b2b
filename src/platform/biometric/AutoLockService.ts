import React from 'react';
import { useAuthStore } from '../../shared/stores/authStore';

export class AutoLockService {
  private static instance: AutoLockService;
  private timeoutTimer: NodeJS.Timeout | null = null;
  private checkInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): AutoLockService {
    if (!AutoLockService.instance) {
      AutoLockService.instance = new AutoLockService();
    }
    return AutoLockService.instance;
  }

  /**
   * Initialize auto-lock monitoring
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    this.startPeriodicCheck();
    this.isInitialized = true;
  }

  /**
   * Clean up auto-lock service
   */
  public cleanup(): void {
    this.clearTimeout();
    this.stopPeriodicCheck();
    this.isInitialized = false;
  }

  /**
   * Reset the auto-lock timer
   */
  public resetTimer(): void {
    const authStore = useAuthStore.getState();
    
    // Update last active time in store
    authStore.updateLastActiveTime();
    
    // Clear existing timer
    this.clearTimeout();
    
    // Only set timer if user is authenticated and timeout is not immediate
    if (authStore.isAuthenticated && authStore.lockTimeout > 0) {
      this.setTimeout(authStore.lockTimeout);
    }
  }

  /**
   * Force lock the app immediately
   */
  public forceLock(): void {
    const authStore = useAuthStore.getState();
    authStore.lock();
    this.clearTimeout();
  }

  /**
   * Set timeout for auto-lock
   */
  private setTimeout(seconds: number): void {
    this.clearTimeout();
    
    this.timeoutTimer = setTimeout(() => {
      const authStore = useAuthStore.getState();
      
      // Double-check that we should still lock
      if (authStore.isAuthenticated && !authStore.isLocked) {
        authStore.lock();
      }
    }, seconds * 1000);
  }

  /**
   * Clear the current timeout
   */
  private clearTimeout(): void {
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }
  }

  /**
   * Start periodic check for timeout conditions
   * This is a backup mechanism in case timer-based approach fails
   */
  private startPeriodicCheck(): void {
    this.stopPeriodicCheck();
    
    // Check every 10 seconds
    this.checkInterval = setInterval(() => {
      const authStore = useAuthStore.getState();
      
      // Check if we should lock due to timeout
      if (authStore.checkLockTimeout()) {
        this.clearTimeout();
      }
    }, 10000);
  }

  /**
   * Stop periodic check
   */
  private stopPeriodicCheck(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Handle user activity - reset timer
   */
  public onUserActivity(): void {
    const authStore = useAuthStore.getState();
    
    // Only reset if user is authenticated
    if (authStore.isAuthenticated && !authStore.isLocked) {
      this.resetTimer();
    }
  }

  /**
   * Handle authentication success - start timer
   */
  public onAuthenticationSuccess(): void {
    this.resetTimer();
  }

  /**
   * Handle lock event - clear timer
   */
  public onLock(): void {
    this.clearTimeout();
  }

  /**
   * Update timeout setting - restart timer with new timeout
   */
  public onTimeoutChange(newTimeoutSeconds: number): void {
    const authStore = useAuthStore.getState();
    
    if (authStore.isAuthenticated && !authStore.isLocked) {
      if (newTimeoutSeconds === 0) {
        // Immediate timeout - lock right away
        this.forceLock();
      } else {
        this.setTimeout(newTimeoutSeconds);
      }
    }
  }

  /**
   * Get current timeout status
   */
  public getTimeoutStatus(): {
    isTimerActive: boolean;
    timeRemaining: number | null;
  } {
    const authStore = useAuthStore.getState();
    
    if (!authStore.isAuthenticated || authStore.isLocked || !this.timeoutTimer) {
      return {
        isTimerActive: false,
        timeRemaining: null,
      };
    }

    const timeSinceLastActive = Date.now() - authStore.lastActiveTime;
    const timeoutMs = authStore.lockTimeout * 1000;
    const timeRemaining = Math.max(0, timeoutMs - timeSinceLastActive);

    return {
      isTimerActive: true,
      timeRemaining: Math.ceil(timeRemaining / 1000),
    };
  }
}

// Hook for using AutoLockService in React components
export const useAutoLock = () => {
  const autoLockService = AutoLockService.getInstance();
  const authStore = useAuthStore();

  React.useEffect(() => {
    autoLockService.initialize();

    return () => {
      // Keep service running, don't cleanup on component unmount
    };
  }, [autoLockService]);

  // Listen for auth state changes
  React.useEffect(() => {
    if (authStore.isAuthenticated && !authStore.isLocked) {
      autoLockService.onAuthenticationSuccess();
    } else {
      autoLockService.onLock();
    }
  }, [authStore.isAuthenticated, authStore.isLocked, autoLockService]);

  // Listen for timeout changes
  React.useEffect(() => {
    autoLockService.onTimeoutChange(authStore.lockTimeout);
  }, [authStore.lockTimeout, autoLockService]);

  return {
    resetTimer: autoLockService.resetTimer.bind(autoLockService),
    forceLock: autoLockService.forceLock.bind(autoLockService),
    onUserActivity: autoLockService.onUserActivity.bind(autoLockService),
    getTimeoutStatus: autoLockService.getTimeoutStatus.bind(autoLockService),
  };
};