import React from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useAuthStore } from '../../shared/stores/authStore';

export class AppStateManager {
  private static instance: AppStateManager;
  private appStateSubscription: ReturnType<typeof AppState.addEventListener> | null = null;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): AppStateManager {
    if (!AppStateManager.instance) {
      AppStateManager.instance = new AppStateManager();
    }
    return AppStateManager.instance;
  }

  /**
   * Initialize app state monitoring
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    this.setupAppStateListener();
    this.isInitialized = true;
  }

  /**
   * Clean up app state monitoring
   */
  public cleanup(): void {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
    this.isInitialized = false;
  }

  /**
   * Setup app state change listener
   */
  private setupAppStateListener(): void {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange
    );
  }

  /**
   * Handle app state changes
   */
  private handleAppStateChange = (nextAppState: AppStateStatus): void => {
    try {
      const authStore = useAuthStore.getState();
      authStore.handleAppStateChange(nextAppState);
    } catch (error) {
      console.error('Error handling app state change:', error);
    }
  };

  /**
   * Get current app state
   */
  public getCurrentAppState(): AppStateStatus {
    return AppState.currentState;
  }

  /**
   * Check if app is currently active
   */
  public isAppActive(): boolean {
    return AppState.currentState === 'active';
  }

  /**
   * Check if app is in background
   */
  public isAppInBackground(): boolean {
    return AppState.currentState === 'background';
  }

  /**
   * Check if app is inactive (iOS specific state)
   */
  public isAppInactive(): boolean {
    return AppState.currentState === 'inactive';
  }
}

// Hook for using AppStateManager in React components
export const useAppStateManager = () => {
  const manager = AppStateManager.getInstance();

  React.useEffect(() => {
    manager.initialize();

    return () => {
      // Don't cleanup on component unmount as this should persist
      // Only cleanup when app is closed
    };
  }, [manager]);

  return {
    getCurrentAppState: manager.getCurrentAppState.bind(manager),
    isAppActive: manager.isAppActive.bind(manager),
    isAppInBackground: manager.isAppInBackground.bind(manager),
    isAppInactive: manager.isAppInactive.bind(manager),
  };
};