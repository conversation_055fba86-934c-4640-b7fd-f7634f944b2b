import { 
  creditCardPatterns, 
  loanEMIPatterns, 
  identifyBankFromSMS, 
  parseAmountFromSMS,
  extractMerchantName,
  extractReferenceNumber,
  extractCardNumber,
  SMSPattern,
  SMSParsingResult
} from '../../data/smsPatterns';
import { Transaction, TransactionType, TransactionMetadata } from '../../shared/types';
import { validateTransactionMetadata } from '../../shared/utils/transactionMetadataValidation';

export interface ParsedTransaction {
  amount: number;
  transactionType: TransactionType;
  description: string;
  transactionDate: Date;
  metadata: TransactionMetadata;
  confidence: number;
  suggestedAccountType?: 'credit' | 'loan' | 'checking' | 'savings';
}

export interface CreditCardSMSDetails {
  merchantName?: string;
  merchantCategory?: string;
  availableCredit?: number;
  cardNumber?: string;
  rewardPoints?: number;
}

export interface LoanSMSDetails {
  principalComponent?: number;
  interestComponent?: number;
  emiNumber?: number;
  outstandingBalance?: number;
  paymentType?: 'regular_emi' | 'prepayment' | 'part_payment';
}

export class SMSParsingService {
  /**
   * Main method to parse transaction from SMS text
   */
  async parseTransactionFromSMS(smsText: string): Promise<ParsedTransaction | null> {
    try {
      // Clean and normalize SMS text
      const cleanedSMS = this.cleanSMSText(smsText);
      
      // Identify bank
      const bankIdentifier = identifyBankFromSMS(cleanedSMS);
      
      // Check for loan-specific keywords first to prioritize loan parsing
      const loanKeywords = /\b(?:loan|emi|installment|prepayment|pre-payment|principal|interest|outstanding|tenure)\b/i;
      const hasLoanKeywords = loanKeywords.test(cleanedSMS);
      
      if (hasLoanKeywords) {
        // Try loan patterns first if loan keywords are present
        const loanResult = this.parseLoanSMS(cleanedSMS);
        if (loanResult) {
          return this.buildParsedTransaction(loanResult, cleanedSMS, bankIdentifier, 'loan');
        }
        
        // Fallback to credit card patterns
        const creditCardResult = this.parseCreditCardSMS(cleanedSMS);
        if (creditCardResult) {
          return this.buildParsedTransaction(creditCardResult, cleanedSMS, bankIdentifier, 'credit');
        }
      } else {
        // Try credit card patterns first for non-loan SMS
        const creditCardResult = this.parseCreditCardSMS(cleanedSMS);
        if (creditCardResult) {
          return this.buildParsedTransaction(creditCardResult, cleanedSMS, bankIdentifier, 'credit');
        }
        
        // Fallback to loan patterns
        const loanResult = this.parseLoanSMS(cleanedSMS);
        if (loanResult) {
          return this.buildParsedTransaction(loanResult, cleanedSMS, bankIdentifier, 'loan');
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error parsing SMS:', error);
      return null;
    }
  }

  /**
   * Parses credit card specific SMS messages
   */
  private parseCreditCardSMS(smsText: string): SMSParsingResult | null {
    // Try each credit card pattern
    for (const [patternName, pattern] of Object.entries(creditCardPatterns)) {
      for (const regex of pattern.patterns) {
        const match = smsText.match(regex);
        if (match) {
          const result = this.extractCreditCardData(smsText, match, pattern, patternName);
          if (result) {
            return result;
          }
        }
      }
    }
    return null;
  }

  /**
   * Parses loan specific SMS messages
   */
  private parseLoanSMS(smsText: string): SMSParsingResult | null {
    // Try each loan pattern
    for (const [patternName, pattern] of Object.entries(loanEMIPatterns)) {
      for (const regex of pattern.patterns) {
        const match = smsText.match(regex);
        if (match) {
          const result = this.extractLoanData(smsText, match, pattern, patternName);
          if (result) {
            return result;
          }
        }
      }
    }
    return null;
  }

  /**
   * Extracts credit card transaction data from SMS
   */
  private extractCreditCardData(
    smsText: string, 
    match: RegExpMatchArray, 
    pattern: SMSPattern, 
    patternName: string
  ): SMSParsingResult | null {
    try {
      const extractedFields: Record<string, any> = {};
      
      // Extract amount (always the first capture group)
      if (match[1]) {
        extractedFields.amount = parseAmountFromSMS(match[1]);
      } else {
        return null; // No amount found
      }
      
      // Extract merchant name for purchase patterns
      if (patternName === 'purchase' && match[2]) {
        extractedFields.merchantName = match[2].trim();
      } else if (patternName === 'purchase') {
        // Try to extract merchant name using common patterns
        extractedFields.merchantName = extractMerchantName(smsText);
      }
      
      // Extract additional credit card details
      const creditCardDetails = this.extractCreditCardDetails(smsText);
      Object.assign(extractedFields, creditCardDetails);
      
      // Calculate confidence
      const confidence = this.calculateParsingConfidence(smsText, extractedFields, patternName);
      
      return {
        amount: extractedFields.amount,
        transactionType: pattern.transactionType,
        description: this.generateTransactionDescription(pattern.transactionType, extractedFields),
        extractedFields,
        confidence,
        patternMatched: patternName
      };
    } catch (error) {
      console.error('Error extracting credit card data:', error);
      return null;
    }
  }

  /**
   * Extracts loan transaction data from SMS
   */
  private extractLoanData(
    smsText: string, 
    match: RegExpMatchArray, 
    pattern: SMSPattern, 
    patternName: string
  ): SMSParsingResult | null {
    try {
      const extractedFields: Record<string, any> = {};
      
      // Extract amount (always the first capture group)
      if (match[1]) {
        extractedFields.amount = parseAmountFromSMS(match[1]);
      } else {
        return null; // No amount found
      }
      
      // Extract additional loan details
      const loanDetails = this.extractLoanDetails(smsText);
      Object.assign(extractedFields, loanDetails);
      
      // For interest rate change, extract the rate
      if (patternName === 'interestRateChange' && match[1]) {
        extractedFields.newInterestRate = parseFloat(match[1]);
      }
      
      // Calculate confidence
      const confidence = this.calculateParsingConfidence(smsText, extractedFields, patternName);
      
      return {
        amount: extractedFields.amount,
        transactionType: pattern.transactionType,
        description: this.generateTransactionDescription(pattern.transactionType, extractedFields),
        extractedFields,
        confidence,
        patternMatched: patternName
      };
    } catch (error) {
      console.error('Error extracting loan data:', error);
      return null;
    }
  }

  /**
   * Extracts credit card specific details from SMS
   */
  extractCreditCardDetails(smsText: string): CreditCardSMSDetails {
    const details: CreditCardSMSDetails = {};
    
    // Extract merchant name
    const merchantName = extractMerchantName(smsText);
    if (merchantName) details.merchantName = merchantName;
    
    // Extract card number (last 4 digits)
    const cardNumber = extractCardNumber(smsText);
    if (cardNumber) details.cardNumber = cardNumber;
    
    // Extract available credit
    const availableCreditMatch = smsText.match(/(?:available|avl).*?(?:credit|limit).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i);
    if (availableCreditMatch) {
      details.availableCredit = parseAmountFromSMS(availableCreditMatch[1]);
    }
    
    // Extract reward points
    const rewardPointsMatch = smsText.match(/(?:reward|points?).*?(\d+)/i);
    if (rewardPointsMatch) {
      details.rewardPoints = parseInt(rewardPointsMatch[1]);
    }
    
    return details;
  }

  /**
   * Extracts loan specific details from SMS
   */
  extractLoanDetails(smsText: string): LoanSMSDetails {
    const details: LoanSMSDetails = {};
    
    // Extract EMI number
    const emiNumberMatch = smsText.match(/(?:emi|installment)\s*(?:no\.?|number)?\s*(\d+)/i);
    if (emiNumberMatch) {
      details.emiNumber = parseInt(emiNumberMatch[1]);
    }
    
    // Extract outstanding balance
    const outstandingMatch = smsText.match(/outstanding.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i);
    if (outstandingMatch) {
      details.outstandingBalance = parseAmountFromSMS(outstandingMatch[1]);
    }
    
    // Extract principal and interest components
    const principalMatch = smsText.match(/principal.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i);
    if (principalMatch) {
      details.principalComponent = parseAmountFromSMS(principalMatch[1]);
    }
    
    const interestMatch = smsText.match(/interest.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i);
    if (interestMatch) {
      details.interestComponent = parseAmountFromSMS(interestMatch[1]);
    }
    
    // Determine payment type
    if (smsText.match(/prepayment|pre-payment/i)) {
      details.paymentType = 'prepayment';
    } else if (smsText.match(/part.*?payment|partial.*?payment/i)) {
      details.paymentType = 'part_payment';
    } else {
      details.paymentType = 'regular_emi';
    }
    
    return details;
  }

  /**
   * Calculates parsing confidence based on various factors
   */
  calculateParsingConfidence(
    smsText: string, 
    extractedData: any, 
    patternMatched: string
  ): number {
    let confidence = 0.5; // Base confidence
    
    // Amount extraction adds confidence
    if (extractedData.amount && extractedData.amount > 0) {
      confidence += 0.3;
    }
    
    // Bank identifier adds confidence
    const bankIdentifier = identifyBankFromSMS(smsText);
    if (bankIdentifier) {
      confidence += 0.1;
    }
    
    // Pattern specific confidence adjustments
    switch (patternMatched) {
      case 'purchase':
        if (extractedData.merchantName) confidence += 0.1;
        break;
      case 'payment':
        confidence += 0.1; // Payment patterns are generally more reliable
        break;
      case 'emiDeducted':
        confidence += 0.15; // EMI patterns are very reliable
        break;
      case 'prepayment':
        if (extractedData.savingsInInterest) confidence += 0.1;
        break;
    }
    
    // SMS length factor (longer SMS generally more reliable)
    if (smsText.length > 100) {
      confidence += 0.05;
    }
    
    // Reference number adds confidence
    const referenceNumber = extractReferenceNumber(smsText);
    if (referenceNumber) {
      confidence += 0.05;
    }
    
    // Cap confidence at 1.0
    return Math.min(confidence, 1.0);
  }

  /**
   * Identifies bank from SMS text
   */
  identifyBankFromSMS(smsText: string): string | null {
    return identifyBankFromSMS(smsText);
  }

  /**
   * Builds the final parsed transaction object
   */
  private buildParsedTransaction(
    result: SMSParsingResult, 
    originalSMS: string, 
    bankIdentifier: string | null,
    suggestedAccountType: 'credit' | 'loan'
  ): ParsedTransaction {
    const metadata: TransactionMetadata = {
      smsDetails: {
        originalText: originalSMS,
        extractedFields: result.extractedFields,
        parsingConfidence: result.confidence,
        patternMatched: result.patternMatched,
        ...(bankIdentifier ? { bankIdentifier } : {})
      },
      source: 'sms',
      lastUpdated: new Date().toISOString()
    };
    
    // Add credit card specific metadata
    if (suggestedAccountType === 'credit') {
      metadata.creditCardDetails = {
        merchantName: result.extractedFields.merchantName,
        availableCreditAfterTransaction: result.extractedFields.availableCredit,
        rewardPoints: result.extractedFields.rewardPoints
      };
    }
    
    // Add loan specific metadata
    if (suggestedAccountType === 'loan') {
      metadata.loanDetails = {
        principalComponent: result.extractedFields.principalComponent,
        interestComponent: result.extractedFields.interestComponent,
        emiNumber: result.extractedFields.emiNumber,
        remainingPrincipalAfterPayment: result.extractedFields.outstandingBalance,
        paymentType: result.extractedFields.paymentType || 'regular_emi'
      };
    }
    
    return {
      amount: result.amount,
      transactionType: result.transactionType,
      description: result.description,
      transactionDate: new Date(), // Current date, could be extracted from SMS
      metadata,
      confidence: result.confidence,
      suggestedAccountType
    };
  }

  /**
   * Generates transaction description based on type and extracted data
   */
  private generateTransactionDescription(
    transactionType: TransactionType, 
    extractedFields: Record<string, any>
  ): string {
    switch (transactionType) {
      case 'credit_charge':
        if (extractedFields.merchantName) {
          // Clean up merchant name and format properly
          let merchantName = extractedFields.merchantName.trim();
          // Remove "at" prefix if present and capitalize properly
          merchantName = merchantName.replace(/^at\s+/i, '');
          merchantName = merchantName.charAt(0).toUpperCase() + merchantName.slice(1).toLowerCase();
          return `Purchase at ${merchantName}`;
        }
        return 'Credit card purchase';
        
      case 'credit_payment':
        return 'Credit card payment';
        
      case 'credit_interest':
        return 'Credit card interest charges';
        
      case 'credit_fee':
        return extractedFields.feeType ? `Credit card ${extractedFields.feeType}` : 'Credit card fee';
        
      case 'loan_emi':
        const emiNum = extractedFields.emiNumber;
        return emiNum ? `Loan EMI #${emiNum}` : 'Loan EMI payment';
        
      case 'loan_prepayment':
        return 'Loan prepayment';
        
      case 'loan_interest':
        return 'Loan interest';
        
      default:
        return 'SMS transaction';
    }
  }

  /**
   * Cleans and normalizes SMS text for better parsing
   */
  private cleanSMSText(smsText: string): string {
    return smsText
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[\r\n]+/g, ' ') // Replace newlines with space
      .trim();
  }
}