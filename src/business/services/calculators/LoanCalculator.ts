/**
 * Loan <PERSON>tor
 * 
 * Specialized calculator for loan accounts
 * Handles EMI calculations, amortization schedules, and principal tracking
 */

import { Account, Transaction } from '../../../shared/types';
import { AccountBalance, ValidationResult, LoanSummary, AmortizationSchedule, EMIComponents } from '../../../shared/types/calculators';
import { BaseAccountCalculator } from './BaseAccountCalculator';

/**
 * Calculator for loan accounts
 * Manages EMI calculations, principal tracking, and amortization
 */
export class LoanCalculator extends BaseAccountCalculator {
  
  /**
   * Get calculator type identifier
   */
  getCalculatorType(): string {
    return 'LoanCalculator';
  }

  /**
   * Calculate account balance for loan accounts
   * Display balance = Remaining Principal, Actual balance = Remaining Principal
   */
  async calculateBalance(accountId: number, transactions: Transaction[]): Promise<AccountBalance> {
    const account = await this.getAccountWithMetadata(accountId);
    if (!account) {
      throw new Error('Account not found');
    }

    // Validate account type
    this.validateAccountType(account, ['loan']);

    // Validate required metadata
    this.validateRequiredMetadata(account, ['principalAmount', 'interestRate', 'emiAmount']);

    // Perform calculation with performance monitoring
    const { result: loanData } = await this.measurePerformance(async () => {
      const originalPrincipal = account.metadata!.principalAmount!;
      const interestRate = account.metadata!.interestRate!;
      const emiAmount = account.metadata!.emiAmount!;
      
      // Calculate total principal payments made
      const principalPayments = this.sumPrincipalPayments(transactions);
      const remainingPrincipal = Math.max(0, originalPrincipal - principalPayments);
      
      // Calculate EMI components for current balance
      const currentEMIComponents = remainingPrincipal > 0 
        ? this.splitEMIComponents(emiAmount, remainingPrincipal, interestRate)
        : { principal: 0, interest: 0 };
      
      // Calculate remaining tenure
      const remainingTenure = remainingPrincipal > 0 
        ? this.calculateRemainingTenure(remainingPrincipal, emiAmount, interestRate)
        : 0;
      
      // Calculate total interest paid till date
      const totalInterestPaid = this.calculateTotalInterestPaid(transactions, originalPrincipal, principalPayments);
      
      return {
        originalPrincipal,
        remainingPrincipal: this.roundToTwoDecimals(remainingPrincipal),
        emiAmount,
        interestRate,
        currentEMIComponents,
        remainingTenure,
        totalInterestPaid: this.roundToTwoDecimals(totalInterestPaid)
      };
    }, transactions.length);

    // Get next EMI date from metadata or calculate
    const nextEmiDate = account.metadata!.nextEmiDate || this.calculateNextEMIDate();

    return {
      displayBalance: loanData.remainingPrincipal, // Show remaining principal
      actualBalance: loanData.remainingPrincipal, // Same as display for loans
      metadata: {
        originalPrincipal: loanData.originalPrincipal,
        remainingPrincipal: loanData.remainingPrincipal,
        emiAmount: loanData.emiAmount,
        interestRate: loanData.interestRate,
        nextEmiDate,
        remainingTenure: loanData.remainingTenure,
        totalInterestPaid: loanData.totalInterestPaid,
        currentEMIPrincipal: loanData.currentEMIComponents.principal,
        currentEMIInterest: loanData.currentEMIComponents.interest,
        // Additional metadata
        totalEMIsPaid: this.countEMIPayments(transactions),
        totalAmountPaid: this.sumTransactionsByType(transactions, ['expense']), // EMI payments are expenses
        loanStartDate: account.metadata!.loanStartDate,
        tenure: account.metadata!.tenure
      },
      lastCalculated: new Date(),
      calculationType: 'loan'
    };
  }

  /**
   * Validate transaction for loan accounts
   */
  async validateTransaction(transaction: Transaction, account: Account): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate account type
    try {
      this.validateAccountType(account, ['loan']);
    } catch (error) {
      errors.push(`${error}`);
    }

    // Validate transaction amount
    const amountValidation = this.validatePositiveAmount(transaction.amount);
    if (!amountValidation.isValid) {
      errors.push(amountValidation.error!);
    }

    // Validate account ID matches
    if (transaction.account_id !== account.id) {
      errors.push('Transaction account ID does not match loan account');
    }

    // Validate transaction type (loan accounts mainly have EMI payments as expenses)
    const validTypes: Transaction['transaction_type'][] = ['expense', 'income'];
    if (!validTypes.includes(transaction.transaction_type)) {
      errors.push('Loan accounts support expense (EMI payments) and income (loan disbursement) transactions');
    }

    // Loan-specific validations
    if (account.metadata?.emiAmount && transaction.transaction_type === 'expense') {
      const emiAmount = account.metadata.emiAmount;
      
      // Warn if payment doesn't match EMI amount
      if (Math.abs(transaction.amount - emiAmount) > 1) { // Allow ₹1 variance
        warnings.push(`Payment amount (${this.formatCurrency(transaction.amount)}) differs from EMI amount (${this.formatCurrency(emiAmount)})`);
      }
      
      // Check if payment exceeds remaining principal
      const currentBalance = await this.getCurrentRemainingPrincipal(account.id);
      if (transaction.amount > currentBalance + (emiAmount * 0.1)) { // Allow 10% variance for final payment
        warnings.push(`Payment amount exceeds remaining loan balance of ${this.formatCurrency(currentBalance)}`);
      }
    }

    // Validate prepayment amounts
    if (transaction.transaction_type === 'expense' && account.metadata?.emiAmount) {
      const emiAmount = account.metadata.emiAmount;
      if (transaction.amount > emiAmount * 2) {
        warnings.push('Large payment detected - this may be a prepayment. Consider proper categorization.');
      }
    }

    // Validate transaction date
    const transactionDate = new Date(transaction.transaction_date);
    if (isNaN(transactionDate.getTime())) {
      errors.push('Invalid transaction date');
    }

    // Check EMI due date alignment
    if (account.metadata?.nextEmiDate && transaction.transaction_type === 'expense') {
      const nextEmiDate = new Date(account.metadata.nextEmiDate);
      const daysDifference = Math.abs((transactionDate.getTime() - nextEmiDate.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysDifference > 7) {
        warnings.push('EMI payment date is more than 7 days from due date');
      }
    }

    return this.createValidationResult(
      errors.length === 0,
      errors,
      warnings,
      errors.length > 0 ? this.createLoanSuggestedCorrections(transaction, errors) : undefined
    );
  }

  /**
   * Calculate EMI amount using compound interest formula
   */
  calculateEMI(principal: number, rate: number, tenure: number): number {
    if (principal <= 0 || rate <= 0 || tenure <= 0) {
      return 0;
    }

    const monthlyRate = rate / 12 / 100;
    const emi = (principal * monthlyRate * Math.pow(1 + monthlyRate, tenure)) / 
                (Math.pow(1 + monthlyRate, tenure) - 1);
    
    return this.roundToTwoDecimals(emi);
  }

  /**
   * Generate amortization schedule for loan
   */
  generateAmortizationSchedule(
    principal: number, 
    rate: number, 
    tenure: number
  ): AmortizationSchedule[] {
    if (principal <= 0 || rate <= 0 || tenure <= 0) {
      return [];
    }

    const emi = this.calculateEMI(principal, rate, tenure);
    const schedule: AmortizationSchedule[] = [];
    let remainingPrincipal = principal;
    
    for (let month = 1; month <= tenure; month++) {
      const interestComponent = (remainingPrincipal * rate) / 12 / 100;
      const principalComponent = emi - interestComponent;
      remainingPrincipal -= principalComponent;
      
      schedule.push({
        month,
        emi,
        principalComponent: this.roundToTwoDecimals(principalComponent),
        interestComponent: this.roundToTwoDecimals(interestComponent),
        remainingPrincipal: this.roundToTwoDecimals(Math.max(0, remainingPrincipal))
      });
    }
    
    return schedule;
  }

  /**
   * Split EMI into principal and interest components
   */
  splitEMIComponents(
    emiAmount: number,
    remainingPrincipal: number,
    interestRate: number
  ): EMIComponents {
    if (remainingPrincipal <= 0 || interestRate <= 0) {
      return { principal: emiAmount, interest: 0 };
    }

    const interestComponent = (remainingPrincipal * interestRate) / 12 / 100;
    const principalComponent = emiAmount - interestComponent;
    
    return {
      principal: this.roundToTwoDecimals(Math.max(0, principalComponent)),
      interest: this.roundToTwoDecimals(Math.max(0, interestComponent))
    };
  }

  /**
   * Calculate remaining tenure based on current balance
   */
  private calculateRemainingTenure(remainingPrincipal: number, emiAmount: number, interestRate: number): number {
    if (remainingPrincipal <= 0 || emiAmount <= 0 || interestRate <= 0) {
      return 0;
    }

    const monthlyRate = interestRate / 12 / 100;
    
    // Using EMI formula to calculate tenure: n = log(1 + (P*r)/EMI) / log(1 + r)
    const numerator = Math.log(1 + (remainingPrincipal * monthlyRate) / emiAmount);
    const denominator = Math.log(1 + monthlyRate);
    
    return Math.ceil(numerator / denominator);
  }

  /**
   * Sum principal payments from transactions
   */
  private sumPrincipalPayments(transactions: Transaction[]): number {
    // For loan accounts, EMI payments are recorded as expenses
    // We need to extract the principal component from each EMI
    const emiPayments = transactions.filter(t => t.transaction_type === 'expense');
    
    // This is a simplified calculation - in reality, we'd need to track
    // the principal component of each EMI based on when it was paid
    return emiPayments.reduce((sum, payment) => {
      // For now, assume the entire payment goes toward principal
      // In a real implementation, we'd calculate the principal component
      // based on the remaining balance at the time of payment
      return sum + payment.amount;
    }, 0);
  }

  /**
   * Calculate total interest paid till date
   */
  private calculateTotalInterestPaid(
    transactions: Transaction[], 
    originalPrincipal: number, 
    principalPayments: number
  ): number {
    const totalPayments = this.sumTransactionsByType(transactions, ['expense']);
    
    // Total interest = Total payments - Principal paid
    return Math.max(0, totalPayments - principalPayments);
  }

  /**
   * Count number of EMI payments made
   */
  private countEMIPayments(transactions: Transaction[]): number {
    return transactions.filter(t => t.transaction_type === 'expense').length;
  }

  /**
   * Get current remaining principal for real-time calculations
   */
  private async getCurrentRemainingPrincipal(accountId: number): Promise<number> {
    try {
      const account = await this.getAccountWithMetadata(accountId);
      if (!account?.metadata?.principalAmount) {
        return 0;
      }

      const transactions = await this.getTransactionsForAccount(accountId);
      const principalPayments = this.sumPrincipalPayments(transactions);
      
      return Math.max(0, account.metadata.principalAmount - principalPayments);
    } catch (error) {
      console.warn(`Failed to get current remaining principal: ${error}`);
      return 0;
    }
  }

  /**
   * Get transactions for account (wrapper for transaction repository)
   */
  private async getTransactionsForAccount(accountId: number): Promise<Transaction[]> {
    // This would normally use the transaction repository
    // For now, return empty array as this method will be called from the service layer
    return [];
  }

  /**
   * Calculate next EMI date
   */
  private calculateNextEMIDate(): string {
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    nextMonth.setDate(5); // Default to 5th of next month
    
    return nextMonth.toISOString().split('T')[0]; // Return YYYY-MM-DD format
  }

  /**
   * Create suggested corrections for invalid loan transactions
   */
  private createLoanSuggestedCorrections(
    transaction: Transaction, 
    errors: string[]
  ): Partial<Transaction> {
    const corrections: Partial<Transaction> = {};

    // Fix negative amounts
    if (errors.some(e => e.includes('positive'))) {
      corrections.amount = Math.abs(transaction.amount);
    }

    // Fix invalid transaction type
    if (errors.some(e => e.includes('transfer'))) {
      // Default to expense for loan transactions (EMI payments)
      corrections.transaction_type = 'expense';
    }

    // Fix invalid transaction date
    if (errors.some(e => e.includes('transaction date'))) {
      corrections.transaction_date = new Date().toISOString();
    }

    return corrections;
  }

  /**
   * Get loan summary
   */
  async getLoanSummary(accountId: number): Promise<LoanSummary> {
    const balance = await this.calculateBalance(accountId, await this.getTransactionsForAccount(accountId));
    
    return {
      remainingPrincipal: balance.displayBalance,
      nextEMIDate: balance.metadata.nextEmiDate || '',
      emiAmount: balance.metadata.emiAmount || 0,
      remainingTenure: balance.metadata.remainingTenure || 0,
      originalPrincipal: balance.metadata.originalPrincipal || 0,
      totalInterestPaid: balance.metadata.totalInterestPaid || 0
    };
  }

  /**
   * Calculate prepayment impact
   */
  calculatePrepaymentImpact(
    remainingPrincipal: number,
    emiAmount: number,
    interestRate: number,
    prepaymentAmount: number
  ): {
    newRemainingPrincipal: number;
    newRemainingTenure: number;
    interestSaved: number;
    newEMI?: number;
  } {
    if (prepaymentAmount >= remainingPrincipal) {
      return {
        newRemainingPrincipal: 0,
        newRemainingTenure: 0,
        interestSaved: this.calculateTotalInterestForTenure(remainingPrincipal, emiAmount, interestRate),
        newEMI: 0
      };
    }

    const newRemainingPrincipal = remainingPrincipal - prepaymentAmount;
    const newRemainingTenure = this.calculateRemainingTenure(newRemainingPrincipal, emiAmount, interestRate);
    
    const originalTotalInterest = this.calculateTotalInterestForTenure(remainingPrincipal, emiAmount, interestRate);
    const newTotalInterest = this.calculateTotalInterestForTenure(newRemainingPrincipal, emiAmount, interestRate);
    const interestSaved = originalTotalInterest - newTotalInterest;

    return {
      newRemainingPrincipal: this.roundToTwoDecimals(newRemainingPrincipal),
      newRemainingTenure,
      interestSaved: this.roundToTwoDecimals(interestSaved)
    };
  }

  /**
   * Calculate total interest for remaining tenure
   */
  private calculateTotalInterestForTenure(principal: number, emiAmount: number, interestRate: number): number {
    const tenure = this.calculateRemainingTenure(principal, emiAmount, interestRate);
    const totalPayments = emiAmount * tenure;
    return Math.max(0, totalPayments - principal);
  }
}