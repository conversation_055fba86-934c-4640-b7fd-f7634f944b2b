/**
 * Default Account Calculator
 * 
 * Calculator for standard account types: checking, savings, investment
 * Provides basic balance calculation logic without specialized features
 */

import { Account, Transaction } from '../../../shared/types';
import { AccountBalance, ValidationResult } from '../../../shared/types/calculators';
import { BaseAccountCalculator } from './BaseAccountCalculator';

/**
 * Default calculator for checking, savings, and investment accounts
 * Uses simple income/expense calculation logic
 */
export class DefaultAccountCalculator extends BaseAccountCalculator {
  
  /**
   * Get calculator type identifier
   */
  getCalculatorType(): string {
    return 'DefaultAccountCalculator';
  }

  /**
   * Calculate account balance for standard account types
   * Simple calculation: income transactions add to balance, expense transactions subtract
   */
  async calculateBalance(accountId: number, transactions: Transaction[]): Promise<AccountBalance> {
    const account = await this.getAccountWithMetadata(accountId);
    if (!account) {
      throw new Error('Account not found');
    }

    // Validate account type is supported
    this.validateAccountType(account, ['checking', 'savings', 'investment']);

    // Perform calculation with performance monitoring
    const { result: balance } = await this.measurePerformance(async () => {
      let calculatedBalance = 0;
      
      for (const transaction of transactions) {
        switch (transaction.transaction_type) {
          case 'income':
            calculatedBalance += transaction.amount;
            break;
          case 'expense':
            calculatedBalance -= transaction.amount;
            break;
          case 'transfer':
            // For transfers, we assume the amount represents the net effect
            // Positive for incoming transfers, negative for outgoing
            // This would need to be refined based on specific transfer handling
            calculatedBalance += transaction.amount;
            break;
        }
      }
      
      return this.roundToTwoDecimals(calculatedBalance);
    }, transactions.length);

    // Determine calculation type based on account type
    let calculationType: AccountBalance['calculationType'];
    switch (account.type) {
      case 'checking':
        calculationType = 'checking';
        break;
      case 'savings':
        calculationType = 'savings';
        break;
      case 'investment':
        calculationType = 'investment';
        break;
      default:
        calculationType = 'checking'; // Fallback
    }

    return {
      displayBalance: balance,
      actualBalance: balance,
      metadata: {
        accountId: account.id,
        accountType: account.type,
        currency: account.currency,
        transactionCount: transactions.length,
        lastTransactionDate: transactions.length > 0 
          ? new Date(Math.max(...transactions.map(t => new Date(t.transaction_date).getTime())))
          : null,
        // Add month-to-date calculations
        monthToDateIncome: this.calculateMonthToDateIncome(transactions),
        monthToDateExpenses: this.calculateMonthToDateExpenses(transactions),
        monthToDateNet: this.calculateMonthToDateNet(transactions)
      },
      lastCalculated: new Date(),
      calculationType
    };
  }

  /**
   * Validate transaction for standard account types
   * Basic validation: amount must be positive, account must match
   */
  async validateTransaction(transaction: Transaction, account: Account): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate account type is supported
    try {
      this.validateAccountType(account, ['checking', 'savings', 'investment']);
    } catch (error) {
      errors.push(`${error}`);
    }

    // Validate transaction amount
    const amountValidation = this.validatePositiveAmount(transaction.amount);
    if (!amountValidation.isValid) {
      errors.push(amountValidation.error!);
    }

    // Validate account ID matches
    if (transaction.account_id !== account.id) {
      errors.push('Transaction account ID does not match target account');
    }

    // Validate transaction type
    const validTypes: Transaction['transaction_type'][] = ['income', 'expense', 'transfer'];
    if (!validTypes.includes(transaction.transaction_type)) {
      errors.push('Invalid transaction type for standard account');
    }

    // Add account-specific warnings
    if (account.type === 'savings' && transaction.transaction_type === 'expense' && transaction.amount > 50000) {
      warnings.push('Large withdrawal from savings account - consider moving to checking account');
    }

    if (account.type === 'investment' && transaction.transaction_type === 'expense' && transaction.amount < 1000) {
      warnings.push('Small investment transaction - consider batch investing to reduce fees');
    }

    // Validate transaction date
    const transactionDate = new Date(transaction.transaction_date);
    if (isNaN(transactionDate.getTime())) {
      errors.push('Invalid transaction date');
    }

    // Check for future dates (warning only)
    const now = new Date();
    if (transactionDate > now) {
      warnings.push('Transaction date is in the future');
    }

    // Check for very old transactions (warning only)
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    if (transactionDate < oneYearAgo) {
      warnings.push('Transaction date is more than one year old');
    }

    return this.createValidationResult(
      errors.length === 0,
      errors,
      warnings,
      errors.length > 0 ? this.createSuggestedCorrections(transaction, errors) : undefined
    );
  }

  /**
   * Calculate month-to-date income for metadata
   */
  private calculateMonthToDateIncome(transactions: Transaction[]): number {
    const currentMonthTransactions = this.getCurrentMonthTransactions(transactions);
    return this.roundToTwoDecimals(
      currentMonthTransactions
        .filter(t => t.transaction_type === 'income')
        .reduce((sum, t) => sum + t.amount, 0)
    );
  }

  /**
   * Calculate month-to-date expenses for metadata
   */
  private calculateMonthToDateExpenses(transactions: Transaction[]): number {
    const currentMonthTransactions = this.getCurrentMonthTransactions(transactions);
    return this.roundToTwoDecimals(
      currentMonthTransactions
        .filter(t => t.transaction_type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0)
    );
  }

  /**
   * Calculate month-to-date net (income - expenses) for metadata
   */
  private calculateMonthToDateNet(transactions: Transaction[]): number {
    const income = this.calculateMonthToDateIncome(transactions);
    const expenses = this.calculateMonthToDateExpenses(transactions);
    return this.roundToTwoDecimals(income - expenses);
  }

  /**
   * Create suggested corrections for invalid transactions
   */
  private createSuggestedCorrections(transaction: Transaction, errors: string[]): Partial<Transaction> {
    const corrections: Partial<Transaction> = {};

    // Fix negative amounts
    if (errors.some(e => e.includes('positive'))) {
      corrections.amount = Math.abs(transaction.amount);
    }

    // Fix invalid transaction type
    if (errors.some(e => e.includes('transaction type'))) {
      // Suggest expense as default for most cases
      corrections.transaction_type = 'expense';
    }

    // Fix invalid transaction date
    if (errors.some(e => e.includes('transaction date'))) {
      corrections.transaction_date = new Date().toISOString();
    }

    return corrections;
  }

  /**
   * Get account type specific balance insights
   * Additional method for enhanced balance information
   */
  async getAccountInsights(accountId: number, transactions: Transaction[]): Promise<{
    averageMonthlyIncome: number;
    averageMonthlyExpenses: number;
    largestTransaction: Transaction | null;
    transactionFrequency: number;
    balanceTrend: 'increasing' | 'decreasing' | 'stable';
  }> {
    if (transactions.length === 0) {
      return {
        averageMonthlyIncome: 0,
        averageMonthlyExpenses: 0,
        largestTransaction: null,
        transactionFrequency: 0,
        balanceTrend: 'stable'
      };
    }

    // Calculate averages over the last 3 months
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    const recentTransactions = this.filterTransactionsByDateRange(transactions, threeMonthsAgo, new Date());

    const income = recentTransactions.filter(t => t.transaction_type === 'income');
    const expenses = recentTransactions.filter(t => t.transaction_type === 'expense');

    const averageMonthlyIncome = income.length > 0 
      ? this.roundToTwoDecimals(income.reduce((sum, t) => sum + t.amount, 0) / 3)
      : 0;

    const averageMonthlyExpenses = expenses.length > 0
      ? this.roundToTwoDecimals(expenses.reduce((sum, t) => sum + t.amount, 0) / 3)
      : 0;

    // Find largest transaction by amount
    const largestTransaction = transactions.reduce((largest, current) => 
      current.amount > (largest?.amount || 0) ? current : largest, null as Transaction | null);

    // Calculate transaction frequency (transactions per month)
    const transactionFrequency = transactions.length > 0
      ? this.roundToTwoDecimals((transactions.length / Math.max(1, this.getMonthSpan(transactions))))
      : 0;

    // Determine balance trend
    const balanceTrend = this.calculateBalanceTrend(transactions);

    return {
      averageMonthlyIncome,
      averageMonthlyExpenses,
      largestTransaction,
      transactionFrequency,
      balanceTrend
    };
  }

  /**
   * Calculate the span of months covered by transactions
   */
  private getMonthSpan(transactions: Transaction[]): number {
    if (transactions.length === 0) return 1;

    const dates = transactions.map(t => new Date(t.transaction_date));
    const oldest = new Date(Math.min(...dates.map(d => d.getTime())));
    const newest = new Date(Math.max(...dates.map(d => d.getTime())));

    const monthDiff = (newest.getFullYear() - oldest.getFullYear()) * 12 + 
                      (newest.getMonth() - oldest.getMonth());

    return Math.max(1, monthDiff + 1); // Add 1 to include the current month
  }

  /**
   * Calculate balance trend over time
   */
  private calculateBalanceTrend(transactions: Transaction[]): 'increasing' | 'decreasing' | 'stable' {
    if (transactions.length < 10) return 'stable'; // Need sufficient data

    // Sort transactions by date
    const sortedTransactions = transactions.sort((a, b) => 
      new Date(a.transaction_date).getTime() - new Date(b.transaction_date).getTime());

    // Calculate running balances for trend analysis
    let runningBalance = 0;
    const balancePoints: { date: Date; balance: number }[] = [];

    for (const transaction of sortedTransactions) {
      switch (transaction.transaction_type) {
        case 'income':
          runningBalance += transaction.amount;
          break;
        case 'expense':
          runningBalance -= transaction.amount;
          break;
        case 'transfer':
          runningBalance += transaction.amount;
          break;
      }
      
      balancePoints.push({
        date: new Date(transaction.transaction_date),
        balance: runningBalance
      });
    }

    // Compare first quarter to last quarter of balance points
    const quarterSize = Math.floor(balancePoints.length / 4);
    const firstQuarter = balancePoints.slice(0, quarterSize);
    const lastQuarter = balancePoints.slice(-quarterSize);

    const firstQuarterAvg = firstQuarter.reduce((sum, point) => sum + point.balance, 0) / quarterSize;
    const lastQuarterAvg = lastQuarter.reduce((sum, point) => sum + point.balance, 0) / quarterSize;

    const change = lastQuarterAvg - firstQuarterAvg;
    const changePercentage = Math.abs(change) / Math.abs(firstQuarterAvg);

    // Consider significant if change is more than 10%
    if (changePercentage > 0.1) {
      return change > 0 ? 'increasing' : 'decreasing';
    }

    return 'stable';
  }
}