/**
 * Credit Card Calculator
 * 
 * Specialized calculator for credit card accounts
 * Handles credit limit, available credit, utilization, and minimum payment calculations
 */

import { Account, Transaction } from '../../../shared/types';
import { AccountBalance, ValidationResult, CreditCardSummary } from '../../../shared/types/calculators';
import { BaseAccountCalculator } from './BaseAccountCalculator';

/**
 * Calculator for credit card accounts
 * Manages credit limits, outstanding balances, and payment calculations
 */
export class CreditCardCalculator extends BaseAccountCalculator {
  
  /**
   * Get calculator type identifier
   */
  getCalculatorType(): string {
    return 'CreditCardCalculator';
  }

  /**
   * Calculate account balance for credit card accounts
   * Display balance = Available Credit, Actual balance = Outstanding Balance
   */
  async calculateBalance(accountId: number, transactions: Transaction[]): Promise<AccountBalance> {
    const account = await this.getAccountWithMetadata(accountId);
    if (!account) {
      throw new Error('Account not found');
    }

    // Validate account type
    this.validateAccountType(account, ['credit']);

    // Validate required metadata
    this.validateRequiredMetadata(account, ['creditLimit']);

    // Perform calculation with performance monitoring
    const { result: balanceData } = await this.measurePerformance(async () => {
      const creditLimit = account.metadata!.creditLimit!;
      
      // Calculate outstanding balance from transactions
      // Credit charges and fees increase outstanding balance
      // Credit payments reduce outstanding balance
      const charges = this.sumTransactionsByCategory(
        transactions,
        this.getCreditChargeCategories()
      );
      
      const payments = this.sumTransactionsByCategory(
        transactions,
        this.getCreditPaymentCategories()
      );
      
      // For credit cards without specific categories, use transaction types
      const chargeTransactions = this.sumTransactionsByType(transactions, ['expense']);
      const paymentTransactions = this.sumTransactionsByType(transactions, ['income']);
      
      // Use category-based calculation if available, otherwise fall back to transaction types
      const totalCharges = charges > 0 ? charges : chargeTransactions;
      const totalPayments = payments > 0 ? payments : paymentTransactions;
      
      const rawOutstandingBalance = totalCharges - totalPayments;
      const outstandingBalance = this.roundToTwoDecimals(Math.max(0, rawOutstandingBalance)); // Can't be negative
      const availableCredit = this.roundToTwoDecimals(Math.max(0, creditLimit - outstandingBalance)); // Can't be negative
      
      // Calculate utilization and minimum payment
      const creditUtilization = this.calculateCreditUtilization(outstandingBalance, creditLimit);
      const minimumPaymentDue = this.calculateMinimumPayment(outstandingBalance);
      
      return {
        outstandingBalance,
        availableCredit,
        creditLimit,
        creditUtilization,
        minimumPaymentDue
      };
    }, transactions.length);

    // Get payment due date from metadata or calculate next month
    const paymentDueDate = account.metadata!.paymentDueDate || this.getNextPaymentDueDate();
    
    return {
      displayBalance: balanceData.availableCredit, // Show available credit to user
      actualBalance: balanceData.outstandingBalance, // Actual debt owed
      metadata: {
        creditLimit: balanceData.creditLimit,
        outstandingBalance: balanceData.outstandingBalance,
        availableCredit: balanceData.availableCredit,
        creditUtilization: balanceData.creditUtilization,
        minimumPaymentDue: balanceData.minimumPaymentDue,
        paymentDueDate,
        // Additional metadata
        totalCharges: this.sumTransactionsByType(transactions, ['expense']),
        totalPayments: this.sumTransactionsByType(transactions, ['income']),
        transactionCount: transactions.length,
        lastTransactionDate: transactions.length > 0 
          ? new Date(Math.max(...transactions.map(t => new Date(t.transaction_date).getTime())))
          : null,
        // Monthly summaries
        currentMonthCharges: this.getCurrentMonthCharges(transactions),
        currentMonthPayments: this.getCurrentMonthPayments(transactions),
        averageMonthlySpend: this.calculateAverageMonthlySpend(transactions)
      },
      lastCalculated: new Date(),
      calculationType: 'credit'
    };
  }

  /**
   * Validate transaction for credit card accounts
   */
  async validateTransaction(transaction: Transaction, account: Account): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate account type
    try {
      this.validateAccountType(account, ['credit']);
    } catch (error) {
      errors.push(`${error}`);
    }

    // Validate transaction amount
    const amountValidation = this.validatePositiveAmount(transaction.amount);
    if (!amountValidation.isValid) {
      errors.push(amountValidation.error!);
    }

    // Validate account ID matches
    if (transaction.account_id !== account.id) {
      errors.push('Transaction account ID does not match credit card account');
    }

    // Validate transaction type
    const validTypes: Transaction['transaction_type'][] = ['income', 'expense'];
    if (!validTypes.includes(transaction.transaction_type)) {
      errors.push('Credit card accounts only support income (payments) and expense (charges) transactions');
    }

    // Credit card specific validations
    if (account.metadata?.creditLimit) {
      const currentBalance = await this.getCurrentOutstandingBalance(account.id);
      
      // Check if expense would exceed credit limit
      if (transaction.transaction_type === 'expense') {
        const projectedBalance = currentBalance + transaction.amount;
        const creditLimit = account.metadata.creditLimit;
        
        if (projectedBalance > creditLimit) {
          errors.push(`Transaction would exceed credit limit. Available credit: ${this.formatCurrency(creditLimit - currentBalance)}`);
        } else if (projectedBalance > creditLimit * 0.9) {
          warnings.push(`Transaction will use over 90% of credit limit`);
        }
      }
      
      // Validate payment amount doesn't exceed outstanding balance
      if (transaction.transaction_type === 'income' && transaction.amount > currentBalance) {
        warnings.push(`Payment amount exceeds current outstanding balance of ${this.formatCurrency(currentBalance)}`);
      }
    }

    // Validate transaction date
    const transactionDate = new Date(transaction.transaction_date);
    if (isNaN(transactionDate.getTime())) {
      errors.push('Invalid transaction date');
    }

    // Check for duplicate transactions
    if (transaction.description && transaction.description.toLowerCase().includes('duplicate')) {
      warnings.push('Transaction description suggests this might be a duplicate');
    }

    // Validate large transaction amounts
    if (transaction.amount > 100000) {
      warnings.push('Large transaction amount - please verify accuracy');
    }

    return this.createValidationResult(
      errors.length === 0,
      errors,
      warnings,
      errors.length > 0 ? this.createCreditCardSuggestedCorrections(transaction, errors) : undefined
    );
  }

  /**
   * Calculate credit utilization percentage
   */
  calculateCreditUtilization(outstandingBalance: number, creditLimit: number): number {
    if (creditLimit <= 0) {
      return 0;
    }
    
    const utilization = (outstandingBalance / creditLimit) * 100;
    return this.roundToTwoDecimals(Math.max(0, Math.min(100, utilization)));
  }

  /**
   * Calculate minimum payment due based on outstanding balance
   */
  calculateMinimumPayment(outstandingBalance: number): number {
    if (outstandingBalance <= 0) {
      return 0;
    }
    
    // Standard minimum payment: 3% of outstanding balance or ₹500, whichever is higher
    const percentageMinimum = outstandingBalance * 0.03;
    const minimumAmount = 500; // ₹500 minimum payment
    
    return this.roundToTwoDecimals(Math.max(percentageMinimum, minimumAmount));
  }

  /**
   * Calculate available credit with real-time updates
   */
  async calculateAvailableCredit(accountId: number): Promise<number> {
    const account = await this.getAccountWithMetadata(accountId);
    if (!account || account.type !== 'credit') {
      throw new Error('Account not found or not a credit card account');
    }

    if (!account.metadata?.creditLimit) {
      throw new Error('Credit card account missing credit limit metadata');
    }

    const currentBalance = await this.getCurrentOutstandingBalance(accountId);
    const availableCredit = account.metadata.creditLimit - currentBalance;
    
    return this.roundToTwoDecimals(Math.max(0, availableCredit));
  }

  /**
   * Get comprehensive credit card summary
   */
  async getCreditCardSummary(accountId: number): Promise<CreditCardSummary> {
    const balance = await this.calculateBalance(accountId, await this.getTransactionsForAccount(accountId));
    
    return {
      availableCredit: balance.displayBalance,
      outstandingBalance: balance.actualBalance,
      creditUtilization: balance.metadata.creditUtilization,
      minimumPaymentDue: balance.metadata.minimumPaymentDue,
      creditLimit: balance.metadata.creditLimit,
      paymentDueDate: balance.metadata.paymentDueDate
    };
  }

  /**
   * Get current outstanding balance for real-time calculations
   */
  private async getCurrentOutstandingBalance(accountId: number): Promise<number> {
    try {
      const transactions = await this.getTransactionsForAccount(accountId);
      const charges = this.sumTransactionsByType(transactions, ['expense']);
      const payments = this.sumTransactionsByType(transactions, ['income']);
      
      return this.roundToTwoDecimals(Math.max(0, charges - payments));
    } catch (error) {
      console.warn(`Failed to get current outstanding balance: ${error}`);
      return 0;
    }
  }

  /**
   * Get transactions for account (wrapper for transaction repository)
   */
  private async getTransactionsForAccount(accountId: number): Promise<Transaction[]> {
    // This would normally use the transaction repository
    // For now, return empty array as this method will be called from the service layer
    return [];
  }

  /**
   * Get credit charge category IDs (would be configured based on system categories)
   */
  private getCreditChargeCategories(): (number | null)[] {
    // These would be actual category IDs for credit card charges
    // For now, return empty array to fall back to transaction type logic
    return [];
  }

  /**
   * Get credit payment category IDs (would be configured based on system categories)
   */
  private getCreditPaymentCategories(): (number | null)[] {
    // These would be actual category IDs for credit card payments
    // For now, return empty array to fall back to transaction type logic
    return [];
  }

  /**
   * Calculate current month charges
   */
  private getCurrentMonthCharges(transactions: Transaction[]): number {
    const currentMonthTransactions = this.getCurrentMonthTransactions(transactions);
    return this.roundToTwoDecimals(
      currentMonthTransactions
        .filter(t => t.transaction_type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0)
    );
  }

  /**
   * Calculate current month payments
   */
  private getCurrentMonthPayments(transactions: Transaction[]): number {
    const currentMonthTransactions = this.getCurrentMonthTransactions(transactions);
    return this.roundToTwoDecimals(
      currentMonthTransactions
        .filter(t => t.transaction_type === 'income')
        .reduce((sum, t) => sum + t.amount, 0)
    );
  }

  /**
   * Calculate average monthly spend over last 6 months
   */
  private calculateAverageMonthlySpend(transactions: Transaction[]): number {
    if (transactions.length === 0) {
      return 0;
    }

    // Get last 6 months of transactions
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    const recentTransactions = this.filterTransactionsByDateRange(
      transactions, 
      sixMonthsAgo, 
      new Date()
    );

    const totalSpend = recentTransactions
      .filter(t => t.transaction_type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    // Calculate average over 6 months
    return this.roundToTwoDecimals(totalSpend / 6);
  }

  /**
   * Get next payment due date (default to next month's 25th)
   */
  private getNextPaymentDueDate(): string {
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    nextMonth.setDate(25); // Default to 25th of next month
    
    return nextMonth.toISOString().split('T')[0]; // Return YYYY-MM-DD format
  }

  /**
   * Create suggested corrections for invalid credit card transactions
   */
  private createCreditCardSuggestedCorrections(
    transaction: Transaction, 
    errors: string[]
  ): Partial<Transaction> {
    const corrections: Partial<Transaction> = {};

    // Fix negative amounts
    if (errors.some(e => e.includes('positive'))) {
      corrections.amount = Math.abs(transaction.amount);
    }

    // Fix invalid transaction type
    if (errors.some(e => e.includes('only support income') || e.includes('transfer'))) {
      // Default to expense for credit card transactions
      corrections.transaction_type = 'expense';
    }

    // Fix invalid transaction date
    if (errors.some(e => e.includes('transaction date'))) {
      corrections.transaction_date = new Date().toISOString();
    }

    return corrections;
  }

  /**
   * Get payment recommendations based on balance and utilization
   */
  getPaymentRecommendations(
    outstandingBalance: number, 
    creditLimit: number, 
    minimumPayment: number
  ): {
    recommendedPayment: number;
    paymentType: 'minimum' | 'recommended' | 'full';
    reasoning: string;
  } {
    const utilization = this.calculateCreditUtilization(outstandingBalance, creditLimit);
    
    // Full payment recommendation
    if (outstandingBalance <= creditLimit * 0.1) {
      return {
        recommendedPayment: outstandingBalance,
        paymentType: 'full',
        reasoning: 'Low balance - pay in full to avoid interest charges'
      };
    }
    
    // High utilization - pay more than minimum
    if (utilization > 30) {
      const recommendedPayment = Math.min(
        outstandingBalance * 0.2, // 20% of balance
        outstandingBalance - (creditLimit * 0.25) // Bring utilization down to 25%
      );
      
      return {
        recommendedPayment: this.roundToTwoDecimals(Math.max(minimumPayment, recommendedPayment)),
        paymentType: 'recommended',
        reasoning: `High utilization (${utilization.toFixed(1)}%) - pay extra to improve credit score`
      };
    }
    
    // Normal utilization - minimum payment is OK
    return {
      recommendedPayment: minimumPayment,
      paymentType: 'minimum',
      reasoning: 'Utilization is healthy - minimum payment is sufficient'
    };
  }
}