/**
 * Account Calculator Factory
 * 
 * Factory class for creating appropriate calculator instances based on account type
 * Ensures consistent calculator selection and instantiation across the application
 */

import { AccountType } from '../../../shared/types';
import { BaseAccountCalculator } from './BaseAccountCalculator';

/**
 * Factory class for creating account-specific calculators
 * Returns the appropriate calculator instance based on account type
 */
export class AccountCalculatorFactory {
  /**
   * Get calculator instance for specific account type
   * @param accountType The type of account requiring calculations
   * @returns Appropriate calculator instance
   * @throws Error if account type is not supported
   */
  static getCalculator(accountType: AccountType): BaseAccountCalculator {
    switch (accountType) {
      case 'credit':
        // Lazy import to avoid circular dependencies
        const { CreditCardCalculator } = require('./CreditCardCalculator');
        return new CreditCardCalculator();
        
      case 'loan':
        // Lazy import to avoid circular dependencies  
        const { LoanCalculator } = require('./LoanCalculator');
        return new LoanCalculator();
        
      case 'checking':
      case 'savings':
      case 'investment':
        // Use default calculator for standard account types
        const { DefaultAccountCalculator } = require('./DefaultAccountCalculator');
        return new DefaultAccountCalculator();
        
      default:
        throw new Error(`Unsupported account type: ${accountType}`);
    }
  }

  /**
   * Check if account type is supported by calculator system
   * @param accountType The account type to validate
   * @returns True if account type has a calculator implementation
   */
  static isAccountTypeSupported(accountType: AccountType): boolean {
    const supportedTypes: AccountType[] = ['checking', 'savings', 'credit', 'loan', 'investment'];
    return supportedTypes.includes(accountType);
  }

  /**
   * Get list of all supported account types
   * @returns Array of supported account types
   */
  static getSupportedAccountTypes(): AccountType[] {
    return ['checking', 'savings', 'credit', 'loan', 'investment'];
  }

  /**
   * Get calculator type identifier without instantiating
   * @param accountType The account type
   * @returns String identifier for calculator type
   */
  static getCalculatorType(accountType: AccountType): string {
    switch (accountType) {
      case 'credit':
        return 'CreditCardCalculator';
      case 'loan':
        return 'LoanCalculator';
      case 'checking':
      case 'savings':
      case 'investment':
        return 'DefaultAccountCalculator';
      default:
        throw new Error(`Unsupported account type: ${accountType}`);
    }
  }

  /**
   * Validate calculator requirements for account type
   * @param accountType The account type to validate
   * @returns Validation result with requirements
   */
  static validateCalculatorRequirements(accountType: AccountType): {
    isValid: boolean;
    requiresMetadata: boolean;
    requiredMetadataFields: string[];
    warnings: string[];
  } {
    const warnings: string[] = [];
    
    switch (accountType) {
      case 'credit':
        return {
          isValid: true,
          requiresMetadata: true,
          requiredMetadataFields: ['creditLimit'],
          warnings: ['Credit card calculations require creditLimit in metadata']
        };
        
      case 'loan':
        return {
          isValid: true,
          requiresMetadata: true,
          requiredMetadataFields: ['principalAmount', 'interestRate', 'emiAmount'],
          warnings: ['Loan calculations require principalAmount, interestRate, and emiAmount in metadata']
        };
        
      case 'checking':
      case 'savings':
      case 'investment':
        return {
          isValid: true,
          requiresMetadata: false,
          requiredMetadataFields: [],
          warnings: []
        };
        
      default:
        return {
          isValid: false,
          requiresMetadata: false,
          requiredMetadataFields: [],
          warnings: [`Unsupported account type: ${accountType}`]
        };
    }
  }

  /**
   * Create multiple calculators for batch operations
   * @param accountTypes Array of account types
   * @returns Map of account types to calculator instances
   */
  static createBatchCalculators(accountTypes: AccountType[]): Map<AccountType, BaseAccountCalculator> {
    const calculators = new Map<AccountType, BaseAccountCalculator>();
    
    for (const accountType of accountTypes) {
      try {
        calculators.set(accountType, this.getCalculator(accountType));
      } catch (error) {
        console.warn(`Failed to create calculator for ${accountType}:`, error);
      }
    }
    
    return calculators;
  }

  /**
   * Get performance expectations for calculator type
   * @param accountType The account type
   * @returns Performance expectations in milliseconds
   */
  static getPerformanceExpectations(accountType: AccountType): {
    maxCalculationTime: number;
    maxTransactionVolume: number;
    warningThreshold: number;
  } {
    switch (accountType) {
      case 'credit':
        return {
          maxCalculationTime: 100, // Credit card calculations should be fast
          maxTransactionVolume: 1000,
          warningThreshold: 50
        };
        
      case 'loan':
        return {
          maxCalculationTime: 500, // EMI calculations may take longer
          maxTransactionVolume: 500, // Fewer transactions expected
          warningThreshold: 200
        };
        
      case 'checking':
      case 'savings':
      case 'investment':
        return {
          maxCalculationTime: 200, // Standard calculations
          maxTransactionVolume: 2000, // Higher volume expected
          warningThreshold: 100
        };
        
      default:
        return {
          maxCalculationTime: 300,
          maxTransactionVolume: 1000,
          warningThreshold: 150
        };
    }
  }
}