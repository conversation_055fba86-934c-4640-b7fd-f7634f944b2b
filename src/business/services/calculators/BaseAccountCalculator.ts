/**
 * Base Account Calculator
 * 
 * Abstract base class for account-specific balance calculations
 * Provides common interface and utility methods for all calculator types
 */

import { Account, Transaction, AccountType } from '../../../shared/types';
import { AccountMetadata } from '../../../shared/types/accountMetadata';
import { AccountBalance, ValidationResult, CalculatorMetrics } from '../../../shared/types/calculators';
import { AccountRepository } from '../../../data/repositories/AccountRepository';

/**
 * Abstract base class for account-specific balance calculations
 * Each account type (credit, loan, checking, etc.) extends this class
 */
export abstract class BaseAccountCalculator {
  protected accountRepository: AccountRepository;

  constructor() {
    this.accountRepository = new AccountRepository();
  }

  /**
   * Calculate account balance from transaction history
   * Must be implemented by each concrete calculator
   */
  abstract calculateBalance(accountId: number, transactions: Transaction[]): Promise<AccountBalance>;

  /**
   * Validate transaction against account-specific rules
   * Must be implemented by each concrete calculator
   */
  abstract validateTransaction(transaction: Transaction, account: Account): Promise<ValidationResult>;

  /**
   * Get the calculator type identifier
   */
  abstract getCalculatorType(): string;

  /**
   * Get account with metadata for calculations
   * Protected method for use by concrete calculators
   */
  protected async getAccountWithMetadata(accountId: number): Promise<Account | null> {
    try {
      return await this.accountRepository.getAccountWithMetadata(accountId);
    } catch (error) {
      throw new Error(`Failed to get account with metadata: ${error}`);
    }
  }

  /**
   * Sum transactions by type for balance calculations
   * Protected utility method for concrete calculators
   */
  protected sumTransactionsByType(
    transactions: Transaction[], 
    types: Transaction['transaction_type'][]
  ): number {
    return transactions
      .filter(t => types.includes(t.transaction_type))
      .reduce((sum, t) => sum + t.amount, 0);
  }

  /**
   * Sum transactions by category for specialized calculations
   * Protected utility method for concrete calculators
   */
  protected sumTransactionsByCategory(
    transactions: Transaction[], 
    categoryIds: (number | null)[]
  ): number {
    return transactions
      .filter(t => categoryIds.includes(t.category_id))
      .reduce((sum, t) => sum + t.amount, 0);
  }

  /**
   * Filter transactions by date range
   * Protected utility method for concrete calculators
   */
  protected filterTransactionsByDateRange(
    transactions: Transaction[],
    startDate: Date,
    endDate: Date
  ): Transaction[] {
    return transactions.filter(t => {
      const transactionDate = new Date(t.transaction_date);
      return transactionDate >= startDate && transactionDate <= endDate;
    });
  }

  /**
   * Get transactions for the current month
   * Protected utility method for concrete calculators
   */
  protected getCurrentMonthTransactions(transactions: Transaction[]): Transaction[] {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    return this.filterTransactionsByDateRange(transactions, startOfMonth, endOfMonth);
  }

  /**
   * Format currency amount for display
   * Protected utility method for all calculators
   */
  protected formatCurrency(amount: number, currency: string = 'INR'): string {
    return new Intl.NumberFormat('en-IN', { 
      style: 'currency', 
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Round amount to two decimal places for accurate calculations
   * Protected utility method for all calculators
   */
  protected roundToTwoDecimals(amount: number): number {
    return Math.round(amount * 100) / 100;
  }

  /**
   * Validate account metadata exists and is valid
   * Protected method for concrete calculators
   */
  protected validateRequiredMetadata(account: Account, requiredFields: string[]): void {
    if (!account.metadata) {
      throw new Error(`${account.type} account requires metadata`);
    }

    const missingFields = requiredFields.filter(field => 
      !(field in account.metadata!) || 
      account.metadata![field as keyof AccountMetadata] == null
    );

    if (missingFields.length > 0) {
      throw new Error(`${account.type} account missing required metadata fields: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Create validation result helper
   * Protected utility for concrete calculators
   */
  protected createValidationResult(
    isValid: boolean, 
    errors: string[] = [], 
    warnings: string[] = [],
    suggestedCorrections?: Partial<Transaction>
  ): ValidationResult {
    return {
      isValid,
      errors,
      warnings,
      suggestedCorrections
    };
  }

  /**
   * Validate transaction amount is positive
   * Protected validation utility
   */
  protected validatePositiveAmount(amount: number): { isValid: boolean; error?: string } {
    if (typeof amount !== 'number' || isNaN(amount) || amount <= 0) {
      return { isValid: false, error: 'Transaction amount must be a positive number' };
    }
    return { isValid: true };
  }

  /**
   * Validate date is in the future (for due dates)
   * Protected validation utility
   */
  protected validateFutureDate(dateString: string): { isValid: boolean; error?: string } {
    const date = new Date(dateString);
    const now = new Date();
    
    if (isNaN(date.getTime())) {
      return { isValid: false, error: 'Invalid date format' };
    }
    
    if (date < now) {
      return { isValid: false, error: 'Date must be in the future' };
    }
    
    return { isValid: true };
  }

  /**
   * Measure calculation performance
   * Protected utility for performance monitoring
   */
  protected async measurePerformance<T>(
    operation: () => Promise<T>,
    transactionCount: number
  ): Promise<{ result: T; metrics: CalculatorMetrics }> {
    const startTime = performance.now();
    const warnings: string[] = [];
    
    try {
      const result = await operation();
      const endTime = performance.now();
      const calculationTime = endTime - startTime;
      
      // Add performance warnings
      if (calculationTime > 500) {
        warnings.push('Calculation took longer than 500ms');
      }
      
      if (transactionCount > 1000 && calculationTime > 200) {
        warnings.push('Large transaction volume with slow calculation');
      }
      
      const metrics: CalculatorMetrics = {
        calculationTime: this.roundToTwoDecimals(calculationTime),
        transactionCount,
        calculatorType: this.getCalculatorType(),
        warnings
      };
      
      return { result, metrics };
    } catch (error) {
      const endTime = performance.now();
      const calculationTime = endTime - startTime;
      
      warnings.push(`Calculation failed: ${error}`);
      
      const metrics: CalculatorMetrics = {
        calculationTime: this.roundToTwoDecimals(calculationTime),
        transactionCount,
        calculatorType: this.getCalculatorType(),
        warnings
      };
      
      throw error;
    }
  }

  /**
   * Validate account type matches calculator
   * Protected validation for concrete calculators
   */
  protected validateAccountType(account: Account, expectedTypes: AccountType[]): void {
    if (!expectedTypes.includes(account.type)) {
      throw new Error(
        `${this.getCalculatorType()} calculator cannot handle ${account.type} accounts. ` +
        `Expected: ${expectedTypes.join(', ')}`
      );
    }
  }

  /**
   * Create default account balance for fallback scenarios
   * Protected utility for concrete calculators
   */
  protected createDefaultAccountBalance(
    account: Account,
    balance: number,
    calculationType: AccountBalance['calculationType']
  ): AccountBalance {
    return {
      displayBalance: balance,
      actualBalance: balance,
      metadata: {
        accountId: account.id,
        accountType: account.type,
        currency: account.currency
      },
      lastCalculated: new Date(),
      calculationType
    };
  }
}