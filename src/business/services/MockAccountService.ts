import { Account } from '@/shared/types';

export interface AccountSummary {
  totalBalance: number;
  accountCount: number;
  balancesByType: Record<Account['type'], number>;
  lastUpdated: Date;
}

export interface AccountValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Mock AccountService for development/testing without database dependencies
 * This allows UI to work while we fix the real database initialization
 */
export class MockAccountService {
  private accounts: Account[] = [];
  private nextId = 1;

  /**
   * Get all active accounts
   */
  async getAllAccounts(): Promise<Account[]> {
    console.log('🔧 MockAccountService: getAllAccounts called');
    return [...this.accounts.filter(account => account.is_active)];
  }

  /**
   * Get account by ID
   */
  async getAccountById(id: number): Promise<Account | null> {
    console.log('🔧 MockAccountService: getAccountById called', id);
    const account = this.accounts.find(acc => acc.id === id && acc.is_active);
    return account || null;
  }

  /**
   * Create a new account
   */
  async createAccount(accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'>): Promise<Account> {
    console.log('🔧 MockAccountService: createAccount called', accountData);
    
    const now = new Date().toISOString();
    const newAccount: Account = {
      ...accountData,
      id: this.nextId++,
      created_at: now,
      updated_at: now,
      sync_status: 'local'
    };

    this.accounts.push(newAccount);
    console.log('✅ MockAccountService: Account created successfully', newAccount);
    return newAccount;
  }

  /**
   * Update an existing account
   */
  async updateAccount(id: number, updates: Partial<Account>): Promise<Account> {
    console.log('🔧 MockAccountService: updateAccount called', id, updates);
    
    const accountIndex = this.accounts.findIndex(acc => acc.id === id);
    if (accountIndex === -1) {
      throw new Error(`Account with id ${id} not found`);
    }

    const updatedAccount = {
      ...this.accounts[accountIndex],
      ...updates,
      updated_at: new Date().toISOString()
    };

    this.accounts[accountIndex] = updatedAccount;
    console.log('✅ MockAccountService: Account updated successfully', updatedAccount);
    return updatedAccount;
  }

  /**
   * Delete an account
   */
  async deleteAccount(id: number, transferToId?: number): Promise<void> {
    console.log('🔧 MockAccountService: deleteAccount called', id, transferToId);
    
    const accountIndex = this.accounts.findIndex(acc => acc.id === id);
    if (accountIndex === -1) {
      throw new Error(`Account with id ${id} not found`);
    }

    const account = this.accounts[accountIndex];

    // Handle balance transfer if specified
    if (transferToId && account.balance !== 0) {
      const toAccountIndex = this.accounts.findIndex(acc => acc.id === transferToId);
      if (toAccountIndex !== -1) {
        this.accounts[toAccountIndex] = {
          ...this.accounts[toAccountIndex],
          balance: this.accounts[toAccountIndex].balance + account.balance,
          updated_at: new Date().toISOString()
        };
        console.log(`✅ MockAccountService: Transferred balance ${account.balance} to account ${transferToId}`);
      }
    }

    // Mark as inactive instead of removing
    this.accounts[accountIndex] = {
      ...account,
      is_active: false,
      updated_at: new Date().toISOString()
    };

    console.log('✅ MockAccountService: Account deleted successfully');
  }

  /**
   * Get account summary statistics
   */
  async getAccountSummary(): Promise<AccountSummary> {
    console.log('🔧 MockAccountService: getAccountSummary called');
    
    const activeAccounts = this.accounts.filter(acc => acc.is_active);
    const totalBalance = activeAccounts.reduce((sum, acc) => sum + acc.balance, 0);
    
    const balancesByType: Record<Account['type'], number> = {
      checking: 0,
      savings: 0,
      credit: 0,
      loan: 0,
      investment: 0
    };

    activeAccounts.forEach(account => {
      balancesByType[account.type] += account.balance;
    });

    return {
      totalBalance,
      accountCount: activeAccounts.length,
      balancesByType,
      lastUpdated: new Date()
    };
  }

  /**
   * Calculate account balance (mock implementation)
   */
  async calculateBalance(accountId: number): Promise<number> {
    console.log('🔧 MockAccountService: calculateBalance called', accountId);
    const account = await this.getAccountById(accountId);
    return account?.balance || 0;
  }

  /**
   * Transfer balance between accounts
   */
  async transferBalance(fromAccountId: number, toAccountId: number, amount: number): Promise<void> {
    console.log('🔧 MockAccountService: transferBalance called', fromAccountId, toAccountId, amount);
    
    if (amount <= 0) {
      throw new Error('Transfer amount must be positive');
    }

    const fromAccount = await this.getAccountById(fromAccountId);
    const toAccount = await this.getAccountById(toAccountId);

    if (!fromAccount) {
      throw new Error(`Source account ${fromAccountId} not found`);
    }

    if (!toAccount) {
      throw new Error(`Destination account ${toAccountId} not found`);
    }

    if (fromAccount.balance < amount) {
      throw new Error('Insufficient balance for transfer');
    }

    // Update balances
    await this.updateAccount(fromAccountId, {
      balance: fromAccount.balance - amount
    });

    await this.updateAccount(toAccountId, {
      balance: toAccount.balance + amount
    });

    console.log('✅ MockAccountService: Balance transferred successfully');
  }

  /**
   * Validate account data
   */
  validateAccount(account: Partial<Account>): AccountValidationResult {
    const errors: string[] = [];

    if (!account.name || account.name.trim().length === 0) {
      errors.push('Account name is required');
    }

    if (!account.type || !['checking', 'savings', 'credit', 'loan', 'investment'].includes(account.type)) {
      errors.push('Valid account type is required');
    }

    if (account.balance !== undefined && account.balance < 0 && account.type !== 'credit' && account.type !== 'loan') {
      errors.push('Balance cannot be negative for this account type');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}