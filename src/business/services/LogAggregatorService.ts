import { Logger } from '@/shared/utils/Logger';
import * as FileSystem from 'expo-file-system';

interface LogFile {
  filename: string;
  timestamp: Date;
  size: number;
  type: 'metro' | 'flipper' | 'app' | 'combined';
}

interface LogProcessingConfig {
  maxFileSize: number; // bytes
  retentionTime: number; // minutes
  batchSize: number; // lines
  maxBatches: number;
  enableRealTimeProcessing: boolean;
}

export class LogAggregatorService {
  private static instance: LogAggregatorService;
  private logger: Logger;
  private logDirectory: string;
  private config: LogProcessingConfig = {
    maxFileSize: 1024 * 1024, // 1MB
    retentionTime: 10, // 10 minutes
    batchSize: 50, // 50 lines per batch
    maxBatches: 20, // max 1000 lines
    enableRealTimeProcessing: true
  };

  private constructor() {
    this.logger = Logger.getInstance();
    this.logDirectory = `${FileSystem.documentDirectory}logs/llm_debug/`;
    this.initializeLogDirectory();
  }

  public static getInstance(): LogAggregatorService {
    if (!LogAggregatorService.instance) {
      LogAggregatorService.instance = new LogAggregatorService();
    }
    return LogAggregatorService.instance;
  }

  private async initializeLogDirectory(): Promise<void> {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.logDirectory);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.logDirectory, { intermediates: true });
        this.logger.info('Created log aggregation directory', 'log-aggregator');
      }
    } catch (error) {
      this.logger.error(
        'Failed to initialize log directory',
        'log-aggregator',
        { directory: this.logDirectory, error: (error as Error).message }
      );
    }
  }

  public configure(config: Partial<LogProcessingConfig>): void {
    this.config = { ...this.config, ...config };
    this.logger.info('Log aggregator configuration updated', 'log-aggregator', config);
  }

  // Metro bundler log processing
  public async captureMetroBundlerLogs(): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `metro-${timestamp}.log`;
    const filepath = `${this.logDirectory}${filename}`;

    try {
      // In a real React Native app, this would read from Metro's log output
      // For now, we'll simulate capturing Metro logs
      const metroLogs = this.simulateMetroLogs();
      
      await FileSystem.writeAsStringAsync(filepath, metroLogs);
      
      this.logger.info(
        'Metro bundler logs captured',
        'log-aggregator',
        { filename, size: metroLogs.length }
      );

      return filename;
    } catch (error) {
      this.logger.error(
        'Failed to capture Metro logs',
        'log-aggregator',
        { filename, error: (error as Error).message }
      );
      throw error;
    }
  }

  // Flipper integration for enhanced debugging
  public async captureFlipperLogs(): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `flipper-${timestamp}.log`;
    const filepath = `${this.logDirectory}${filename}`;

    try {
      // In a real app, this would integrate with Flipper's logging system
      const flipperLogs = this.simulateFlipperLogs();
      
      await FileSystem.writeAsStringAsync(filepath, flipperLogs);
      
      this.logger.info(
        'Flipper logs captured',
        'log-aggregator', 
        { filename, size: flipperLogs.length }
      );

      return filename;
    } catch (error) {
      this.logger.error(
        'Failed to capture Flipper logs',
        'log-aggregator',
        { filename, error: (error as Error).message }
      );
      throw error;
    }
  }

  // Combine logs from multiple sources
  public async createCombinedLogFile(logFiles: string[]): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `combined-${timestamp}.log`;
    const filepath = `${this.logDirectory}${filename}`;

    try {
      // const combinedLogs: string[] = []; // Reserved for future combined log format
      const logEntries: { timestamp: Date; source: string; content: string }[] = [];

      // Read and parse all log files
      for (const logFile of logFiles) {
        const logPath = `${this.logDirectory}${logFile}`;
        const logExists = await FileSystem.getInfoAsync(logPath);
        
        if (logExists.exists) {
          const content = await FileSystem.readAsStringAsync(logPath);
          const lines = content.split('\n');
          
          lines.forEach(line => {
            if (line.trim()) {
              try {
                const parsed = JSON.parse(line);
                logEntries.push({
                  timestamp: new Date(parsed.timestamp),
                  source: logFile,
                  content: line
                });
              } catch {
                // Handle non-JSON log lines
                logEntries.push({
                  timestamp: new Date(),
                  source: logFile,
                  content: line
                });
              }
            }
          });
        }
      }

      // Sort by timestamp
      logEntries.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

      // Create combined log content
      const header = `# Combined Log File\n# Generated: ${new Date().toISOString()}\n# Sources: ${logFiles.join(', ')}\n\n`;
      const logContent = logEntries.map(entry => 
        `[${entry.timestamp.toISOString()}] [${entry.source}] ${entry.content}`
      ).join('\n');

      const combinedContent = header + logContent;
      await FileSystem.writeAsStringAsync(filepath, combinedContent);

      this.logger.info(
        'Combined log file created',
        'log-aggregator',
        { filename, sources: logFiles.length, entries: logEntries.length }
      );

      return filename;
    } catch (error) {
      this.logger.error(
        'Failed to create combined log file',
        'log-aggregator',
        { filename, error: (error as Error).message }
      );
      throw error;
    }
  }

  // Progressive context gathering for Claude Code analysis
  public async processLogsProgressively(filename: string): Promise<{
    batches: { batchNumber: number; content: string; lineCount: number }[];
    totalLines: number;
    processingComplete: boolean;
  }> {
    const filepath = `${this.logDirectory}${filename}`;
    
    try {
      const content = await FileSystem.readAsStringAsync(filepath);
      const lines = content.split('\n').filter(line => line.trim());
      
      const batches: { batchNumber: number; content: string; lineCount: number }[] = [];
      const maxLines = this.config.maxBatches * this.config.batchSize;
      const actualLines = Math.min(lines.length, maxLines);

      for (let i = 0; i < actualLines; i += this.config.batchSize) {
        const batchNumber = Math.floor(i / this.config.batchSize) + 1;
        const batchLines = lines.slice(i, i + this.config.batchSize);
        
        batches.push({
          batchNumber,
          content: batchLines.join('\n'),
          lineCount: batchLines.length
        });
      }

      const processingComplete = actualLines >= lines.length;

      this.logger.info(
        'Logs processed progressively',
        'log-aggregator',
        { 
          filename, 
          totalLines: lines.length, 
          processedLines: actualLines,
          batchCount: batches.length,
          processingComplete 
        }
      );

      return {
        batches,
        totalLines: lines.length,
        processingComplete
      };
    } catch (error) {
      this.logger.error(
        'Failed to process logs progressively',
        'log-aggregator',
        { filename, error: (error as Error).message }
      );
      throw error;
    }
  }

  // Real-time log streaming
  public async streamLogs(callback: (logLine: string) => void): Promise<void> {
    if (!this.config.enableRealTimeProcessing) {
      return;
    }

    // In a real implementation, this would set up file watchers
    // For now, we'll simulate streaming by processing recent logs
    const recentLogs = this.logger.getLogs(undefined, undefined, 50);
    
    recentLogs.forEach(log => {
      const logLine = JSON.stringify({
        timestamp: log.timestamp.toISOString(),
        level: log.level,
        category: log.category,
        message: log.message,
        metadata: log.metadata
      });
      
      callback(logLine);
    });

    this.logger.debug('Log streaming session completed', 'log-aggregator');
  }

  // Clean up old log files based on retention policy
  public async cleanupOldLogs(): Promise<void> {
    try {
      const files = await this.getLogFiles();
      const cutoffTime = new Date(Date.now() - this.config.retentionTime * 60 * 1000);
      
      let deletedCount = 0;
      
      for (const file of files) {
        if (file.timestamp < cutoffTime) {
          await FileSystem.deleteAsync(`${this.logDirectory}${file.filename}`);
          deletedCount++;
        }
      }

      if (deletedCount > 0) {
        this.logger.info(
          'Old log files cleaned up',
          'log-aggregator',
          { deletedCount, retentionMinutes: this.config.retentionTime }
        );
      }
    } catch (error) {
      this.logger.error(
        'Failed to cleanup old logs',
        'log-aggregator',
        { error: (error as Error).message }
      );
    }
  }

  public async getLogFiles(): Promise<LogFile[]> {
    try {
      const files = await FileSystem.readDirectoryAsync(this.logDirectory);
      const logFiles: LogFile[] = [];

      for (const filename of files) {
        const filepath = `${this.logDirectory}${filename}`;
        const fileInfo = await FileSystem.getInfoAsync(filepath);
        
        if (fileInfo.exists && !fileInfo.isDirectory) {
          const type: LogFile['type'] = 
            filename.startsWith('metro-') ? 'metro' :
            filename.startsWith('flipper-') ? 'flipper' :
            filename.startsWith('combined-') ? 'combined' : 'app';

          logFiles.push({
            filename,
            timestamp: new Date(fileInfo.modificationTime! * 1000),
            size: fileInfo.size!,
            type
          });
        }
      }

      return logFiles.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    } catch (error) {
      this.logger.error(
        'Failed to get log files',
        'log-aggregator',
        { error: (error as Error).message }
      );
      return [];
    }
  }

  // Error analysis and automated bug detection
  public async analyzeLogsForErrors(filename: string): Promise<{
    errorPatterns: { pattern: string; count: number; severity: 'low' | 'medium' | 'high' }[];
    performanceIssues: { issue: string; count: number }[];
    recommendations: string[];
  }> {
    try {
      const { batches } = await this.processLogsProgressively(filename);
      const allContent = batches.map(b => b.content).join('\n');
      const lines = allContent.split('\n');

      const errorPatterns: Record<string, { count: number; severity: 'low' | 'medium' | 'high' }> = {};
      const performanceIssues: Record<string, number> = {};
      const recommendations: string[] = [];

      // Analyze log lines for patterns
      lines.forEach(line => {
        // Error patterns
        if (line.includes('ERROR') || line.includes('FATAL')) {
          const pattern = 'Critical Error';
          errorPatterns[pattern] = { 
            count: (errorPatterns[pattern]?.count || 0) + 1, 
            severity: 'high' 
          };
        }
        
        if (line.includes('WARN')) {
          const pattern = 'Warning';
          errorPatterns[pattern] = { 
            count: (errorPatterns[pattern]?.count || 0) + 1, 
            severity: 'medium' 
          };
        }

        // Performance issues
        if (line.includes('Slow query') || line.includes('slow')) {
          performanceIssues['Slow Operations'] = (performanceIssues['Slow Operations'] || 0) + 1;
        }

        if (line.includes('timeout') || line.includes('TIMEOUT')) {
          performanceIssues['Timeouts'] = (performanceIssues['Timeouts'] || 0) + 1;
        }

        if (line.includes('memory') && line.includes('high')) {
          performanceIssues['Memory Issues'] = (performanceIssues['Memory Issues'] || 0) + 1;
        }
      });

      // Generate recommendations
      if (errorPatterns['Critical Error']?.count > 5) {
        recommendations.push('High number of critical errors detected - review error handling');
      }

      if (performanceIssues['Slow Operations'] > 10) {
        recommendations.push('Multiple slow operations - consider database optimization');
      }

      if (performanceIssues['Timeouts'] > 3) {
        recommendations.push('Network timeouts detected - check connectivity and retry logic');
      }

      const result = {
        errorPatterns: Object.entries(errorPatterns).map(([pattern, data]) => ({
          pattern,
          count: data.count,
          severity: data.severity
        })),
        performanceIssues: Object.entries(performanceIssues).map(([issue, count]) => ({
          issue,
          count
        })),
        recommendations
      };

      this.logger.info(
        'Log analysis completed',
        'log-aggregator',
        { 
          filename, 
          errorPatternCount: result.errorPatterns.length,
          performanceIssueCount: result.performanceIssues.length,
          recommendationCount: result.recommendations.length 
        }
      );

      return result;
    } catch (error) {
      this.logger.error(
        'Failed to analyze logs for errors',
        'log-aggregator',
        { filename, error: (error as Error).message }
      );
      throw error;
    }
  }

  // Simulate Metro bundler logs for testing
  private simulateMetroLogs(): string {
    const logs = [
      { timestamp: new Date().toISOString(), level: 'info', message: 'Metro bundler started' },
      { timestamp: new Date().toISOString(), level: 'info', message: 'Bundle build started' },
      { timestamp: new Date().toISOString(), level: 'warn', message: 'Slow transformation on module' },
      { timestamp: new Date().toISOString(), level: 'info', message: 'Bundle build completed' },
      { timestamp: new Date().toISOString(), level: 'error', message: 'Failed to resolve module' }
    ];

    return logs.map(log => JSON.stringify(log)).join('\n');
  }

  // Simulate Flipper logs for testing  
  private simulateFlipperLogs(): string {
    const logs = [
      { timestamp: new Date().toISOString(), source: 'flipper', message: 'Connected to device' },
      { timestamp: new Date().toISOString(), source: 'flipper', message: 'Database plugin loaded' },
      { timestamp: new Date().toISOString(), source: 'flipper', message: 'Network plugin active' },
      { timestamp: new Date().toISOString(), source: 'flipper', message: 'Performance monitor started' }
    ];

    return logs.map(log => JSON.stringify(log)).join('\n');
  }
}