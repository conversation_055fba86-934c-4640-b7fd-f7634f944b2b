import { Account, AccountMetadata, AccountType, ValidationResult, Transaction } from '@/shared/types';
import { AccountBalance, CreditCardSummary, LoanSummary } from '@/shared/types/calculators';
import { AccountCalculatorFactory } from './calculators/AccountCalculatorFactory';
// Dynamic imports for calculator types to avoid circular dependencies
import type { CreditCardCalculator } from './calculators/CreditCardCalculator';
import type { LoanCalculator } from './calculators/LoanCalculator';
import { AccountRepository } from '@/data/repositories/AccountRepository';
import { TransactionRepository } from '@/data/repositories/TransactionRepository';
import { DatabaseService } from '@/data/database/DatabaseService';
import { AccountValidationUtils } from '@/shared/utils/accountValidation';

export interface AccountSummary {
  totalBalance: number;
  accountCount: number;
  balancesByType: Record<Account['type'], number>;
  lastUpdated: Date;
}

export interface AccountValidationResult {
  isValid: boolean;
  errors: string[];
}

export class AccountService {
  private accountRepository: AccountRepository;
  private transactionRepository: TransactionRepository;
  private databaseService: DatabaseService;
  private readonly FREE_TIER_ACCOUNT_LIMIT = 5;

  constructor() {
    this.accountRepository = new AccountRepository();
    this.transactionRepository = new TransactionRepository();
    this.databaseService = DatabaseService.getInstance();
  }

  /**
   * Check if database is ready for operations
   */
  private isDatabaseReady(): boolean {
    return this.databaseService.isInitialized();
  }

  /**
   * Wait for database to be initialized
   */
  private async waitForDatabase(): Promise<void> {
    if (!this.isDatabaseReady()) {
      await this.databaseService.waitForInitialization();
    }
  }

  /**
   * Get all active accounts
   */
  async getAllAccounts(): Promise<Account[]> {
    try {
      // Check if database is ready first
      if (!this.isDatabaseReady()) {
        console.warn('⚠️ Database not ready, returning empty account list');
        return [];
      }
      
      return await this.accountRepository.findAll();
    } catch (error) {
      console.error('Failed to fetch accounts:', error);
      // Return empty array instead of throwing to prevent infinite loops
      return [];
    }
  }

  /**
   * Get account by ID
   */
  async getAccountById(id: number): Promise<Account | null> {
    try {
      return await this.accountRepository.findById(id);
    } catch (error) {
      throw new Error(`Failed to fetch account: ${error}`);
    }
  }

  /**
   * Create a new account with validation and optional metadata
   */
  async createAccount(accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'>): Promise<Account> {
    // Ensure database is ready
    await this.waitForDatabase();

    // Validate account data
    const validation = await this.validateAccountData(accountData);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Validate metadata if provided
    if (accountData.metadata) {
      const metadataValidation = this.validateAccountMetadata(accountData.type, accountData.metadata);
      if (!metadataValidation.isValid) {
        throw new Error(`Metadata validation failed: ${metadataValidation.errors.join(', ')}`);
      }
    }

    // Check free tier limit - use direct repository call to avoid error masking
    const existingAccounts = await this.accountRepository.findAll();
    if (existingAccounts.length >= this.FREE_TIER_ACCOUNT_LIMIT) {
      throw new Error(`Free tier limit reached. Maximum ${this.FREE_TIER_ACCOUNT_LIMIT} accounts allowed.`);
    }

    // Check name uniqueness
    const existingAccount = existingAccounts.find(
      account => account.name.toLowerCase() === accountData.name.toLowerCase()
    );
    if (existingAccount) {
      throw new Error('Account name already exists');
    }

    try {
      const { metadata, ...accountDataWithoutMetadata } = accountData;
      
      const newAccount = await this.accountRepository.create({
        ...accountDataWithoutMetadata,
        currency: accountData.currency || 'INR',
        is_active: true,
        sync_status: 'local'
      });

      // Create metadata if provided
      if (metadata) {
        await this.accountRepository.createAccountMetadata(newAccount.id, metadata);
        return await this.accountRepository.getAccountWithMetadata(newAccount.id) || newAccount;
      }

      return newAccount;
    } catch (error) {
      throw new Error(`Failed to create account: ${error}`);
    }
  }

  /**
   * Create account with metadata - enhanced method
   */
  async createAccountWithMetadata(
    accountData: Omit<Account, 'id' | 'created_at' | 'updated_at' | 'metadata'>, 
    metadata?: AccountMetadata
  ): Promise<Account> {
    if (metadata) {
      return await this.createAccount({
        ...accountData,
        metadata
      });
    }
    return await this.createAccount(accountData);
  }

  /**
   * Update an existing account
   */
  async updateAccount(id: number, updates: Partial<Account>): Promise<Account> {
    const existingAccount = await this.getAccountById(id);
    if (!existingAccount) {
      throw new Error('Account not found');
    }

    // Validate updates if name is being changed
    if (updates.name) {
      const validation = await this.validateAccountData({ ...existingAccount, ...updates });
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // Check name uniqueness (excluding current account) - use direct repository call
      const allAccounts = await this.accountRepository.findAll();
      const nameExists = allAccounts.some(
        account => account.id !== id && account.name.toLowerCase() === updates.name!.toLowerCase()
      );
      if (nameExists) {
        throw new Error('Account name already exists');
      }
    }

    try {
      return await this.accountRepository.update(id, {
        ...updates,
        sync_status: 'local' // Mark as needing sync
      });
    } catch (error) {
      throw new Error(`Failed to update account: ${error}`);
    }
  }

  /**
   * Delete an account with transaction impact analysis
   */
  async deleteAccount(id: number, transferToAccountId?: number): Promise<{ success: boolean; transactionCount: number }> {
    const account = await this.getAccountById(id);
    if (!account) {
      throw new Error('Account not found');
    }

    // Get transaction count for impact analysis
    const transactions = await this.transactionRepository.findByAccount(id);
    const transactionCount = transactions.length;

    // If there are transactions and a transfer account is specified
    if (transactionCount > 0 && transferToAccountId) {
      const transferAccount = await this.getAccountById(transferToAccountId);
      if (!transferAccount) {
        throw new Error('Transfer account not found');
      }

      // Transfer all transactions to the new account
      for (const transaction of transactions) {
        await this.transactionRepository.update(transaction.id, {
          account_id: transferToAccountId,
          sync_status: 'local'
        });
      }

      // Recalculate balances
      await this.recalculateAccountBalance(transferToAccountId);
    }

    try {
      const success = await this.accountRepository.delete(id);
      return { success, transactionCount };
    } catch (error) {
      throw new Error(`Failed to delete account: ${error}`);
    }
  }

  /**
   * Calculate account balance from transaction history
   */
  async calculateAccountBalance(accountId: number): Promise<number> {
    try {
      const transactions = await this.transactionRepository.findByAccount(accountId);
      
      let balance = 0;
      for (const transaction of transactions) {
        if (transaction.transaction_type === 'income') {
          balance += transaction.amount;
        } else if (transaction.transaction_type === 'expense') {
          balance -= transaction.amount;
        }
        // For transfers, the amount handling depends on the specific transaction
      }

      return balance;
    } catch (error) {
      throw new Error(`Failed to calculate balance: ${error}`);
    }
  }

  /**
   * Calculate account balance using account-specific calculators
   * Enhanced method that uses appropriate calculator based on account type
   */
  async calculateAccountBalanceEnhanced(accountId: number): Promise<AccountBalance> {
    try {
      const account = await this.getAccountById(accountId);
      if (!account) {
        throw new Error('Account not found');
      }

      const transactions = await this.transactionRepository.findByAccount(accountId);
      const calculator = AccountCalculatorFactory.getCalculator(account.type);
      
      return await calculator.calculateBalance(accountId, transactions);
    } catch (error) {
      throw new Error(`Failed to calculate enhanced account balance: ${error}`);
    }
  }

  /**
   * Get credit card summary using CreditCardCalculator
   */
  async getCreditCardSummary(accountId: number): Promise<CreditCardSummary> {
    try {
      const account = await this.getAccountById(accountId);
      if (!account || account.type !== 'credit') {
        throw new Error('Account not found or not a credit card account');
      }

      const transactions = await this.transactionRepository.findByAccount(accountId);
      const calculator = AccountCalculatorFactory.getCalculator('credit') as CreditCardCalculator;
      
      const balance = await calculator.calculateBalance(accountId, transactions);
      
      return {
        availableCredit: balance.displayBalance,
        outstandingBalance: balance.actualBalance,
        creditUtilization: balance.metadata.creditUtilization || 0,
        minimumPaymentDue: balance.metadata.minimumPaymentDue || 0,
        creditLimit: balance.metadata.creditLimit || 0,
        paymentDueDate: balance.metadata.paymentDueDate
      };
    } catch (error) {
      throw new Error(`Failed to get credit card summary: ${error}`);
    }
  }

  /**
   * Get loan summary using LoanCalculator
   */
  async getLoanSummary(accountId: number): Promise<LoanSummary> {
    try {
      const account = await this.getAccountById(accountId);
      if (!account || account.type !== 'loan') {
        throw new Error('Account not found or not a loan account');
      }

      const transactions = await this.transactionRepository.findByAccount(accountId);
      const calculator = AccountCalculatorFactory.getCalculator('loan') as LoanCalculator;
      
      const balance = await calculator.calculateBalance(accountId, transactions);
      
      return {
        remainingPrincipal: balance.displayBalance,
        nextEMIDate: balance.metadata.nextEMIDate || '',
        emiAmount: balance.metadata.emiAmount || 0,
        remainingTenure: balance.metadata.remainingTenure || 0,
        originalPrincipal: balance.metadata.originalPrincipal || 0,
        totalInterestPaid: balance.metadata.totalInterestPaid || 0
      };
    } catch (error) {
      throw new Error(`Failed to get loan summary: ${error}`);
    }
  }

  /**
   * Validate transaction for account using account-specific calculator
   */
  async validateTransactionForAccount(transaction: Transaction, accountId: number): Promise<ValidationResult> {
    try {
      const account = await this.getAccountById(accountId);
      if (!account) {
        throw new Error('Account not found');
      }

      const calculator = AccountCalculatorFactory.getCalculator(account.type);
      return await calculator.validateTransaction(transaction, account);
    } catch (error) {
      throw new Error(`Failed to validate transaction: ${error}`);
    }
  }

  /**
   * Get calculator performance metrics for account type
   */
  getCalculatorPerformanceExpectations(accountType: AccountType): {
    maxCalculationTime: number;
    maxTransactionVolume: number;
    warningThreshold: number;
  } {
    return AccountCalculatorFactory.getPerformanceExpectations(accountType);
  }

  /**
   * Check if account type requires calculator metadata
   */
  requiresCalculatorMetadata(accountType: AccountType): boolean {
    const requirements = AccountCalculatorFactory.validateCalculatorRequirements(accountType);
    return requirements.requiresMetadata;
  }

  /**
   * Get required metadata fields for calculator
   */
  getRequiredCalculatorMetadata(accountType: AccountType): string[] {
    const requirements = AccountCalculatorFactory.validateCalculatorRequirements(accountType);
    return requirements.requiredMetadataFields;
  }

  /**
   * Validate calculator requirements for account
   */
  validateCalculatorRequirements(accountType: AccountType): {
    isValid: boolean;
    requiresMetadata: boolean;
    requiredMetadataFields: string[];
    warnings: string[];
  } {
    return AccountCalculatorFactory.validateCalculatorRequirements(accountType);
  }

  /**
   * Recalculate and update account balance from transactions
   */
  async recalculateAccountBalance(accountId: number): Promise<Account> {
    const calculatedBalance = await this.calculateAccountBalance(accountId);
    return await this.accountRepository.updateBalance(accountId, calculatedBalance, 'set');
  }

  /**
   * Get account summary for dashboard
   */
  async getAccountSummary(): Promise<AccountSummary> {
    try {
      // Use direct repository calls to avoid error masking
      const accounts = await this.accountRepository.findAll();
      const totalBalance = await this.accountRepository.getTotalBalance();
      const balancesByType = await this.accountRepository.getBalanceByType();

      return {
        totalBalance,
        accountCount: accounts.length,
        balancesByType,
        lastUpdated: new Date()
      };
    } catch (error) {
      throw new Error(`Failed to get account summary: ${error}`);
    }
  }

  /**
   * Validate account data
   */
  private async validateAccountData(accountData: Partial<Account>): Promise<AccountValidationResult> {
    const errors: string[] = [];

    // Name validation
    if (!accountData.name || accountData.name.trim().length === 0) {
      errors.push('Account name is required');
    } else if (accountData.name.trim().length < 2) {
      errors.push('Account name must be at least 2 characters long');
    } else if (accountData.name.trim().length > 50) {
      errors.push('Account name must be less than 50 characters');
    }

    // Type validation
    const validTypes: Account['type'][] = ['checking', 'savings', 'credit', 'loan', 'investment'];
    if (!accountData.type || !validTypes.includes(accountData.type)) {
      errors.push('Valid account type is required');
    }

    // Balance validation
    if (accountData.balance !== undefined) {
      if (typeof accountData.balance !== 'number') {
        errors.push('Balance must be a number');
      } else if (accountData.balance < -*********.99 || accountData.balance > *********.99) {
        errors.push('Balance must be between -999,999,999.99 and 999,999,999.99');
      }
    }

    // Currency validation
    if (accountData.currency && accountData.currency.length !== 3) {
      errors.push('Currency must be a 3-letter code (e.g., INR, USD)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get account types configuration
   */
  getAccountTypesConfig() {
    return {
      checking: { icon: 'bank', color: '#4A90E2', label: 'Checking Account' },
      savings: { icon: 'wallet', color: '#7ED321', label: 'Savings Account' },
      credit: { icon: 'credit-card', color: '#F5A623', label: 'Credit Card' },
      loan: { icon: 'trending-down', color: '#D0021B', label: 'Loan Account' },
      investment: { icon: 'trending-up', color: '#9013FE', label: 'Investment Account' }
    };
  }

  /**
   * Check if free tier limit is reached
   */
  async isFreeTierLimitReached(): Promise<boolean> {
    // Use direct repository call to avoid error masking
    const accounts = await this.accountRepository.findAll();
    return accounts.length >= this.FREE_TIER_ACCOUNT_LIMIT;
  }

  /**
   * Get remaining account slots for free tier
   */
  async getRemainingAccountSlots(): Promise<number> {
    // Use direct repository call to avoid error masking
    const accounts = await this.accountRepository.findAll();
    return Math.max(0, this.FREE_TIER_ACCOUNT_LIMIT - accounts.length);
  }

  // Account Metadata Management Methods

  /**
   * Update account metadata
   */
  async updateAccountMetadata(accountId: number, metadata: Partial<AccountMetadata>): Promise<void> {
    const account = await this.getAccountById(accountId);
    if (!account) {
      throw new Error('Account not found');
    }

    // Validate metadata
    const metadataValidation = this.validateAccountMetadata(account.type, metadata);
    if (!metadataValidation.isValid) {
      throw new Error(`Metadata validation failed: ${metadataValidation.errors.join(', ')}`);
    }

    try {
      await this.accountRepository.updateAccountMetadata(accountId, metadata);
    } catch (error) {
      throw new Error(`Failed to update account metadata: ${error}`);
    }
  }

  /**
   * Get account with full details including metadata
   */
  async getAccountWithFullDetails(accountId: number): Promise<Account | null> {
    try {
      return await this.accountRepository.getAccountWithMetadata(accountId);
    } catch (error) {
      throw new Error(`Failed to get account with metadata: ${error}`);
    }
  }

  /**
   * Validate account metadata for specific account type
   */
  validateAccountMetadata(accountType: AccountType, metadata: Partial<AccountMetadata>): ValidationResult {
    return AccountValidationUtils.validateAccountMetadata(accountType, metadata as AccountMetadata);
  }

  /**
   * Create account type detection utilities
   */
  detectAccountType(accountName: string, metadata?: AccountMetadata): AccountType {
    // Basic heuristics for account type detection
    const name = accountName.toLowerCase();
    
    if (name.includes('credit') || name.includes('card')) {
      return 'credit';
    }
    if (name.includes('loan') || name.includes('emi')) {
      return 'loan';
    }
    if (name.includes('investment') || name.includes('mutual') || name.includes('stock')) {
      return 'investment';
    }
    if (name.includes('savings')) {
      return 'savings';
    }
    
    // Check metadata for more specific detection
    if (metadata?.creditLimit || metadata?.outstandingBalance) {
      return 'credit';
    }
    if (metadata?.emiAmount || metadata?.principalAmount) {
      return 'loan';
    }

    // Default to checking account
    return 'checking';
  }

  /**
   * Get accounts by specific type with metadata
   */
  async getAccountsByType(type: AccountType): Promise<Account[]> {
    try {
      return await this.accountRepository.getAccountsByType(type);
    } catch (error) {
      throw new Error(`Failed to get ${type} accounts: ${error}`);
    }
  }

  /**
   * Get credit card accounts
   */
  async getCreditCardAccounts(): Promise<Account[]> {
    return await this.getAccountsByType('credit');
  }

  /**
   * Get loan accounts
   */
  async getLoanAccounts(): Promise<Account[]> {
    return await this.getAccountsByType('loan');
  }

  /**
   * Check if account type requires metadata
   */
  requiresMetadata(accountType: AccountType): boolean {
    return AccountValidationUtils.requiresMetadata(accountType);
  }

  /**
   * Check if account type is supported
   */
  isAccountTypeSupported(accountType: AccountType): boolean {
    return AccountValidationUtils.isAccountTypeSupported(accountType);
  }

  /**
   * Enhanced delete method that handles metadata
   */
  async deleteAccountWithMetadata(id: number, transferToAccountId?: number): Promise<{ success: boolean; transactionCount: number }> {
    const account = await this.getAccountById(id);
    if (!account) {
      throw new Error('Account not found');
    }

    // Get transaction count for impact analysis
    const transactions = await this.transactionRepository.findByAccount(id);
    const transactionCount = transactions.length;

    // If there are transactions and a transfer account is specified
    if (transactionCount > 0 && transferToAccountId) {
      const transferAccount = await this.getAccountById(transferToAccountId);
      if (!transferAccount) {
        throw new Error('Transfer account not found');
      }

      // Transfer all transactions to the new account
      for (const transaction of transactions) {
        await this.transactionRepository.update(transaction.id, {
          account_id: transferToAccountId,
          sync_status: 'local'
        });
      }

      // Recalculate balances
      await this.recalculateAccountBalance(transferToAccountId);
    }

    try {
      // This will cascade delete metadata due to foreign key constraint
      const success = await this.accountRepository.deleteWithMetadata(id);
      return { success, transactionCount };
    } catch (error) {
      throw new Error(`Failed to delete account with metadata: ${error}`);
    }
  }
}