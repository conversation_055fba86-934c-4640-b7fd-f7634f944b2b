/**
 * Feedback Service
 * Story 1.9: Anonymous Feedback and Feature Requests
 * 
 * Core business service for feedback operations with offline-first approach
 * Handles local SQLite storage, validation, and sync queue management
 */

import { DatabaseService } from '../../data/database/DatabaseService';
import { DeviceFingerprintService } from '../../shared/utils/deviceFingerprint';
import { 
  FeedbackSubmission, 
  FeatureVote, 
  EarlyAccessToken,
  FeedbackFormData,
  VoteSubmissionData,
  SyncOperation,
  CreateFeedbackSubmission,
  CreateFeatureVote,
  FeedbackValidationError
} from '../../shared/types/feedback';

export class FeedbackService {
  private static instance: FeedbackService;
  private db: DatabaseService;
  private deviceService: DeviceFingerprintService;

  private constructor() {
    this.db = DatabaseService.getInstance();
    this.deviceService = DeviceFingerprintService.getInstance();
  }

  public static getInstance(): FeedbackService {
    if (!FeedbackService.instance) {
      FeedbackService.instance = new FeedbackService();
    }
    return FeedbackService.instance;
  }

  /**
   * Submit new feedback - stores locally and queues for sync
   */
  public async submitFeedback(formData: FeedbackFormData): Promise<FeedbackSubmission> {
    // Validate input
    const validationErrors = this.validateFeedbackForm(formData);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.map(e => e.message).join(', ')}`);
    }

    // Get device ID for anonymous tracking
    const deviceId = await this.deviceService.getDeviceId();

    // Encrypt contact info if provided
    const encryptedContactInfo = formData.contactInfo ? 
      await this.encryptContactInfo(formData.contactInfo) : undefined;

    // Create feedback submission record
    const feedbackData: CreateFeedbackSubmission = {
      category: formData.category,
      title: formData.title.trim(),
      description: formData.description.trim(),
      priorityLevel: formData.priorityLevel,
      deviceId,
      contactInfo: encryptedContactInfo,
      rewardEligible: formData.rewardEligible,
      screenshotPath: formData.screenshotPath
    };

    // Insert into local database
    const [result] = await this.db.executeSql(`
      INSERT INTO feedback_submissions (
        category, title, description, priority_level, device_id,
        contact_info, reward_eligible, screenshot_path, submitted_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `, [
      feedbackData.category,
      feedbackData.title,
      feedbackData.description,
      feedbackData.priorityLevel,
      feedbackData.deviceId,
      feedbackData.contactInfo,
      feedbackData.rewardEligible ? 1 : 0,
      feedbackData.screenshotPath
    ]);

    const feedbackId = result.insertId;
    if (!feedbackId) {
      throw new Error('Failed to create feedback submission - no ID returned');
    }

    // Add to sync queue
    await this.addToSyncQueue('INSERT', 'feedback_submissions', feedbackId, feedbackData);

    // Fetch the created record
    const feedback = await this.getFeedbackById(feedbackId);
    if (!feedback) {
      throw new Error('Failed to retrieve created feedback');
    }

    return feedback;
  }

  /**
   * Get feedback by ID
   */
  public async getFeedbackById(id: number): Promise<FeedbackSubmission | null> {
    const [result] = await this.db.executeSql(
      'SELECT * FROM feedback_submissions WHERE id = ?',
      [id]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return this.mapRowToFeedback(result.rows.item(0));
  }

  /**
   * Get all feedback for current device
   */
  public async getUserFeedback(): Promise<FeedbackSubmission[]> {
    const deviceId = await this.deviceService.getDeviceId();
    
    const [result] = await this.db.executeSql(`
      SELECT * FROM feedback_submissions 
      WHERE device_id = ? 
      ORDER BY submitted_at DESC
    `, [deviceId]);

    const feedback: FeedbackSubmission[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      feedback.push(this.mapRowToFeedback(result.rows.item(i)));
    }

    return feedback;
  }

  /**
   * Submit vote on feature request
   */
  public async submitVote(voteData: VoteSubmissionData): Promise<FeatureVote> {
    const deviceId = await this.deviceService.getDeviceId();

    // Check if user already voted on this feature
    const existingVote = await this.getExistingVote(voteData.featureRequestId, deviceId);
    
    if (existingVote) {
      // Update existing vote
      return await this.updateVote(existingVote.id, voteData.voteValue);
    } else {
      // Create new vote
      return await this.createNewVote(voteData, deviceId);
    }
  }

  /**
   * Get user's vote on a specific feature request
   */
  public async getUserVote(featureRequestId: string): Promise<FeatureVote | null> {
    const deviceId = await this.deviceService.getDeviceId();
    return await this.getExistingVote(featureRequestId, deviceId);
  }

  /**
   * Get all user votes
   */
  public async getUserVotes(): Promise<FeatureVote[]> {
    const deviceId = await this.deviceService.getDeviceId();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM feature_votes WHERE device_id = ? ORDER BY voted_at DESC',
      [deviceId]
    );

    const votes: FeatureVote[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      votes.push(this.mapRowToVote(result.rows.item(i)));
    }

    return votes;
  }

  /**
   * Get user's active early access tokens
   */
  public async getEarlyAccessTokens(): Promise<EarlyAccessToken[]> {
    const deviceId = await this.deviceService.getDeviceId();
    
    const [result] = await this.db.executeSql(`
      SELECT * FROM early_access_tokens 
      WHERE device_id = ? AND is_active = 1 AND expires_at > datetime('now')
      ORDER BY created_at DESC
    `, [deviceId]);

    const tokens: EarlyAccessToken[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      tokens.push(this.mapRowToToken(result.rows.item(i)));
    }

    return tokens;
  }

  /**
   * Check if user has early access for a specific feature
   */
  public async hasEarlyAccess(featureType: 'contributor' | 'community_wave'): Promise<boolean> {
    const tokens = await this.getEarlyAccessTokens();
    return tokens.some(token => token.tokenType === featureType);
  }

  /**
   * Get pending sync operations count
   */
  public async getPendingSyncCount(): Promise<number> {
    const [result] = await this.db.executeSql(
      'SELECT COUNT(*) as count FROM feedback_sync_queue'
    );
    
    return result.rows.item(0).count;
  }

  /**
   * Get failed sync operations for retry
   */
  public async getFailedSyncOperations(): Promise<SyncOperation[]> {
    const [result] = await this.db.executeSql(`
      SELECT * FROM feedback_sync_queue 
      WHERE retry_count < 3
      ORDER BY created_at ASC
      LIMIT 10
    `);

    const operations: SyncOperation[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      operations.push({
        id: row.id,
        operationType: row.operation_type,
        tableName: row.table_name,
        localId: row.local_id,
        payload: JSON.parse(row.payload),
        retryCount: row.retry_count,
        createdAt: new Date(row.created_at),
        lastAttemptAt: row.last_attempt_at ? new Date(row.last_attempt_at) : undefined,
        errorMessage: row.error_message
      });
    }

    return operations;
  }

  // Private helper methods

  private validateFeedbackForm(formData: FeedbackFormData): FeedbackValidationError[] {
    const errors: FeedbackValidationError[] = [];

    if (!formData.title || formData.title.trim().length === 0) {
      errors.push({
        field: 'title',
        message: 'Title is required',
        code: 'TITLE_REQUIRED'
      });
    }

    if (formData.title && formData.title.length > 100) {
      errors.push({
        field: 'title',
        message: 'Title must be less than 100 characters',
        code: 'TITLE_TOO_LONG'
      });
    }

    if (!formData.description || formData.description.trim().length === 0) {
      errors.push({
        field: 'description',
        message: 'Description is required',
        code: 'DESCRIPTION_REQUIRED'
      });
    }

    if (formData.description && formData.description.length > 2000) {
      errors.push({
        field: 'description',
        message: 'Description must be less than 2000 characters',
        code: 'DESCRIPTION_TOO_LONG'
      });
    }

    if (formData.rewardEligible && (!formData.contactInfo || formData.contactInfo.trim().length === 0)) {
      errors.push({
        field: 'contactInfo',
        message: 'Contact info required for reward eligibility',
        code: 'CONTACT_INFO_REQUIRED_FOR_REWARDS'
      });
    }

    if (formData.contactInfo && !this.isValidEmail(formData.contactInfo)) {
      errors.push({
        field: 'contactInfo',
        message: 'Valid email address required',
        code: 'INVALID_EMAIL'
      });
    }

    return errors;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private async encryptContactInfo(contactInfo: string): Promise<string> {
    try {
      // Use simple base64 encoding using React Native compatible methods
      // Convert string to base64 directly without TextEncoder
      const base64String = btoa(unescape(encodeURIComponent(contactInfo)));
      return base64String;
    } catch (error) {
      console.error('Failed to encrypt contact info:', error);
      // Ultimate fallback - return the original string (for development only)
      return contactInfo;
    }
  }

  private async decryptContactInfo(encryptedInfo: string): Promise<string> {
    try {
      // Decode from base64 using React Native compatible methods
      const decodedString = decodeURIComponent(escape(atob(encryptedInfo)));
      return decodedString;
    } catch (error) {
      console.error('Failed to decrypt contact info:', error);
      return '[Encrypted]';
    }
  }

  private async addToSyncQueue(
    operationType: 'INSERT' | 'UPDATE' | 'DELETE',
    tableName: string,
    localId: number,
    payload: any
  ): Promise<void> {
    await this.db.executeSql(`
      INSERT INTO feedback_sync_queue (
        operation_type, table_name, local_id, payload
      ) VALUES (?, ?, ?, ?)
    `, [operationType, tableName, localId, JSON.stringify(payload)]);
  }

  private async getExistingVote(featureRequestId: string, deviceId: string): Promise<FeatureVote | null> {
    const [result] = await this.db.executeSql(
      'SELECT * FROM feature_votes WHERE feature_request_id = ? AND device_id = ?',
      [featureRequestId, deviceId]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return this.mapRowToVote(result.rows.item(0));
  }

  private async updateVote(voteId: number, newVoteValue: 1 | -1): Promise<FeatureVote> {
    await this.db.executeSql(`
      UPDATE feature_votes 
      SET vote_value = ?, voted_at = datetime('now'), sync_status = 'pending'
      WHERE id = ?
    `, [newVoteValue, voteId]);

    // Add to sync queue
    await this.addToSyncQueue('UPDATE', 'feature_votes', voteId, { vote_value: newVoteValue });

    const vote = await this.getVoteById(voteId);
    if (!vote) {
      throw new Error('Failed to retrieve updated vote');
    }

    return vote;
  }

  private async createNewVote(voteData: VoteSubmissionData, deviceId: string): Promise<FeatureVote> {
    const [result] = await this.db.executeSql(`
      INSERT INTO feature_votes (
        feature_request_id, device_id, vote_value, voted_at
      ) VALUES (?, ?, ?, datetime('now'))
    `, [voteData.featureRequestId, deviceId, voteData.voteValue]);

    const voteId = result.insertId;
    if (!voteId) {
      throw new Error('Failed to create vote - no ID returned');
    }

    // Add to sync queue
    await this.addToSyncQueue('INSERT', 'feature_votes', voteId, {
      feature_request_id: voteData.featureRequestId,
      device_id: deviceId,
      vote_value: voteData.voteValue
    });

    const vote = await this.getVoteById(voteId);
    if (!vote) {
      throw new Error('Failed to retrieve created vote');
    }

    return vote;
  }

  private async getVoteById(id: number): Promise<FeatureVote | null> {
    const [result] = await this.db.executeSql(
      'SELECT * FROM feature_votes WHERE id = ?',
      [id]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return this.mapRowToVote(result.rows.item(0));
  }

  private mapRowToFeedback(row: any): FeedbackSubmission {
    // Safely parse dates from database
    const parseDate = (dateStr: string | null): Date => {
      if (!dateStr) {
        return new Date(); // Default to current date if missing
      }
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? new Date() : date;
    };

    return {
      id: row.id,
      supabaseId: row.supabase_id,
      category: row.category,
      title: row.title,
      description: row.description,
      priorityLevel: row.priority_level,
      deviceId: row.device_id,
      contactInfo: row.contact_info,
      rewardEligible: row.reward_eligible === 1,
      screenshotPath: row.screenshot_path,
      submittedAt: parseDate(row.submitted_at),
      syncStatus: row.sync_status || 'pending',
      lastSyncedAt: row.last_synced_at ? parseDate(row.last_synced_at) : undefined,
      status: row.status || 'submitted',
      adminFeedback: row.admin_feedback,
      version: row.version || 1
    };
  }

  private mapRowToVote(row: any): FeatureVote {
    // Safely parse dates from database
    const parseDate = (dateStr: string | null): Date => {
      if (!dateStr) {
        return new Date(); // Default to current date if missing
      }
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? new Date() : date;
    };

    return {
      id: row.id,
      supabaseId: row.supabase_id,
      featureRequestId: row.feature_request_id,
      deviceId: row.device_id,
      voteValue: row.vote_value,
      votedAt: parseDate(row.voted_at),
      syncStatus: row.sync_status || 'pending',
      lastSyncedAt: row.last_synced_at ? parseDate(row.last_synced_at) : undefined
    };
  }

  private mapRowToToken(row: any): EarlyAccessToken {
    // Safely parse dates from database
    const parseDate = (dateStr: string | null): Date => {
      if (!dateStr) {
        return new Date(); // Default to current date if missing
      }
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? new Date() : date;
    };

    return {
      id: row.id,
      supabaseId: row.supabase_id,
      deviceId: row.device_id,
      tokenType: row.token_type,
      grantedFor: row.granted_for,
      expiresAt: parseDate(row.expires_at),
      createdAt: parseDate(row.created_at),
      syncStatus: row.sync_status || 'pending',
      isActive: row.is_active === 1
    };
  }
}