import { Category } from '@/shared/types';
import { CategoryRepository } from '@/data/repositories/CategoryRepository';
import { DatabaseService } from '@/data/database/DatabaseService';

export interface CategorySuggestion {
  categoryId: number;
  categoryName: string;
  confidence: number;
  reason: 'keyword_match' | 'frequent_pattern' | 'amount_pattern' | 'ml_suggestion' | 'merchant_pattern';
}

export interface CategoryPattern {
  keywords: string[];
  categoryId: number;
  weight: number;
}

export interface MerchantPattern {
  merchantName: string;
  categoryId: number;
  frequency: number;
}

export class CategorySuggestionService {
  private categoryRepository: CategoryRepository;
  private databaseService: DatabaseService;
  
  // Predefined keyword patterns for better categorization
  private readonly KEYWORD_PATTERNS: Record<string, CategoryPattern[]> = {
    food: [
      { keywords: ['restaurant', 'food', 'meal', 'dining', 'lunch', 'dinner', 'breakfast', 'cafe', 'pizza', 'burger'], categoryId: 1, weight: 0.8 },
      { keywords: ['grocery', 'supermarket', 'market', 'store', 'shop'], categoryId: 1, weight: 0.7 },
    ],
    transportation: [
      { keywords: ['uber', 'lyft', 'taxi', 'cab', 'metro', 'bus', 'train', 'fuel', 'gas', 'petrol'], categoryId: 2, weight: 0.9 },
      { keywords: ['parking', 'toll', 'highway'], categoryId: 2, weight: 0.7 },
    ],
    shopping: [
      { keywords: ['amazon', 'flipkart', 'myntra', 'shopping', 'store', 'mall', 'purchase', 'buy'], categoryId: 3, weight: 0.8 },
      { keywords: ['clothes', 'clothing', 'apparel', 'fashion'], categoryId: 3, weight: 0.7 },
    ],
    bills: [
      { keywords: ['electricity', 'water', 'internet', 'phone', 'mobile', 'recharge', 'bill'], categoryId: 4, weight: 0.9 },
      { keywords: ['insurance', 'premium', 'policy'], categoryId: 4, weight: 0.8 },
    ],
    income: [
      { keywords: ['salary', 'wage', 'pay', 'payroll', 'income', 'bonus'], categoryId: 5, weight: 0.9 },
      { keywords: ['freelance', 'consulting', 'project', 'contract'], categoryId: 6, weight: 0.8 },
    ],
  };

  // Amount-based patterns for categorization
  private readonly AMOUNT_PATTERNS = {
    smallExpense: { min: 0, max: 500, categories: [1, 2] }, // Food, Transportation
    mediumExpense: { min: 500, max: 5000, categories: [3, 4] }, // Shopping, Bills
    largeExpense: { min: 5000, max: Infinity, categories: [4] }, // Bills
    income: { min: 1000, max: Infinity, categories: [5, 6] }, // Salary, Freelance
  };

  constructor() {
    this.categoryRepository = new CategoryRepository();
    this.databaseService = DatabaseService.getInstance();
  }

  /**
   * Get category suggestions based on transaction description and optional amount
   */
  async suggestCategories(
    description: string, 
    amount?: number,
    transactionType: 'income' | 'expense' = 'expense'
  ): Promise<CategorySuggestion[]> {
    if (!description || description.trim().length < 2) {
      return [];
    }

    try {
      // Get all categories for the transaction type
      const allCategories = await this.categoryRepository.findAll();
      const relevantCategories = allCategories.filter(cat => 
        cat.category_type === transactionType
      );

      const suggestions = new Map<number, CategorySuggestion>();
      const descriptionLower = description.toLowerCase().trim();

      // 1. Keyword-based suggestions
      const keywordSuggestions = this.getKeywordBasedSuggestions(
        descriptionLower, 
        relevantCategories
      );
      this.mergeSuggestions(suggestions, keywordSuggestions);

      // 2. Historical pattern matching
      const historicalSuggestions = await this.getHistoricalPatternSuggestions(
        descriptionLower, 
        relevantCategories
      );
      this.mergeSuggestions(suggestions, historicalSuggestions);

      // 3. Merchant pattern matching
      const merchantSuggestions = await this.getMerchantPatternSuggestions(
        descriptionLower, 
        relevantCategories
      );
      this.mergeSuggestions(suggestions, merchantSuggestions);

      // 4. Amount-based suggestions (if amount is provided)
      if (amount && amount > 0) {
        const amountSuggestions = this.getAmountBasedSuggestions(
          amount, 
          relevantCategories,
          transactionType
        );
        this.mergeSuggestions(suggestions, amountSuggestions);
      }

      // 5. Frequency-based boosting
      await this.boostFrequentCategories(suggestions, relevantCategories);

      // Convert to array, sort by confidence, and return top 5
      return Array.from(suggestions.values())
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 5);

    } catch (error) {
      console.error('Failed to suggest categories:', error);
      return [];
    }
  }

  /**
   * Get keyword-based category suggestions
   */
  private getKeywordBasedSuggestions(
    description: string, 
    categories: Category[]
  ): CategorySuggestion[] {
    const suggestions: CategorySuggestion[] = [];
    const words = description.split(' ').filter(word => word.length > 2);

    // Check predefined keyword patterns
    for (const [, patterns] of Object.entries(this.KEYWORD_PATTERNS)) {
      for (const pattern of patterns) {
        let matchCount = 0;
        const totalKeywords = pattern.keywords.length;

        for (const keyword of pattern.keywords) {
          if (description.includes(keyword) || 
              words.some(word => 
                word.includes(keyword) || keyword.includes(word)
              )) {
            matchCount++;
          }
        }

        if (matchCount > 0) {
          const category = categories.find(c => c.id === pattern.categoryId);
          if (category) {
            const confidence = Math.min(0.9, 
              (matchCount / Math.max(totalKeywords, words.length)) * pattern.weight
            );

            suggestions.push({
              categoryId: category.id,
              categoryName: category.name,
              confidence,
              reason: 'keyword_match'
            });
          }
        }
      }
    }

    // Check against category names directly
    for (const category of categories) {
      const categoryWords = category.name.toLowerCase().split(' ');
      let matchScore = 0;

      for (const word of words) {
        for (const catWord of categoryWords) {
          if (word.includes(catWord) || catWord.includes(word)) {
            matchScore += 1;
            break;
          }
        }
      }

      if (matchScore > 0 && !suggestions.find(s => s.categoryId === category.id)) {
        suggestions.push({
          categoryId: category.id,
          categoryName: category.name,
          confidence: Math.min(0.8, matchScore / Math.max(categoryWords.length, words.length)),
          reason: 'keyword_match'
        });
      }
    }

    return suggestions;
  }

  /**
   * Get suggestions based on historical transaction patterns
   */
  private async getHistoricalPatternSuggestions(
    description: string, 
    categories: Category[]
  ): Promise<CategorySuggestion[]> {
    const suggestions: CategorySuggestion[] = [];

    try {
      // Find similar descriptions in transaction history
      const [result] = await this.databaseService.executeSql(
        `SELECT category_id, description, COUNT(*) as frequency,
                AVG(confidence_score) as avg_confidence
         FROM transactions 
         WHERE category_id IS NOT NULL 
           AND (description LIKE ? OR description LIKE ?)
         GROUP BY category_id 
         ORDER BY frequency DESC, avg_confidence DESC 
         LIMIT 5`,
        [`%${description}%`, `%${description.substring(0, 10)}%`]
      );

      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        const category = categories.find(c => c.id === row.category_id);
        
        if (category) {
          const confidence = Math.min(0.9, 
            0.5 + (row.frequency * 0.1) + (row.avg_confidence || 0) * 0.2
          );

          suggestions.push({
            categoryId: category.id,
            categoryName: category.name,
            confidence,
            reason: 'frequent_pattern'
          });
        }
      }
    } catch (error) {
      console.error('Failed to get historical patterns:', error);
    }

    return suggestions;
  }

  /**
   * Get suggestions based on merchant patterns
   */
  private async getMerchantPatternSuggestions(
    description: string, 
    categories: Category[]
  ): Promise<CategorySuggestion[]> {
    const suggestions: CategorySuggestion[] = [];

    try {
      // Extract potential merchant names (words > 3 characters)
      const potentialMerchants = description.split(' ')
        .filter(word => word.length > 3)
        .slice(0, 3); // Take first 3 potential merchant names

      if (potentialMerchants.length === 0) {
        return suggestions;
      }

      // Build query for merchant pattern matching
      const merchantConditions = potentialMerchants
        .map(() => 'description LIKE ?')
        .join(' OR ');

      const merchantParams = potentialMerchants
        .map(merchant => `%${merchant}%`);

      const [result] = await this.databaseService.executeSql(
        `SELECT category_id, COUNT(*) as frequency, 
                GROUP_CONCAT(DISTINCT description) as descriptions
         FROM transactions 
         WHERE category_id IS NOT NULL AND (${merchantConditions})
         GROUP BY category_id 
         ORDER BY frequency DESC 
         LIMIT 3`,
        merchantParams
      );

      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        const category = categories.find(c => c.id === row.category_id);
        
        if (category) {
          const confidence = Math.min(0.8, 0.6 + (row.frequency * 0.05));

          suggestions.push({
            categoryId: category.id,
            categoryName: category.name,
            confidence,
            reason: 'merchant_pattern'
          });
        }
      }
    } catch (error) {
      console.error('Failed to get merchant patterns:', error);
    }

    return suggestions;
  }

  /**
   * Get amount-based category suggestions
   */
  private getAmountBasedSuggestions(
    amount: number, 
    categories: Category[],
    transactionType: 'income' | 'expense'
  ): CategorySuggestion[] {
    const suggestions: CategorySuggestion[] = [];

    // Apply amount patterns based on transaction type
    if (transactionType === 'expense') {
      for (const [patternName, pattern] of Object.entries(this.AMOUNT_PATTERNS)) {
        if (patternName !== 'income' && amount >= pattern.min && amount <= pattern.max) {
          for (const categoryId of pattern.categories) {
            const category = categories.find(c => c.id === categoryId);
            if (category) {
              suggestions.push({
                categoryId: category.id,
                categoryName: category.name,
                confidence: 0.6,
                reason: 'amount_pattern'
              });
            }
          }
        }
      }
    } else if (transactionType === 'income') {
      const incomePattern = this.AMOUNT_PATTERNS.income;
      if (amount >= incomePattern.min && amount <= incomePattern.max) {
        for (const categoryId of incomePattern.categories) {
          const category = categories.find(c => c.id === categoryId);
          if (category) {
            suggestions.push({
              categoryId: category.id,
              categoryName: category.name,
              confidence: 0.7,
              reason: 'amount_pattern'
            });
          }
        }
      }
    }

    return suggestions;
  }

  /**
   * Boost suggestions based on user's frequent categories
   */
  private async boostFrequentCategories(
    suggestions: Map<number, CategorySuggestion>,
    categories: Category[]
  ): Promise<void> {
    try {
      // Get user's most frequent categories in the last 30 days
      const [result] = await this.databaseService.executeSql(
        `SELECT category_id, COUNT(*) as usage_count
         FROM transactions 
         WHERE category_id IS NOT NULL 
           AND transaction_date >= date('now', '-30 days')
         GROUP BY category_id 
         ORDER BY usage_count DESC 
         LIMIT 10`
      );

      const totalTransactions = await this.getTotalRecentTransactions();
      
      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        const categoryId = row.category_id;
        const usageFrequency = row.usage_count / totalTransactions;
        
        // Boost existing suggestions
        if (suggestions.has(categoryId)) {
          const suggestion = suggestions.get(categoryId)!;
          suggestion.confidence = Math.min(0.95, 
            suggestion.confidence + (usageFrequency * 0.2)
          );
        }
      }
    } catch (error) {
      console.error('Failed to boost frequent categories:', error);
    }
  }

  /**
   * Merge suggestions, combining confidence scores for duplicate categories
   */
  private mergeSuggestions(
    existingSuggestions: Map<number, CategorySuggestion>,
    newSuggestions: CategorySuggestion[]
  ): void {
    for (const suggestion of newSuggestions) {
      if (existingSuggestions.has(suggestion.categoryId)) {
        const existing = existingSuggestions.get(suggestion.categoryId)!;
        // Combine confidence scores with weighted average
        existing.confidence = Math.min(0.95,
          (existing.confidence * 0.6) + (suggestion.confidence * 0.4)
        );
      } else {
        existingSuggestions.set(suggestion.categoryId, suggestion);
      }
    }
  }

  /**
   * Get total number of recent transactions for frequency calculation
   */
  private async getTotalRecentTransactions(): Promise<number> {
    try {
      const [result] = await this.databaseService.executeSql(
        `SELECT COUNT(*) as total 
         FROM transactions 
         WHERE transaction_date >= date('now', '-30 days')`
      );
      
      return result.rows.item(0).total || 1;
    } catch (error) {
      console.error('Failed to get total recent transactions:', error);
      return 1;
    }
  }

  /**
   * Learn from user selections to improve future suggestions
   */
  async learnFromUserSelection(
    description: string,
    selectedCategoryId: number,
    rejectedCategoryIds: number[] = []
  ): Promise<void> {
    try {
      // Store learning data for future ML training
      // For now, we'll just update the confidence scores in existing transactions
      await this.databaseService.executeSql(
        `UPDATE transactions 
         SET confidence_score = COALESCE(confidence_score, 0) + 0.1
         WHERE category_id = ? AND description LIKE ?`,
        [selectedCategoryId, `%${description}%`]
      );

      // Decrease confidence for rejected categories
      for (const rejectedId of rejectedCategoryIds) {
        await this.databaseService.executeSql(
          `UPDATE transactions 
           SET confidence_score = COALESCE(confidence_score, 0.5) - 0.05
           WHERE category_id = ? AND description LIKE ?`,
          [rejectedId, `%${description}%`]
        );
      }
    } catch (error) {
      console.error('Failed to learn from user selection:', error);
    }
  }

  /**
   * Get category suggestion statistics
   */
  async getSuggestionStats(): Promise<{
    totalSuggestions: number;
    accuracyRate: number;
    topCategories: { categoryId: number; categoryName: string; usage: number }[];
  }> {
    try {
      // Get total transactions with categories
      const [totalResult] = await this.databaseService.executeSql(
        'SELECT COUNT(*) as total FROM transactions WHERE category_id IS NOT NULL'
      );

      // Get transactions with high confidence (accurate suggestions)
      const [accurateResult] = await this.databaseService.executeSql(
        'SELECT COUNT(*) as accurate FROM transactions WHERE confidence_score > 0.7'
      );

      // Get top categories by usage
      const [topResult] = await this.databaseService.executeSql(
        `SELECT t.category_id, c.name as category_name, COUNT(*) as usage
         FROM transactions t
         JOIN categories c ON t.category_id = c.id
         WHERE t.category_id IS NOT NULL
         GROUP BY t.category_id, c.name
         ORDER BY usage DESC
         LIMIT 5`
      );

      const topCategories = [];
      for (let i = 0; i < topResult.rows.length; i++) {
        const row = topResult.rows.item(i);
        topCategories.push({
          categoryId: row.category_id,
          categoryName: row.category_name,
          usage: row.usage
        });
      }

      const total = totalResult.rows.item(0).total || 0;
      const accurate = accurateResult.rows.item(0).accurate || 0;

      return {
        totalSuggestions: total,
        accuracyRate: total > 0 ? (accurate / total) * 100 : 0,
        topCategories
      };
    } catch (error) {
      console.error('Failed to get suggestion stats:', error);
      return {
        totalSuggestions: 0,
        accuracyRate: 0,
        topCategories: []
      };
    }
  }
}