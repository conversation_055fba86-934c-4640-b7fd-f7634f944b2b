import { Logger } from '@/shared/utils/Logger';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON>, AppError } from '@/shared/utils/ErrorHandler';

interface TelemetryEvent {
  eventType: 'error' | 'performance' | 'user_action' | 'feature_usage';
  timestamp: Date;
  data: Record<string, any>;
  sessionId: string;
  anonymized: boolean;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'mb' | 'count' | 'percent';
  threshold?: number;
  tags?: Record<string, string>;
}

interface UserActionEvent {
  action: string;
  screen: string;
  duration?: number;
  success: boolean;
  metadata?: Record<string, any>;
}

export class TelemetryService {
  private static instance: TelemetryService;
  private logger: Logger;
  private errorHandler: ErrorHandler;
  private events: TelemetryEvent[] = [];
  private sessionId: string;
  private isEnabled: boolean = true;
  private maxEvents: number = 500;

  private constructor() {
    this.logger = Logger.getInstance();
    this.errorHandler = ErrorHandler.getInstance();
    this.sessionId = this.generateSessionId();
  }

  public static getInstance(): TelemetryService {
    if (!TelemetryService.instance) {
      TelemetryService.instance = new TelemetryService();
    }
    return TelemetryService.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    this.logger.info(`Telemetry ${enabled ? 'enabled' : 'disabled'}`, 'telemetry');
  }

  private addEvent(event: TelemetryEvent): void {
    if (!this.isEnabled) {
      return;
    }

    this.events.push(event);

    // Maintain event limit
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log for Claude Code integration
    this.logger.info(
      `Telemetry event: ${event.eventType}`,
      'telemetry',
      { eventType: event.eventType, data: event.data }
    );
  }

  public trackError(error: AppError, context?: string, recovered: boolean = false): void {
    const errorEvent: TelemetryEvent = {
      eventType: 'error',
      timestamp: new Date(),
      sessionId: this.sessionId,
      anonymized: true,
      data: {
        category: error.category,
        code: error.code,
        isRetryable: error.isRetryable,
        recovered,
        context,
        hasMetadata: !!error.metadata,
        metadataKeys: error.metadata ? Object.keys(error.metadata) : [],
        stackTraceLength: error.stack?.length || 0,
        // Don't include actual error message or metadata to maintain privacy
      }
    };

    this.addEvent(errorEvent);
  }

  public trackPerformance(metric: PerformanceMetric): void {
    const performanceEvent: TelemetryEvent = {
      eventType: 'performance',
      timestamp: new Date(),
      sessionId: this.sessionId,
      anonymized: true,
      data: {
        name: metric.name,
        value: metric.value,
        unit: metric.unit,
        threshold: metric.threshold,
        exceedsThreshold: metric.threshold ? metric.value > metric.threshold : false,
        tags: metric.tags || {}
      }
    };

    this.addEvent(performanceEvent);

    // Log performance warnings
    if (metric.threshold && metric.value > metric.threshold) {
      this.logger.logPerformanceMetric(metric.name, metric.value, metric.threshold);
    }
  }

  public trackUserAction(actionEvent: UserActionEvent): void {
    const telemetryEvent: TelemetryEvent = {
      eventType: 'user_action',
      timestamp: new Date(),
      sessionId: this.sessionId,
      anonymized: true,
      data: {
        action: actionEvent.action,
        screen: actionEvent.screen,
        duration: actionEvent.duration,
        success: actionEvent.success,
        // Metadata keys only, not values for privacy
        metadataKeys: actionEvent.metadata ? Object.keys(actionEvent.metadata) : []
      }
    };

    this.addEvent(telemetryEvent);
  }

  public trackFeatureUsage(feature: string, subFeature?: string, metadata?: Record<string, any>): void {
    const featureEvent: TelemetryEvent = {
      eventType: 'feature_usage',
      timestamp: new Date(),
      sessionId: this.sessionId,
      anonymized: true,
      data: {
        feature,
        subFeature,
        hasMetadata: !!metadata,
        metadataKeys: metadata ? Object.keys(metadata) : []
      }
    };

    this.addEvent(featureEvent);
  }

  // Anonymous reporting for patterns and insights
  public async generateAnonymousReport(): Promise<{
    summary: Record<string, any>;
    patterns: Record<string, any>;
    recommendations: string[];
  }> {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const recentEvents = this.events.filter(event => event.timestamp >= last24Hours);

    // Error patterns
    const errorEvents = recentEvents.filter(e => e.eventType === 'error');
    const errorsByCategory = errorEvents.reduce((acc, event) => {
      const category = event.data.category;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const recoveryRate = errorEvents.length > 0 ? 
      errorEvents.filter(e => e.data.recovered).length / errorEvents.length : 0;

    // Performance patterns
    const performanceEvents = recentEvents.filter(e => e.eventType === 'performance');
    const slowOperations = performanceEvents.filter(e => e.data.exceedsThreshold);
    
    // Feature usage patterns
    const featureEvents = recentEvents.filter(e => e.eventType === 'feature_usage');
    const featureUsage = featureEvents.reduce((acc, event) => {
      const feature = event.data.feature;
      acc[feature] = (acc[feature] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Generate recommendations
    const recommendations: string[] = [];
    
    if (recoveryRate < 0.5) {
      recommendations.push('Error recovery rate is low - consider improving error handling strategies');
    }
    
    if (slowOperations.length > 10) {
      recommendations.push('Multiple slow operations detected - database optimization recommended');
    }
    
    if (errorsByCategory.database > errorsByCategory.network) {
      recommendations.push('Database errors are frequent - consider connection pooling improvements');
    }

    const summary = {
      sessionId: this.sessionId,
      reportPeriod: '24h',
      totalEvents: recentEvents.length,
      errorCount: errorEvents.length,
      recoveryRate: Math.round(recoveryRate * 100),
      slowOperationCount: slowOperations.length,
      topFeatures: Object.entries(featureUsage)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([feature, count]) => ({ feature, count }))
    };

    const patterns = {
      errorsByCategory,
      performanceIssues: slowOperations.map(e => ({
        metric: e.data.name,
        value: e.data.value,
        threshold: e.data.threshold
      })),
      featureAdoption: featureUsage
    };

    return { summary, patterns, recommendations };
  }

  public getSessionStats(): {
    sessionId: string;
    startTime: Date;
    eventCount: number;
    errorCount: number;
    performanceEventCount: number;
    userActionCount: number;
    featureUsageCount: number;
  } {
    return {
      sessionId: this.sessionId,
      startTime: this.events.length > 0 ? this.events[0].timestamp : new Date(),
      eventCount: this.events.length,
      errorCount: this.events.filter(e => e.eventType === 'error').length,
      performanceEventCount: this.events.filter(e => e.eventType === 'performance').length,
      userActionCount: this.events.filter(e => e.eventType === 'user_action').length,
      featureUsageCount: this.events.filter(e => e.eventType === 'feature_usage').length
    };
  }

  // Database performance tracking
  public trackDatabaseOperation(operation: string, table: string, duration: number, success: boolean): void {
    this.trackPerformance({
      name: `database_${operation}`,
      value: duration,
      unit: 'ms',
      threshold: 500,
      tags: { table, operation }
    });

    if (!success) {
      const error = this.errorHandler.createDatabaseError(
        `Database ${operation} failed`,
        'DB_OPERATION_FAILED',
        { table, operation, duration }
      );
      this.trackError(error, 'database_operation');
    }
  }

  // SMS processing tracking
  public trackSMSProcessing(success: boolean, confidence?: number, processingTime?: number): void {
    this.trackFeatureUsage('sms_processing', success ? 'success' : 'failure', {
      confidence,
      processingTime
    });

    if (processingTime) {
      this.trackPerformance({
        name: 'sms_processing_time',
        value: processingTime,
        unit: 'ms',
        threshold: 2000
      });
    }

    if (!success) {
      const error = this.errorHandler.createSMSProcessingError('SMS processing failed');
      this.trackError(error, 'sms_processing');
    }
  }

  // ML inference tracking
  public trackMLInference(modelType: string, confidence: number, processingTime: number): void {
    this.trackFeatureUsage('ml_inference', modelType, { confidence });
    
    this.trackPerformance({
      name: `ml_inference_${modelType}`,
      value: processingTime,
      unit: 'ms',
      threshold: 1000,
      tags: { modelType }
    });

    this.trackPerformance({
      name: `ml_confidence_${modelType}`,
      value: confidence,
      unit: 'percent',
      threshold: 70,
      tags: { modelType }
    });
  }

  public exportTelemetryData(): string {
    return JSON.stringify({
      sessionId: this.sessionId,
      exportTime: new Date().toISOString(),
      events: this.events.map(event => ({
        ...event,
        timestamp: event.timestamp.toISOString()
      }))
    }, null, 2);
  }

  public clearTelemetryData(): void {
    this.events = [];
    this.sessionId = this.generateSessionId();
    this.logger.info('Telemetry data cleared', 'telemetry');
  }
}