import { Category } from '@/shared/types';
import { CategoryRepository } from '@/data/repositories/CategoryRepository';
import { 
  ALL_SYSTEM_CATEGORIES, 
  SYSTEM_EXPENSE_CATEGORIES, 
  SYSTEM_INCOME_CATEGORIES,
  SystemCategoryData,
  validateSystemCategoryData 
} from '@/data/systemCategories';
import AsyncStorage from '@react-native-async-storage/async-storage';

export class SystemCategoriesService {
  private categoryRepository: CategoryRepository;
  private static readonly INITIALIZATION_KEY = 'categories_initialized';
  private static readonly SYSTEM_CATEGORIES_VERSION_KEY = 'system_categories_version';
  private static readonly CURRENT_VERSION = '1.0.0';

  constructor() {
    this.categoryRepository = new CategoryRepository();
  }

  /**
   * Initialize system categories if not already done
   */
  async initializeSystemCategories(): Promise<void> {
    const isInitialized = await this.isSystemCategoriesInitialized();
    
    if (!isInitialized) {
      await this.createSystemCategories();
      await this.markAsInitialized();
    } else {
      // Check for version updates
      await this.handleSystemCategoriesUpdate();
    }
  }

  /**
   * Force re-initialization of system categories (for updates)
   */
  async reinitializeSystemCategories(): Promise<void> {
    await this.createSystemCategories();
    await this.markAsInitialized();
  }

  /**
   * Check if system categories have been initialized
   */
  async isSystemCategoriesInitialized(): Promise<boolean> {
    try {
      const initialized = await AsyncStorage.getItem(SystemCategoriesService.INITIALIZATION_KEY);
      return initialized === 'true';
    } catch (error) {
      console.error('Error checking system categories initialization:', error);
      return false;
    }
  }

  /**
   * Get the current version of system categories
   */
  async getSystemCategoriesVersion(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(SystemCategoriesService.SYSTEM_CATEGORIES_VERSION_KEY);
    } catch (error) {
      console.error('Error getting system categories version:', error);
      return null;
    }
  }

  /**
   * Create all system categories
   */
  private async createSystemCategories(): Promise<void> {
    console.log('Initializing system categories...');
    
    let createdCount = 0;
    let skippedCount = 0;

    for (const categoryData of ALL_SYSTEM_CATEGORIES) {
      try {
        // Validate system category data
        if (!validateSystemCategoryData(categoryData)) {
          console.warn(`Invalid system category data for: ${categoryData.name}`);
          continue;
        }

        // Check if category already exists
        const existingCategories = await this.categoryRepository.findByType(categoryData.categoryType);
        const categoryExists = existingCategories.some(cat => 
          cat.name.toLowerCase() === categoryData.name.toLowerCase() && cat.is_system
        );

        if (!categoryExists) {
          await this.categoryRepository.create({
            name: categoryData.name,
            parent_id: null,
            category_type: categoryData.categoryType,
            color_code: categoryData.colorCode,
            icon_name: categoryData.iconName,
            is_system: true,
            sync_status: 'local',
          });
          createdCount++;
          console.log(`✓ Created system category: ${categoryData.name}`);
        } else {
          skippedCount++;
          console.log(`• Skipped existing system category: ${categoryData.name}`);
        }
      } catch (error) {
        console.error(`Failed to create system category ${categoryData.name}:`, error);
      }
    }

    console.log(`System categories initialization complete: ${createdCount} created, ${skippedCount} skipped`);
  }

  /**
   * Handle system categories updates when version changes
   */
  private async handleSystemCategoriesUpdate(): Promise<void> {
    const currentVersion = await this.getSystemCategoriesVersion();
    
    if (currentVersion !== SystemCategoriesService.CURRENT_VERSION) {
      console.log(`Updating system categories from version ${currentVersion || 'unknown'} to ${SystemCategoriesService.CURRENT_VERSION}`);
      
      // Add any new system categories that weren't in previous versions
      await this.addMissingSystemCategories();
      
      // Update version
      await AsyncStorage.setItem(
        SystemCategoriesService.SYSTEM_CATEGORIES_VERSION_KEY, 
        SystemCategoriesService.CURRENT_VERSION
      );
    }
  }

  /**
   * Add any system categories that are missing from current installation
   */
  private async addMissingSystemCategories(): Promise<void> {
    const existingCategories = await this.categoryRepository.findAll();
    const existingSystemCategories = existingCategories.filter(cat => cat.is_system);
    
    let addedCount = 0;

    for (const categoryData of ALL_SYSTEM_CATEGORIES) {
      const categoryExists = existingSystemCategories.some(cat => 
        cat.name.toLowerCase() === categoryData.name.toLowerCase() &&
        cat.category_type === categoryData.categoryType
      );

      if (!categoryExists && validateSystemCategoryData(categoryData)) {
        try {
          await this.categoryRepository.create({
            name: categoryData.name,
            parent_id: null,
            category_type: categoryData.categoryType,
            color_code: categoryData.colorCode,
            icon_name: categoryData.iconName,
            is_system: true,
            sync_status: 'local',
          });
          addedCount++;
          console.log(`✓ Added missing system category: ${categoryData.name}`);
        } catch (error) {
          console.error(`Failed to add missing system category ${categoryData.name}:`, error);
        }
      }
    }

    if (addedCount > 0) {
      console.log(`Added ${addedCount} missing system categories`);
    }
  }

  /**
   * Mark system categories as initialized
   */
  private async markAsInitialized(): Promise<void> {
    try {
      await AsyncStorage.setItem(SystemCategoriesService.INITIALIZATION_KEY, 'true');
      await AsyncStorage.setItem(
        SystemCategoriesService.SYSTEM_CATEGORIES_VERSION_KEY, 
        SystemCategoriesService.CURRENT_VERSION
      );
    } catch (error) {
      console.error('Error marking system categories as initialized:', error);
    }
  }

  /**
   * Get all system expense categories
   */
  getSystemExpenseCategories(): SystemCategoryData[] {
    return SYSTEM_EXPENSE_CATEGORIES;
  }

  /**
   * Get all system income categories
   */
  getSystemIncomeCategories(): SystemCategoryData[] {
    return SYSTEM_INCOME_CATEGORIES;
  }

  /**
   * Get all system categories
   */
  getAllSystemCategories(): SystemCategoryData[] {
    return ALL_SYSTEM_CATEGORIES;
  }

  /**
   * Check if a category is a protected system category
   */
  async isProtectedSystemCategory(categoryId: number): Promise<boolean> {
    try {
      const category = await this.categoryRepository.findById(categoryId);
      return category ? category.is_system : false;
    } catch (error) {
      console.error('Error checking if category is protected system category:', error);
      return false;
    }
  }

  /**
   * Validate that system categories cannot be deleted
   */
  async validateSystemCategoryDeletion(categoryId: number): Promise<{canDelete: boolean, reason?: string}> {
    const isProtected = await this.isProtectedSystemCategory(categoryId);
    
    if (isProtected) {
      return {
        canDelete: false,
        reason: 'System categories cannot be deleted. They can only be archived or hidden.'
      };
    }
    
    return { canDelete: true };
  }

  /**
   * Archive a system category instead of deleting it
   */
  async archiveSystemCategory(categoryId: number): Promise<Category> {
    const isProtected = await this.isProtectedSystemCategory(categoryId);
    
    if (!isProtected) {
      throw new Error('Only system categories can be archived using this method');
    }

    // Update sync_status to indicate archived state
    return await this.categoryRepository.update(categoryId, { 
      sync_status: 'conflict' // Using conflict status to indicate archived
    });
  }

  /**
   * Restore an archived system category
   */
  async restoreSystemCategory(categoryId: number): Promise<Category> {
    const category = await this.categoryRepository.findById(categoryId);
    
    if (!category || !category.is_system) {
      throw new Error('Category not found or not a system category');
    }

    return await this.categoryRepository.update(categoryId, { 
      sync_status: 'local'
    });
  }

  /**
   * Get system category usage statistics
   */
  async getSystemCategoryUsageStats(): Promise<{
    totalSystemCategories: number;
    usedSystemCategories: number;
    mostUsedSystemCategory: string | null;
    leastUsedSystemCategory: string | null;
  }> {
    try {
      const allCategories = await this.categoryRepository.findAll();
      const systemCategories = allCategories.filter(cat => cat.is_system);
      
      const usageStats = await Promise.all(
        systemCategories.map(async cat => {
          const stats = await this.categoryRepository.getCategoryUsageStats(cat.id);
          return { ...cat, usage_count: stats.usage_count };
        })
      );

      const usedCategories = usageStats.filter(cat => cat.usage_count > 0);
      const sortedByUsage = usageStats.sort((a, b) => b.usage_count - a.usage_count);

      return {
        totalSystemCategories: systemCategories.length,
        usedSystemCategories: usedCategories.length,
        mostUsedSystemCategory: sortedByUsage.length > 0 && sortedByUsage[0].usage_count > 0 
          ? sortedByUsage[0].name 
          : null,
        leastUsedSystemCategory: sortedByUsage.length > 0 && sortedByUsage[sortedByUsage.length - 1].usage_count === 0
          ? sortedByUsage[sortedByUsage.length - 1].name
          : null,
      };
    } catch (error) {
      console.error('Error getting system category usage stats:', error);
      throw error;
    }
  }

  /**
   * Reset system categories to defaults (for testing or troubleshooting)
   */
  async resetSystemCategories(): Promise<void> {
    console.log('Resetting system categories to defaults...');
    
    try {
      // Get all system categories
      const allCategories = await this.categoryRepository.findAll();
      const systemCategories = allCategories.filter(cat => cat.is_system);

      // Remove existing system categories that can be safely removed
      for (const category of systemCategories) {
        try {
          // Check if category has transactions
          const stats = await this.categoryRepository.getCategoryUsageStats(category.id);
          
          if (stats.usage_count === 0) {
            await this.categoryRepository.delete(category.id);
            console.log(`Removed unused system category: ${category.name}`);
          } else {
            console.log(`Kept system category with transactions: ${category.name} (${stats.usage_count} transactions)`);
          }
        } catch (error) {
          console.warn(`Could not remove system category ${category.name}:`, error);
        }
      }

      // Clear initialization flags
      await AsyncStorage.removeItem(SystemCategoriesService.INITIALIZATION_KEY);
      await AsyncStorage.removeItem(SystemCategoriesService.SYSTEM_CATEGORIES_VERSION_KEY);

      // Reinitialize
      await this.initializeSystemCategories();
      
      console.log('System categories reset complete');
    } catch (error) {
      console.error('Error resetting system categories:', error);
      throw error;
    }
  }
}