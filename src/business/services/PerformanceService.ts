import { DatabaseService } from '@/data/database/DatabaseService';

interface QueryPerformanceMetric {
  query: string;
  executionTime: number;
  rowsAffected: number;
  timestamp: Date;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export class PerformanceService {
  private static instance: PerformanceService;
  private db: DatabaseService;
  private queryMetrics: QueryPerformanceMetric[] = [];
  private cache: Map<string, CacheEntry<any>> = new Map();
  private readonly CACHE_DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 100;

  private constructor() {
    this.db = DatabaseService.getInstance();
  }

  public static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService();
    }
    return PerformanceService.instance;
  }

  public async executeWithPerformanceTracking<T>(
    query: string,
    params: any[] = [],
    cacheKey?: string
  ): Promise<{ data: T; performance: QueryPerformanceMetric }> {
    // Check cache first
    if (cacheKey && this.getCachedData<T>(cacheKey)) {
      const cachedData = this.getCachedData<T>(cacheKey);
      return {
        data: cachedData!,
        performance: {
          query: `CACHED: ${query}`,
          executionTime: 0,
          rowsAffected: 0,
          timestamp: new Date()
        }
      };
    }

    const startTime = Date.now();
    
    try {
      const [result] = await this.db.executeSql(query, params);
      const executionTime = Date.now() - startTime;
      
      const metric: QueryPerformanceMetric = {
        query: query.substring(0, 100), // Truncate for storage
        executionTime,
        rowsAffected: result.rows.length,
        timestamp: new Date()
      };

      // Store performance metric
      this.addQueryMetric(metric);

      // Log slow queries
      if (executionTime > 500) {
        console.warn(`🐌 Slow query detected (${executionTime}ms):`, query.substring(0, 200));
      }

      // Cache results if cache key provided
      if (cacheKey && executionTime < 1000) { // Don't cache slow queries
        this.setCachedData(cacheKey, result as T);
      }

      return {
        data: result as T,
        performance: metric
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      const errorMetric: QueryPerformanceMetric = {
        query: `ERROR: ${query.substring(0, 100)}`,
        executionTime,
        rowsAffected: 0,
        timestamp: new Date()
      };

      this.addQueryMetric(errorMetric);
      throw error;
    }
  }

  private addQueryMetric(metric: QueryPerformanceMetric): void {
    this.queryMetrics.push(metric);
    
    // Keep only last 1000 metrics to prevent memory bloat
    if (this.queryMetrics.length > 1000) {
      this.queryMetrics = this.queryMetrics.slice(-1000);
    }
  }

  private setCachedData<T>(key: string, data: T, ttl: number = this.CACHE_DEFAULT_TTL): void {
    // Implement LRU cache behavior
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private getCachedData<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if cache entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  public async updateDashboardSummary(accountId: number): Promise<void> {
    await this.db.initialize();

    const upsertQuery = `
      INSERT OR REPLACE INTO dashboard_summary (
        account_id, current_balance, monthly_spending, budget_utilization, last_transaction_date, updated_at
      )
      SELECT 
        a.id as account_id,
        a.balance as current_balance,
        COALESCE(monthly.spending, 0) as monthly_spending,
        COALESCE(budget.utilization, 0) as budget_utilization,
        COALESCE(last_tx.transaction_date, a.created_at) as last_transaction_date,
        CURRENT_TIMESTAMP as updated_at
      FROM accounts a
      LEFT JOIN (
        SELECT 
          account_id,
          SUM(amount) as spending
        FROM transactions 
        WHERE transaction_type = 'expense' 
        AND transaction_date >= date('now', 'start of month')
        AND account_id = ?
      ) monthly ON a.id = monthly.account_id
      LEFT JOIN (
        SELECT 
          bc.budget_id,
          AVG(bc.spent_amount / bc.allocated_amount * 100) as utilization
        FROM budget_categories bc
        JOIN budgets b ON bc.budget_id = b.id
        WHERE b.is_active = 1
      ) budget ON 1=1
      LEFT JOIN (
        SELECT 
          account_id,
          MAX(transaction_date) as transaction_date
        FROM transactions
        WHERE account_id = ?
      ) last_tx ON a.id = last_tx.account_id
      WHERE a.id = ?
    `;

    await this.db.executeSql(upsertQuery, [accountId, accountId, accountId]);
    
    // Invalidate cache for dashboard data
    this.cache.delete(`dashboard_summary_${accountId}`);
  }

  public async optimizeDatabase(): Promise<void> {
    await this.db.initialize();
    
    console.log('🔧 Starting database optimization...');
    
    // Run VACUUM to optimize database file size
    await this.db.executeSql('VACUUM', []);
    
    // Re-analyze query planner statistics
    await this.db.executeSql('ANALYZE', []);
    
    // Update all dashboard summaries
    const [accounts] = await this.db.executeSql('SELECT id FROM accounts WHERE is_active = 1', []);
    
    for (let i = 0; i < accounts.rows.length; i++) {
      const accountId = accounts.rows.item(i).id;
      await this.updateDashboardSummary(accountId);
    }
    
    console.log('✅ Database optimization completed');
  }

  public getPerformanceReport(): {
    totalQueries: number;
    averageExecutionTime: number;
    slowQueries: QueryPerformanceMetric[];
    cacheHitRate: number;
    recentMetrics: QueryPerformanceMetric[];
  } {
    const slowQueries = this.queryMetrics.filter(m => m.executionTime > 500);
    const totalExecutionTime = this.queryMetrics.reduce((sum, m) => sum + m.executionTime, 0);
    const averageExecutionTime = this.queryMetrics.length > 0 ? totalExecutionTime / this.queryMetrics.length : 0;
    
    // Calculate cache hit rate from cached queries
    const cachedQueries = this.queryMetrics.filter(m => m.query.startsWith('CACHED:'));
    const cacheHitRate = this.queryMetrics.length > 0 ? cachedQueries.length / this.queryMetrics.length : 0;
    
    return {
      totalQueries: this.queryMetrics.length,
      averageExecutionTime: Math.round(averageExecutionTime),
      slowQueries: slowQueries.slice(-10), // Last 10 slow queries
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      recentMetrics: this.queryMetrics.slice(-20) // Last 20 queries
    };
  }

  public async benchmarkQueries(): Promise<{
    transactionQueries: number;
    accountQueries: number;
    categoryQueries: number;
    dashboardQueries: number;
    passed: boolean;
  }> {
    await this.db.initialize();
    
    console.log('🏃‍♂️ Running performance benchmark...');
    
    // Test transaction queries (most critical)
    const transactionStart = Date.now();
    await this.db.executeSql(
      'SELECT * FROM transactions WHERE account_id = 1 ORDER BY transaction_date DESC LIMIT 100',
      []
    );
    const transactionTime = Date.now() - transactionStart;
    
    // Test account queries
    const accountStart = Date.now();
    await this.db.executeSql('SELECT * FROM accounts WHERE is_active = 1', []);
    const accountTime = Date.now() - accountStart;
    
    // Test category queries
    const categoryStart = Date.now();
    await this.db.executeSql('SELECT * FROM categories WHERE category_type = ?', ['expense']);
    const categoryTime = Date.now() - categoryStart;
    
    // Test dashboard summary queries
    const dashboardStart = Date.now();
    await this.db.executeSql(
      'SELECT * FROM dashboard_summary WHERE account_id IN (SELECT id FROM accounts LIMIT 5)',
      []
    );
    const dashboardTime = Date.now() - dashboardStart;
    
    const results = {
      transactionQueries: transactionTime,
      accountQueries: accountTime,
      categoryQueries: categoryTime,
      dashboardQueries: dashboardTime,
      passed: transactionTime < 500 && accountTime < 200 && categoryTime < 100 && dashboardTime < 300
    };
    
    console.log('📊 Benchmark Results:');
    console.log(`  Transaction queries: ${results.transactionQueries}ms`);
    console.log(`  Account queries: ${results.accountQueries}ms`);
    console.log(`  Category queries: ${results.categoryQueries}ms`);
    console.log(`  Dashboard queries: ${results.dashboardQueries}ms`);
    console.log(`  Overall: ${results.passed ? '✅ PASSED' : '❌ FAILED'}`);
    
    return results;
  }

  public clearCache(): void {
    this.cache.clear();
    console.log('🗑️  Performance cache cleared');
  }

  public getCacheStats(): { size: number; maxSize: number; hitRate: number } {
    const cachedQueries = this.queryMetrics.filter(m => m.query.startsWith('CACHED:'));
    const hitRate = this.queryMetrics.length > 0 ? cachedQueries.length / this.queryMetrics.length : 0;
    
    return {
      size: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }

  public async createOptimizedViews(): Promise<void> {
    await this.db.initialize();
    
    // Monthly spending summary view (optimized for large datasets)
    const monthlyViewSQL = `
      CREATE VIEW IF NOT EXISTS monthly_spending_summary AS
      SELECT 
        strftime('%Y-%m', transaction_date) as month,
        category_id,
        c.name as category_name,
        SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as total_expenses,
        SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as total_income,
        COUNT(*) as transaction_count
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      WHERE transaction_date >= date('now', '-12 months')
      GROUP BY strftime('%Y-%m', transaction_date), category_id
    `;
    
    await this.db.executeSql(monthlyViewSQL, []);
    
    console.log('✅ Optimized database views created');
  }

  public async ensurePerformanceIndexes(): Promise<void> {
    await this.db.initialize();
    
    const additionalIndexes = [
      'CREATE INDEX IF NOT EXISTS idx_transactions_amount ON transactions(amount)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name)',
      'CREATE INDEX IF NOT EXISTS idx_accounts_balance ON accounts(balance)',
      'CREATE INDEX IF NOT EXISTS idx_dashboard_summary_updated ON dashboard_summary(updated_at)'
    ];
    
    for (const indexSQL of additionalIndexes) {
      await this.db.executeSql(indexSQL, []);
    }
    
    console.log('✅ Performance indexes verified');
  }
}