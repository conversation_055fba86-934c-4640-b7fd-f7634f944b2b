import { Logger } from '@/shared/utils/Logger';
import { LogAggregatorService } from './LogAggregatorService';
import { TelemetryService } from './TelemetryService';
import { PerformanceService } from './PerformanceService';
import { DatabaseService } from '@/data/database/DatabaseService';

type HookEvent = 
  | 'pre-build'
  | 'post-build' 
  | 'build-error'
  | 'test-failure'
  | 'performance-degradation'
  | 'error-threshold-exceeded';

interface HookConfig {
  enabled: boolean;
  errorThreshold: number; // errors per minute
  performanceThreshold: number; // ms
  autoAnalysis: boolean;
  notificationEnabled: boolean;
}

interface BuildAnalysis {
  timestamp: Date;
  buildStatus: 'success' | 'failure' | 'warning';
  errorCount: number;
  warningCount: number;
  performanceMetrics: {
    buildTime: number;
    bundleSize?: number;
    memoryUsage?: number;
  };
  recommendations: string[];
  logFiles: string[];
}

export class BuildHookService {
  private static instance: BuildHookService;
  private logger: Logger;
  private logAggregator: LogAggregatorService;
  private telemetry: TelemetryService;
  private performance: PerformanceService;
  private database: DatabaseService;
  
  private config: HookConfig = {
    enabled: true,
    errorThreshold: 10, // 10 errors per minute triggers analysis
    performanceThreshold: 5000, // 5 second build time threshold
    autoAnalysis: true,
    notificationEnabled: true
  };

  private buildAnalysisHistory: BuildAnalysis[] = [];

  private constructor() {
    this.logger = Logger.getInstance();
    this.logAggregator = LogAggregatorService.getInstance();
    this.telemetry = TelemetryService.getInstance();
    this.performance = PerformanceService.getInstance();
    this.database = DatabaseService.getInstance();
    
    this.setupHooks();
  }

  public static getInstance(): BuildHookService {
    if (!BuildHookService.instance) {
      BuildHookService.instance = new BuildHookService();
    }
    return BuildHookService.instance;
  }

  public configure(config: Partial<HookConfig>): void {
    this.config = { ...this.config, ...config };
    this.logger.info('Build hook configuration updated', 'build-hooks', config);
  }

  private setupHooks(): void {
    // Set up error monitoring
    this.startErrorMonitoring();
    
    // Set up performance monitoring  
    this.startPerformanceMonitoring();
    
    this.logger.info('Build hooks initialized', 'build-hooks');
  }

  private startErrorMonitoring(): void {
    // Monitor error rate and trigger analysis when threshold exceeded
    setInterval(async () => {
      if (!this.config.enabled) return;

      // Get error statistics for monitoring
      this.logger.getLogStats();
      const recentErrors = this.logger.getLogsSince(new Date(Date.now() - 60000)); // Last minute
      const errorRate = recentErrors.filter(log => log.level === 'error').length;

      if (errorRate > this.config.errorThreshold) {
        await this.triggerHook('error-threshold-exceeded', {
          errorRate,
          threshold: this.config.errorThreshold,
          recentErrors: errorRate
        });
      }
    }, 60000); // Check every minute
  }

  private startPerformanceMonitoring(): void {
    // Monitor performance metrics and trigger analysis when degraded
    setInterval(async () => {
      if (!this.config.enabled) return;

      const performanceReport = this.performance.getPerformanceReport();
      
      if (performanceReport.averageExecutionTime > this.config.performanceThreshold) {
        await this.triggerHook('performance-degradation', {
          averageTime: performanceReport.averageExecutionTime,
          threshold: this.config.performanceThreshold,
          slowQueries: performanceReport.slowQueries.length
        });
      }
    }, 300000); // Check every 5 minutes
  }

  public async triggerHook(event: HookEvent, data?: Record<string, any>): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    this.logger.info(
      `Build hook triggered: ${event}`,
      'build-hooks',
      { event, data }
    );

    switch (event) {
      case 'pre-build':
        await this.handlePreBuild(data);
        break;
      case 'post-build':
        await this.handlePostBuild(data);
        break;
      case 'build-error':
        await this.handleBuildError(data);
        break;
      case 'test-failure':
        await this.handleTestFailure(data);
        break;
      case 'performance-degradation':
        await this.handlePerformanceDegradation(data);
        break;
      case 'error-threshold-exceeded':
        await this.handleErrorThresholdExceeded(data);
        break;
    }

    // Track hook events in telemetry
    this.telemetry.trackFeatureUsage('build-hooks', event, data);
  }

  private async handlePreBuild(data?: Record<string, any>): Promise<void> {
    this.logger.info('Pre-build hook executed', 'build-hooks');
    
    // Clean up old logs
    await this.logAggregator.cleanupOldLogs();
    
    // Clear performance metrics
    this.performance.clearCache();
    
    // Check database health
    const health = await this.database.healthCheck();
    if (health.status !== 'healthy') {
      this.logger.warn('Database health check failed during pre-build', 'build-hooks', health.details);
    }
  }

  private async handlePostBuild(data?: Record<string, any>): Promise<void> {
    // Track build start time for metrics
    Date.now();
    
    try {
      // Capture build logs
      const metroLogs = await this.logAggregator.captureMetroBundlerLogs();
      const flipperLogs = await this.logAggregator.captureFlipperLogs();
      const combinedLogs = await this.logAggregator.createCombinedLogFile([metroLogs, flipperLogs]);

      // Analyze build results
      const analysis = await this.analyzeBuildResults(combinedLogs, data);
      
      // Store analysis
      this.buildAnalysisHistory.push(analysis);
      
      // Keep only last 50 analyses
      if (this.buildAnalysisHistory.length > 50) {
        this.buildAnalysisHistory = this.buildAnalysisHistory.slice(-50);
      }

      // Auto-analysis if enabled
      if (this.config.autoAnalysis && analysis.errorCount > 0) {
        await this.performAutomatedAnalysis(combinedLogs);
      }

      // Notify if enabled
      if (this.config.notificationEnabled) {
        await this.sendBuildNotification(analysis);
      }

      this.logger.info(
        'Post-build hook completed',
        'build-hooks',
        { 
          buildStatus: analysis.buildStatus,
          errorCount: analysis.errorCount,
          buildTime: analysis.performanceMetrics.buildTime 
        }
      );
    } catch (error) {
      this.logger.error(
        'Post-build hook failed',
        'build-hooks',
        { error: (error as Error).message }
      );
    }
  }

  private async handleBuildError(data?: Record<string, any>): Promise<void> {
    this.logger.error('Build error detected', 'build-hooks', data);
    
    // Capture error context
    const errorContext = {
      timestamp: new Date().toISOString(),
      errorDetails: data,
      recentLogs: this.logger.getLogs('error', undefined, 10),
      performanceMetrics: this.performance.getPerformanceReport()
    };

    // Create error report log file
    new Date().toISOString().replace(/[:.]/g, '-');
    const errorReportContent = JSON.stringify(errorContext, null, 2);
    
    this.logger.error(
      'Build error context captured',
      'build-hooks',
      { contextSize: errorReportContent.length }
    );
    
    // If auto-analysis enabled, analyze the error
    if (this.config.autoAnalysis) {
      await this.performBuildErrorAnalysis(errorContext);
    }
  }

  private async handleTestFailure(data?: Record<string, any>): Promise<void> {
    this.logger.warn('Test failure detected', 'build-hooks', data);
    
    // Capture test failure context
    const testContext = {
      timestamp: new Date().toISOString(),
      failureDetails: data,
      recentLogs: this.logger.getLogs(undefined, 'testing', 20)
    };

    // Auto-analysis for test failures
    if (this.config.autoAnalysis) {
      await this.performTestFailureAnalysis(testContext);
    }
  }

  private async handlePerformanceDegradation(data?: Record<string, any>): Promise<void> {
    this.logger.warn('Performance degradation detected', 'build-hooks', data);
    
    // Run performance optimization
    await this.performance.optimizeDatabase();
    
    // Create performance report
    const performanceReport = this.performance.getPerformanceReport();
    
    this.logger.info(
      'Performance optimization completed',
      'build-hooks',
      { 
        averageTime: performanceReport.averageExecutionTime,
        slowQueries: performanceReport.slowQueries.length 
      }
    );
  }

  private async handleErrorThresholdExceeded(data?: Record<string, any>): Promise<void> {
    this.logger.error('Error threshold exceeded', 'build-hooks', data);
    
    // Capture current system state
    const systemState = {
      timestamp: new Date().toISOString(),
      errorRate: data?.errorRate,
      threshold: data?.threshold,
      databaseHealth: await this.database.healthCheck(),
      performanceMetrics: this.performance.getPerformanceReport(),
      telemetryStats: this.telemetry.getSessionStats()
    };

    // Auto-analysis for high error rate
    if (this.config.autoAnalysis) {
      await this.performHighErrorRateAnalysis(systemState);
    }
  }

  private async analyzeBuildResults(logFile: string, buildData?: Record<string, any>): Promise<BuildAnalysis> {
    const analysis = await this.logAggregator.analyzeLogsForErrors(logFile);
    
    const buildAnalysis: BuildAnalysis = {
      timestamp: new Date(),
      buildStatus: analysis.errorPatterns.some(p => p.severity === 'high') ? 'failure' :
                  analysis.errorPatterns.some(p => p.severity === 'medium') ? 'warning' : 'success',
      errorCount: analysis.errorPatterns.reduce((sum, p) => sum + p.count, 0),
      warningCount: analysis.errorPatterns.filter(p => p.severity === 'medium').reduce((sum, p) => sum + p.count, 0),
      performanceMetrics: {
        buildTime: buildData?.buildTime || 0,
        bundleSize: buildData?.bundleSize,
        memoryUsage: buildData?.memoryUsage
      },
      recommendations: analysis.recommendations,
      logFiles: [logFile]
    };

    return buildAnalysis;
  }

  private async performAutomatedAnalysis(logFile: string): Promise<void> {
    this.logger.info('Starting automated log analysis', 'build-hooks');
    
    const progressiveAnalysis = await this.logAggregator.processLogsProgressively(logFile);
    
    // Simulate Claude Code analysis by processing batches
    for (const batch of progressiveAnalysis.batches) {
      this.logger.debug(
        `Processing log batch ${batch.batchNumber}`,
        'build-hooks',
        { lineCount: batch.lineCount }
      );
      
      // In a real implementation, this would send batch to Claude Code for analysis
      // For now, we'll simulate analysis results
      await this.simulateClaudeCodeAnalysis(batch.content, batch.batchNumber);
    }

    this.logger.info(
      'Automated analysis completed',
      'build-hooks',
      { 
        totalBatches: progressiveAnalysis.batches.length,
        totalLines: progressiveAnalysis.totalLines,
        processingComplete: progressiveAnalysis.processingComplete 
      }
    );
  }

  private async simulateClaudeCodeAnalysis(content: string, batchNumber: number): Promise<void> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Simulate finding issues in content
    const issues = [];
    if (content.includes('ERROR')) {
      issues.push('Critical error pattern detected');
    }
    if (content.includes('timeout')) {
      issues.push('Network timeout issues found');
    }
    if (content.includes('slow')) {
      issues.push('Performance bottleneck identified');
    }

    if (issues.length > 0) {
      this.logger.info(
        `Claude Code analysis - Batch ${batchNumber}`,
        'claude-analysis',
        { issues, batchNumber }
      );
    }
  }

  private async performBuildErrorAnalysis(errorContext: any): Promise<void> {
    this.logger.info('Performing build error analysis', 'build-hooks');
    
    // Analyze error patterns and suggest fixes
    const errorAnalysis = {
      errorType: this.classifyBuildError(errorContext),
      suggestedFixes: this.generateErrorFixes(errorContext),
      relatedIssues: this.findRelatedIssues(errorContext)
    };

    this.logger.info(
      'Build error analysis completed',
      'build-hooks',
      errorAnalysis
    );
  }

  private async performTestFailureAnalysis(testContext: any): Promise<void> {
    this.logger.info('Performing test failure analysis', 'build-hooks');
    
    // Analyze test failures and suggest fixes
    const testAnalysis = {
      failureType: this.classifyTestFailure(testContext),
      suggestedFixes: this.generateTestFixes(testContext),
      flakyTestDetection: this.detectFlakyTests(testContext)
    };

    this.logger.info(
      'Test failure analysis completed', 
      'build-hooks',
      testAnalysis
    );
  }

  private async performHighErrorRateAnalysis(systemState: any): Promise<void> {
    this.logger.info('Performing high error rate analysis', 'build-hooks');
    
    const errorAnalysis = {
      rootCause: this.identifyErrorRootCause(systemState),
      systemImpact: this.assessSystemImpact(systemState),
      remediationSteps: this.generateRemediationSteps(systemState)
    };

    this.logger.warn(
      'High error rate analysis completed',
      'build-hooks', 
      errorAnalysis
    );
  }

  private async sendBuildNotification(analysis: BuildAnalysis): Promise<void> {
    // In a real implementation, this would send notifications via email, Slack, etc.
    const notification = {
      title: `Build ${analysis.buildStatus.toUpperCase()}`,
      message: `Errors: ${analysis.errorCount}, Warnings: ${analysis.warningCount}`,
      timestamp: analysis.timestamp,
      recommendations: analysis.recommendations.slice(0, 3) // Top 3 recommendations
    };

    this.logger.info('Build notification sent', 'build-hooks', notification);
  }

  // Helper methods for analysis
  private classifyBuildError(errorContext: any): string {
    // Simplified error classification
    if (errorContext.errorDetails?.message?.includes('module')) return 'MODULE_RESOLUTION';
    if (errorContext.errorDetails?.message?.includes('syntax')) return 'SYNTAX_ERROR';
    if (errorContext.errorDetails?.message?.includes('memory')) return 'MEMORY_ERROR';
    return 'UNKNOWN_BUILD_ERROR';
  }

  private generateErrorFixes(errorContext: any): string[] {
    // Simplified fix suggestions
    return [
      'Check module dependencies in package.json',
      'Clear node_modules and reinstall',
      'Verify TypeScript configuration'
    ];
  }

  private findRelatedIssues(errorContext: any): string[] {
    // Simplified related issue detection
    return [
      'Similar error occurred 3 times in last week',
      'Related to recent dependency update'
    ];
  }

  private classifyTestFailure(testContext: any): string {
    return 'ASSERTION_FAILURE'; // Simplified
  }

  private generateTestFixes(testContext: any): string[] {
    return ['Review test assertions', 'Check test data setup'];
  }

  private detectFlakyTests(testContext: any): string[] {
    return ['TestA failed 3/10 runs', 'TestB has timing issues'];
  }

  private identifyErrorRootCause(systemState: any): string {
    return 'Database connection pool exhaustion'; // Simplified
  }

  private assessSystemImpact(systemState: any): string {
    return 'High - User transactions affected'; // Simplified
  }

  private generateRemediationSteps(systemState: any): string[] {
    return [
      'Increase database connection pool size',
      'Add circuit breaker pattern',
      'Implement request throttling'
    ];
  }

  public getBuildHistory(): BuildAnalysis[] {
    return [...this.buildAnalysisHistory];
  }

  public getLastBuildAnalysis(): BuildAnalysis | null {
    return this.buildAnalysisHistory.length > 0 
      ? this.buildAnalysisHistory[this.buildAnalysisHistory.length - 1]
      : null;
  }

  public getBuildStats(): {
    totalBuilds: number;
    successRate: number;
    averageErrorCount: number;
    averageBuildTime: number;
  } {
    const total = this.buildAnalysisHistory.length;
    
    if (total === 0) {
      return { totalBuilds: 0, successRate: 0, averageErrorCount: 0, averageBuildTime: 0 };
    }

    const successful = this.buildAnalysisHistory.filter(b => b.buildStatus === 'success').length;
    const totalErrors = this.buildAnalysisHistory.reduce((sum, b) => sum + b.errorCount, 0);
    const totalBuildTime = this.buildAnalysisHistory.reduce((sum, b) => sum + b.performanceMetrics.buildTime, 0);

    return {
      totalBuilds: total,
      successRate: Math.round((successful / total) * 100),
      averageErrorCount: Math.round(totalErrors / total),
      averageBuildTime: Math.round(totalBuildTime / total)
    };
  }
}