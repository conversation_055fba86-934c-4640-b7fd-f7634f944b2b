import { Transaction, TransactionType, TransactionMetadata, Account } from '@/shared/types';
import { TransactionRepository } from '@/data/repositories/TransactionRepository';
import { AccountRepository } from '@/data/repositories/AccountRepository';
import { CategoryRepository } from '@/data/repositories/CategoryRepository';
import { DatabaseService } from '@/data/database/DatabaseService';
import { validateTransactionType, validateTransactionCompatibility } from '@/shared/utils/transactionUtils';
import { validateTransactionMetadata } from '@/shared/utils/transactionMetadataValidation';
import { SMSParsingService, ParsedTransaction } from './SMSParsingService';

export interface TransactionValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface TransactionFilters {
  accountId?: number;
  categoryId?: number;
  transactionType?: TransactionType;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  searchQuery?: string;
}

export interface TransactionSearchResult {
  transactions: Transaction[];
  totalCount: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface CategorySuggestion {
  categoryId: number;
  categoryName: string;
  confidence: number;
  reason: 'keyword_match' | 'frequent_pattern' | 'amount_pattern' | 'ml_suggestion';
}

export class TransactionService {
  private transactionRepository: TransactionRepository;
  private accountRepository: AccountRepository;
  private categoryRepository: CategoryRepository;
  private databaseService: DatabaseService;
  private smsParsingService: SMSParsingService;

  constructor() {
    this.transactionRepository = new TransactionRepository();
    this.accountRepository = new AccountRepository();
    this.categoryRepository = new CategoryRepository();
    this.databaseService = DatabaseService.getInstance();
    this.smsParsingService = new SMSParsingService();
  }

  /**
   * Wait for database to be initialized
   */
  private async waitForDatabase(): Promise<void> {
    if (!this.databaseService.isInitialized()) {
      await this.databaseService.waitForInitialization();
    }
  }

  /**
   * Create a new transaction with validation and balance update
   */
  async createTransaction(transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'hash'>): Promise<Transaction> {
    await this.waitForDatabase();

    // Validate transaction data
    const validation = await this.validateTransactionData(transactionData);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Check for duplicate transactions
    const hashData = `${transactionData.account_id}-${transactionData.amount}-${transactionData.description}-${transactionData.transaction_date}`;
    const hash = this.generateTransactionHash(hashData);
    
    const duplicates = await this.transactionRepository.findDuplicates(hash);
    if (duplicates.length > 0) {
      throw new Error('Duplicate transaction detected');
    }

    try {
      // Create the transaction
      const transaction = await this.transactionRepository.create({
        ...transactionData,
        sync_status: 'local'
      });

      // Update account balance
      await this.updateAccountBalance(transactionData.account_id);

      return transaction;
    } catch (error) {
      throw new Error(`Failed to create transaction: ${error}`);
    }
  }

  /**
   * Update an existing transaction
   */
  async updateTransaction(id: number, updates: Partial<Transaction>): Promise<Transaction> {
    const existingTransaction = await this.transactionRepository.findById(id);
    if (!existingTransaction) {
      throw new Error('Transaction not found');
    }

    // Store old account ID for balance recalculation
    const oldAccountId = existingTransaction.account_id;

    // Validate updates
    const mergedData = { ...existingTransaction, ...updates };
    const validation = await this.validateTransactionData(mergedData);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    try {
      const updatedTransaction = await this.transactionRepository.update(id, {
        ...updates,
        sync_status: 'local'
      });

      // Update account balance(s)
      await this.updateAccountBalance(oldAccountId);
      if (updates.account_id && updates.account_id !== oldAccountId) {
        await this.updateAccountBalance(updates.account_id);
      }

      return updatedTransaction;
    } catch (error) {
      throw new Error(`Failed to update transaction: ${error}`);
    }
  }

  /**
   * Delete a transaction
   */
  async deleteTransaction(id: number): Promise<boolean> {
    await this.waitForDatabase();
    
    console.log(`Attempting to delete transaction with id: ${id}`);
    
    const transaction = await this.transactionRepository.findById(id);
    if (!transaction) {
      console.error(`Transaction with id ${id} not found`);
      throw new Error('Transaction not found');
    }

    console.log(`Found transaction to delete:`, transaction);

    try {
      const success = await this.transactionRepository.delete(id);
      console.log(`Transaction repository delete result: ${success}`);
      
      if (success) {
        console.log(`Successfully deleted transaction ${id}, updating account balance for account ${transaction.account_id}`);
        // Update account balance
        await this.updateAccountBalance(transaction.account_id);
        console.log(`Account balance updated after transaction deletion`);
      } else {
        console.error(`Transaction repository returned false for delete operation`);
        throw new Error('Failed to delete transaction from database');
      }

      return success;
    } catch (error) {
      console.error(`Error in deleteTransaction:`, error);
      throw new Error(`Failed to delete transaction: ${error}`);
    }
  }

  /**
   * Get transactions with filters and pagination
   */
  async getTransactions(
    filters: TransactionFilters = {},
    page: number = 1,
    limit: number = 50
  ): Promise<TransactionSearchResult> {
    await this.waitForDatabase();

    try {
      // Start building the query
      let whereClause = '';
      const params: any[] = [];
      const conditions: string[] = [];

      // Apply filters
      if (filters.accountId) {
        conditions.push('account_id = ?');
        params.push(filters.accountId);
      }

      if (filters.categoryId) {
        conditions.push('category_id = ?');
        params.push(filters.categoryId);
      }

      if (filters.transactionType) {
        conditions.push('transaction_type = ?');
        params.push(filters.transactionType);
      }

      if (filters.startDate && filters.endDate) {
        conditions.push('transaction_date BETWEEN ? AND ?');
        params.push(filters.startDate, filters.endDate);
      }

      if (filters.minAmount !== undefined) {
        conditions.push('amount >= ?');
        params.push(filters.minAmount);
      }

      if (filters.maxAmount !== undefined) {
        conditions.push('amount <= ?');
        params.push(filters.maxAmount);
      }

      if (filters.searchQuery) {
        conditions.push('description LIKE ?');
        params.push(`%${filters.searchQuery}%`);
      }

      if (conditions.length > 0) {
        whereClause = 'WHERE ' + conditions.join(' AND ');
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM transactions ${whereClause}`;
      const [countResult] = await this.databaseService.executeSql(countQuery, params);
      const totalCount = countResult.rows.item(0).total;

      // Get paginated results
      const offset = (page - 1) * limit;
      const dataQuery = `SELECT * FROM transactions ${whereClause} ORDER BY transaction_date DESC, created_at DESC LIMIT ? OFFSET ?`;
      const [dataResult] = await this.databaseService.executeSql(
        dataQuery, 
        [...params, limit, offset]
      );

      const transactions: Transaction[] = [];
      for (let i = 0; i < dataResult.rows.length; i++) {
        transactions.push(dataResult.rows.item(i) as Transaction);
      }

      return {
        transactions,
        totalCount,
        page,
        limit,
        hasMore: offset + transactions.length < totalCount
      };
    } catch (error) {
      throw new Error(`Failed to fetch transactions: ${error}`);
    }
  }

  /**
   * Search transactions by description
   */
  async searchTransactions(query: string, limit: number = 20): Promise<Transaction[]> {
    if (!query || query.trim().length < 2) {
      return [];
    }

    try {
      const [result] = await this.databaseService.executeSql(
        `SELECT * FROM transactions 
         WHERE description LIKE ? 
         ORDER BY transaction_date DESC 
         LIMIT ?`,
        [`%${query.trim()}%`, limit]
      );

      const transactions: Transaction[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        transactions.push(result.rows.item(i) as Transaction);
      }

      return transactions;
    } catch (error) {
      throw new Error(`Failed to search transactions: ${error}`);
    }
  }

  /**
   * Suggest categories based on transaction description
   */
  async suggestCategories(description: string, amount?: number): Promise<CategorySuggestion[]> {
    if (!description || description.trim().length < 2) {
      return [];
    }

    try {
      const categories = await this.categoryRepository.findAll();
      const suggestions: CategorySuggestion[] = [];
      const descLower = description.toLowerCase().trim();

      // 1. Keyword matching against category names
      for (const category of categories) {
        const categoryNameLower = category.name.toLowerCase();
        
        // Exact match or category name contains description words
        const words = descLower.split(' ');
        const categoryWords = categoryNameLower.split(' ');
        
        let matchScore = 0;
        for (const word of words) {
          if (word.length > 2 && categoryWords.some(cw => cw.includes(word) || word.includes(cw))) {
            matchScore += 1;
          }
        }

        if (matchScore > 0) {
          suggestions.push({
            categoryId: category.id,
            categoryName: category.name,
            confidence: Math.min(0.8, matchScore / words.length),
            reason: 'keyword_match'
          });
        }
      }

      // 2. Historical pattern matching
      const [historyResult] = await this.databaseService.executeSql(
        `SELECT category_id, COUNT(*) as frequency 
         FROM transactions 
         WHERE description LIKE ? AND category_id IS NOT NULL 
         GROUP BY category_id 
         ORDER BY frequency DESC 
         LIMIT 3`,
        [`%${descLower}%`]
      );

      for (let i = 0; i < historyResult.rows.length; i++) {
        const row = historyResult.rows.item(i);
        const category = categories.find(c => c.id === row.category_id);
        
        if (category && !suggestions.find(s => s.categoryId === category.id)) {
          suggestions.push({
            categoryId: category.id,
            categoryName: category.name,
            confidence: Math.min(0.9, 0.5 + (row.frequency * 0.1)),
            reason: 'frequent_pattern'
          });
        }
      }

      // 3. Amount-based patterns (basic heuristics)
      if (amount && amount > 0) {
        const amountCategories = await this.getAmountBasedSuggestions(amount, categories);
        for (const suggestion of amountCategories) {
          if (!suggestions.find(s => s.categoryId === suggestion.categoryId)) {
            suggestions.push(suggestion);
          }
        }
      }

      // Sort by confidence and return top 5
      return suggestions
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 5);

    } catch (error) {
      console.error('Failed to suggest categories:', error);
      return [];
    }
  }

  /**
   * Validate transaction data
   */
  private async validateTransactionData(data: Partial<Transaction>): Promise<TransactionValidationResult> {
    const errors: string[] = [];

    // Amount validation
    if (data.amount === undefined || data.amount === null || typeof data.amount !== 'number') {
      errors.push('Valid amount is required');
    } else if (data.amount === 0) {
      errors.push('Amount cannot be zero');
    } else if (data.amount < -999999999.99 || data.amount > 999999999.99) {
      errors.push('Amount must be between -999,999,999.99 and 999,999,999.99');
    }

    // Description validation
    if (!data.description || data.description.trim().length === 0) {
      errors.push('Transaction description is required');
    } else if (data.description.trim().length < 3) {
      errors.push('Description must be at least 3 characters long');
    } else if (data.description.trim().length > 200) {
      errors.push('Description must be less than 200 characters');
    }

    // Account validation
    if (!data.account_id) {
      errors.push('Account ID is required');
    } else {
      const account = await this.accountRepository.findById(data.account_id);
      if (!account) {
        errors.push('Invalid account ID');
      } else if (!account.is_active) {
        errors.push('Cannot create transaction for inactive account');
      }
    }

    // Category validation (if provided)
    if (data.category_id) {
      const category = await this.categoryRepository.findById(data.category_id);
      if (!category) {
        errors.push('Invalid category ID');
      }
    }

    // Transaction type validation
    if (!data.transaction_type) {
      errors.push('Transaction type is required');
    } else {
      // Validate transaction type against account type if account exists
      if (data.account_id) {
        const account = await this.accountRepository.findById(data.account_id);
        if (account) {
          const validation = validateTransactionCompatibility(
            data.transaction_type,
            data.amount || 0,
            account.type
          );
          if (!validation.isValid) {
            errors.push(validation.error || 'Invalid transaction type for this account');
          }
        }
      }
    }

    // Date validation
    if (!data.transaction_date) {
      errors.push('Transaction date is required');
    } else {
      const transactionDate = new Date(data.transaction_date);
      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today
      
      if (isNaN(transactionDate.getTime())) {
        errors.push('Invalid transaction date format');
      } else if (transactionDate > today) {
        errors.push('Transaction date cannot be in the future');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Update account balance based on transactions
   */
  private async updateAccountBalance(accountId: number): Promise<void> {
    try {
      // Get current account to preserve its initial balance
      const account = await this.accountRepository.findById(accountId);
      if (!account) {
        console.error(`Account ${accountId} not found for balance update`);
        return;
      }

      // Check if we have stored the account's initial balance
      // The table uses 'key' and 'value' columns (not setting_key/setting_value)
      const [initialBalanceResult] = await this.databaseService.executeSql(
        'SELECT value FROM user_settings WHERE key = ?',
        [`account_${accountId}_initial_balance`]
      );
      
      let initialBalance: number;
      
      if (initialBalanceResult.rows.length > 0) {
        // We have a stored initial balance
        initialBalance = parseFloat(initialBalanceResult.rows.item(0).value);
      } else {
        // First time - store current account balance as initial balance
        initialBalance = account.balance;
        const now = new Date().toISOString();
        
        await this.databaseService.executeSql(
          'INSERT OR REPLACE INTO user_settings (key, value, created_at, updated_at) VALUES (?, ?, ?, ?)',
          [`account_${accountId}_initial_balance`, initialBalance.toString(), now, now]
        );
      }

      // Calculate net transaction effects
      const [result] = await this.databaseService.executeSql(
        `SELECT 
          COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as total_income,
          COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as total_expense
         FROM transactions WHERE account_id = ?`,
        [accountId]
      );

      const row = result.rows.item(0);
      const netTransactionAmount = row.total_income - row.total_expense;
      
      // Calculate correct balance: initial balance + net transaction effects
      const newBalance = initialBalance + netTransactionAmount;
      
      // Update the account balance
      await this.accountRepository.updateBalance(accountId, newBalance, 'set');
      
      console.log(`Updated account ${accountId} balance: ${initialBalance} + ${netTransactionAmount} = ${newBalance}`);
    } catch (error) {
      console.error(`Failed to update account balance for account ${accountId}:`, error);
      throw error; // Re-throw to let caller handle appropriately
    }
  }

  /**
   * Generate transaction hash for duplicate detection
   */
  private generateTransactionHash(data: string): string {
    // Simple hash function - in production, use a proper crypto hash
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * Get amount-based category suggestions
   */
  private async getAmountBasedSuggestions(amount: number, categories: any[]): Promise<CategorySuggestion[]> {
    const suggestions: CategorySuggestion[] = [];

    try {
      // Get average amounts per category
      const [result] = await this.databaseService.executeSql(
        `SELECT category_id, AVG(amount) as avg_amount, COUNT(*) as count
         FROM transactions 
         WHERE category_id IS NOT NULL AND ABS(amount - ?) <= (? * 0.2)
         GROUP BY category_id 
         HAVING count >= 3
         ORDER BY count DESC
         LIMIT 2`,
        [amount, amount]
      );

      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        const category = categories.find(c => c.id === row.category_id);
        
        if (category) {
          suggestions.push({
            categoryId: category.id,
            categoryName: category.name,
            confidence: 0.6,
            reason: 'amount_pattern'
          });
        }
      }
    } catch (error) {
      console.error('Failed to get amount-based suggestions:', error);
    }

    return suggestions;
  }

  /**
   * Get transaction statistics
   */
  // ===== ENHANCED TRANSACTION METHODS =====

  /**
   * Creates transaction with metadata support
   */
  async createTransactionWithMetadata(
    transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'hash'>,
    metadata?: TransactionMetadata
  ): Promise<Transaction> {
    await this.waitForDatabase();

    // Validate metadata if provided
    if (metadata) {
      const metadataValidation = validateTransactionMetadata(metadata);
      if (!metadataValidation.isValid) {
        throw new Error(`Metadata validation failed: ${metadataValidation.errors.join(', ')}`);
      }
    }

    // Use the repository's createWithMetadata method
    const transaction = await this.transactionRepository.createWithMetadata(transactionData, metadata);

    // Update account balance
    await this.updateAccountBalance(transactionData.account_id);

    return transaction;
  }

  /**
   * Creates transaction from SMS text
   */
  async createTransactionFromSMS(
    smsText: string,
    accountId?: number
  ): Promise<Transaction | null> {
    try {
      // Parse SMS to extract transaction details
      const parsedTransaction = await this.smsParsingService.parseTransactionFromSMS(smsText);
      
      if (!parsedTransaction) {
        return null; // Could not parse SMS
      }

      // If accountId provided, use it; otherwise try to find account by type
      let targetAccountId = accountId;
      if (!targetAccountId && parsedTransaction.suggestedAccountType) {
        // Find account by type
        const accounts = await this.accountRepository.findByType(parsedTransaction.suggestedAccountType);
        if (accounts.length > 0) {
          targetAccountId = accounts[0].id; // Use first account of the type
        }
      }

      if (!targetAccountId) {
        throw new Error('No suitable account found for SMS transaction');
      }

      // Create transaction data
      const transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'hash'> = {
        account_id: targetAccountId,
        amount: parsedTransaction.amount,
        description: parsedTransaction.description,
        transaction_type: parsedTransaction.transactionType,
        transaction_date: parsedTransaction.transactionDate.toISOString().split('T')[0],
        category_id: null, // Will be suggested later
        sms_source: smsText,
        confidence_score: parsedTransaction.confidence,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local'
      };

      // Create transaction with metadata
      return await this.createTransactionWithMetadata(transactionData, parsedTransaction.metadata);

    } catch (error) {
      console.error('Error creating transaction from SMS:', error);
      throw new Error(`Failed to create transaction from SMS: ${error}`);
    }
  }

  /**
   * Updates transaction metadata
   */
  async updateTransactionMetadata(
    transactionId: number,
    metadata: Partial<TransactionMetadata>
  ): Promise<void> {
    // Validate metadata
    const metadataValidation = validateTransactionMetadata(metadata);
    if (!metadataValidation.isValid) {
      throw new Error(`Metadata validation failed: ${metadataValidation.errors.join(', ')}`);
    }

    await this.transactionRepository.updateMetadata(transactionId, metadata);
  }

  /**
   * Gets transaction with its metadata
   */
  async getTransactionWithMetadata(transactionId: number): Promise<(Transaction & { metadata?: TransactionMetadata }) | null> {
    return await this.transactionRepository.findByIdWithMetadata(transactionId);
  }

  /**
   * Gets transactions with metadata for an account
   */
  async getTransactionsWithMetadata(
    accountId: number, 
    limit = 50, 
    offset = 0
  ): Promise<(Transaction & { metadata?: TransactionMetadata })[]> {
    return await this.transactionRepository.findByAccountWithMetadata(accountId, limit, offset);
  }

  /**
   * Validates transaction type against account type
   */
  async validateTransactionType(
    transactionType: TransactionType,
    accountId: number
  ): Promise<boolean> {
    const account = await this.accountRepository.findById(accountId);
    if (!account) {
      return false;
    }

    return validateTransactionType(transactionType, account.type);
  }

  /**
   * Splits loan EMI transaction into principal and interest components
   */
  async splitLoanEMITransaction(
    transactionId: number,
    principalAmount: number,
    interestAmount: number
  ): Promise<void> {
    const transaction = await this.getTransactionWithMetadata(transactionId);
    if (!transaction) {
      throw new Error('Transaction not found');
    }

    if (transaction.transaction_type !== 'loan_emi') {
      throw new Error('Can only split loan EMI transactions');
    }

    // Update the metadata with component breakdown
    const updatedMetadata: Partial<TransactionMetadata> = {
      ...transaction.metadata,
      loanDetails: {
        ...transaction.metadata?.loanDetails,
        principalComponent: principalAmount,
        interestComponent: interestAmount,
        feeComponent: transaction.amount - principalAmount - interestAmount
      },
      lastUpdated: new Date().toISOString()
    };

    await this.updateTransactionMetadata(transactionId, updatedMetadata);
  }

  /**
   * Auto-detects transaction type based on account type and description
   */
  async autoDetectTransactionType(
    accountType: Account['type'],
    description: string,
    amount: number
  ): Promise<TransactionType> {
    const descLower = description.toLowerCase();

    // Account-specific detection
    switch (accountType) {
      case 'credit':
        // Credit card transaction detection
        if (descLower.includes('payment') || descLower.includes('credited')) {
          return 'credit_payment';
        }
        if (descLower.includes('interest') || descLower.includes('finance charge')) {
          return 'credit_interest';
        }
        if (descLower.includes('fee') || descLower.includes('annual')) {
          return 'credit_fee';
        }
        return 'credit_charge'; // Default for credit cards

      case 'loan':
        // Loan transaction detection
        if (descLower.includes('prepayment') || descLower.includes('prepaid')) {
          return 'loan_prepayment';
        }
        if (descLower.includes('interest')) {
          return 'loan_interest';
        }
        if (descLower.includes('fee') || descLower.includes('processing')) {
          return 'loan_fee';
        }
        if (descLower.includes('principal')) {
          return 'loan_principal';
        }
        return 'loan_emi'; // Default for loans

      default:
        // Traditional account types
        if (amount > 0) {
          if (descLower.includes('salary') || descLower.includes('income') || descLower.includes('credit')) {
            return 'income';
          }
          if (descLower.includes('transfer') || descLower.includes('from')) {
            return 'transfer';
          }
          return 'income'; // Default positive amount
        } else {
          if (descLower.includes('transfer') || descLower.includes('to')) {
            return 'transfer';
          }
          return 'expense'; // Default negative amount
        }
    }
  }

  /**
   * Processes batch SMS messages for transaction creation
   */
  async processBatchSMS(
    smsMessages: string[],
    defaultAccountId?: number
  ): Promise<{
    successful: Transaction[];
    failed: { sms: string; error: string }[];
    totalProcessed: number;
  }> {
    const successful: Transaction[] = [];
    const failed: { sms: string; error: string }[] = [];

    for (const smsText of smsMessages) {
      try {
        const transaction = await this.createTransactionFromSMS(smsText, defaultAccountId);
        if (transaction) {
          successful.push(transaction);
        } else {
          failed.push({ sms: smsText, error: 'Could not parse SMS message' });
        }
      } catch (error) {
        failed.push({ 
          sms: smsText, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    return {
      successful,
      failed,
      totalProcessed: smsMessages.length
    };
  }

  /**
   * Get transaction statistics
   */
  async getTransactionStats(accountId?: number): Promise<{
    totalIncome: number;
    totalExpense: number;
    netBalance: number;
    transactionCount: number;
  }> {
    try {
      let sql = `
        SELECT 
          COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as total_income,
          COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as total_expense,
          COUNT(*) as transaction_count
        FROM transactions
      `;
      
      const params: any[] = [];
      if (accountId) {
        sql += ' WHERE account_id = ?';
        params.push(accountId);
      }

      const [result] = await this.databaseService.executeSql(sql, params);
      const row = result.rows.item(0);

      return {
        totalIncome: row.total_income || 0,
        totalExpense: row.total_expense || 0,
        netBalance: (row.total_income || 0) - (row.total_expense || 0),
        transactionCount: row.transaction_count || 0
      };
    } catch (error) {
      throw new Error(`Failed to get transaction statistics: ${error}`);
    }
  }
}