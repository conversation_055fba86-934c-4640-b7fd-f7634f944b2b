import { Category } from '@/shared/types';
import { CategoryRepository } from '@/data/repositories/CategoryRepository';
import { TransactionRepository } from '@/data/repositories/TransactionRepository';

export interface CategoryStats {
  totalTransactions: number;
  totalAmount: number;
  averageAmount: number;
  monthlyTrend: number[];
  lastTransaction: Date | null;
  budgetUtilization?: number;
}

export interface CategoryTree {
  category: Category;
  children: CategoryTree[];
  totalAmount: number;
  transactionCount: number;
}

export interface CategoryValidationResult {
  isValid: boolean;
  errors: string[];
}

export class CategoryService {
  private categoryRepository: CategoryRepository;
  private transactionRepository: TransactionRepository;

  constructor() {
    this.categoryRepository = new CategoryRepository();
    this.transactionRepository = new TransactionRepository();
  }

  // Category CRUD operations
  async getAllCategories(): Promise<Category[]> {
    return this.categoryRepository.findAll();
  }

  async getCategoryById(id: number): Promise<Category | null> {
    return this.categoryRepository.findById(id);
  }

  async getCategoriesByType(type: Category['category_type']): Promise<Category[]> {
    return this.categoryRepository.findByType(type);
  }

  async getSubcategories(parentId: number): Promise<Category[]> {
    return this.categoryRepository.findByParent(parentId);
  }

  async getRootCategories(): Promise<Category[]> {
    return this.categoryRepository.findByParent(null);
  }

  async createCategory(categoryData: Omit<Category, 'id' | 'created_at'>): Promise<Category> {
    // Validate category data
    const validation = await this.validateCategoryData(categoryData);
    if (!validation.isValid) {
      throw new Error(`Category validation failed: ${validation.errors.join(', ')}`);
    }

    return this.categoryRepository.create(categoryData);
  }

  async updateCategory(id: number, updates: Partial<Category>): Promise<Category> {
    // Validate updates
    if (updates.parent_id !== undefined) {
      const hierarchyValidation = await this.validateCategoryHierarchy(id, updates.parent_id);
      if (!hierarchyValidation.isValid) {
        throw new Error(`Category hierarchy validation failed: ${hierarchyValidation.errors.join(', ')}`);
      }
    }

    return this.categoryRepository.update(id, updates);
  }

  async deleteCategory(id: number, reassignToId?: number): Promise<void> {
    const category = await this.categoryRepository.findById(id);
    if (!category) {
      throw new Error(`Category with id ${id} not found`);
    }

    // Prevent deletion of system categories
    if (category.is_system) {
      throw new Error('System categories cannot be deleted');
    }

    // Check for child categories
    const children = await this.categoryRepository.findByParent(id);
    if (children.length > 0 && !reassignToId) {
      throw new Error('Cannot delete category with child categories. Please reassign or delete children first.');
    }

    // Handle transaction reassignment
    if (reassignToId) {
      await this.reassignTransactions(id, reassignToId);
    }

    // Reassign child categories if needed
    if (children.length > 0 && reassignToId) {
      for (const child of children) {
        await this.categoryRepository.update(child.id, { parent_id: reassignToId });
      }
    }

    await this.categoryRepository.delete(id);
  }

  async archiveCategory(id: number): Promise<Category> {
    // For future implementation - we could add an archived field
    // For now, we'll use sync_status to indicate archived
    return this.categoryRepository.update(id, { sync_status: 'local' });
  }

  // Category merge functionality
  async mergeCategories(sourceIds: number[], targetId: number): Promise<void> {
    const targetCategory = await this.categoryRepository.findById(targetId);
    if (!targetCategory) {
      throw new Error(`Target category with id ${targetId} not found`);
    }

    // Validate source categories exist
    const sourceCategories = await Promise.all(
      sourceIds.map(id => this.categoryRepository.findById(id))
    );

    const missingCategories = sourceCategories
      .map((cat, index) => cat ? null : sourceIds[index])
      .filter(id => id !== null);

    if (missingCategories.length > 0) {
      throw new Error(`Source categories not found: ${missingCategories.join(', ')}`);
    }

    // Reassign all transactions from source categories to target category
    for (const sourceId of sourceIds) {
      await this.reassignTransactions(sourceId, targetId);
    }

    // Move child categories to target category
    for (const sourceId of sourceIds) {
      const children = await this.categoryRepository.findByParent(sourceId);
      for (const child of children) {
        await this.categoryRepository.update(child.id, { parent_id: targetId });
      }
    }

    // Delete source categories
    for (const sourceId of sourceIds) {
      await this.categoryRepository.delete(sourceId);
    }
  }

  // Category statistics and analytics
  async getCategoryStats(categoryId: number, startDate?: Date, endDate?: Date): Promise<CategoryStats> {
    const category = await this.categoryRepository.findById(categoryId);
    if (!category) {
      throw new Error(`Category with id ${categoryId} not found`);
    }

    // Get all transactions for this category (including subcategories)
    const categoryIds = await this.getCategoryAndDescendantIds(categoryId);
    
    let dateFilter = '';
    const params: any[] = [];
    
    if (startDate || endDate) {
      if (startDate && endDate) {
        dateFilter = ' AND transaction_date BETWEEN ? AND ?';
        params.push(startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]);
      } else if (startDate) {
        dateFilter = ' AND transaction_date >= ?';
        params.push(startDate.toISOString().split('T')[0]);
      } else if (endDate) {
        dateFilter = ' AND transaction_date <= ?';
        params.push(endDate.toISOString().split('T')[0]);
      }
    }

    // Get transaction statistics
    const statsQuery = `
      SELECT 
        COUNT(*) as totalTransactions,
        SUM(amount) as totalAmount,
        AVG(amount) as averageAmount,
        MAX(transaction_date) as lastTransaction
      FROM transactions 
      WHERE category_id IN (${categoryIds.map(() => '?').join(',')})${dateFilter}
    `;

    const [result] = await this.transactionRepository['db'].executeSql(statsQuery, [...categoryIds, ...params]);
    const stats = result.rows.item(0);

    // Get monthly trend (last 12 months)
    const monthlyTrendQuery = `
      SELECT 
        strftime('%Y-%m', transaction_date) as month,
        SUM(amount) as monthlyAmount
      FROM transactions 
      WHERE category_id IN (${categoryIds.map(() => '?').join(',')})
        AND transaction_date >= date('now', '-12 months')
      GROUP BY strftime('%Y-%m', transaction_date)
      ORDER BY month
    `;

    const [trendResult] = await this.transactionRepository['db'].executeSql(monthlyTrendQuery, categoryIds);
    const monthlyTrend: number[] = [];
    
    for (let i = 0; i < trendResult.rows.length; i++) {
      monthlyTrend.push(trendResult.rows.item(i).monthlyAmount || 0);
    }

    return {
      totalTransactions: stats.totalTransactions || 0,
      totalAmount: stats.totalAmount || 0,
      averageAmount: stats.averageAmount || 0,
      monthlyTrend,
      lastTransaction: stats.lastTransaction ? new Date(stats.lastTransaction) : null,
    };
  }

  async getCategoryHierarchyWithStats(): Promise<CategoryTree[]> {
    const rootCategories = await this.getRootCategories();
    
    const buildTree = async (category: Category): Promise<CategoryTree> => {
      const children = await this.getSubcategories(category.id);
      const stats = await this.getCategoryStats(category.id);
      
      const childTrees = await Promise.all(children.map(child => buildTree(child)));
      
      return {
        category,
        children: childTrees,
        totalAmount: stats.totalAmount,
        transactionCount: stats.totalTransactions,
      };
    };

    return Promise.all(rootCategories.map(cat => buildTree(cat)));
  }

  async getTopCategories(type: Category['category_type'], limit: number = 5): Promise<(Category & {usage_count: number})[]> {
    return this.categoryRepository.getTopCategories(type, limit);
  }

  // Category search and filtering
  async searchCategories(query: string): Promise<Category[]> {
    const allCategories = await this.categoryRepository.findAll();
    const lowerQuery = query.toLowerCase();
    
    return allCategories.filter(category =>
      category.name.toLowerCase().includes(lowerQuery)
    );
  }

  // System category initialization
  async initializeSystemCategories(): Promise<void> {
    const { SystemCategoriesService } = require('./SystemCategoriesService');
    const systemCategoriesService = new SystemCategoriesService();
    await systemCategoriesService.initializeSystemCategories();
  }

  // Validation methods
  private async validateCategoryData(categoryData: Omit<Category, 'id' | 'created_at'>): Promise<CategoryValidationResult> {
    const errors: string[] = [];

    // Check name is provided and valid
    if (!categoryData.name || categoryData.name.trim().length === 0) {
      errors.push('Category name is required');
    } else if (categoryData.name.trim().length > 50) {
      errors.push('Category name must be 50 characters or less');
    }

    // Check for name uniqueness within same parent
    if (categoryData.name) {
      const siblings = await this.categoryRepository.findByParent(categoryData.parent_id);
      const nameExists = siblings.some(cat => 
        cat.name.toLowerCase() === categoryData.name.toLowerCase()
      );
      
      if (nameExists) {
        errors.push('Category name must be unique within the same parent level');
      }
    }

    // Validate category type
    if (!['income', 'expense'].includes(categoryData.category_type)) {
      errors.push('Category type must be either income or expense');
    }

    // Validate parent hierarchy depth
    if (categoryData.parent_id) {
      const depth = await this.getCategoryDepth(categoryData.parent_id);
      if (depth >= 3) {
        errors.push('Category hierarchy cannot exceed 3 levels deep');
      }
    }

    // Validate color code format
    if (categoryData.color_code && !/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(categoryData.color_code)) {
      errors.push('Color code must be a valid hex color');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private async validateCategoryHierarchy(categoryId: number, newParentId: number | null): Promise<CategoryValidationResult> {
    const errors: string[] = [];

    if (newParentId === null) {
      return { isValid: true, errors: [] };
    }

    // Prevent self-parenting
    if (categoryId === newParentId) {
      errors.push('Category cannot be its own parent');
    }

    // Prevent circular references
    const isDescendant = await this.isCategoryDescendant(newParentId, categoryId);
    if (isDescendant) {
      errors.push('Cannot create circular reference in category hierarchy');
    }

    // Check depth limit - if parent has depth 2, adding child would make depth 3 (max allowed)
    const depth = await this.getCategoryDepth(newParentId);
    if (depth >= 3) { // Parent + Child + Grandchild = 3 levels max
      errors.push('Category hierarchy cannot exceed 3 levels deep');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Helper methods
  private async getCategoryDepth(categoryId: number): Promise<number> {
    let depth = 0;
    let currentId: number | null = categoryId;

    while (currentId !== null && depth < 10) { // Safety limit to prevent infinite loops
      const category = await this.categoryRepository.findById(currentId);
      if (!category) break;
      
      currentId = category.parent_id;
      depth++;
    }

    return depth;
  }

  private async isCategoryDescendant(categoryId: number, potentialAncestorId: number): Promise<boolean> {
    const descendants = await this.getCategoryAndDescendantIds(potentialAncestorId);
    return descendants.includes(categoryId);
  }

  private async getCategoryAndDescendantIds(categoryId: number): Promise<number[]> {
    const ids = [categoryId];
    const children = await this.categoryRepository.findByParent(categoryId);
    
    for (const child of children) {
      const descendantIds = await this.getCategoryAndDescendantIds(child.id);
      ids.push(...descendantIds);
    }

    return ids;
  }

  private async reassignTransactions(fromCategoryId: number, toCategoryId: number): Promise<void> {
    await this.transactionRepository['db'].executeSql(
      'UPDATE transactions SET category_id = ? WHERE category_id = ?',
      [toCategoryId, fromCategoryId]
    );
  }
}