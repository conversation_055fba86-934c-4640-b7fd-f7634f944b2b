import { KeychainService } from '../../platform/biometric/KeychainService';
import * as Keychain from 'react-native-keychain';
import CryptoJS from 'crypto-js';

// Mock crypto-js with persistent implementations
jest.mock('crypto-js', () => ({
  SHA256: jest.fn((input) => ({
    toString: jest.fn(() => `hashed_${input}`),
  })),
  lib: {
    WordArray: {
      random: jest.fn(() => ({
        toString: jest.fn(() => 'mocked_random_key'),
      })),
    },
  },
}));

// Mock react-native-keychain
jest.mock('react-native-keychain', () => ({
  __esModule: true,
  setInternetCredentials: jest.fn(),
  getInternetCredentials: jest.fn(),
  resetInternetCredentials: jest.fn(),
  getSupportedBiometryType: jest.fn(),
  ACCESS_CONTROL: {
    BIOMETRY_ANY_OR_DEVICE_PASSCODE: 'BiometryAnyOrDevicePasscode',
    BIOMETRY_CURRENT_SET: 'BiometryCurrentSet',
  },
  AUTHENTICATION_TYPE: {
    DEVICE_PASSCODE_OR_BIOMETRICS: 'DevicePasscodeOrBiometrics',
    BIOMETRICS: 'Biometrics',
  },
  SECURITY_LEVEL: {
    SECURE_HARDWARE: 'SecureHardware',
  },
  BIOMETRY_TYPE: {
    FACE_ID: 'FaceID',
    TOUCH_ID: 'TouchID',
    FINGERPRINT: 'Fingerprint',
  },
}));



// Get reference to the mocked functions
const mockedKeychain = Keychain as jest.Mocked<typeof Keychain>;
const mockedCryptoJS = CryptoJS as jest.Mocked<typeof CryptoJS>;

describe('KeychainService', () => {
  beforeEach(() => {
    // Clear all mocks completely
    jest.clearAllMocks();
    jest.resetAllMocks();
    
    // Reset the CryptoJS mock to its original implementation
    (mockedCryptoJS.SHA256 as jest.Mock).mockImplementation((input) => ({
      toString: jest.fn(() => `hashed_${input}`),
    }));
    
    // Reset the CryptoJS lib.WordArray.random mock
    (mockedCryptoJS.lib.WordArray.random as jest.Mock).mockImplementation(() => ({
      toString: jest.fn(() => 'mocked_random_key'),
    }));
    
    // Ensure all keychain operations return success by default
    mockedKeychain.setInternetCredentials.mockResolvedValue({ service: 'FinVibeAuth', storage: 'keychain' });
    mockedKeychain.getInternetCredentials.mockResolvedValue(false);
    mockedKeychain.resetInternetCredentials.mockResolvedValue(undefined);
    mockedKeychain.getSupportedBiometryType.mockResolvedValue(null);
  });

  describe('setCredentials', () => {
    it('should store credentials successfully', async () => {
      mockedKeychain.setInternetCredentials.mockResolvedValue({ service: 'FinVibeAuth', storage: 'keychain' });
      console.log('Test: mockedKeychain.setInternetCredentials set to resolve with true');

      const result = await KeychainService.setCredentials('testuser', 'testpass');
      console.log('Test: KeychainService.setCredentials returned:', result);
      console.log('Test: mockedKeychain.setInternetCredentials was called:', mockedKeychain.setInternetCredentials.mock.calls.length, 'times');
      console.log('Test: mockedKeychain.setInternetCredentials calls:', mockedKeychain.setInternetCredentials.mock.calls);

      expect(result).toBe(true);
      expect(mockedKeychain.setInternetCredentials).toHaveBeenCalledWith(
        'FinVibeAuth',
        'testuser',
        'testpass'
      );
    });

    it('should store credentials with biometric authentication when requested', async () => {
      mockedKeychain.setInternetCredentials.mockResolvedValue({ service: 'FinVibeAuth', storage: 'keychain' });

      const result = await KeychainService.setCredentials('testuser', 'testpass', {
        touchID: true,
        kPromptMessage: 'Authenticate to save',
      });

      expect(result).toBe(true);
      expect(mockedKeychain.setInternetCredentials).toHaveBeenCalledWith(
        'FinVibeAuth',
        'testuser',
        'testpass'
      );
    });

    it('should handle keychain errors', async () => {
      mockedKeychain.setInternetCredentials.mockRejectedValue(new Error('Keychain error'));

      const result = await KeychainService.setCredentials('testuser', 'testpass');

      expect(result).toBe(false);
    });

    it('should return false when keychain returns false', async () => {
      mockedKeychain.setInternetCredentials.mockResolvedValue(false);

      const result = await KeychainService.setCredentials('testuser', 'testpass');

      expect(result).toBe(false);
    });
  });

  describe('getCredentials', () => {
    it('should retrieve credentials successfully', async () => {
      mockedKeychain.getInternetCredentials.mockResolvedValue({
        username: 'testuser',
        password: 'testpass',
        service: 'FinVibeAuth',
        storage: 'keychain',
        server: 'FinVibeAuth'
      });

      const result = await KeychainService.getCredentials();

      expect(result).toEqual({
        username: 'testuser',
        password: 'testpass',
      });
      expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('FinVibeAuth');
    });

    it('should return null when no credentials exist', async () => {
      mockedKeychain.getInternetCredentials.mockResolvedValue(false);

      const result = await KeychainService.getCredentials();

      expect(result).toBeNull();
    });

    it('should handle keychain errors', async () => {
      mockedKeychain.getInternetCredentials.mockRejectedValue(new Error('Keychain error'));

      const result = await KeychainService.getCredentials();

      expect(result).toBeNull();
    });
  });

  describe('removeCredentials', () => {
    it('should remove credentials successfully', async () => {
      mockedKeychain.resetInternetCredentials.mockResolvedValue(undefined);

      const result = await KeychainService.removeCredentials();

      expect(result).toBe(true);
      expect(mockedKeychain.resetInternetCredentials).toHaveBeenCalledWith('FinVibeAuth');
    });

    it('should remove credentials for custom service', async () => {
      mockedKeychain.resetInternetCredentials.mockResolvedValue(undefined);

      const result = await KeychainService.removeCredentials('CustomService');

      expect(result).toBe(true);
      expect(mockedKeychain.resetInternetCredentials).toHaveBeenCalledWith('CustomService');
    });

    it('should handle removal errors', async () => {
      mockedKeychain.resetInternetCredentials.mockRejectedValue(new Error('Removal failed'));

      const result = await KeychainService.removeCredentials();

      expect(result).toBe(false);
    });
  });

  describe('PIN operations', () => {

    describe('storePIN', () => {
      it('should store hashed PIN successfully', async () => {
        // Ensure setInternetCredentials returns success for this specific test
        mockedKeychain.setInternetCredentials.mockResolvedValueOnce({ service: 'FinVibeAuth', storage: 'keychain' });

        const result = await KeychainService.storePIN('1234');

        expect(result).toBe(true);
        expect(mockedCryptoJS.SHA256).toHaveBeenCalledWith('1234');
        expect(mockedKeychain.setInternetCredentials).toHaveBeenCalledWith('user_pin', 'user_pin', 'hashed_1234');
      });

      it('should handle PIN storage errors', async () => {
        mockedCryptoJS.SHA256.mockImplementation(() => {
          throw new Error('Crypto error');
        });

        const result = await KeychainService.storePIN('1234');

        expect(result).toBe(false);
      });
    });

    describe('verifyPIN', () => {
      it('should verify correct PIN successfully', async () => {
        // Override the default mock for this specific test
        mockedKeychain.getInternetCredentials.mockResolvedValueOnce({
          username: 'user_pin',
          password: 'hashed_1234',
          service: 'user_pin',
          storage: 'keychain',
          server: 'user_pin'
        });

        const result = await KeychainService.verifyPIN('1234');

        expect(result).toBe(true);
        expect(mockedCryptoJS.SHA256).toHaveBeenCalledWith('1234');
        expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('user_pin');
      });

      it('should reject incorrect PIN', async () => {
        mockedKeychain.getInternetCredentials.mockResolvedValue({
          username: 'user_pin',
          password: 'different_hash',
          service: 'user_pin',
          storage: 'keychain',
          server: 'user_pin'
        });

        const result = await KeychainService.verifyPIN('1234');

        expect(result).toBe(false);
        expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('user_pin');
      });

      it('should handle missing PIN', async () => {
        mockedKeychain.getInternetCredentials.mockResolvedValue(false);

        const result = await KeychainService.verifyPIN('1234');

        expect(result).toBe(false);
        expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('user_pin');
      });

      it('should handle verification errors', async () => {
        mockedKeychain.getInternetCredentials.mockRejectedValue(new Error('Keychain error'));

        const result = await KeychainService.verifyPIN('1234');

        expect(result).toBe(false);
        expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('user_pin');
      });
    });
  });

  describe('Pattern operations', () => {

    describe('storePattern', () => {
      it('should store hashed pattern successfully', async () => {
        // Ensure setInternetCredentials returns success for this specific test
        mockedKeychain.setInternetCredentials.mockResolvedValueOnce({ service: 'FinVibeAuth', storage: 'keychain' });

        const result = await KeychainService.storePattern([1, 2, 3, 4]);

        expect(result).toBe(true);
        expect(mockedCryptoJS.SHA256).toHaveBeenCalledWith('1,2,3,4');
        expect(mockedKeychain.setInternetCredentials).toHaveBeenCalledWith('user_pattern', 'user_pattern', 'hashed_1,2,3,4');
      });

      it('should handle pattern storage errors', async () => {
        mockedCryptoJS.SHA256.mockImplementation(() => {
          throw new Error('Crypto error');
        });

        const result = await KeychainService.storePattern([1, 2, 3, 4]);

        expect(result).toBe(false);
      });
    });

    describe('verifyPattern', () => {
      it('should verify correct pattern successfully', async () => {
        // Override the default mock for this specific test
        mockedKeychain.getInternetCredentials.mockResolvedValueOnce({
          username: 'user_pattern',
          password: 'hashed_1,2,3,4',
          service: 'user_pattern',
          storage: 'keychain',
          server: 'user_pattern'
        });

        const result = await KeychainService.verifyPattern([1, 2, 3, 4]);

        expect(result).toBe(true);
        expect(mockedCryptoJS.SHA256).toHaveBeenCalledWith('1,2,3,4');
        expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('user_pattern');
      });

      it('should reject incorrect pattern', async () => {
        mockedKeychain.getInternetCredentials.mockResolvedValue({
          username: 'user_pattern',
          password: 'different_hash',
          service: 'user_pattern',
          storage: 'keychain',
          server: 'user_pattern'
        });

        const result = await KeychainService.verifyPattern([1, 2, 3, 4]);

        expect(result).toBe(false);
        expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('user_pattern');
      });

      it('should handle missing pattern', async () => {
        mockedKeychain.getInternetCredentials.mockResolvedValue(false);

        const result = await KeychainService.verifyPattern([1, 2, 3, 4]);

        expect(result).toBe(false);
        expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('user_pattern');
      });
    });
  });

  describe('Database key operations', () => {
    
    describe('generateDatabaseKey', () => {
      it('should generate and store database key successfully', async () => {
        // Ensure setInternetCredentials returns success
        mockedKeychain.setInternetCredentials.mockResolvedValue({ service: 'FinVibeAuth', storage: 'keychain' });
        
        const result = await KeychainService.generateDatabaseKey();

        expect(result).toBe('mocked_random_key');
        expect(mockedKeychain.setInternetCredentials).toHaveBeenCalledWith(
          'database_encryption_key',
          'database_encryption_key',
          'mocked_random_key'
        );
      });

      it('should return null if storage fails', async () => {
        mockedKeychain.setInternetCredentials.mockResolvedValue(false);

        const result = await KeychainService.generateDatabaseKey();

        expect(result).toBeNull();
      });
    });

    describe('getDatabaseKey', () => {
      it('should retrieve database key successfully', async () => {
        mockedKeychain.getInternetCredentials.mockResolvedValue({
          username: 'database_encryption_key',
          password: 'stored_database_key',
          service: 'database_encryption_key',
          storage: 'keychain',
          server: 'database_encryption_key'
        });

        const result = await KeychainService.getDatabaseKey();

        expect(result).toBe('stored_database_key');
        expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('database_encryption_key');
      });

      it('should return null if no key exists', async () => {
        mockedKeychain.getInternetCredentials.mockResolvedValue(false);

        const result = await KeychainService.getDatabaseKey();

        expect(result).toBeNull();
        expect(mockedKeychain.getInternetCredentials).toHaveBeenCalledWith('database_encryption_key');
      });
    });
  });

  describe('Utility methods', () => {
    describe('isKeychainAvailable', () => {
      it('should return true when keychain is available', async () => {
        mockedKeychain.getSupportedBiometryType.mockResolvedValue('FaceID' as any);

        const result = await KeychainService.isKeychainAvailable();

        expect(result).toBe(true);
      });

      it('should return false when keychain is not available', async () => {
        mockedKeychain.getSupportedBiometryType.mockResolvedValue(null);

        const result = await KeychainService.isKeychainAvailable();

        expect(result).toBe(false); // Method returns false when biometry is null
      });

      it('should handle errors gracefully', async () => {
        mockedKeychain.getSupportedBiometryType.mockRejectedValue(new Error('Error'));

        const result = await KeychainService.isKeychainAvailable();

        expect(result).toBe(false);
      });
    });

    describe('clearAllCredentials', () => {
      it('should clear all credential types successfully', async () => {
        mockedKeychain.resetInternetCredentials.mockResolvedValue(undefined);

        const result = await KeychainService.clearAllCredentials();

        expect(result).toBe(true);
        expect(mockedKeychain.resetInternetCredentials).toHaveBeenCalledTimes(4);
      });

      it('should return false if any clear operation fails', async () => {
        mockedKeychain.resetInternetCredentials
          .mockResolvedValueOnce(undefined)
          .mockRejectedValueOnce(new Error('Failed'))
          .mockResolvedValueOnce(undefined)
          .mockResolvedValueOnce(undefined);

        const result = await KeychainService.clearAllCredentials();

        expect(result).toBe(false);
      });
    });
  });
});