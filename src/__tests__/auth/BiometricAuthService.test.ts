import { BiometricAuthService } from '../../platform/biometric/BiometricAuthService';
import * as LocalAuthentication from 'expo-local-authentication';

// Mock expo-local-authentication
jest.mock('expo-local-authentication', () => ({
  hasHardwareAsync: jest.fn(),
  isEnrolledAsync: jest.fn(),
  supportedAuthenticationTypesAsync: jest.fn(),
  authenticateAsync: jest.fn(),
  AuthenticationType: {
    FINGERPRINT: 1,
    FACIAL_RECOGNITION: 2,
    IRIS: 3,
  },
}));

const mockLocalAuth = LocalAuthentication as jest.Mocked<typeof LocalAuthentication>;

describe('BiometricAuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('detectCapabilities', () => {
    it('should return available capabilities when hardware and enrollment are available', async () => {
      mockLocalAuth.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuth.isEnrolledAsync.mockResolvedValue(true);
      mockLocalAuth.supportedAuthenticationTypesAsync.mockResolvedValue([
        LocalAuthentication.AuthenticationType.FINGERPRINT,
        LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION,
      ]);

      const capabilities = await BiometricAuthService.detectCapabilities();

      expect(capabilities).toEqual({
        isAvailable: true,
        supportedTypes: [
          LocalAuthentication.AuthenticationType.FINGERPRINT,
          LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION,
        ],
        hasHardware: true,
        isEnrolled: true,
      });
    });

    it('should return unavailable when no hardware is present', async () => {
      mockLocalAuth.hasHardwareAsync.mockResolvedValue(false);
      mockLocalAuth.isEnrolledAsync.mockResolvedValue(false);
      mockLocalAuth.supportedAuthenticationTypesAsync.mockResolvedValue([]);

      const capabilities = await BiometricAuthService.detectCapabilities();

      expect(capabilities).toEqual({
        isAvailable: false,
        supportedTypes: [],
        hasHardware: false,
        isEnrolled: false,
      });
    });

    it('should return unavailable when hardware exists but no enrollment', async () => {
      mockLocalAuth.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuth.isEnrolledAsync.mockResolvedValue(false);
      mockLocalAuth.supportedAuthenticationTypesAsync.mockResolvedValue([
        LocalAuthentication.AuthenticationType.FINGERPRINT,
      ]);

      const capabilities = await BiometricAuthService.detectCapabilities();

      expect(capabilities).toEqual({
        isAvailable: false,
        supportedTypes: [LocalAuthentication.AuthenticationType.FINGERPRINT],
        hasHardware: true,
        isEnrolled: false,
      });
    });

    it('should handle errors gracefully', async () => {
      mockLocalAuth.hasHardwareAsync.mockRejectedValue(new Error('Hardware check failed'));

      const capabilities = await BiometricAuthService.detectCapabilities();

      expect(capabilities).toEqual({
        isAvailable: false,
        supportedTypes: [],
        hasHardware: false,
        isEnrolled: false,
      });
    });
  });

  describe('authenticate', () => {
    it('should return success when authentication succeeds', async () => {
      mockLocalAuth.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuth.isEnrolledAsync.mockResolvedValue(true);
      mockLocalAuth.supportedAuthenticationTypesAsync.mockResolvedValue([
        LocalAuthentication.AuthenticationType.FINGERPRINT,
      ]);
      mockLocalAuth.authenticateAsync.mockResolvedValue({ success: true });

      const result = await BiometricAuthService.authenticate('Test authentication');

      expect(result).toEqual({ success: true });
      expect(mockLocalAuth.authenticateAsync).toHaveBeenCalledWith({
        promptMessage: 'Test authentication',
        fallbackLabel: 'Use PIN',
        disableDeviceFallback: false,
        cancelLabel: 'Cancel',
      });
    });

    it('should return error when no biometric hardware available', async () => {
      mockLocalAuth.hasHardwareAsync.mockResolvedValue(false);
      mockLocalAuth.isEnrolledAsync.mockResolvedValue(false);
      mockLocalAuth.supportedAuthenticationTypesAsync.mockResolvedValue([]);

      const result = await BiometricAuthService.authenticate();

      expect(result).toEqual({
        success: false,
        error: 'No biometric hardware available',
      });
      expect(mockLocalAuth.authenticateAsync).not.toHaveBeenCalled();
    });

    it('should return error when no biometric enrollment', async () => {
      mockLocalAuth.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuth.isEnrolledAsync.mockResolvedValue(false);
      mockLocalAuth.supportedAuthenticationTypesAsync.mockResolvedValue([
        LocalAuthentication.AuthenticationType.FINGERPRINT,
      ]);

      const result = await BiometricAuthService.authenticate();

      expect(result).toEqual({
        success: false,
        error: 'No biometric credentials enrolled',
      });
      expect(mockLocalAuth.authenticateAsync).not.toHaveBeenCalled();
    });

    it('should handle authentication failure with specific error types', async () => {
      mockLocalAuth.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuth.isEnrolledAsync.mockResolvedValue(true);
      mockLocalAuth.supportedAuthenticationTypesAsync.mockResolvedValue([
        LocalAuthentication.AuthenticationType.FINGERPRINT,
      ]);
      mockLocalAuth.authenticateAsync.mockResolvedValue({
        success: false,
        error: 'user_cancel',
      });

      const result = await BiometricAuthService.authenticate();

      expect(result).toEqual({
        success: false,
        error: 'Authentication cancelled by user',
      });
    });

    it('should handle lockout errors', async () => {
      mockLocalAuth.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuth.isEnrolledAsync.mockResolvedValue(true);
      mockLocalAuth.supportedAuthenticationTypesAsync.mockResolvedValue([
        LocalAuthentication.AuthenticationType.FINGERPRINT,
      ]);
      mockLocalAuth.authenticateAsync.mockResolvedValue({
        success: false,
        error: 'lockout',
      });

      const result = await BiometricAuthService.authenticate();

      expect(result).toEqual({
        success: false,
        error: 'Too many failed attempts. Try again later.',
      });
    });

    it('should handle system errors gracefully', async () => {
      mockLocalAuth.hasHardwareAsync.mockResolvedValue(true);
      mockLocalAuth.isEnrolledAsync.mockResolvedValue(true);
      mockLocalAuth.supportedAuthenticationTypesAsync.mockResolvedValue([
        LocalAuthentication.AuthenticationType.FINGERPRINT,
      ]);
      mockLocalAuth.authenticateAsync.mockRejectedValue(new Error('System error'));

      const result = await BiometricAuthService.authenticate();

      expect(result).toEqual({
        success: false,
        error: 'Biometric authentication error occurred',
      });
    });
  });

  describe('getBiometricTypesDescription', () => {
    it('should return correct descriptions for fingerprint', () => {
      const descriptions = BiometricAuthService.getBiometricTypesDescription([
        LocalAuthentication.AuthenticationType.FINGERPRINT,
      ]);

      expect(descriptions).toEqual(['Fingerprint']);
    });

    it('should return correct descriptions for face recognition', () => {
      const descriptions = BiometricAuthService.getBiometricTypesDescription([
        LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION,
      ]);

      expect(descriptions).toEqual(['Face ID']);
    });

    it('should return correct descriptions for iris', () => {
      const descriptions = BiometricAuthService.getBiometricTypesDescription([
        LocalAuthentication.AuthenticationType.IRIS,
      ]);

      expect(descriptions).toEqual(['Iris']);
    });

    it('should return multiple descriptions for multiple types', () => {
      const descriptions = BiometricAuthService.getBiometricTypesDescription([
        LocalAuthentication.AuthenticationType.FINGERPRINT,
        LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION,
        LocalAuthentication.AuthenticationType.IRIS,
      ]);

      expect(descriptions).toEqual(['Fingerprint', 'Face ID', 'Iris']);
    });

    it('should return empty array for no types', () => {
      const descriptions = BiometricAuthService.getBiometricTypesDescription([]);

      expect(descriptions).toEqual([]);
    });
  });
});