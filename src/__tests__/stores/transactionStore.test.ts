import { renderHook, act } from '@testing-library/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTransactionStore, setTransactionStoreServices } from '@/shared/stores/transactionStore';
import { TransactionService } from '@/business/services/TransactionService';
import { CategorySuggestionService } from '@/business/services/CategorySuggestionService';
import { Transaction, CategorySuggestion } from '@/shared/types';

// Mock dependencies
jest.mock('@/business/services/TransactionService');
jest.mock('@/business/services/CategorySuggestionService');
jest.mock('@react-native-async-storage/async-storage');

describe('TransactionStore', () => {
  let mockTransactionService: jest.Mocked<TransactionService>;
  let mockCategorySuggestionService: jest.Mocked<CategorySuggestionService>;

  const mockTransaction: Transaction = {
    id: 1,
    account_id: 1,
    amount: 100.50,
    description: 'Test transaction',
    category_id: 1,
    transaction_type: 'expense',
    transaction_date: '2024-01-15',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    sms_source: null,
    confidence_score: null,
    is_recurring: false,
    recurring_pattern: null,
    sync_status: 'local',
    hash: 'abc123'
  };

  const mockTransactionList = [
    mockTransaction,
    {
      ...mockTransaction,
      id: 2,
      amount: 2000,
      description: 'Salary',
      transaction_type: 'income' as const,
      hash: 'def456'
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock services
    mockTransactionService = {
      createTransaction: jest.fn(),
      updateTransaction: jest.fn(),
      deleteTransaction: jest.fn(),
      getTransactions: jest.fn(),
      getTransactionById: jest.fn(),
    } as any;

    mockCategorySuggestionService = {
      suggestCategories: jest.fn(),
      learnFromUserSelection: jest.fn(),
      getSuggestionStats: jest.fn(),
    } as any;

    // Inject mock services into the store
    setTransactionStoreServices(mockTransactionService, mockCategorySuggestionService);

    // Mock AsyncStorage
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
  });

  afterEach(() => {
    // Reset store state using Zustand's setState
    useTransactionStore.setState({
      transactions: [],
      selectedTransactions: new Set(),
      loading: false,
      loadingMore: false,
      refreshing: false,
      error: null,
      searchQuery: '',
      filters: {},
      categorySuggestions: [],
      suggestionsLoading: false,
      pagination: {
        page: 1,
        limit: 20,
        totalCount: 0,
        hasMore: false,
      },
      pendingOperations: new Map(),
    });
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useTransactionStore());

      expect(result.current.transactions).toEqual([]);
      expect(result.current.selectedTransactions).toEqual(new Set());
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
      expect(result.current.searchQuery).toBe('');
      expect(result.current.filters).toEqual({});
      expect(result.current.pagination).toEqual({
        page: 1,
        limit: 20,
        totalCount: 0,
        hasMore: false,
      });
    });
  });

  describe('Transaction CRUD Operations', () => {
    describe('createTransaction', () => {
      it('should create transaction with optimistic update', async () => {
        const { result } = renderHook(() => useTransactionStore());
        
        const newTransactionData = {
          account_id: 1,
          amount: 50.00,
          description: 'New transaction',
          category_id: 1,
          transaction_type: 'expense' as const,
          transaction_date: '2024-01-16',
          sms_source: null,
          confidence_score: null,
          is_recurring: false,
          recurring_pattern: null,
          sync_status: 'local' as const,
        };

        const createdTransaction = { ...newTransactionData, id: 3, created_at: '2024-01-16T10:00:00Z', updated_at: '2024-01-16T10:00:00Z', hash: 'ghi789' };
        mockTransactionService.createTransaction.mockResolvedValue(createdTransaction);

        let createPromise: Promise<any>;
        
        // Execute the create transaction call
        act(() => {
          createPromise = result.current.createTransaction(newTransactionData);
        });

        // Optimistic transaction should appear immediately after the call
        expect(result.current.transactions).toHaveLength(1);
        expect(result.current.transactions[0].description).toBe('New transaction');
        expect(result.current.transactions[0].id).toBeLessThan(0); // Temporary ID
        
        // Now wait for the actual service call to complete
        await act(async () => {
          await createPromise;
        });

        // Should be replaced with real transaction
        expect(result.current.transactions).toHaveLength(1);
        expect(result.current.transactions[0]).toEqual(createdTransaction);
        expect(mockTransactionService.createTransaction).toHaveBeenCalledWith(newTransactionData);
      });

      it('should revert optimistic update on error', async () => {
        const { result } = renderHook(() => useTransactionStore());
        
        const newTransactionData = {
          account_id: 1,
          amount: 50.00,
          description: 'New transaction',
          category_id: 1,
          transaction_type: 'expense' as const,
          transaction_date: '2024-01-16',
          sms_source: null,
          confidence_score: null,
          is_recurring: false,
          recurring_pattern: null,
          sync_status: 'local' as const,
        };

        mockTransactionService.createTransaction.mockRejectedValue(new Error('Network error'));

        await act(async () => {
          try {
            await result.current.createTransaction(newTransactionData);
          } catch {
            // Expected error
          }
        });

        // Should revert optimistic update
        expect(result.current.transactions).toHaveLength(0);
        expect(result.current.error).toBe('Network error');
      });
    });

    describe('updateTransaction', () => {
      it('should update transaction with optimistic update', async () => {
        const { result } = renderHook(() => useTransactionStore());
        
        // Set initial transactions manually
        act(() => {
          useTransactionStore.setState({ transactions: [mockTransaction] });
        });

        const updates = { description: 'Updated description', amount: 200 };
        const updatedTransaction = { ...mockTransaction, ...updates };
        mockTransactionService.updateTransaction.mockResolvedValue(updatedTransaction);

        await act(async () => {
          await result.current.updateTransaction(1, updates);
        });

        expect(result.current.transactions[0]).toEqual(updatedTransaction);
        expect(mockTransactionService.updateTransaction).toHaveBeenCalledWith(1, updates);
      });

      it('should revert on update error', async () => {
        const { result } = renderHook(() => useTransactionStore());
        
        // Set initial transactions manually
        act(() => {
          useTransactionStore.setState({ transactions: [mockTransaction] });
        });

        const updates = { description: 'Updated description' };
        mockTransactionService.updateTransaction.mockRejectedValue(new Error('Update failed'));

        await act(async () => {
          try {
            await result.current.updateTransaction(1, updates);
          } catch {
            // Expected error
          }
        });

        // Should revert to original
        expect(result.current.transactions[0]).toEqual(mockTransaction);
        expect(result.current.error).toBe('Update failed');
      });
    });

    describe('deleteTransaction', () => {
      it('should delete transaction with optimistic update', async () => {
        const { result } = renderHook(() => useTransactionStore());
        
        // Set initial transactions manually
        act(() => {
          useTransactionStore.setState({ transactions: mockTransactionList });
        });

        mockTransactionService.deleteTransaction.mockResolvedValue(true);

        await act(async () => {
          await result.current.deleteTransaction(1);
        });

        expect(result.current.transactions).toHaveLength(1);
        expect(result.current.transactions[0].id).toBe(2);
        expect(mockTransactionService.deleteTransaction).toHaveBeenCalledWith(1);
      });

      it('should revert on delete error', async () => {
        const { result } = renderHook(() => useTransactionStore());
        
        // Set initial transactions manually
        act(() => {
          useTransactionStore.setState({ transactions: mockTransactionList });
        });

        mockTransactionService.deleteTransaction.mockRejectedValue(new Error('Delete failed'));

        await act(async () => {
          try {
            await result.current.deleteTransaction(1);
          } catch {
            // Expected error
          }
        });

        // Should revert delete
        expect(result.current.transactions).toHaveLength(2);
        expect(result.current.error).toBe('Delete failed');
      });
    });
  });

  describe('Selection Management', () => {
    beforeEach(() => {
      act(() => {
        useTransactionStore.setState({ transactions: mockTransactionList });
      });
    });

    it('should select and deselect transactions', () => {
      const { result } = renderHook(() => useTransactionStore());

      act(() => {
        result.current.selectTransaction(1);
      });

      expect(result.current.selectedTransactions.has(1)).toBe(true);

      act(() => {
        result.current.deselectTransaction(1);
      });

      expect(result.current.selectedTransactions.has(1)).toBe(false);
    });

    it('should select all transactions', () => {
      const { result } = renderHook(() => useTransactionStore());

      act(() => {
        result.current.selectAllTransactions();
      });

      expect(result.current.selectedTransactions.size).toBe(2);
      expect(result.current.selectedTransactions.has(1)).toBe(true);
      expect(result.current.selectedTransactions.has(2)).toBe(true);
    });

    it('should clear selection', () => {
      const { result } = renderHook(() => useTransactionStore());

      act(() => {
        result.current.selectAllTransactions();
        result.current.clearSelection();
      });

      expect(result.current.selectedTransactions.size).toBe(0);
    });

    it('should delete selected transactions', async () => {
      const { result } = renderHook(() => useTransactionStore());

      mockTransactionService.deleteTransaction.mockResolvedValue(true);

      act(() => {
        result.current.selectTransaction(1);
        result.current.selectTransaction(2);
      });

      await act(async () => {
        await result.current.deleteSelectedTransactions();
      });

      expect(result.current.transactions).toHaveLength(0);
      expect(result.current.selectedTransactions.size).toBe(0);
      expect(mockTransactionService.deleteTransaction).toHaveBeenCalledTimes(2);
    });
  });

  describe('Data Loading', () => {
    it('should load transactions', async () => {
      const { result } = renderHook(() => useTransactionStore());

      const mockResult = {
        transactions: mockTransactionList,
        page: 1,
        limit: 20,
        totalCount: 2,
        hasMore: false,
      };

      mockTransactionService.getTransactions.mockResolvedValue(mockResult);

      await act(async () => {
        await result.current.loadTransactions();
      });

      expect(result.current.transactions).toEqual(mockTransactionList);
      expect(result.current.pagination).toEqual({
        page: 1,
        limit: 20,
        totalCount: 2,
        hasMore: false,
      });
      expect(result.current.loading).toBe(false);
    });

    it('should handle loading error', async () => {
      const { result } = renderHook(() => useTransactionStore());

      mockTransactionService.getTransactions.mockRejectedValue(new Error('Load failed'));

      await act(async () => {
        try {
          await result.current.loadTransactions();
        } catch {
          // Expected error
        }
      });

      expect(result.current.error).toBe('Load failed');
      expect(result.current.loading).toBe(false);
    });

    it('should load more transactions', async () => {
      const { result } = renderHook(() => useTransactionStore());

      // Set initial state with more data available
      act(() => {
        useTransactionStore.setState({ 
          transactions: mockTransactionList,
          pagination: {
            page: 1,
            limit: 20,
            totalCount: 4,
            hasMore: true,
          }
        });
      });

      const moreTransactions = [
        { ...mockTransaction, id: 3, description: 'Transaction 3' },
        { ...mockTransaction, id: 4, description: 'Transaction 4' },
      ];

      const mockResult = {
        transactions: moreTransactions,
        page: 2,
        limit: 20,
        totalCount: 4,
        hasMore: false,
      };

      mockTransactionService.getTransactions.mockResolvedValue(mockResult);

      await act(async () => {
        await result.current.loadMoreTransactions();
      });

      expect(result.current.transactions).toHaveLength(4);
      expect(result.current.pagination.page).toBe(2);
      expect(result.current.pagination.hasMore).toBe(false);
    });

    it('should refresh transactions', async () => {
      const { result } = renderHook(() => useTransactionStore());

      const mockResult = {
        transactions: mockTransactionList,
        page: 1,
        limit: 20,
        totalCount: 2,
        hasMore: false,
      };

      mockTransactionService.getTransactions.mockResolvedValue(mockResult);

      await act(async () => {
        await result.current.refreshTransactions();
      });

      expect(result.current.transactions).toEqual(mockTransactionList);
      expect(result.current.refreshing).toBe(false);
    });
  });

  describe('Search and Filtering', () => {
    beforeEach(() => {
      act(() => {
        useTransactionStore.setState({ transactions: mockTransactionList });
      });
    });

    it('should set search query', () => {
      const { result } = renderHook(() => useTransactionStore());

      act(() => {
        result.current.setSearchQuery('test');
      });

      expect(result.current.searchQuery).toBe('test');
    });

    it('should set filters', () => {
      const { result } = renderHook(() => useTransactionStore());

      act(() => {
        result.current.setFilters({ transactionType: 'expense' });
      });

      expect(result.current.filters.transactionType).toBe('expense');
    });

    it('should clear filters', () => {
      const { result } = renderHook(() => useTransactionStore());

      act(() => {
        result.current.setFilters({ transactionType: 'expense' });
        result.current.setSearchQuery('test');
        result.current.clearFilters();
      });

      expect(result.current.filters).toEqual({});
      expect(result.current.searchQuery).toBe('');
    });

    it('should get filtered transactions by search query', () => {
      const { result } = renderHook(() => useTransactionStore());

      act(() => {
        result.current.setSearchQuery('salary');
      });

      const filtered = result.current.getFilteredTransactions();
      expect(filtered).toHaveLength(1);
      expect(filtered[0].description).toBe('Salary');
    });

    it('should get filtered transactions by type', () => {
      const { result } = renderHook(() => useTransactionStore());

      act(() => {
        result.current.setFilters({ transactionType: 'income' });
      });

      const filtered = result.current.getFilteredTransactions();
      expect(filtered).toHaveLength(1);
      expect(filtered[0].transaction_type).toBe('income');
    });

    it('should search transactions and reload data', async () => {
      const { result } = renderHook(() => useTransactionStore());

      const mockResult = {
        transactions: [mockTransactionList[1]], // Only salary
        page: 1,
        limit: 20,
        totalCount: 1,
        hasMore: false,
      };

      mockTransactionService.getTransactions.mockResolvedValue(mockResult);

      await act(async () => {
        await result.current.searchTransactions('salary');
      });

      expect(result.current.searchQuery).toBe('salary');
      expect(result.current.transactions).toHaveLength(1);
      expect(mockTransactionService.getTransactions).toHaveBeenCalledWith(
        { searchQuery: 'salary' },
        1,
        20
      );
    });
  });

  describe('Category Suggestions', () => {
    it('should get category suggestions', async () => {
      const { result } = renderHook(() => useTransactionStore());

      const mockSuggestions: CategorySuggestion[] = [
        {
          categoryId: 1,
          categoryName: 'Food & Dining',
          confidence: 0.9,
          reason: 'keyword_match'
        }
      ];

      mockCategorySuggestionService.suggestCategories.mockResolvedValue(mockSuggestions);

      let suggestions: CategorySuggestion[] = [];
      await act(async () => {
        suggestions = await result.current.getSuggestions('restaurant dinner', 100);
      });

      expect(suggestions).toEqual(mockSuggestions);
      expect(result.current.categorySuggestions).toEqual(mockSuggestions);
      expect(result.current.suggestionsLoading).toBe(false);
      expect(mockCategorySuggestionService.suggestCategories).toHaveBeenCalledWith('restaurant dinner', 100);
    });

    it('should handle suggestion errors', async () => {
      const { result } = renderHook(() => useTransactionStore());

      mockCategorySuggestionService.suggestCategories.mockRejectedValue(new Error('Suggestion failed'));

      let suggestions: CategorySuggestion[] = [];
      await act(async () => {
        suggestions = await result.current.getSuggestions('test', 100);
      });

      expect(suggestions).toEqual([]);
      expect(result.current.error).toBe('Suggestion failed');
      expect(result.current.suggestionsLoading).toBe(false);
    });
  });

  describe('Utility Methods', () => {
    beforeEach(() => {
      act(() => {
        useTransactionStore.setState({ transactions: mockTransactionList });
      });
    });

    it('should get transaction by ID', () => {
      const { result } = renderHook(() => useTransactionStore());

      const transaction = result.current.getTransactionById(1);
      expect(transaction).toEqual(mockTransaction);

      const nonExistent = result.current.getTransactionById(999);
      expect(nonExistent).toBeUndefined();
    });

    it('should calculate total amount', () => {
      const { result } = renderHook(() => useTransactionStore());

      const totalAll = result.current.getTotalAmount();
      expect(totalAll).toBe(1899.5); // 2000 income - 100.50 expense

      const totalIncome = result.current.getTotalAmount('income');
      expect(totalIncome).toBe(2000);

      const totalExpense = result.current.getTotalAmount('expense');
      expect(totalExpense).toBe(-100.5);
    });
  });

  describe('Error Handling', () => {
    it('should set and clear errors', () => {
      const { result } = renderHook(() => useTransactionStore());

      act(() => {
        result.current.setError('Test error');
      });

      expect(result.current.error).toBe('Test error');

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBe(null);
    });
  });

  describe('Store Reset', () => {
    it('should reset store to initial state', () => {
      const { result } = renderHook(() => useTransactionStore());

      // Modify state
      act(() => {
        useTransactionStore.setState({ transactions: mockTransactionList });
        result.current.setSearchQuery('test');
        result.current.setError('test error');
        result.current.selectTransaction(1);
      });

      // Reset
      act(() => {
        result.current.reset();
      });

      expect(result.current.transactions).toEqual([]);
      expect(result.current.searchQuery).toBe('');
      expect(result.current.error).toBe(null);
      expect(result.current.selectedTransactions.size).toBe(0);
    });
  });

  describe('Pending Operations', () => {
    it('should track pending operations during optimistic updates', async () => {
      const { result } = renderHook(() => useTransactionStore());
      
      const newTransactionData = {
        account_id: 1,
        amount: 50.00,
        description: 'New transaction',
        category_id: 1,
        transaction_type: 'expense' as const,
        transaction_date: '2024-01-16',
        sms_source: null,
        confidence_score: null,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local' as const,
      };

      // Create a slow-resolving promise to test pending state
      let resolveCreate: (value: Transaction) => void;
      const createPromise = new Promise<Transaction>((resolve) => {
        resolveCreate = resolve;
      });
      mockTransactionService.createTransaction.mockReturnValue(createPromise);

      let transactionPromise: Promise<any>;
      act(() => {
        transactionPromise = result.current.createTransaction(newTransactionData);
      });

      // Should have pending operation
      expect(result.current.pendingOperations.size).toBe(1);
      
      // Resolve the promise
      const createdTransaction = { ...newTransactionData, id: 3, created_at: '2024-01-16T10:00:00Z', updated_at: '2024-01-16T10:00:00Z', hash: 'ghi789' };
      resolveCreate!(createdTransaction);
      
      await act(async () => {
        await transactionPromise;
      });

      // Should eventually clean up pending operations
      await new Promise(resolve => setTimeout(resolve, 6000)); // Wait for cleanup timeout
      expect(result.current.pendingOperations.size).toBe(0);
    }, 10000);
  });
});