/**
 * CreditCardCalculator Unit Tests
 * 
 * Tests credit card specific balance calculations, utilization, and validation
 */

import { CreditCardCalculator } from '../../../../business/services/calculators/CreditCardCalculator';
import { Account, Transaction } from '../../../../shared/types';
import { AccountBalance, ValidationResult, CreditCardSummary } from '../../../../shared/types/calculators';
import { AccountMetadata } from '../../../../shared/types/accountMetadata';

// Mock AccountRepository
jest.mock('../../../../data/repositories/AccountRepository');

describe('CreditCardCalculator', () => {
  let calculator: CreditCardCalculator;
  let mockCreditAccount: Account;
  let mockTransactions: Transaction[];

  beforeEach(() => {
    calculator = new CreditCardCalculator();
    
    mockCreditAccount = {
      id: 1,
      name: 'Test Credit Card',
      type: 'credit',
      balance: 0, // Not used for credit cards
      currency: 'INR',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sync_status: 'local',
      metadata: {
        creditLimit: 50000,
        paymentDueDate: '2025-01-25'
      }
    };

    // Mock transactions: charges and payments
    mockTransactions = [
      // Charges (expenses)
      {
        id: 1,
        account_id: 1,
        amount: 5000,
        description: 'Shopping',
        category_id: 1,
        transaction_type: 'expense',
        transaction_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sms_source: null,
        confidence_score: null,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local',
        hash: 'hash-1'
      },
      {
        id: 2,
        account_id: 1,
        amount: 2000,
        description: 'Restaurant',
        category_id: 2,
        transaction_type: 'expense',
        transaction_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sms_source: null,
        confidence_score: null,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local',
        hash: 'hash-2'
      },
      // Payments (income)
      {
        id: 3,
        account_id: 1,
        amount: 3000,
        description: 'Credit card payment',
        category_id: 3,
        transaction_type: 'income',
        transaction_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sms_source: null,
        confidence_score: null,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local',
        hash: 'hash-3'
      }
    ];

    // Mock the repository method
    jest.spyOn(calculator['accountRepository'], 'getAccountWithMetadata')
      .mockResolvedValue(mockCreditAccount);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Calculator Identity', () => {
    it('should return correct calculator type', () => {
      expect(calculator.getCalculatorType()).toBe('CreditCardCalculator');
    });
  });

  describe('calculateBalance', () => {
    it('should calculate credit card balance correctly', async () => {
      const balance = await calculator.calculateBalance(1, mockTransactions);
      
      // Outstanding balance: 5000 + 2000 - 3000 = 4000
      // Available credit: 50000 - 4000 = 46000
      expect(balance.displayBalance).toBe(46000); // Available credit
      expect(balance.actualBalance).toBe(4000); // Outstanding balance
      expect(balance.calculationType).toBe('credit');
      expect(balance.metadata.creditLimit).toBe(50000);
      expect(balance.metadata.outstandingBalance).toBe(4000);
      expect(balance.metadata.availableCredit).toBe(46000);
    });

    it('should calculate credit utilization correctly', async () => {
      const balance = await calculator.calculateBalance(1, mockTransactions);
      
      // Utilization: (4000 / 50000) * 100 = 8%
      expect(balance.metadata.creditUtilization).toBe(8);
    });

    it('should calculate minimum payment correctly', async () => {
      const balance = await calculator.calculateBalance(1, mockTransactions);
      
      // Minimum payment: max(4000 * 0.03, 500) = max(120, 500) = 500
      expect(balance.metadata.minimumPaymentDue).toBe(500);
    });

    it('should handle zero outstanding balance', async () => {
      // Equal charges and payments
      const balancedTransactions = [
        ...mockTransactions,
        {
          ...mockTransactions[2],
          id: 4,
          amount: 4000 // Total payment = 7000, total charges = 7000
        }
      ];

      const balance = await calculator.calculateBalance(1, balancedTransactions);
      
      expect(balance.actualBalance).toBe(0); // No outstanding balance
      expect(balance.displayBalance).toBe(50000); // Full credit available
      expect(balance.metadata.creditUtilization).toBe(0);
      expect(balance.metadata.minimumPaymentDue).toBe(0);
    });

    it('should handle overpayments correctly', async () => {
      // Payment exceeds charges
      const overpaidTransactions = [
        {
          ...mockTransactions[0],
          id: 10,
          amount: 1000, // Only 1000 in charges
          transaction_type: 'expense' as const
        },
        {
          ...mockTransactions[2],
          id: 11,
          amount: 2000, // 2000 in payments
          transaction_type: 'income' as const
        }
      ];

      const balance = await calculator.calculateBalance(1, overpaidTransactions);
      
      expect(balance.actualBalance).toBe(0); // Can't have negative outstanding balance
      expect(balance.displayBalance).toBe(50000); // Full credit available
    });

    it('should throw error for non-credit account', async () => {
      const nonCreditAccount = { ...mockCreditAccount, type: 'checking' as const };
      jest.spyOn(calculator['accountRepository'], 'getAccountWithMetadata')
        .mockResolvedValue(nonCreditAccount);

      await expect(calculator.calculateBalance(1, mockTransactions))
        .rejects.toThrow('CreditCardCalculator calculator cannot handle checking accounts');
    });

    it('should throw error for missing credit limit', async () => {
      const accountWithoutLimit = {
        ...mockCreditAccount,
        metadata: {} // No credit limit
      };
      jest.spyOn(calculator['accountRepository'], 'getAccountWithMetadata')
        .mockResolvedValue(accountWithoutLimit);

      await expect(calculator.calculateBalance(1, mockTransactions))
        .rejects.toThrow('credit account missing required metadata fields: creditLimit');
    });

    it('should include comprehensive metadata', async () => {
      const balance = await calculator.calculateBalance(1, mockTransactions);
      
      expect(balance.metadata).toHaveProperty('totalCharges');
      expect(balance.metadata).toHaveProperty('totalPayments');
      expect(balance.metadata).toHaveProperty('transactionCount');
      expect(balance.metadata).toHaveProperty('currentMonthCharges');
      expect(balance.metadata).toHaveProperty('currentMonthPayments');
      expect(balance.metadata).toHaveProperty('averageMonthlySpend');
      expect(balance.metadata).toHaveProperty('paymentDueDate');
      
      expect(balance.metadata.transactionCount).toBe(3);
      expect(balance.metadata.totalCharges).toBe(7000);
      expect(balance.metadata.totalPayments).toBe(3000);
    });
  });

  describe('validateTransaction', () => {
    it('should validate valid credit card charge', async () => {
      const validCharge = {
        ...mockTransactions[0],
        amount: 1000
      };

      const result = await calculator.validateTransaction(validCharge, mockCreditAccount);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate valid credit card payment', async () => {
      const validPayment = {
        ...mockTransactions[2],
        amount: 2000
      };

      const result = await calculator.validateTransaction(validPayment, mockCreditAccount);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject negative amounts', async () => {
      const invalidTransaction = {
        ...mockTransactions[0],
        amount: -100
      };

      const result = await calculator.validateTransaction(invalidTransaction, mockCreditAccount);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Transaction amount must be a positive number');
    });

    it('should reject transfer transactions', async () => {
      const transferTransaction = {
        ...mockTransactions[0],
        transaction_type: 'transfer' as const
      };

      const result = await calculator.validateTransaction(transferTransaction, mockCreditAccount);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Credit card accounts only support income (payments) and expense (charges) transactions');
    });

    it('should reject mismatched account ID', async () => {
      const mismatchedTransaction = {
        ...mockTransactions[0],
        account_id: 999
      };

      const result = await calculator.validateTransaction(mismatchedTransaction, mockCreditAccount);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Transaction account ID does not match credit card account');
    });

    it('should warn for large transactions', async () => {
      const largeTransaction = {
        ...mockTransactions[0],
        amount: 150000 // Large amount
      };

      const result = await calculator.validateTransaction(largeTransaction, mockCreditAccount);
      
      expect(result.warnings).toContain('Large transaction amount - please verify accuracy');
    });

    it('should reject transactions that exceed credit limit', async () => {
      // Mock getCurrentOutstandingBalance to return high balance
      jest.spyOn(calculator as any, 'getCurrentOutstandingBalance')
        .mockResolvedValue(45000); // Current balance near limit

      const exceedingTransaction = {
        ...mockTransactions[0],
        amount: 10000 // Would exceed 50000 limit
      };

      const result = await calculator.validateTransaction(exceedingTransaction, mockCreditAccount);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('exceed credit limit'))).toBe(true);
    });

    it('should warn when transaction uses over 90% of credit limit', async () => {
      jest.spyOn(calculator as any, 'getCurrentOutstandingBalance')
        .mockResolvedValue(40000);

      const highUtilizationTransaction = {
        ...mockTransactions[0],
        amount: 6000 // Total would be 46000/50000 = 92%
      };

      const result = await calculator.validateTransaction(highUtilizationTransaction, mockCreditAccount);
      
      expect(result.warnings).toContain('Transaction will use over 90% of credit limit');
    });

    it('should provide suggested corrections', async () => {
      const invalidTransaction = {
        ...mockTransactions[0],
        amount: -100,
        transaction_type: 'transfer' as const
      };

      const result = await calculator.validateTransaction(invalidTransaction, mockCreditAccount);
      
      expect(result.isValid).toBe(false);
      expect(result.suggestedCorrections).toBeDefined();
      expect(result.suggestedCorrections!.amount).toBe(100);
      expect(result.suggestedCorrections!.transaction_type).toBe('expense');
    });
  });

  describe('calculateCreditUtilization', () => {
    it('should calculate utilization correctly', () => {
      expect(calculator.calculateCreditUtilization(10000, 50000)).toBe(20);
      expect(calculator.calculateCreditUtilization(25000, 50000)).toBe(50);
      expect(calculator.calculateCreditUtilization(50000, 50000)).toBe(100);
    });

    it('should handle zero credit limit', () => {
      expect(calculator.calculateCreditUtilization(1000, 0)).toBe(0);
    });

    it('should handle zero balance', () => {
      expect(calculator.calculateCreditUtilization(0, 50000)).toBe(0);
    });

    it('should cap at 100% utilization', () => {
      expect(calculator.calculateCreditUtilization(60000, 50000)).toBe(100);
    });

    it('should round to two decimal places', () => {
      expect(calculator.calculateCreditUtilization(3333, 50000)).toBe(6.67);
    });
  });

  describe('calculateMinimumPayment', () => {
    it('should calculate minimum payment correctly', () => {
      // 3% rule: 3% of 10000 = 300, but minimum 500
      expect(calculator.calculateMinimumPayment(10000)).toBe(500);
      
      // 3% rule: 3% of 20000 = 600, which is > 500
      expect(calculator.calculateMinimumPayment(20000)).toBe(600);
      
      // 3% rule: 3% of 100000 = 3000
      expect(calculator.calculateMinimumPayment(100000)).toBe(3000);
    });

    it('should return zero for zero balance', () => {
      expect(calculator.calculateMinimumPayment(0)).toBe(0);
    });

    it('should return zero for negative balance', () => {
      expect(calculator.calculateMinimumPayment(-1000)).toBe(0);
    });

    it('should round to two decimal places', () => {
      expect(calculator.calculateMinimumPayment(3333.33)).toBe(500); // 3% = 99.99, so minimum 500
      expect(calculator.calculateMinimumPayment(33333.33)).toBe(1000); // 3% = 999.99, rounds to 1000
    });
  });

  describe('calculateAvailableCredit', () => {
    it('should calculate available credit correctly', async () => {
      // Mock getCurrentOutstandingBalance
      jest.spyOn(calculator as any, 'getCurrentOutstandingBalance')
        .mockResolvedValue(15000);

      const availableCredit = await calculator.calculateAvailableCredit(1);
      
      // 50000 - 15000 = 35000
      expect(availableCredit).toBe(35000);
    });

    it('should handle zero outstanding balance', async () => {
      jest.spyOn(calculator as any, 'getCurrentOutstandingBalance')
        .mockResolvedValue(0);

      const availableCredit = await calculator.calculateAvailableCredit(1);
      
      expect(availableCredit).toBe(50000);
    });

    it('should not return negative available credit', async () => {
      jest.spyOn(calculator as any, 'getCurrentOutstandingBalance')
        .mockResolvedValue(60000); // Exceeds credit limit

      const availableCredit = await calculator.calculateAvailableCredit(1);
      
      expect(availableCredit).toBe(0);
    });

    it('should throw error for non-credit account', async () => {
      const nonCreditAccount = { ...mockCreditAccount, type: 'checking' as const };
      jest.spyOn(calculator['accountRepository'], 'getAccountWithMetadata')
        .mockResolvedValue(nonCreditAccount);

      await expect(calculator.calculateAvailableCredit(1))
        .rejects.toThrow('Account not found or not a credit card account');
    });

    it('should throw error for missing credit limit', async () => {
      const accountWithoutLimit = {
        ...mockCreditAccount,
        metadata: {} // No credit limit
      };
      jest.spyOn(calculator['accountRepository'], 'getAccountWithMetadata')
        .mockResolvedValue(accountWithoutLimit);

      await expect(calculator.calculateAvailableCredit(1))
        .rejects.toThrow('Credit card account missing credit limit metadata');
    });
  });

  describe('getPaymentRecommendations', () => {
    it('should recommend full payment for low balance', () => {
      const recommendation = calculator.getPaymentRecommendations(2000, 50000, 500);
      
      expect(recommendation.paymentType).toBe('full');
      expect(recommendation.recommendedPayment).toBe(2000);
      expect(recommendation.reasoning).toContain('pay in full');
    });

    it('should recommend extra payment for high utilization', () => {
      const recommendation = calculator.getPaymentRecommendations(20000, 50000, 600);
      
      expect(recommendation.paymentType).toBe('recommended');
      expect(recommendation.recommendedPayment).toBeGreaterThan(600);
      expect(recommendation.reasoning).toContain('High utilization');
    });

    it('should recommend minimum payment for healthy utilization', () => {
      const recommendation = calculator.getPaymentRecommendations(10000, 50000, 500);
      
      expect(recommendation.paymentType).toBe('minimum');
      expect(recommendation.recommendedPayment).toBe(500);
      expect(recommendation.reasoning).toContain('minimum payment is sufficient');
    });

    it('should ensure recommended payment is at least minimum', () => {
      const recommendation = calculator.getPaymentRecommendations(1000, 50000, 500);
      
      // Even for very low balance, should recommend at least minimum payment
      expect(recommendation.recommendedPayment).toBeGreaterThanOrEqual(500);
    });
  });

  describe('getCreditCardSummary', () => {
    it('should return comprehensive credit card summary', async () => {
      // Mock the calculateBalance method
      const mockBalance: AccountBalance = {
        displayBalance: 46000,
        actualBalance: 4000,
        metadata: {
          creditLimit: 50000,
          outstandingBalance: 4000,
          availableCredit: 46000,
          creditUtilization: 8,
          minimumPaymentDue: 500,
          paymentDueDate: '2025-01-25'
        },
        lastCalculated: new Date(),
        calculationType: 'credit'
      };

      jest.spyOn(calculator, 'calculateBalance').mockResolvedValue(mockBalance);
      jest.spyOn(calculator as any, 'getTransactionsForAccount').mockResolvedValue([]);

      const summary = await calculator.getCreditCardSummary(1);
      
      expect(summary).toEqual({
        availableCredit: 46000,
        outstandingBalance: 4000,
        creditUtilization: 8,
        minimumPaymentDue: 500,
        creditLimit: 50000,
        paymentDueDate: '2025-01-25'
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle account not found', async () => {
      jest.spyOn(calculator['accountRepository'], 'getAccountWithMetadata')
        .mockResolvedValue(null);

      await expect(calculator.calculateBalance(999, mockTransactions))
        .rejects.toThrow('Account not found');
    });

    it('should handle invalid transaction dates', async () => {
      const invalidTransaction = {
        ...mockTransactions[0],
        transaction_date: 'invalid-date'
      };

      const result = await calculator.validateTransaction(invalidTransaction, mockCreditAccount);
      
      expect(result.errors).toContain('Invalid transaction date');
    });

    it('should handle empty transaction list', async () => {
      const balance = await calculator.calculateBalance(1, []);
      
      expect(balance.actualBalance).toBe(0);
      expect(balance.displayBalance).toBe(50000); // Full credit limit available
      expect(balance.metadata.transactionCount).toBe(0);
    });

    it('should round all currency amounts correctly', async () => {
      const preciseTransactions = [
        {
          ...mockTransactions[0],
          amount: 1234.567 // Will be used as-is, rounding happens in calculation
        },
        {
          ...mockTransactions[2],
          amount: 234.567
        }
      ];

      const balance = await calculator.calculateBalance(1, preciseTransactions);
      
      // All monetary values should be rounded to 2 decimal places
      expect(Number(balance.actualBalance.toFixed(2))).toBe(balance.actualBalance);
      expect(Number(balance.displayBalance.toFixed(2))).toBe(balance.displayBalance);
    });
  });

  describe('Performance Tests', () => {
    it('should handle large transaction volumes efficiently', async () => {
      // Create 1000 transactions
      const manyTransactions = Array.from({ length: 1000 }, (_, i) => ({
        ...mockTransactions[0],
        id: i + 1,
        amount: Math.random() * 1000
      }));

      const startTime = performance.now();
      const balance = await calculator.calculateBalance(1, manyTransactions);
      const endTime = performance.now();

      expect(balance).toBeDefined();
      expect(endTime - startTime).toBeLessThan(200); // Should complete within 200ms
    });

    it('should maintain accuracy with large amounts', async () => {
      const largeAmountTransactions = [
        {
          ...mockTransactions[0],
          amount: 9999999.99 // Very large charge
        },
        {
          ...mockTransactions[2],
          amount: 5000000 // Large payment
        }
      ];

      const balance = await calculator.calculateBalance(1, largeAmountTransactions);
      
      expect(balance.actualBalance).toBe(4999999.99);
      expect(balance.displayBalance).toBe(0); // Available credit capped at 0 when balance exceeds limit
    });
  });
});