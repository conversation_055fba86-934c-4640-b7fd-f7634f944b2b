/**
 * AccountCalculatorFactory Unit Tests
 * 
 * Tests the factory pattern for creating account-specific calculators
 */

import { AccountCalculatorFactory } from '../../../../business/services/calculators/AccountCalculatorFactory';
import { AccountType } from '../../../../shared/types';

// Mock the calculator classes to avoid circular dependencies during testing
jest.mock('../../../../business/services/calculators/CreditCardCalculator', () => ({
  CreditCardCalculator: jest.fn().mockImplementation(() => ({
    calculateBalance: jest.fn(),
    validateTransaction: jest.fn(),
    getCalculatorType: () => 'CreditCardCalculator'
  }))
}));

jest.mock('../../../../business/services/calculators/LoanCalculator', () => ({
  LoanCalculator: jest.fn().mockImplementation(() => ({
    calculateBalance: jest.fn(),
    validateTransaction: jest.fn(),
    getCalculatorType: () => 'LoanCalculator'
  }))
}));

jest.mock('../../../../business/services/calculators/DefaultAccountCalculator', () => ({
  DefaultAccountCalculator: jest.fn().mockImplementation(() => ({
    calculateBalance: jest.fn(),
    validateTransaction: jest.fn(),
    getCalculatorType: () => 'DefaultAccountCalculator'
  }))
}));

describe('AccountCalculatorFactory', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getCalculator', () => {
    it('should return CreditCardCalculator for credit account type', () => {
      const calculator = AccountCalculatorFactory.getCalculator('credit');
      expect(calculator).toBeDefined();
      expect(calculator.getCalculatorType()).toBe('CreditCardCalculator');
    });

    it('should return LoanCalculator for loan account type', () => {
      const calculator = AccountCalculatorFactory.getCalculator('loan');
      expect(calculator).toBeDefined();
      expect(calculator.getCalculatorType()).toBe('LoanCalculator');
    });

    it('should return DefaultAccountCalculator for checking account type', () => {
      const calculator = AccountCalculatorFactory.getCalculator('checking');
      expect(calculator).toBeDefined();
      expect(calculator.getCalculatorType()).toBe('DefaultAccountCalculator');
    });

    it('should return DefaultAccountCalculator for savings account type', () => {
      const calculator = AccountCalculatorFactory.getCalculator('savings');
      expect(calculator).toBeDefined();
      expect(calculator.getCalculatorType()).toBe('DefaultAccountCalculator');
    });

    it('should return DefaultAccountCalculator for investment account type', () => {
      const calculator = AccountCalculatorFactory.getCalculator('investment');
      expect(calculator).toBeDefined();
      expect(calculator.getCalculatorType()).toBe('DefaultAccountCalculator');
    });

    it('should throw error for unsupported account type', () => {
      expect(() => {
        AccountCalculatorFactory.getCalculator('unsupported' as AccountType);
      }).toThrow('Unsupported account type: unsupported');
    });
  });

  describe('isAccountTypeSupported', () => {
    it('should return true for supported account types', () => {
      const supportedTypes: AccountType[] = ['checking', 'savings', 'credit', 'loan', 'investment'];
      
      supportedTypes.forEach(type => {
        expect(AccountCalculatorFactory.isAccountTypeSupported(type)).toBe(true);
      });
    });

    it('should return false for unsupported account types', () => {
      expect(AccountCalculatorFactory.isAccountTypeSupported('unsupported' as AccountType)).toBe(false);
    });
  });

  describe('getSupportedAccountTypes', () => {
    it('should return array of supported account types', () => {
      const supportedTypes = AccountCalculatorFactory.getSupportedAccountTypes();
      
      expect(supportedTypes).toHaveLength(5);
      expect(supportedTypes).toContain('checking');
      expect(supportedTypes).toContain('savings');
      expect(supportedTypes).toContain('credit');
      expect(supportedTypes).toContain('loan');
      expect(supportedTypes).toContain('investment');
    });

    it('should return a new array each time (not a reference)', () => {
      const types1 = AccountCalculatorFactory.getSupportedAccountTypes();
      const types2 = AccountCalculatorFactory.getSupportedAccountTypes();
      
      expect(types1).toEqual(types2);
      expect(types1).not.toBe(types2); // Different array instances
    });
  });

  describe('getCalculatorType', () => {
    it('should return correct calculator type identifiers', () => {
      expect(AccountCalculatorFactory.getCalculatorType('credit')).toBe('CreditCardCalculator');
      expect(AccountCalculatorFactory.getCalculatorType('loan')).toBe('LoanCalculator');
      expect(AccountCalculatorFactory.getCalculatorType('checking')).toBe('DefaultAccountCalculator');
      expect(AccountCalculatorFactory.getCalculatorType('savings')).toBe('DefaultAccountCalculator');
      expect(AccountCalculatorFactory.getCalculatorType('investment')).toBe('DefaultAccountCalculator');
    });

    it('should throw error for unsupported account type', () => {
      expect(() => {
        AccountCalculatorFactory.getCalculatorType('unsupported' as AccountType);
      }).toThrow('Unsupported account type: unsupported');
    });
  });

  describe('validateCalculatorRequirements', () => {
    it('should validate credit card requirements', () => {
      const requirements = AccountCalculatorFactory.validateCalculatorRequirements('credit');
      
      expect(requirements.isValid).toBe(true);
      expect(requirements.requiresMetadata).toBe(true);
      expect(requirements.requiredMetadataFields).toContain('creditLimit');
      expect(requirements.warnings).toHaveLength(1);
      expect(requirements.warnings[0]).toContain('creditLimit');
    });

    it('should validate loan requirements', () => {
      const requirements = AccountCalculatorFactory.validateCalculatorRequirements('loan');
      
      expect(requirements.isValid).toBe(true);
      expect(requirements.requiresMetadata).toBe(true);
      expect(requirements.requiredMetadataFields).toContain('principalAmount');
      expect(requirements.requiredMetadataFields).toContain('interestRate');
      expect(requirements.requiredMetadataFields).toContain('emiAmount');
      expect(requirements.warnings).toHaveLength(1);
    });

    it('should validate standard account requirements', () => {
      const standardTypes: AccountType[] = ['checking', 'savings', 'investment'];
      
      standardTypes.forEach(type => {
        const requirements = AccountCalculatorFactory.validateCalculatorRequirements(type);
        
        expect(requirements.isValid).toBe(true);
        expect(requirements.requiresMetadata).toBe(false);
        expect(requirements.requiredMetadataFields).toHaveLength(0);
        expect(requirements.warnings).toHaveLength(0);
      });
    });

    it('should handle unsupported account types', () => {
      const requirements = AccountCalculatorFactory.validateCalculatorRequirements('unsupported' as AccountType);
      
      expect(requirements.isValid).toBe(false);
      expect(requirements.requiresMetadata).toBe(false);
      expect(requirements.requiredMetadataFields).toHaveLength(0);
      expect(requirements.warnings).toHaveLength(1);
      expect(requirements.warnings[0]).toContain('Unsupported account type');
    });
  });

  describe('createBatchCalculators', () => {
    it('should create calculators for all valid account types', () => {
      const accountTypes: AccountType[] = ['checking', 'credit', 'loan'];
      const calculators = AccountCalculatorFactory.createBatchCalculators(accountTypes);
      
      expect(calculators.size).toBe(3);
      expect(calculators.has('checking')).toBe(true);
      expect(calculators.has('credit')).toBe(true);
      expect(calculators.has('loan')).toBe(true);
      
      expect(calculators.get('checking')?.getCalculatorType()).toBe('DefaultAccountCalculator');
      expect(calculators.get('credit')?.getCalculatorType()).toBe('CreditCardCalculator');
      expect(calculators.get('loan')?.getCalculatorType()).toBe('LoanCalculator');
    });

    it('should handle mixed valid and invalid account types', () => {
      const accountTypes = ['checking', 'invalid', 'credit'] as AccountType[];
      
      // Mock console.warn to capture warning messages
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      const calculators = AccountCalculatorFactory.createBatchCalculators(accountTypes);
      
      expect(calculators.size).toBe(2); // Only valid types
      expect(calculators.has('checking')).toBe(true);
      expect(calculators.has('credit')).toBe(true);
      expect(calculators.has('invalid' as AccountType)).toBe(false);
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to create calculator for invalid:',
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });

    it('should handle empty account types array', () => {
      const calculators = AccountCalculatorFactory.createBatchCalculators([]);
      
      expect(calculators.size).toBe(0);
      expect(calculators instanceof Map).toBe(true);
    });
  });

  describe('getPerformanceExpectations', () => {
    it('should return correct expectations for credit account type', () => {
      const expectations = AccountCalculatorFactory.getPerformanceExpectations('credit');
      
      expect(expectations.maxCalculationTime).toBe(100);
      expect(expectations.maxTransactionVolume).toBe(1000);
      expect(expectations.warningThreshold).toBe(50);
    });

    it('should return correct expectations for loan account type', () => {
      const expectations = AccountCalculatorFactory.getPerformanceExpectations('loan');
      
      expect(expectations.maxCalculationTime).toBe(500);
      expect(expectations.maxTransactionVolume).toBe(500);
      expect(expectations.warningThreshold).toBe(200);
    });

    it('should return correct expectations for standard account types', () => {
      const standardTypes: AccountType[] = ['checking', 'savings', 'investment'];
      
      standardTypes.forEach(type => {
        const expectations = AccountCalculatorFactory.getPerformanceExpectations(type);
        
        expect(expectations.maxCalculationTime).toBe(200);
        expect(expectations.maxTransactionVolume).toBe(2000);
        expect(expectations.warningThreshold).toBe(100);
      });
    });

    it('should return default expectations for unsupported account types', () => {
      const expectations = AccountCalculatorFactory.getPerformanceExpectations('unsupported' as AccountType);
      
      expect(expectations.maxCalculationTime).toBe(300);
      expect(expectations.maxTransactionVolume).toBe(1000);
      expect(expectations.warningThreshold).toBe(150);
    });
  });

  describe('Integration Tests', () => {
    it('should create different calculator instances for different types', () => {
      const calculator1 = AccountCalculatorFactory.getCalculator('checking');
      const calculator2 = AccountCalculatorFactory.getCalculator('credit');
      
      expect(calculator1).toBeDefined();
      expect(calculator2).toBeDefined();
      expect(calculator1).not.toBe(calculator2);
      expect(calculator1.getCalculatorType()).toBe('DefaultAccountCalculator');
      expect(calculator2.getCalculatorType()).toBe('CreditCardCalculator');
    });

    it('should create new instances on each call', () => {
      const calculator1 = AccountCalculatorFactory.getCalculator('checking');
      const calculator2 = AccountCalculatorFactory.getCalculator('checking');
      
      expect(calculator1).toBeDefined();
      expect(calculator2).toBeDefined();
      expect(calculator1).not.toBe(calculator2); // Different instances
      expect(calculator1.getCalculatorType()).toBe(calculator2.getCalculatorType()); // Same type
    });

    it('should work with all supported account types without throwing', () => {
      const supportedTypes = AccountCalculatorFactory.getSupportedAccountTypes();
      
      supportedTypes.forEach(type => {
        expect(() => {
          const calculator = AccountCalculatorFactory.getCalculator(type);
          expect(calculator).toBeDefined();
          expect(calculator.getCalculatorType()).toBeDefined();
        }).not.toThrow();
      });
    });
  });

  describe('Error Handling', () => {
    it('should provide meaningful error messages', () => {
      expect(() => {
        AccountCalculatorFactory.getCalculator('invalid-type' as AccountType);
      }).toThrow('Unsupported account type: invalid-type');

      expect(() => {
        AccountCalculatorFactory.getCalculatorType('another-invalid' as AccountType);
      }).toThrow('Unsupported account type: another-invalid');
    });
  });
});