/**
 * BaseAccountCalculator Unit Tests
 * 
 * Tests the base calculator functionality and utility methods
 */

import { BaseAccountCalculator } from '../../../../business/services/calculators/BaseAccountCalculator';
import { Account, Transaction, AccountType } from '../../../../shared/types';
import { AccountBalance, ValidationResult } from '../../../../shared/types/calculators';
import { AccountMetadata } from '../../../../shared/types/accountMetadata';

// Mock concrete implementation for testing abstract class
class TestAccountCalculator extends BaseAccountCalculator {
  async calculateBalance(accountId: number, transactions: Transaction[]): Promise<AccountBalance> {
    // Simple test implementation
    const account = await this.getAccountWithMetadata(accountId);
    if (!account) {
      throw new Error('Account not found');
    }

    const balance = transactions.reduce((sum, t) => {
      return t.transaction_type === 'income' ? sum + t.amount : sum - t.amount;
    }, 0);

    return {
      displayBalance: balance,
      actualBalance: balance,
      metadata: { testImplementation: true },
      lastCalculated: new Date(),
      calculationType: 'checking'
    };
  }

  async validateTransaction(transaction: Transaction, account: Account): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (transaction.amount <= 0) {
      errors.push('Amount must be positive');
    }

    return this.createValidationResult(errors.length === 0, errors, warnings);
  }

  getCalculatorType(): string {
    return 'TestAccountCalculator';
  }
}

// Mock AccountRepository
jest.mock('../../../../data/repositories/AccountRepository');

describe('BaseAccountCalculator', () => {
  let calculator: TestAccountCalculator;
  let mockAccount: Account;
  let mockTransactions: Transaction[];

  beforeEach(() => {
    calculator = new TestAccountCalculator();
    
    mockAccount = {
      id: 1,
      name: 'Test Account',
      type: 'checking',
      balance: 1000,
      currency: 'INR',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sync_status: 'local',
      metadata: {
        creditLimit: 50000,
        interestRate: 12.5
      }
    };

    mockTransactions = [
      {
        id: 1,
        account_id: 1,
        amount: 1000,
        description: 'Test income',
        category_id: 1,
        transaction_type: 'income',
        transaction_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sms_source: null,
        confidence_score: null,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local',
        hash: 'test-hash-1'
      },
      {
        id: 2,
        account_id: 1,
        amount: 500,
        description: 'Test expense',
        category_id: 2,
        transaction_type: 'expense',
        transaction_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sms_source: null,
        confidence_score: null,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local',
        hash: 'test-hash-2'
      }
    ];

    // Mock the repository method
    jest.spyOn(calculator['accountRepository'], 'getAccountWithMetadata')
      .mockResolvedValue(mockAccount);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Abstract Methods', () => {
    it('should implement calculateBalance method', async () => {
      const balance = await calculator.calculateBalance(1, mockTransactions);
      
      expect(balance).toBeDefined();
      expect(balance.displayBalance).toBe(500); // 1000 - 500
      expect(balance.actualBalance).toBe(500);
      expect(balance.calculationType).toBe('checking');
      expect(balance.metadata.testImplementation).toBe(true);
    });

    it('should implement validateTransaction method', async () => {
      const validTransaction = mockTransactions[0];
      const result = await calculator.validateTransaction(validTransaction, mockAccount);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should implement getCalculatorType method', () => {
      expect(calculator.getCalculatorType()).toBe('TestAccountCalculator');
    });
  });

  describe('Utility Methods', () => {
    describe('sumTransactionsByType', () => {
      it('should sum income transactions correctly', () => {
        const income = calculator['sumTransactionsByType'](mockTransactions, ['income']);
        expect(income).toBe(1000);
      });

      it('should sum expense transactions correctly', () => {
        const expenses = calculator['sumTransactionsByType'](mockTransactions, ['expense']);
        expect(expenses).toBe(500);
      });

      it('should handle empty transaction array', () => {
        const sum = calculator['sumTransactionsByType']([], ['income']);
        expect(sum).toBe(0);
      });

      it('should handle multiple transaction types', () => {
        const sum = calculator['sumTransactionsByType'](mockTransactions, ['income', 'expense']);
        expect(sum).toBe(1500);
      });
    });

    describe('sumTransactionsByCategory', () => {
      it('should sum transactions by category ID', () => {
        const categorySum = calculator['sumTransactionsByCategory'](mockTransactions, [1]);
        expect(categorySum).toBe(1000);
      });

      it('should handle null category IDs', () => {
        const transactionsWithNull = [
          ...mockTransactions,
          {
            ...mockTransactions[0],
            id: 3,
            category_id: null,
            amount: 200
          }
        ];
        
        const sum = calculator['sumTransactionsByCategory'](transactionsWithNull, [null]);
        expect(sum).toBe(200);
      });
    });

    describe('filterTransactionsByDateRange', () => {
      it('should filter transactions within date range', () => {
        const now = new Date();
        const startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // Yesterday
        const endDate = new Date(now.getTime() + 24 * 60 * 60 * 1000); // Tomorrow
        
        const filtered = calculator['filterTransactionsByDateRange'](
          mockTransactions, 
          startDate, 
          endDate
        );
        
        expect(filtered).toHaveLength(2); // Both transactions should be in range
      });

      it('should exclude transactions outside date range', () => {
        const futureDate = new Date('2026-01-01');
        const farFutureDate = new Date('2026-12-31');
        
        const filtered = calculator['filterTransactionsByDateRange'](
          mockTransactions, 
          futureDate, 
          farFutureDate
        );
        
        expect(filtered).toHaveLength(0);
      });
    });

    describe('getCurrentMonthTransactions', () => {
      it('should return current month transactions', () => {
        const currentMonthTransactions = calculator['getCurrentMonthTransactions'](mockTransactions);
        expect(currentMonthTransactions).toHaveLength(2); // Both mock transactions are current
      });
    });

    describe('formatCurrency', () => {
      it('should format INR currency correctly', () => {
        const formatted = calculator['formatCurrency'](1000);
        expect(formatted).toContain('1,000');
        expect(formatted).toContain('₹');
      });

      it('should format USD currency correctly', () => {
        const formatted = calculator['formatCurrency'](1000, 'USD');
        expect(formatted).toContain('1,000');
        expect(formatted).toContain('$');
      });

      it('should handle decimal amounts', () => {
        const formatted = calculator['formatCurrency'](1000.50);
        expect(formatted).toContain('1,000.50');
      });
    });

    describe('roundToTwoDecimals', () => {
      it('should round to two decimal places', () => {
        expect(calculator['roundToTwoDecimals'](1.2345)).toBe(1.23);
        expect(calculator['roundToTwoDecimals'](1.236)).toBe(1.24);
        expect(calculator['roundToTwoDecimals'](1)).toBe(1);
      });

      it('should handle negative numbers', () => {
        expect(calculator['roundToTwoDecimals'](-1.2345)).toBe(-1.23);
        expect(calculator['roundToTwoDecimals'](-1.236)).toBe(-1.24);
      });
    });
  });

  describe('Validation Methods', () => {
    describe('validateRequiredMetadata', () => {
      it('should pass when all required fields are present', () => {
        expect(() => {
          calculator['validateRequiredMetadata'](mockAccount, ['creditLimit']);
        }).not.toThrow();
      });

      it('should throw when metadata is missing', () => {
        const accountWithoutMetadata = { ...mockAccount };
        delete (accountWithoutMetadata as any).metadata;
        
        expect(() => {
          calculator['validateRequiredMetadata'](accountWithoutMetadata, ['creditLimit']);
        }).toThrow('checking account requires metadata');
      });

      it('should throw when required field is missing', () => {
        const accountWithPartialMetadata = {
          ...mockAccount,
          metadata: { interestRate: 12.5 } // missing creditLimit
        };
        
        expect(() => {
          calculator['validateRequiredMetadata'](accountWithPartialMetadata, ['creditLimit']);
        }).toThrow('checking account missing required metadata fields: creditLimit');
      });
    });

    describe('validatePositiveAmount', () => {
      it('should validate positive numbers', () => {
        const result = calculator['validatePositiveAmount'](100);
        expect(result.isValid).toBe(true);
        expect(result.error).toBeUndefined();
      });

      it('should reject zero amounts', () => {
        const result = calculator['validatePositiveAmount'](0);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Transaction amount must be a positive number');
      });

      it('should reject negative amounts', () => {
        const result = calculator['validatePositiveAmount'](-100);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Transaction amount must be a positive number');
      });

      it('should reject non-numeric amounts', () => {
        const result = calculator['validatePositiveAmount'](NaN);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Transaction amount must be a positive number');
      });
    });

    describe('validateFutureDate', () => {
      it('should validate future dates', () => {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 1);
        
        const result = calculator['validateFutureDate'](futureDate.toISOString());
        expect(result.isValid).toBe(true);
        expect(result.error).toBeUndefined();
      });

      it('should reject past dates', () => {
        const pastDate = new Date();
        pastDate.setDate(pastDate.getDate() - 1);
        
        const result = calculator['validateFutureDate'](pastDate.toISOString());
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Date must be in the future');
      });

      it('should reject invalid date formats', () => {
        const result = calculator['validateFutureDate']('invalid-date');
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Invalid date format');
      });
    });

    describe('validateAccountType', () => {
      it('should pass for valid account types', () => {
        expect(() => {
          calculator['validateAccountType'](mockAccount, ['checking', 'savings']);
        }).not.toThrow();
      });

      it('should throw for invalid account types', () => {
        expect(() => {
          calculator['validateAccountType'](mockAccount, ['credit', 'loan']);
        }).toThrow('TestAccountCalculator calculator cannot handle checking accounts');
      });
    });
  });

  describe('Helper Methods', () => {
    describe('createValidationResult', () => {
      it('should create valid validation result', () => {
        const result = calculator['createValidationResult'](true, [], []);
        
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.warnings).toHaveLength(0);
        expect(result.suggestedCorrections).toBeUndefined();
      });

      it('should create invalid validation result with corrections', () => {
        const corrections = { amount: 100 };
        const result = calculator['createValidationResult'](
          false, 
          ['Error message'], 
          ['Warning message'], 
          corrections
        );
        
        expect(result.isValid).toBe(false);
        expect(result.errors).toHaveLength(1);
        expect(result.warnings).toHaveLength(1);
        expect(result.suggestedCorrections).toEqual(corrections);
      });
    });

    describe('createDefaultAccountBalance', () => {
      it('should create default account balance', () => {
        const balance = calculator['createDefaultAccountBalance'](
          mockAccount, 
          1000, 
          'checking'
        );
        
        expect(balance.displayBalance).toBe(1000);
        expect(balance.actualBalance).toBe(1000);
        expect(balance.calculationType).toBe('checking');
        expect(balance.metadata.accountId).toBe(1);
        expect(balance.metadata.accountType).toBe('checking');
        expect(balance.metadata.currency).toBe('INR');
        expect(balance.lastCalculated).toBeInstanceOf(Date);
      });
    });
  });

  describe('Performance Monitoring', () => {
    describe('measurePerformance', () => {
      it('should measure operation performance', async () => {
        const operation = async () => {
          await new Promise(resolve => setTimeout(resolve, 10)); // Simulate work
          return 'test-result';
        };

        const { result, metrics } = await calculator['measurePerformance'](operation, 100);
        
        expect(result).toBe('test-result');
        expect(metrics.calculationTime).toBeGreaterThan(0);
        expect(metrics.transactionCount).toBe(100);
        expect(metrics.calculatorType).toBe('TestAccountCalculator');
        expect(metrics.warnings).toBeInstanceOf(Array);
      });

      it('should add performance warnings for slow operations', async () => {
        const slowOperation = async () => {
          await new Promise(resolve => setTimeout(resolve, 600)); // Simulate slow work
          return 'slow-result';
        };

        const { metrics } = await calculator['measurePerformance'](slowOperation, 10);
        
        expect(metrics.warnings).toContain('Calculation took longer than 500ms');
      });

      it('should add warnings for large transaction volumes with slow calculation', async () => {
        const operation = async () => {
          await new Promise(resolve => setTimeout(resolve, 300)); // Simulate work
          return 'result';
        };

        const { metrics } = await calculator['measurePerformance'](operation, 1500);
        
        expect(metrics.warnings).toContain('Large transaction volume with slow calculation');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle errors in calculateBalance gracefully', async () => {
      // Mock repository to throw error
      jest.spyOn(calculator['accountRepository'], 'getAccountWithMetadata')
        .mockRejectedValue(new Error('Database error'));

      await expect(calculator.calculateBalance(1, mockTransactions))
        .rejects.toThrow('Database error');
    });

    it('should validate transaction with error handling', async () => {
      const invalidTransaction = {
        ...mockTransactions[0],
        amount: -100 // Invalid negative amount
      };

      const result = await calculator.validateTransaction(invalidTransaction, mockAccount);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Amount must be positive');
    });
  });

  describe('Integration Tests', () => {
    it('should work end-to-end with repository', async () => {
      const balance = await calculator.calculateBalance(1, mockTransactions);
      
      expect(balance).toBeDefined();
      expect(balance.displayBalance).toBe(500);
      expect(balance.lastCalculated).toBeInstanceOf(Date);
      
      // Verify repository was called
      expect(calculator['accountRepository'].getAccountWithMetadata).toHaveBeenCalledWith(1);
    });

    it('should handle empty transaction list', async () => {
      const balance = await calculator.calculateBalance(1, []);
      
      expect(balance.displayBalance).toBe(0);
      expect(balance.actualBalance).toBe(0);
    });
  });
});