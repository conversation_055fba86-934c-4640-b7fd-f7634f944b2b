import { AccountService } from '@/business/services/AccountService';
import { AccountRepository } from '@/data/repositories/AccountRepository';
import { TransactionRepository } from '@/data/repositories/TransactionRepository';
import { Account, Transaction, AccountMetadata } from '@/shared/types';

// Mock the repositories
jest.mock('@/data/repositories/AccountRepository');
jest.mock('@/data/repositories/TransactionRepository');

const MockedAccountRepository = AccountRepository as jest.MockedClass<typeof AccountRepository>;
const MockedTransactionRepository = TransactionRepository as jest.MockedClass<typeof TransactionRepository>;

describe('AccountService', () => {
  let accountService: AccountService;
  let mockAccountRepo: jest.Mocked<AccountRepository>;
  let mockTransactionRepo: jest.Mocked<TransactionRepository>;

  const mockAccount: Account = {
    id: 1,
    name: 'Test Account',
    type: 'checking',
    balance: 1000,
    currency: 'INR',
    is_active: true,
    sync_status: 'local',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  const mockTransaction: Transaction = {
    id: 1,
    account_id: 1,
    amount: 100,
    description: 'Test transaction',
    category_id: null,
    transaction_type: 'expense',
    transaction_date: '2024-01-01',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    sms_source: null,
    confidence_score: null,
    is_recurring: false,
    recurring_pattern: null,
    sync_status: 'local',
    hash: 'test-hash',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockAccountRepo = new MockedAccountRepository() as jest.Mocked<AccountRepository>;
    mockTransactionRepo = new MockedTransactionRepository() as jest.Mocked<TransactionRepository>;
    
    // Add missing mock methods
    mockAccountRepo.getTotalBalance = jest.fn();
    mockAccountRepo.getBalanceByType = jest.fn();
    mockAccountRepo.updateBalance = jest.fn();
    mockAccountRepo.createAccountMetadata = jest.fn();
    mockAccountRepo.updateAccountMetadata = jest.fn();
    mockAccountRepo.getAccountMetadata = jest.fn();
    mockAccountRepo.getAccountWithMetadata = jest.fn();
    mockAccountRepo.deleteAccountMetadata = jest.fn();
    mockAccountRepo.getCreditCardAccounts = jest.fn();
    mockAccountRepo.getLoanAccounts = jest.fn();
    mockAccountRepo.getAccountsByType = jest.fn();
    mockAccountRepo.deleteWithMetadata = jest.fn();
    
    // Add missing transaction repository mock methods
    mockTransactionRepo.findByAccount = jest.fn();
    
    accountService = new AccountService();
    // Inject mocked dependencies
    (accountService as any).accountRepository = mockAccountRepo;
    (accountService as any).transactionRepository = mockTransactionRepo;
  });

  describe('createAccount', () => {
    it('should create a valid account successfully', async () => {
      const accountData = {
        name: 'New Account',
        type: 'savings' as const,
        balance: 500,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      mockAccountRepo.findAll.mockResolvedValue([]);
      mockAccountRepo.create.mockResolvedValue({ ...mockAccount, ...accountData });

      const result = await accountService.createAccount(accountData);

      expect(mockAccountRepo.create).toHaveBeenCalledWith(accountData);
      expect(result.name).toBe(accountData.name);
      expect(result.type).toBe(accountData.type);
    });

    it('should throw error for invalid account name', async () => {
      const accountData = {
        name: '', // Invalid empty name
        type: 'checking' as const,
        balance: 0,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      await expect(accountService.createAccount(accountData))
        .rejects
        .toThrow('Validation failed: Account name is required');
    });

    it('should throw error when free tier limit is reached', async () => {
      const accountData = {
        name: 'New Account',
        type: 'checking' as const,
        balance: 0,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      // Mock 5 existing accounts (free tier limit)
      const existingAccounts = Array.from({ length: 5 }, (_, i) => ({
        ...mockAccount,
        id: i + 1,
        name: `Account ${i + 1}`,
      }));
      mockAccountRepo.findAll.mockResolvedValue(existingAccounts);

      await expect(accountService.createAccount(accountData))
        .rejects
        .toThrow('Free tier limit reached. Maximum 5 accounts allowed.');
    });

    it('should throw error for duplicate account name', async () => {
      const accountData = {
        name: 'Test Account', // Same as mockAccount
        type: 'checking' as const,
        balance: 0,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      mockAccountRepo.findAll.mockResolvedValue([mockAccount]);

      await expect(accountService.createAccount(accountData))
        .rejects
        .toThrow('Account name already exists');
    });
  });

  describe('updateAccount', () => {
    it('should update account successfully', async () => {
      const updateData = { name: 'Updated Account' };
      const updatedAccount = { ...mockAccount, ...updateData };

      mockAccountRepo.findById.mockResolvedValue(mockAccount);
      mockAccountRepo.findAll.mockResolvedValue([mockAccount]);
      mockAccountRepo.update.mockResolvedValue(updatedAccount);

      const result = await accountService.updateAccount(1, updateData);

      expect(mockAccountRepo.update).toHaveBeenCalledWith(1, { ...updateData, sync_status: 'local' });
      expect(result.name).toBe(updateData.name);
    });

    it('should throw error when account not found', async () => {
      mockAccountRepo.findById.mockResolvedValue(null);

      await expect(accountService.updateAccount(999, { name: 'Updated' }))
        .rejects
        .toThrow('Account not found');
    });

    it('should throw error for duplicate name during update', async () => {
      const anotherAccount = { ...mockAccount, id: 2, name: 'Another Account' };
      const updateData = { name: 'Another Account' };

      mockAccountRepo.findById.mockResolvedValue(mockAccount);
      mockAccountRepo.findAll.mockResolvedValue([mockAccount, anotherAccount]);

      await expect(accountService.updateAccount(1, updateData))
        .rejects
        .toThrow('Account name already exists');
    });
  });

  describe('deleteAccount', () => {
    it('should delete account successfully when no transactions exist', async () => {
      mockAccountRepo.findById.mockResolvedValue(mockAccount);
      mockTransactionRepo.findByAccount.mockResolvedValue([]);
      mockAccountRepo.delete.mockResolvedValue(true);

      const result = await accountService.deleteAccount(1);

      expect(mockAccountRepo.delete).toHaveBeenCalledWith(1);
      expect(result).toEqual({ success: true, transactionCount: 0 });
    });

    it('should handle account with transactions', async () => {
      mockAccountRepo.findById.mockResolvedValue(mockAccount);
      mockTransactionRepo.findByAccount.mockResolvedValue([mockTransaction]);

      const result = await accountService.deleteAccount(1);

      expect(result).toEqual({ success: undefined, transactionCount: 1 });
    });

    it('should throw error when account not found', async () => {
      mockAccountRepo.findById.mockResolvedValue(null);

      await expect(accountService.deleteAccount(999))
        .rejects
        .toThrow('Account not found');
    });
  });



  describe('calculateAccountBalance', () => {
    it('should calculate balance from transactions correctly', async () => {
      const transactions = [
        { ...mockTransaction, amount: 1000, transaction_type: 'income' as const },
        { ...mockTransaction, amount: 300, transaction_type: 'expense' as const },
        { ...mockTransaction, amount: 200, transaction_type: 'expense' as const },
      ];

      mockTransactionRepo.findByAccount.mockResolvedValue(transactions);

      const balance = await accountService.calculateAccountBalance(1);

      expect(balance).toBe(500); // 1000 - 300 - 200
    });

    it('should return 0 for account with no transactions', async () => {
      mockTransactionRepo.findByAccount.mockResolvedValue([]);

      const balance = await accountService.calculateAccountBalance(1);

      expect(balance).toBe(0);
    });
  });

  describe('recalculateAccountBalance', () => {
    it('should recalculate and update account balance', async () => {
      const updatedAccount = { ...mockAccount, balance: 500 };
      const transactions = [
        { ...mockTransaction, amount: 500, transaction_type: 'income' as const },
      ];

      mockTransactionRepo.findByAccount.mockResolvedValue(transactions);
      mockAccountRepo.updateBalance.mockResolvedValue(updatedAccount);

      const result = await accountService.recalculateAccountBalance(1);

      expect(mockAccountRepo.updateBalance).toHaveBeenCalledWith(1, 500, 'set');
      expect(result).toEqual(updatedAccount);
    });
  });

  describe('getAccountSummary', () => {
    it('should generate account summary correctly', async () => {
      const accounts = [
        { ...mockAccount, id: 1, type: 'checking' as const, balance: 1000 },
        { ...mockAccount, id: 2, type: 'savings' as const, balance: 2000 },
        { ...mockAccount, id: 3, type: 'credit' as const, balance: -500 },
      ];

      mockAccountRepo.findAll.mockResolvedValue(accounts);
      mockAccountRepo.getTotalBalance.mockResolvedValue(2500);
      mockAccountRepo.getBalanceByType.mockResolvedValue({
        checking: 1000,
        savings: 2000,
        credit: -500,
        loan: 0,
        investment: 0
      });

      const summary = await accountService.getAccountSummary();

      expect(summary.accountCount).toBe(3);
      expect(summary.totalBalance).toBe(2500); // 1000 + 2000 + (-500)
      expect(summary.balancesByType.checking).toBe(1000);
      expect(summary.balancesByType.savings).toBe(2000);
      expect(summary.balancesByType.credit).toBe(-500);
    });

    it('should handle empty account list', async () => {
      mockAccountRepo.findAll.mockResolvedValue([]);
      mockAccountRepo.getTotalBalance.mockResolvedValue(0);
      mockAccountRepo.getBalanceByType.mockResolvedValue({
        checking: 0,
        savings: 0,
        credit: 0,
        loan: 0,
        investment: 0
      });

      const summary = await accountService.getAccountSummary();

      expect(summary.accountCount).toBe(0);
      expect(summary.totalBalance).toBe(0);
      expect(summary.balancesByType.checking).toBe(0);
      expect(summary.balancesByType.savings).toBe(0);
    });
  });

  describe('isFreeTierLimitReached', () => {
    it('should return false when under limit', async () => {
      const accounts = Array.from({ length: 3 }, (_, i) => ({
        ...mockAccount,
        id: i + 1,
      }));
      mockAccountRepo.findAll.mockResolvedValue(accounts);

      const result = await accountService.isFreeTierLimitReached();

      expect(result).toBe(false);
    });

    it('should return true when at limit', async () => {
      const accounts = Array.from({ length: 5 }, (_, i) => ({
        ...mockAccount,
        id: i + 1,
      }));
      mockAccountRepo.findAll.mockResolvedValue(accounts);

      const result = await accountService.isFreeTierLimitReached();

      expect(result).toBe(true);
    });
  });

  describe('validateAccountData', () => {
    it('should validate correct account data', () => {
      const validData = {
        name: 'Valid Account',
        type: 'checking' as const,
        balance: 1000,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      expect(() => (accountService as any).validateAccountData(validData))
        .not.toThrow();
    });

    it('should return validation errors for invalid account name', async () => {
      const invalidData = {
        name: '',
        type: 'checking' as const,
        balance: 1000,
        currency: 'INR' as const
      };

      const result = await (accountService as any).validateAccountData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Account name is required');
    });

    it('should return validation errors for invalid balance', async () => {
      const invalidData = {
        name: 'Valid Account',
        type: 'checking' as const,
        balance: **********, // Too large
        currency: 'INR' as const
      };

      const result = await (accountService as any).validateAccountData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Balance must be between -999,999,999.99 and 999,999,999.99');
    });

    it('should return validation errors for invalid account type', async () => {
      const invalidData = {
        name: 'Valid Account',
        type: 'invalid' as any,
        balance: 0,
        currency: 'INR' as const
      };

      const result = await (accountService as any).validateAccountData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Valid account type is required');
    });
  });

  // New tests for metadata functionality
  describe('Account Metadata Management', () => {
    const mockCreditMetadata: AccountMetadata = {
      creditLimit: 50000,
      outstandingBalance: 15000,
      minimumPaymentDue: 1500,
      paymentDueDate: '2024-02-15T00:00:00.000Z',
      autoCalculate: true
    };

    const mockLoanMetadata: AccountMetadata = {
      principalAmount: 500000,
      currentPrincipal: 350000,
      interestRate: 8.5,
      emiAmount: 6000,
      tenure: 60,
      remainingTenure: 42,
      nextEmiDate: '2024-02-01T00:00:00.000Z',
      autoCalculate: true
    };

    describe('createAccountWithMetadata', () => {
      it('should create credit card account with metadata', async () => {
        const accountData = {
          name: 'HDFC Credit Card',
          type: 'credit' as const,
          balance: -15000,
          currency: 'INR',
          is_active: true,
          sync_status: 'local' as const,
        };

        const expectedAccount = { ...mockAccount, ...accountData, metadata: mockCreditMetadata };

        mockAccountRepo.findAll.mockResolvedValue([]);
        mockAccountRepo.create.mockResolvedValue({ ...mockAccount, ...accountData });
        mockAccountRepo.createAccountMetadata.mockResolvedValue(undefined);
        mockAccountRepo.getAccountWithMetadata.mockResolvedValue(expectedAccount);

        const result = await accountService.createAccountWithMetadata(accountData, mockCreditMetadata);

        expect(mockAccountRepo.createAccountMetadata).toHaveBeenCalledWith(mockAccount.id, mockCreditMetadata);
        expect(result.metadata).toEqual(mockCreditMetadata);
      });

      it('should create loan account with metadata', async () => {
        const accountData = {
          name: 'Home Loan',
          type: 'loan' as const,
          balance: -350000,
          currency: 'INR',
          is_active: true,
          sync_status: 'local' as const,
        };

        const expectedAccount = { ...mockAccount, ...accountData, metadata: mockLoanMetadata };

        mockAccountRepo.findAll.mockResolvedValue([]);
        mockAccountRepo.create.mockResolvedValue({ ...mockAccount, ...accountData });
        mockAccountRepo.createAccountMetadata.mockResolvedValue(undefined);
        mockAccountRepo.getAccountWithMetadata.mockResolvedValue(expectedAccount);

        const result = await accountService.createAccountWithMetadata(accountData, mockLoanMetadata);

        expect(mockAccountRepo.createAccountMetadata).toHaveBeenCalledWith(mockAccount.id, mockLoanMetadata);
        expect(result.metadata).toEqual(mockLoanMetadata);
      });

      it('should create account without metadata when not provided', async () => {
        const accountData = {
          name: 'Savings Account',
          type: 'savings' as const,
          balance: 10000,
          currency: 'INR',
          is_active: true,
          sync_status: 'local' as const,
        };

        mockAccountRepo.findAll.mockResolvedValue([]);
        mockAccountRepo.create.mockResolvedValue({ ...mockAccount, ...accountData });

        const result = await accountService.createAccountWithMetadata(accountData);

        expect(mockAccountRepo.createAccountMetadata).not.toHaveBeenCalled();
        expect(result.metadata).toBeUndefined();
      });

      it('should validate metadata for credit cards', async () => {
        const accountData = {
          name: 'Credit Card',
          type: 'credit' as const,
          balance: 0,
          currency: 'INR',
          is_active: true,
          sync_status: 'local' as const,
        };

        const invalidMetadata: AccountMetadata = {
          creditLimit: -1000, // Invalid negative limit
        };

        mockAccountRepo.findAll.mockResolvedValue([]);

        await expect(accountService.createAccountWithMetadata(accountData, invalidMetadata))
          .rejects
          .toThrow('Metadata validation failed');
      });
    });

    describe('updateAccountMetadata', () => {
      it('should update account metadata successfully', async () => {
        const updateData = {
          outstandingBalance: 20000,
          minimumPaymentDue: 2000,
        };

        const creditAccount = { ...mockAccount, type: 'credit' as const };
        mockAccountRepo.findById.mockResolvedValue(creditAccount);
        mockAccountRepo.updateAccountMetadata.mockResolvedValue(undefined);

        await accountService.updateAccountMetadata(1, updateData);

        expect(mockAccountRepo.updateAccountMetadata).toHaveBeenCalledWith(1, updateData);
      });

      it('should throw error when account not found', async () => {
        mockAccountRepo.findById.mockResolvedValue(null);

        await expect(accountService.updateAccountMetadata(999, { creditLimit: 50000 }))
          .rejects
          .toThrow('Account not found');
      });

      it('should validate metadata before update', async () => {
        const creditAccount = { ...mockAccount, type: 'credit' as const };
        const invalidUpdate = { creditLimit: -5000 };

        mockAccountRepo.findById.mockResolvedValue(creditAccount);

        await expect(accountService.updateAccountMetadata(1, invalidUpdate))
          .rejects
          .toThrow('Metadata validation failed');
      });
    });

    describe('getAccountWithFullDetails', () => {
      it('should return account with metadata', async () => {
        const accountWithMetadata = { ...mockAccount, metadata: mockCreditMetadata };
        mockAccountRepo.getAccountWithMetadata.mockResolvedValue(accountWithMetadata);

        const result = await accountService.getAccountWithFullDetails(1);

        expect(result?.metadata).toEqual(mockCreditMetadata);
        expect(mockAccountRepo.getAccountWithMetadata).toHaveBeenCalledWith(1);
      });

      it('should return null for non-existent account', async () => {
        mockAccountRepo.getAccountWithMetadata.mockResolvedValue(null);

        const result = await accountService.getAccountWithFullDetails(999);

        expect(result).toBeNull();
      });
    });

    describe('detectAccountType', () => {
      it('should detect credit card from name', () => {
        const result = accountService.detectAccountType('HDFC Credit Card');
        expect(result).toBe('credit');
      });

      it('should detect loan from name', () => {
        const result = accountService.detectAccountType('Home Loan Account');
        expect(result).toBe('loan');
      });

      it('should detect savings from name', () => {
        const result = accountService.detectAccountType('Savings Account');
        expect(result).toBe('savings');
      });

      it('should detect investment from name', () => {
        const result = accountService.detectAccountType('Mutual Fund Investment');
        expect(result).toBe('investment');
      });

      it('should detect credit from metadata', () => {
        const result = accountService.detectAccountType('Account', { creditLimit: 50000 });
        expect(result).toBe('credit');
      });

      it('should detect loan from metadata', () => {
        const result = accountService.detectAccountType('Account', { emiAmount: 5000 });
        expect(result).toBe('loan');
      });

      it('should default to checking', () => {
        const result = accountService.detectAccountType('Regular Account');
        expect(result).toBe('checking');
      });
    });

    describe('Account type specific getters', () => {
      it('should get credit card accounts', async () => {
        const creditAccounts = [{ ...mockAccount, type: 'credit' as const }];
        mockAccountRepo.getAccountsByType.mockResolvedValue(creditAccounts);

        const result = await accountService.getCreditCardAccounts();

        expect(mockAccountRepo.getAccountsByType).toHaveBeenCalledWith('credit');
        expect(result).toEqual(creditAccounts);
      });

      it('should get loan accounts', async () => {
        const loanAccounts = [{ ...mockAccount, type: 'loan' as const }];
        mockAccountRepo.getAccountsByType.mockResolvedValue(loanAccounts);

        const result = await accountService.getLoanAccounts();

        expect(mockAccountRepo.getAccountsByType).toHaveBeenCalledWith('loan');
        expect(result).toEqual(loanAccounts);
      });
    });

    describe('deleteAccountWithMetadata', () => {
      it('should delete account with metadata successfully', async () => {
        mockAccountRepo.findById.mockResolvedValue(mockAccount);
        mockTransactionRepo.findByAccount.mockResolvedValue([]);
        mockAccountRepo.deleteWithMetadata.mockResolvedValue(true);

        const result = await accountService.deleteAccountWithMetadata(1);

        expect(mockAccountRepo.deleteWithMetadata).toHaveBeenCalledWith(1);
        expect(result).toEqual({ success: true, transactionCount: 0 });
      });

      it('should handle transactions during deletion', async () => {
        const transferAccountId = 2;
        const transferAccount = { ...mockAccount, id: transferAccountId };

        mockAccountRepo.findById
          .mockResolvedValueOnce(mockAccount)
          .mockResolvedValueOnce(transferAccount);
        mockTransactionRepo.findByAccount.mockResolvedValue([mockTransaction]);
        mockTransactionRepo.update.mockResolvedValue(mockTransaction);
        mockAccountRepo.updateBalance.mockResolvedValue(transferAccount);
        mockAccountRepo.deleteWithMetadata.mockResolvedValue(true);

        const result = await accountService.deleteAccountWithMetadata(1, transferAccountId);

        expect(mockTransactionRepo.update).toHaveBeenCalledWith(mockTransaction.id, {
          account_id: transferAccountId,
          sync_status: 'local'
        });
        expect(result).toEqual({ success: true, transactionCount: 1 });
      });
    });

    describe('Utility methods', () => {
      it('should check if account type requires metadata', () => {
        expect(accountService.requiresMetadata('credit')).toBe(true);
        expect(accountService.requiresMetadata('loan')).toBe(true);
        expect(accountService.requiresMetadata('checking')).toBe(false);
        expect(accountService.requiresMetadata('savings')).toBe(false);
      });

      it('should check if account type is supported', () => {
        expect(accountService.isAccountTypeSupported('credit')).toBe(true);
        expect(accountService.isAccountTypeSupported('loan')).toBe(true);
        expect(accountService.isAccountTypeSupported('checking')).toBe(true);
      });
    });
  });
});