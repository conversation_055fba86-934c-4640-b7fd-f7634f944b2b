import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { AccountManagementScreen } from '@/presentation/screens/accounts/AccountManagementScreen';
import { useAccountStore } from '@/shared/stores/accountStore';
import { Account } from '@/shared/types';

// Mock the account store
jest.mock('@/shared/stores/accountStore');
const mockUseAccountStore = useAccountStore as jest.MockedFunction<typeof useAccountStore>;

// Mock Alert
jest.spyOn(Alert, 'alert');

// Mock navigation
const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
  setOptions: jest.fn(),
};

describe('AccountManagementScreen', () => {
  const mockLoadAccounts = jest.fn();
  const mockCreateAccount = jest.fn();
  const mockUpdateAccount = jest.fn();
  const mockDeleteAccount = jest.fn();
  const mockTransferBalance = jest.fn();
  const mockSelectAccount = jest.fn();
  
  const mockAccounts: Account[] = [
    {
      id: 1,
      name: 'Checking Account',
      type: 'checking',
      balance: 1500.50,
      currency: 'INR',
      is_active: true,
      sync_status: 'synced',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T12:00:00Z',
    },
    {
      id: 2,
      name: 'Savings Account',
      type: 'savings',
      balance: 5000.00,
      currency: 'INR',
      is_active: true,
      sync_status: 'local',
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T12:00:00Z',
    },
  ];

  const mockAccountSummary = {
    totalAccounts: 2,
    totalBalance: 6500.50,
    accountsByType: {
      checking: { count: 1, balance: 1500.50 },
      savings: { count: 1, balance: 5000.00 },
      credit: { count: 0, balance: 0 },
      loan: { count: 0, balance: 0 },
      investment: { count: 0, balance: 0 },
    },
    lastUpdated: new Date('2024-01-02T12:00:00Z'),
  };

  const mockStoreState = {
    accounts: mockAccounts,
    loading: false,
    error: null,
    selectedAccountId: null,
    accountSummary: mockAccountSummary,
    loadAccounts: mockLoadAccounts,
    createAccount: mockCreateAccount,
    updateAccount: mockUpdateAccount,
    deleteAccount: mockDeleteAccount,
    transferBalance: mockTransferBalance,
    selectAccount: mockSelectAccount,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAccountStore.mockReturnValue(mockStoreState as any);
  });

  const defaultProps = {
    navigation: mockNavigation as any,
    route: { params: {} } as any,
  };

  it('should render screen title and main components', () => {
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    expect(getByText('Account Management')).toBeTruthy();
    expect(getByText('Create Account')).toBeTruthy();
  });

  it('should display account list', () => {
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    expect(getByText('Checking Account')).toBeTruthy();
    expect(getByText('Savings Account')).toBeTruthy();
  });

  it('should show account summary', () => {
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    expect(getByText('Account Summary')).toBeTruthy();
    expect(getByText('Total Accounts: 2')).toBeTruthy();
    expect(getByText('Total Balance: ₹6,500.50')).toBeTruthy();
  });

  it('should open create account modal when create button is pressed', () => {
    const { getByText, queryByText } = render(
      <AccountManagementScreen {...defaultProps} />
    );

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    expect(queryByText('Create New Account')).toBeTruthy();
  });

  it('should close create account modal when cancel is pressed', () => {
    const { getByText, queryByText } = render(
      <AccountManagementScreen {...defaultProps} />
    );

    // Open modal
    const createButton = getByText('Create Account');
    fireEvent.press(createButton);
    expect(queryByText('Create New Account')).toBeTruthy();

    // Close modal
    const cancelButton = getByText('Cancel');
    fireEvent.press(cancelButton);
    
    // Modal should be closed (this might need adjustment based on actual modal implementation)
    expect(queryByText('Create New Account')).toBeNull();
  });

  it('should handle successful account creation', async () => {
    const newAccount: Account = {
      id: 3,
      name: 'New Account',
      type: 'investment',
      balance: 1000,
      currency: 'INR',
      is_active: true,
      sync_status: 'local',
      created_at: '2024-01-03T00:00:00Z',
      updated_at: '2024-01-03T00:00:00Z',
    };

    mockCreateAccount.mockResolvedValue(newAccount);

    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    // Open modal
    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    // Fill form and submit (this would need to be adjusted based on actual form implementation)
    // For now, we'll simulate the success callback
    render(<AccountManagementScreen {...defaultProps} />);
    
    // Simulate successful account creation
    await waitFor(() => {
      expect(mockLoadAccounts).toHaveBeenCalled();
    });
  });

  it('should show loading state', () => {
    mockUseAccountStore.mockReturnValue({
      ...mockStoreState,
      loading: true,
    } as any);

    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    expect(getByText('Loading accounts...')).toBeTruthy();
  });

  it('should show error state', () => {
    const errorMessage = 'Failed to load accounts';
    mockUseAccountStore.mockReturnValue({
      ...mockStoreState,
      error: errorMessage,
    } as any);

    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    expect(getByText('Error loading accounts')).toBeTruthy();
    expect(getByText(errorMessage)).toBeTruthy();
  });

  it('should handle account selection', () => {
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    const checkingAccount = getByText('Checking Account');
    fireEvent.press(checkingAccount.parent);

    expect(mockSelectAccount).toHaveBeenCalledWith('1');
  });

  it('should handle account long press with edit and delete options', () => {
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    const checkingAccount = getByText('Checking Account');
    fireEvent(checkingAccount.parent, 'longPress');

    expect(Alert.alert).toHaveBeenCalledWith(
      'Account Options',
      `Options for: ${mockAccounts[0].name}`,
      expect.arrayContaining([
        expect.objectContaining({ text: 'Edit' }),
        expect.objectContaining({ text: 'Transfer Balance' }),
        expect.objectContaining({ text: 'Delete', style: 'destructive' }),
        expect.objectContaining({ text: 'Cancel', style: 'cancel' }),
      ])
    );
  });

  it('should handle account edit from long press', async () => {
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    const checkingAccount = getByText('Checking Account');
    fireEvent(checkingAccount.parent, 'longPress');

    // Get the alert call and simulate pressing Edit
    const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const editOption = alertCall[2].find((option: any) => option.text === 'Edit');
    
    editOption.onPress();

    // Should show edit form (implementation would depend on actual edit modal)
    expect(Alert.alert).toHaveBeenCalled();
  });

  it('should handle balance transfer from long press', async () => {
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    const checkingAccount = getByText('Checking Account');
    fireEvent(checkingAccount.parent, 'longPress');

    // Get the alert call and simulate pressing Transfer Balance
    const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const transferOption = alertCall[2].find((option: any) => option.text === 'Transfer Balance');
    
    transferOption.onPress();

    // Should show transfer form
    expect(Alert.alert).toHaveBeenCalled();
  });

  it('should handle account deletion from long press', async () => {
    mockDeleteAccount.mockResolvedValue(true);
    
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    const checkingAccount = getByText('Checking Account');
    fireEvent(checkingAccount.parent, 'longPress');

    // Get the alert call and simulate pressing Delete
    const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const deleteOption = alertCall[2].find((option: any) => option.text === 'Delete');
    
    await deleteOption.onPress();

    // Should show confirmation dialog
    expect(Alert.alert).toHaveBeenCalledWith(
      'Delete Account',
      `Are you sure you want to delete "${mockAccounts[0].name}"? This action cannot be undone.`,
      expect.arrayContaining([
        expect.objectContaining({ text: 'Cancel', style: 'cancel' }),
        expect.objectContaining({ text: 'Delete', style: 'destructive' }),
      ])
    );
  });

  it('should confirm account deletion', async () => {
    mockDeleteAccount.mockResolvedValue(true);
    
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    const checkingAccount = getByText('Checking Account');
    fireEvent(checkingAccount.parent, 'longPress');

    // First alert - options menu
    const firstAlertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const deleteOption = firstAlertCall[2].find((option: any) => option.text === 'Delete');
    
    await deleteOption.onPress();

    // Second alert - confirmation
    const secondAlertCall = (Alert.alert as jest.Mock).mock.calls[1];
    const confirmDeleteOption = secondAlertCall[2].find((option: any) => option.text === 'Delete');
    
    await confirmDeleteOption.onPress();

    expect(mockDeleteAccount).toHaveBeenCalledWith('1');
  });

  it('should handle balance transfer between accounts', async () => {
    mockTransferBalance.mockResolvedValue(true);
    
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    const checkingAccount = getByText('Checking Account');
    fireEvent(checkingAccount.parent, 'longPress');

    // Get the alert call and simulate pressing Transfer Balance
    const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const transferOption = alertCall[2].find((option: any) => option.text === 'Transfer Balance');
    
    transferOption.onPress();

    // This would open a transfer form - implementation depends on actual form
    expect(Alert.alert).toHaveBeenCalled();
  });

  it('should handle pull to refresh', async () => {
    const { getByTestId } = render(<AccountManagementScreen {...defaultProps} />);

    // Assuming the AccountList component has a testID
    const accountList = getByTestId('account-flat-list');
    fireEvent(accountList, 'refresh');

    expect(mockLoadAccounts).toHaveBeenCalled();
  });

  it('should show empty state when no accounts', () => {
    mockUseAccountStore.mockReturnValue({
      ...mockStoreState,
      accounts: [],
      accountSummary: {
        ...mockAccountSummary,
        totalAccounts: 0,
        totalBalance: 0,
      },
    } as any);

    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    expect(getByText('No accounts found')).toBeTruthy();
    expect(getByText('Create your first account to get started.')).toBeTruthy();
  });

  it('should load accounts on screen focus', () => {
    render(<AccountManagementScreen {...defaultProps} />);

    expect(mockLoadAccounts).toHaveBeenCalled();
  });

  it('should handle account update successfully', async () => {
    const updatedAccount = { ...mockAccounts[0], name: 'Updated Account' };
    mockUpdateAccount.mockResolvedValue(updatedAccount);

    render(<AccountManagementScreen {...defaultProps} />);

    // Simulate account update (this would depend on actual edit form implementation)
    await waitFor(() => {
      expect(mockLoadAccounts).toHaveBeenCalled();
    });
  });

  it('should handle account update error', async () => {
    const errorMessage = 'Update failed';
    mockUpdateAccount.mockRejectedValue(new Error(errorMessage));

    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    // Simulate account update error
    // This would depend on actual error handling implementation
    expect(getByText('Checking Account')).toBeTruthy();
  });

  it('should handle transfer balance error', async () => {
    const errorMessage = 'Transfer failed';
    mockTransferBalance.mockRejectedValue(new Error(errorMessage));

    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    // Simulate transfer error
    // This would depend on actual error handling implementation
    expect(getByText('Checking Account')).toBeTruthy();
  });

  it('should handle delete account error', async () => {
    const errorMessage = 'Delete failed';
    mockDeleteAccount.mockRejectedValue(new Error(errorMessage));

    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    const checkingAccount = getByText('Checking Account');
    fireEvent(checkingAccount.parent, 'longPress');

    // Simulate delete error
    const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const deleteOption = alertCall[2].find((option: any) => option.text === 'Delete');
    
    await deleteOption.onPress();

    // Should still show confirmation dialog
    expect(Alert.alert).toHaveBeenCalled();
  });

  it('should display sorting and filtering options', () => {
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    // These would depend on actual UI implementation
    expect(getByText('Account Management')).toBeTruthy();
  });

  it('should handle screen navigation', () => {
    const { getByText } = render(<AccountManagementScreen {...defaultProps} />);

    // Test any navigation that might occur
    expect(getByText('Account Management')).toBeTruthy();
  });
});