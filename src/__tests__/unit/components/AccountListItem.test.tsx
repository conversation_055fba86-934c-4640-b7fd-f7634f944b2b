import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { AccountListItem, formatCurrency, getAccountTypeConfig } from '@/presentation/components/accounts/AccountListItem';
import { Account } from '@/shared/types';

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('AccountListItem', () => {
  const mockAccount: Account = {
    id: 1,
    name: 'Test Checking Account',
    type: 'checking',
    balance: 1500.50,
    currency: 'INR',
    is_active: true,
    sync_status: 'synced',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T12:00:00Z',
  };

  const mockOnPress = jest.fn();
  const mockOnLongPress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    account: mockAccount,
    onPress: mockOnPress,
    onLongPress: mockOnLongPress,
  };

  it('should render account information correctly', () => {
    const { getByText } = render(<AccountListItem {...defaultProps} />);

    expect(getByText('Test Checking Account')).toBeTruthy();
    expect(getByText('Checking')).toBeTruthy();
    expect(getByText('₹1,500.50')).toBeTruthy();
  });

  it('should display account type icon and color', () => {
    const { getByText } = render(<AccountListItem {...defaultProps} />);

    const typeLabel = getByText('Checking');
    expect(typeLabel).toBeTruthy();
    
    // The icon should be rendered (🏦 for checking accounts)
    const iconText = getByText('🏦');
    expect(iconText).toBeTruthy();
  });

  it('should show sync status indicator', () => {
    const { getByText } = render(<AccountListItem {...defaultProps} />);

    // Should show synced status
    expect(getByText('✓')).toBeTruthy();
  });

  it('should show different sync status for local accounts', () => {
    const localAccount = { ...mockAccount, sync_status: 'local' as const };
    const { getByText } = render(
      <AccountListItem {...defaultProps} account={localAccount} />
    );

    // Should show local status
    expect(getByText('📱')).toBeTruthy();
  });

  it('should show different sync status for pending accounts', () => {
    const pendingAccount = { ...mockAccount, sync_status: 'pending' as const };
    const { getByText } = render(
      <AccountListItem {...defaultProps} account={pendingAccount} />
    );

    // Should show pending status
    expect(getByText('⏳')).toBeTruthy();
  });

  it('should hide balance when showBalance is false', () => {
    const { queryByText } = render(
      <AccountListItem {...defaultProps} showBalance={false} />
    );

    expect(queryByText('₹1,500.50')).toBeNull();
  });

  it('should show selected state when isSelected is true', () => {
    const { getByTestId } = render(
      <AccountListItem {...defaultProps} isSelected={true} />
    );

    const container = getByTestId('account-list-item');
    expect(container.props.style).toEqual(
      expect.objectContaining({
        backgroundColor: expect.any(String),
        borderWidth: 2,
        borderColor: '#4A90E2',
      })
    );
  });

  it('should call onPress when pressed', () => {
    const { getByTestId } = render(<AccountListItem {...defaultProps} />);

    const container = getByTestId('account-list-item');
    fireEvent.press(container);

    expect(mockOnPress).toHaveBeenCalledWith(mockAccount);
  });

  it('should call onLongPress when long pressed', () => {
    const { getByTestId } = render(<AccountListItem {...defaultProps} />);

    const container = getByTestId('account-list-item');
    fireEvent(container, 'longPress');

    expect(mockOnLongPress).toHaveBeenCalledWith(mockAccount);
  });

  it('should show default alert when no onPress is provided', () => {
    const { getByTestId } = render(
      <AccountListItem account={mockAccount} />
    );

    const container = getByTestId('account-list-item');
    fireEvent.press(container);

    expect(Alert.alert).toHaveBeenCalledWith(
      'Account Selected',
      `You selected: ${mockAccount.name}`
    );
  });

  it('should show default alert when no onLongPress is provided', () => {
    const { getByTestId } = render(
      <AccountListItem account={mockAccount} />
    );

    const container = getByTestId('account-list-item');
    fireEvent(container, 'longPress');

    expect(Alert.alert).toHaveBeenCalledWith(
      'Account Options',
      `Options for: ${mockAccount.name}`,
      [
        { text: 'Edit', onPress: expect.any(Function) },
        { text: 'Delete', style: 'destructive', onPress: expect.any(Function) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  });

  it('should render different account types correctly', () => {
    const accountTypes: Account['type'][] = ['checking', 'savings', 'credit', 'loan', 'investment'];
    
    accountTypes.forEach(type => {
      const account = { ...mockAccount, type };
      const { getByText } = render(
        <AccountListItem account={account} />
      );
      
      const config = getAccountTypeConfig(type);
      expect(getByText(config.label)).toBeTruthy();
      expect(getByText(config.icon)).toBeTruthy();
    });
  });

  it('should format negative balances correctly for credit accounts', () => {
    const creditAccount: Account = {
      ...mockAccount,
      type: 'credit',
      balance: -500.75,
    };

    const { getByText } = render(
      <AccountListItem account={creditAccount} />
    );

    expect(getByText('-₹500.75')).toBeTruthy();
  });

  it('should format zero balance correctly', () => {
    const zeroBalanceAccount = { ...mockAccount, balance: 0 };
    const { getByText } = render(
      <AccountListItem account={zeroBalanceAccount} />
    );

    expect(getByText('₹0.00')).toBeTruthy();
  });

  it('should handle inactive accounts', () => {
    const inactiveAccount = { ...mockAccount, is_active: false };
    const { getByTestId } = render(
      <AccountListItem account={inactiveAccount} />
    );

    const container = getByTestId('account-list-item');
    expect(container.props.style).toEqual(
      expect.objectContaining({
        opacity: 0.5,
      })
    );
  });

  it('should use custom currency when provided', () => {
    const usdAccount = { ...mockAccount, currency: 'USD' };
    const { getByText } = render(
      <AccountListItem account={usdAccount} />
    );

    expect(getByText('$1,500.50')).toBeTruthy();
  });
});

describe('formatCurrency utility function', () => {
  it('should format INR currency correctly', () => {
    expect(formatCurrency(1500.50, 'INR')).toBe('₹1,500.50');
    expect(formatCurrency(0, 'INR')).toBe('₹0.00');
    expect(formatCurrency(-500.75, 'INR')).toBe('-₹500.75');
  });

  it('should format USD currency correctly', () => {
    expect(formatCurrency(1500.50, 'USD')).toBe('$1,500.50');
    expect(formatCurrency(0, 'USD')).toBe('$0.00');
    expect(formatCurrency(-500.75, 'USD')).toBe('-$500.75');
  });

  it('should default to INR when no currency provided', () => {
    expect(formatCurrency(1500.50)).toBe('₹1,500.50');
  });

  it('should handle large numbers correctly', () => {
    expect(formatCurrency(1234567.89, 'INR')).toBe('₹1,234,567.89');
  });

  it('should handle very small numbers correctly', () => {
    expect(formatCurrency(0.01, 'INR')).toBe('₹0.01');
  });
});

describe('getAccountTypeConfig', () => {
  it('should have configuration for all account types', () => {
    const expectedTypes = ['checking', 'savings', 'credit', 'loan', 'investment'];
    
    expectedTypes.forEach(type => {
      const config = getAccountTypeConfig(type as Account['type']);
      expect(config).toBeDefined();
      expect(config.label).toBeTruthy();
      expect(config.icon).toBeTruthy();
      expect(config.color).toBeTruthy();
    });
  });

  it('should have unique colors for each account type', () => {
    const expectedTypes: Account['type'][] = ['checking', 'savings', 'credit', 'loan', 'investment'];
    const colors = expectedTypes.map(type => getAccountTypeConfig(type).color);
    const uniqueColors = [...new Set(colors)];
    
    expect(colors.length).toBe(uniqueColors.length);
  });

  it('should have unique icons for each account type', () => {
    const expectedTypes: Account['type'][] = ['checking', 'savings', 'credit', 'loan', 'investment'];
    const icons = expectedTypes.map(type => getAccountTypeConfig(type).icon);
    const uniqueIcons = [...new Set(icons)];
    
    expect(icons.length).toBe(uniqueIcons.length);
  });
});