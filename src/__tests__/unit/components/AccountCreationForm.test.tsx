import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { AccountCreationForm } from '@/presentation/components/accounts/AccountCreationForm';
import { useAccountStore } from '@/shared/stores/accountStore';
import { Account } from '@/shared/types';

// Mock the account store
jest.mock('@/shared/stores/accountStore');
const mockUseAccountStore = useAccountStore as jest.MockedFunction<typeof useAccountStore>;

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('AccountCreationForm', () => {
  const mockCreateAccount = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnCancel = jest.fn();
  
  const mockStoreState = {
    accounts: [] as Account[],
    loading: false,
    error: null,
    createAccount: mockCreateAccount,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAccountStore.mockReturnValue(mockStoreState as any);
  });

  const defaultProps = {
    onSuccess: mockOnSuccess,
    onCancel: mockOnCancel,
  };

  it('should render form fields correctly', () => {
    const { getByPlaceholderText, getByText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    expect(getByPlaceholderText('e.g., Main Checking, Emergency Savings')).toBeTruthy();
    expect(getByPlaceholderText('0.00')).toBeTruthy();
    expect(getByText('Create Account')).toBeTruthy();
    expect(getByText('Cancel')).toBeTruthy();
  });

  it('should render account type options', () => {
    const { getByText } = render(<AccountCreationForm {...defaultProps} />);

    expect(getByText('Checking Account')).toBeTruthy();
    expect(getByText('Savings Account')).toBeTruthy();
    expect(getByText('Credit Card')).toBeTruthy();
    expect(getByText('Loan Account')).toBeTruthy();
    expect(getByText('Investment Account')).toBeTruthy();
  });

  it('should update account name when text input changes', () => {
    const { getByPlaceholderText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'My Checking Account');

    expect(nameInput.props.value).toBe('My Checking Account');
  });

  it('should update initial balance when text input changes', () => {
    const { getByPlaceholderText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const balanceInput = getByPlaceholderText('0.00');
    fireEvent.changeText(balanceInput, '1000');

    expect(balanceInput.props.value).toBe('1000');
  });

  it('should select account type when pressed', () => {
    const { getByText } = render(<AccountCreationForm {...defaultProps} />);

    const savingsOption = getByText('Savings Account');
    fireEvent.press(savingsOption);

    // Check if the savings option is selected (this would need to be verified through styling or other indicators)
    expect(savingsOption).toBeTruthy();
  });

  it('should show validation error for empty account name', async () => {
    const { getByText, getByPlaceholderText } = render(<AccountCreationForm {...defaultProps} />);

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    
    // Select account type first to get past the initial validation
    const savingsOption = getByText('Savings Account');
    fireEvent.press(savingsOption);
    
    // Set empty name and trigger validation
    fireEvent.changeText(nameInput, '');
    fireEvent(nameInput, 'blur');
    
    // Should show inline error text for name validation after blur
    await waitFor(() => {
      expect(getByText('Account name is required')).toBeTruthy();
    });
  });

  it('should show validation error for account name too long', async () => {
    const { getByPlaceholderText, getByText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'A'.repeat(51)); // 51 characters

    // Select account type first to get past the initial validation
    const savingsOption = getByText('Savings Account');
    fireEvent.press(savingsOption);

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    // Should show inline error text for name validation
    await waitFor(() => {
      expect(getByText('Account name must be less than 50 characters')).toBeTruthy();
    });
  });

  it('should show validation error for duplicate account name', async () => {
    const existingAccount: Account = {
      id: 1,
      name: 'Existing Account',
      type: 'checking',
      balance: 1000,
      currency: 'INR',
      is_active: true,
      sync_status: 'local',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    };

    mockUseAccountStore.mockReturnValue({
      ...mockStoreState,
      accounts: [existingAccount],
    } as any);

    const { getByPlaceholderText, getByText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'Existing Account');

    // Select account type first to get past the initial validation
    const savingsOption = getByText('Savings Account');
    fireEvent.press(savingsOption);

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    // Should show inline error text for duplicate name
    await waitFor(() => {
      expect(getByText('Account name already exists')).toBeTruthy();
    });
  });

  it('should show validation error for invalid balance', async () => {
    const { getByPlaceholderText, getByText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'Valid Account Name');

    const balanceInput = getByPlaceholderText('0.00');
    // Type an invalid numeric format that would pass the regex but fail parseFloat
    fireEvent.changeText(balanceInput, '--123'); // Double negative, will be cleaned to --123 which is still invalid
    // Trigger onBlur to validate
    fireEvent(balanceInput, 'blur');

    // Select account type first to get past the initial validation
    const savingsOption = getByText('Savings Account');
    fireEvent.press(savingsOption);

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    // Should show inline error text for invalid balance
    await waitFor(() => {
      expect(getByText('Please enter a valid number')).toBeTruthy();
    });
  });

  it('should show free tier limit warning', async () => {
    const accounts = Array.from({ length: 5 }, (_, i) => ({
      id: `${i + 1}`,
      name: `Account ${i + 1}`,
      type: 'checking' as const,
      balance: 1000,
      currency: 'INR',
      is_active: true,
      sync_status: 'local' as const,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    }));

    mockUseAccountStore.mockReturnValue({
      ...mockStoreState,
      accounts,
    } as any);

    const { getByPlaceholderText, getByText } = render(<AccountCreationForm {...defaultProps} />);

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'New Account');
    
    const savingsOption = getByText('Savings Account');
    fireEvent.press(savingsOption);

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Account Limit Reached',
        'You have reached the maximum of 5 accounts for the free tier. Upgrade to premium to add more accounts.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Upgrade', onPress: expect.any(Function) }
        ]
      );
    });
  });

  it('should create account successfully', async () => {
    const createdAccount: Account = {
      id: 1,
      name: 'New Account',
      type: 'savings',
      balance: 500,
      currency: 'INR',
      is_active: true,
      sync_status: 'local',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    };

    mockCreateAccount.mockResolvedValue(createdAccount);

    const { getByPlaceholderText, getByText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'New Account');

    const balanceInput = getByPlaceholderText('0.00');
    fireEvent.changeText(balanceInput, '500');

    const savingsOption = getByText('Savings Account');
    fireEvent.press(savingsOption);

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    await waitFor(() => {
      expect(mockCreateAccount).toHaveBeenCalledWith({
        name: 'New Account',
        type: 'savings',
        balance: 500,
        currency: 'INR',
        is_active: true,
        sync_status: 'local',
      });
      expect(mockOnSuccess).toHaveBeenCalledWith(createdAccount);
    });
  });

  it('should handle account creation error', async () => {
    const errorMessage = 'Account creation failed';
    mockCreateAccount.mockRejectedValue(new Error(errorMessage));

    const { getByPlaceholderText, getByText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'New Account');

    const savingsOption = getByText('Savings Account');
    fireEvent.press(savingsOption);

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Error',
        errorMessage
      );
    });
  });

  it('should call onCancel when cancel button is pressed', () => {
    const { getByText } = render(<AccountCreationForm {...defaultProps} />);

    const cancelButton = getByText('Cancel');
    fireEvent.press(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

});