import { 
  validateTransactionMetadata,
  validateCreditCardMetadata,
  validateLoanMetadata,
  validateSMSParsingMetadata,
  sanitizeTransactionMetadata
} from '@/shared/utils/transactionMetadataValidation';
import { TransactionMetadata } from '@/shared/types';

describe('TransactionMetadataValidation', () => {
  describe('validateTransactionMetadata', () => {
    it('should validate complete credit card metadata successfully', () => {
      const metadata: TransactionMetadata = {
        creditCardDetails: {
          merchantName: 'Amazon',
          merchantCategory: 'online_shopping',
          rewardPoints: 100,
          availableCreditAfterTransaction: 50000,
          installmentInfo: {
            isInstallment: true,
            installmentNumber: 1,
            totalInstallments: 6
          }
        },
        smsDetails: {
          originalText: 'Purchase of Rs.5000 at Amazon',
          parsingConfidence: 0.95,
          bankIdentifier: 'HDFC',
          patternMatched: 'purchase'
        },
        source: 'sms',
        lastUpdated: '2025-08-12T10:00:00.000Z'
      };

      const result = validateTransactionMetadata(metadata);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate complete loan metadata successfully', () => {
      const metadata: TransactionMetadata = {
        loanDetails: {
          principalComponent: 8000,
          interestComponent: 2000,
          feeComponent: 100,
          remainingPrincipalAfterPayment: 150000,
          emiNumber: 12,
          paymentType: 'regular_emi'
        },
        smsDetails: {
          originalText: 'Loan EMI of Rs.10100 debited',
          parsingConfidence: 0.98,
          bankIdentifier: 'SBI'
        },
        source: 'sms'
      };

      const result = validateTransactionMetadata(metadata);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should fail validation for invalid credit card data', () => {
      const metadata: TransactionMetadata = {
        creditCardDetails: {
          merchantName: '', // Empty merchant name
          rewardPoints: -10, // Negative reward points
          availableCreditAfterTransaction: -5000, // Negative available credit
          installmentInfo: {
            isInstallment: true,
            installmentNumber: 7, // Greater than total installments
            totalInstallments: 6
          }
        }
      };

      const result = validateTransactionMetadata(metadata);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Merchant name cannot be empty');
      expect(result.errors).toContain('Reward points cannot be negative');
      expect(result.errors).toContain('Available credit after transaction cannot be negative');
      expect(result.errors).toContain('Installment number cannot be greater than total installments');
    });

    it('should fail validation for invalid loan data', () => {
      const metadata: TransactionMetadata = {
        loanDetails: {
          principalComponent: -1000, // Negative principal
          interestComponent: -500, // Negative interest
          emiNumber: 0, // Invalid EMI number
          paymentType: 'invalid_type' as any // Invalid payment type
        }
      };

      const result = validateTransactionMetadata(metadata);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Principal component cannot be negative');
      expect(result.errors).toContain('Interest component cannot be negative');
      expect(result.errors).toContain('EMI number must be positive');
      expect(result.errors).toContain('Invalid payment type. Must be one of: regular_emi, prepayment, part_payment');
    });

    it('should fail validation for invalid SMS parsing data', () => {
      const metadata: TransactionMetadata = {
        smsDetails: {
          originalText: '', // Empty original text
          parsingConfidence: 1.5, // Invalid confidence > 1
          bankIdentifier: '' // Empty bank identifier
        }
      };

      const result = validateTransactionMetadata(metadata);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Original SMS text cannot be empty');
      expect(result.errors).toContain('Parsing confidence must be between 0 and 1');
      expect(result.errors).toContain('Bank identifier cannot be empty');
    });

    it('should fail validation for invalid source', () => {
      const metadata: TransactionMetadata = {
        source: 'invalid_source' as any
      };

      const result = validateTransactionMetadata(metadata);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid source. Must be one of: sms, manual, import');
    });
  });

  describe('validateCreditCardMetadata', () => {
    it('should validate valid credit card metadata', () => {
      const creditCardDetails = {
        merchantName: 'Swiggy',
        merchantCategory: 'food_delivery',
        rewardPoints: 50
      };

      const result = validateCreditCardMetadata(creditCardDetails);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should require merchant name for credit card transactions', () => {
      const creditCardDetails = {
        rewardPoints: 50
      };

      const result = validateCreditCardMetadata(creditCardDetails);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Merchant name is required for credit card transactions');
    });

    it('should validate merchant name length', () => {
      const creditCardDetails = {
        merchantName: 'A'.repeat(101), // Exceeds 100 character limit
        rewardPoints: 50
      };

      const result = validateCreditCardMetadata(creditCardDetails);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Merchant name cannot exceed 100 characters');
    });
  });

  describe('validateLoanMetadata', () => {
    it('should validate valid loan metadata', () => {
      const loanDetails = {
        principalComponent: 5000,
        interestComponent: 1000,
        paymentType: 'regular_emi' as const,
        emiNumber: 24
      };

      const result = validateLoanMetadata(loanDetails);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate payment type for regular EMI', () => {
      const loanDetails = {
        paymentType: 'regular_emi' as const,
        emiNumber: 0 // Invalid EMI number for regular EMI
      };

      const result = validateLoanMetadata(loanDetails);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('EMI number must be positive for regular EMI payments');
    });
  });

  describe('validateSMSParsingMetadata', () => {
    it('should validate valid SMS parsing metadata', () => {
      const smsDetails = {
        originalText: 'Transaction of Rs.1000 at Store',
        parsingConfidence: 0.9,
        bankIdentifier: 'ICICI',
        patternMatched: 'purchase'
      };

      const result = validateSMSParsingMetadata(smsDetails);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should require parsing confidence', () => {
      const smsDetails = {
        originalText: 'Transaction of Rs.1000',
        bankIdentifier: 'ICICI'
      };

      const result = validateSMSParsingMetadata(smsDetails);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Parsing confidence is required for SMS parsed transactions');
    });

    it('should require original text', () => {
      const smsDetails = {
        parsingConfidence: 0.9,
        bankIdentifier: 'ICICI'
      };

      const result = validateSMSParsingMetadata(smsDetails);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Original SMS text is required for SMS parsed transactions');
    });
  });

  describe('sanitizeTransactionMetadata', () => {
    it('should sanitize credit card details', () => {
      const metadata: TransactionMetadata = {
        creditCardDetails: {
          merchantName: '  Amazon  ',
          merchantCategory: '  ONLINE_SHOPPING  '
        },
        smsDetails: {
          bankIdentifier: '  hdfc  ',
          patternMatched: '  purchase  '
        },
        lastUpdated: '2025-08-12T10:00:00Z'
      };

      const sanitized = sanitizeTransactionMetadata(metadata);
      
      expect(sanitized.creditCardDetails?.merchantName).toBe('Amazon');
      expect(sanitized.creditCardDetails?.merchantCategory).toBe('online_shopping');
      expect(sanitized.smsDetails?.bankIdentifier).toBe('HDFC');
      expect(sanitized.smsDetails?.patternMatched).toBe('purchase');
      expect(sanitized.lastUpdated).toBe('2025-08-12T10:00:00.000Z');
    });

    it('should handle invalid dates in lastUpdated', () => {
      const metadata: TransactionMetadata = {
        lastUpdated: 'invalid-date'
      };

      const sanitized = sanitizeTransactionMetadata(metadata);
      expect(sanitized.lastUpdated).toBeUndefined();
    });
  });
});