import {
  validateTransactionType,
  getValidTransactionTypes,
  isCreditCardTransaction,
  isLoanTransaction,
  getDefaultTransactionType,
  getCompatibleAccountTypes,
  validateTransactionCompatibility,
  ACCOUNT_TRANSACTION_COMPATIBILITY,
  CREDIT_CARD_TRANSACTION_TYPES,
  LOAN_TRANSACTION_TYPES
} from '@/shared/utils/transactionUtils';
import { TransactionType } from '@/shared/types';

describe('TransactionUtils', () => {
  describe('validateTransactionType', () => {
    it('should validate credit card transaction types', () => {
      expect(validateTransactionType('credit_payment', 'credit')).toBe(true);
      expect(validateTransactionType('credit_charge', 'credit')).toBe(true);
      expect(validateTransactionType('credit_interest', 'credit')).toBe(true);
      expect(validateTransactionType('credit_fee', 'credit')).toBe(true);
    });

    it('should validate loan transaction types', () => {
      expect(validateTransactionType('loan_emi', 'loan')).toBe(true);
      expect(validateTransactionType('loan_interest', 'loan')).toBe(true);
      expect(validateTransactionType('loan_principal', 'loan')).toBe(true);
      expect(validateTransactionType('loan_fee', 'loan')).toBe(true);
      expect(validateTransactionType('loan_prepayment', 'loan')).toBe(true);
    });

    it('should validate traditional transaction types for checking accounts', () => {
      expect(validateTransactionType('income', 'checking')).toBe(true);
      expect(validateTransactionType('expense', 'checking')).toBe(true);
      expect(validateTransactionType('transfer', 'checking')).toBe(true);
    });

    it('should reject incompatible transaction types', () => {
      expect(validateTransactionType('credit_payment', 'checking')).toBe(false);
      expect(validateTransactionType('loan_emi', 'credit')).toBe(false);
      expect(validateTransactionType('income', 'credit')).toBe(false);
    });
  });

  describe('getValidTransactionTypes', () => {
    it('should return correct transaction types for credit accounts', () => {
      const validTypes = getValidTransactionTypes('credit');
      expect(validTypes).toEqual(['credit_payment', 'credit_charge', 'credit_interest', 'credit_fee']);
    });

    it('should return correct transaction types for loan accounts', () => {
      const validTypes = getValidTransactionTypes('loan');
      expect(validTypes).toEqual(['loan_emi', 'loan_interest', 'loan_principal', 'loan_fee', 'loan_prepayment']);
    });

    it('should return correct transaction types for checking accounts', () => {
      const validTypes = getValidTransactionTypes('checking');
      expect(validTypes).toEqual(['income', 'expense', 'transfer']);
    });

    it('should return empty array for unknown account type', () => {
      const validTypes = getValidTransactionTypes('unknown' as any);
      expect(validTypes).toEqual([]);
    });
  });

  describe('isCreditCardTransaction', () => {
    it('should identify credit card transaction types', () => {
      expect(isCreditCardTransaction('credit_payment')).toBe(true);
      expect(isCreditCardTransaction('credit_charge')).toBe(true);
      expect(isCreditCardTransaction('credit_interest')).toBe(true);
      expect(isCreditCardTransaction('credit_fee')).toBe(true);
    });

    it('should reject non-credit card transaction types', () => {
      expect(isCreditCardTransaction('loan_emi')).toBe(false);
      expect(isCreditCardTransaction('income')).toBe(false);
      expect(isCreditCardTransaction('expense')).toBe(false);
    });
  });

  describe('isLoanTransaction', () => {
    it('should identify loan transaction types', () => {
      expect(isLoanTransaction('loan_emi')).toBe(true);
      expect(isLoanTransaction('loan_interest')).toBe(true);
      expect(isLoanTransaction('loan_principal')).toBe(true);
      expect(isLoanTransaction('loan_fee')).toBe(true);
      expect(isLoanTransaction('loan_prepayment')).toBe(true);
    });

    it('should reject non-loan transaction types', () => {
      expect(isLoanTransaction('credit_payment')).toBe(false);
      expect(isLoanTransaction('income')).toBe(false);
      expect(isLoanTransaction('expense')).toBe(false);
    });
  });

  describe('getDefaultTransactionType', () => {
    it('should return default transaction types for each account type', () => {
      expect(getDefaultTransactionType('credit')).toBe('credit_charge');
      expect(getDefaultTransactionType('loan')).toBe('loan_emi');
      expect(getDefaultTransactionType('checking')).toBe('expense');
      expect(getDefaultTransactionType('savings')).toBe('expense');
      expect(getDefaultTransactionType('investment')).toBe('expense');
    });
  });

  describe('getCompatibleAccountTypes', () => {
    it('should return compatible account types for credit card transactions', () => {
      expect(getCompatibleAccountTypes('credit_payment')).toEqual(['credit']);
      expect(getCompatibleAccountTypes('credit_charge')).toEqual(['credit']);
    });

    it('should return compatible account types for loan transactions', () => {
      expect(getCompatibleAccountTypes('loan_emi')).toEqual(['loan']);
      expect(getCompatibleAccountTypes('loan_prepayment')).toEqual(['loan']);
    });

    it('should return compatible account types for traditional transactions', () => {
      const compatibleTypes = getCompatibleAccountTypes('income');
      expect(compatibleTypes).toEqual(['checking', 'savings', 'investment']);
    });
  });

  describe('validateTransactionCompatibility', () => {
    it('should validate compatible transaction type and amount', () => {
      const result = validateTransactionCompatibility('credit_charge', 1000, 'credit');
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject incompatible transaction type', () => {
      const result = validateTransactionCompatibility('credit_charge', 1000, 'checking');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('not compatible with account type');
    });

    it('should reject zero or negative amounts', () => {
      const result1 = validateTransactionCompatibility('expense', 0, 'checking');
      expect(result1.isValid).toBe(false);
      expect(result1.error).toContain('must be greater than zero');

      const result2 = validateTransactionCompatibility('expense', -100, 'checking');
      expect(result2.isValid).toBe(false);
      expect(result2.error).toContain('must be greater than zero');
    });

    it('should validate credit card specific rules', () => {
      // Credit card charges must be positive
      const result1 = validateTransactionCompatibility('credit_charge', -1000, 'credit');
      expect(result1.isValid).toBe(false);
      expect(result1.error).toContain('Credit card charges must be positive amounts');

      // Credit card payments must be positive
      const result2 = validateTransactionCompatibility('credit_payment', -500, 'credit');
      expect(result2.isValid).toBe(false);
      expect(result2.error).toContain('Credit card payments must be positive amounts');
    });

    it('should validate loan specific rules', () => {
      // Loan transactions must be positive
      const result1 = validateTransactionCompatibility('loan_emi', -1000, 'loan');
      expect(result1.isValid).toBe(false);
      expect(result1.error).toContain('Loan transaction amounts must be positive');

      const result2 = validateTransactionCompatibility('loan_prepayment', -5000, 'loan');
      expect(result2.isValid).toBe(false);
      expect(result2.error).toContain('Loan transaction amounts must be positive');
    });
  });

  describe('constant values', () => {
    it('should have correct ACCOUNT_TRANSACTION_COMPATIBILITY mapping', () => {
      expect(ACCOUNT_TRANSACTION_COMPATIBILITY.credit).toEqual([
        'credit_payment', 'credit_charge', 'credit_interest', 'credit_fee'
      ]);
      expect(ACCOUNT_TRANSACTION_COMPATIBILITY.loan).toEqual([
        'loan_emi', 'loan_interest', 'loan_principal', 'loan_fee', 'loan_prepayment'
      ]);
      expect(ACCOUNT_TRANSACTION_COMPATIBILITY.checking).toEqual([
        'income', 'expense', 'transfer'
      ]);
    });

    it('should have correct CREDIT_CARD_TRANSACTION_TYPES', () => {
      expect(CREDIT_CARD_TRANSACTION_TYPES).toEqual([
        'credit_payment', 'credit_charge', 'credit_interest', 'credit_fee'
      ]);
    });

    it('should have correct LOAN_TRANSACTION_TYPES', () => {
      expect(LOAN_TRANSACTION_TYPES).toEqual([
        'loan_emi', 'loan_interest', 'loan_principal', 'loan_fee', 'loan_prepayment'
      ]);
    });
  });
});