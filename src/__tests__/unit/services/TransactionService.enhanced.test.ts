import { TransactionService } from '@/business/services/TransactionService';
import { TransactionRepository } from '@/data/repositories/TransactionRepository';
import { AccountRepository } from '@/data/repositories/AccountRepository';
import { DatabaseService } from '@/data/database/DatabaseService';
import { Transaction, TransactionMetadata, Account } from '@/shared/types';

// Mock the dependencies
jest.mock('@/data/repositories/TransactionRepository');
jest.mock('@/data/repositories/AccountRepository');
jest.mock('@/data/database/DatabaseService');
jest.mock('@/business/services/SMSParsingService');

describe('TransactionService - Enhanced Features', () => {
  let transactionService: TransactionService;
  let mockTransactionRepository: jest.Mocked<TransactionRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockDatabaseService: jest.Mocked<DatabaseService>;

  const mockCreditAccount: Account = {
    id: 1,
    name: 'HDFC Credit Card',
    type: 'credit',
    balance: -5000,
    currency: 'INR',
    is_active: true,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
    sync_status: 'local'
  };

  const mockLoanAccount: Account = {
    id: 2,
    name: 'SBI Home Loan',
    type: 'loan',
    balance: -250000,
    currency: 'INR',
    is_active: true,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
    sync_status: 'local'
  };

  const mockTransaction: Transaction = {
    id: 1,
    account_id: 1,
    amount: 1500,
    description: 'Purchase at Amazon',
    category_id: null,
    transaction_type: 'credit_charge',
    transaction_date: '2025-08-12',
    created_at: '2025-08-12T10:00:00Z',
    updated_at: '2025-08-12T10:00:00Z',
    sms_source: 'SMS text here',
    confidence_score: 0.9,
    is_recurring: false,
    recurring_pattern: null,
    sync_status: 'local',
    hash: 'abc123'
  };

  beforeEach(() => {
    // Create mocked instances
    mockTransactionRepository = new TransactionRepository() as jest.Mocked<TransactionRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockDatabaseService = DatabaseService.getInstance() as jest.Mocked<DatabaseService>;

    // Mock the constructors to return our mocked instances
    (TransactionRepository as jest.Mock).mockImplementation(() => mockTransactionRepository);
    (AccountRepository as jest.Mock).mockImplementation(() => mockAccountRepository);
    (DatabaseService.getInstance as jest.Mock).mockReturnValue(mockDatabaseService);

    // Mock database initialization
    mockDatabaseService.isInitialized.mockReturnValue(true);
    mockDatabaseService.waitForInitialization.mockResolvedValue();

    transactionService = new TransactionService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createTransactionWithMetadata', () => {
    it('should create transaction with credit card metadata', async () => {
      const transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'hash'> = {
        account_id: 1,
        amount: 2500,
        description: 'Purchase at Amazon',
        category_id: null,
        transaction_type: 'credit_charge',
        transaction_date: '2025-08-12',
        sms_source: 'SMS text',
        confidence_score: 0.9,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local'
      };

      const metadata: TransactionMetadata = {
        creditCardDetails: {
          merchantName: 'Amazon',
          merchantCategory: 'online_shopping',
          rewardPoints: 25,
          availableCreditAfterTransaction: 47500
        },
        smsDetails: {
          originalText: 'HDFC Card charged Rs.2500 at Amazon',
          parsingConfidence: 0.9,
          bankIdentifier: 'HDFC',
          patternMatched: 'purchase'
        },
        source: 'sms'
      };

      mockAccountRepository.findById.mockResolvedValue(mockCreditAccount);
      mockTransactionRepository.createWithMetadata.mockResolvedValue(mockTransaction);

      const result = await transactionService.createTransactionWithMetadata(transactionData, metadata);

      expect(mockTransactionRepository.createWithMetadata).toHaveBeenCalledWith(transactionData, metadata);
      expect(result).toEqual(mockTransaction);
    });

    it('should validate metadata before creating transaction', async () => {
      const transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'hash'> = {
        account_id: 1,
        amount: 1000,
        description: 'Test transaction',
        category_id: null,
        transaction_type: 'credit_charge',
        transaction_date: '2025-08-12',
        sms_source: null,
        confidence_score: null,
        is_recurring: false,
        recurring_pattern: null,
        sync_status: 'local'
      };

      const invalidMetadata: TransactionMetadata = {
        creditCardDetails: {
          merchantName: '', // Invalid: empty merchant name
          rewardPoints: -10 // Invalid: negative reward points
        }
      };

      await expect(
        transactionService.createTransactionWithMetadata(transactionData, invalidMetadata)
      ).rejects.toThrow('Metadata validation failed');
    });
  });

  describe('createTransactionFromSMS', () => {
    it('should create transaction from credit card SMS', async () => {
      const smsText = 'HDFC Card charged Rs.1500 at Amazon. Available: Rs.48500';

      // Mock SMS parsing result
      const mockSMSParsingService = require('@/business/services/SMSParsingService').SMSParsingService;
      mockSMSParsingService.prototype.parseTransactionFromSMS = jest.fn().mockResolvedValue({
        amount: 1500,
        transactionType: 'credit_charge',
        description: 'Purchase at Amazon',
        transactionDate: new Date('2025-08-12'),
        metadata: {
          creditCardDetails: {
            merchantName: 'Amazon',
            availableCreditAfterTransaction: 48500
          },
          smsDetails: {
            originalText: smsText,
            parsingConfidence: 0.9,
            bankIdentifier: 'HDFC'
          }
        },
        confidence: 0.9,
        suggestedAccountType: 'credit'
      });

      mockAccountRepository.findByType.mockResolvedValue([mockCreditAccount]);
      mockTransactionRepository.createWithMetadata.mockResolvedValue(mockTransaction);

      const result = await transactionService.createTransactionFromSMS(smsText);

      expect(result).toEqual(mockTransaction);
      expect(mockAccountRepository.findByType).toHaveBeenCalledWith('credit');
    });

    it('should use provided account ID when specified', async () => {
      const smsText = 'HDFC Card charged Rs.1500 at Amazon';
      const accountId = 1;

      const mockSMSParsingService = require('@/business/services/SMSParsingService').SMSParsingService;
      mockSMSParsingService.prototype.parseTransactionFromSMS = jest.fn().mockResolvedValue({
        amount: 1500,
        transactionType: 'credit_charge',
        description: 'Purchase at Amazon',
        transactionDate: new Date('2025-08-12'),
        metadata: {},
        confidence: 0.9,
        suggestedAccountType: 'credit'
      });

      mockTransactionRepository.createWithMetadata.mockResolvedValue(mockTransaction);

      const result = await transactionService.createTransactionFromSMS(smsText, accountId);

      expect(result).toEqual(mockTransaction);
      expect(mockAccountRepository.findByType).not.toHaveBeenCalled();
    });

    it('should return null for unparseable SMS', async () => {
      const smsText = 'This is not a transaction SMS';

      const mockSMSParsingService = require('@/business/services/SMSParsingService').SMSParsingService;
      mockSMSParsingService.prototype.parseTransactionFromSMS = jest.fn().mockResolvedValue(null);

      const result = await transactionService.createTransactionFromSMS(smsText);

      expect(result).toBeNull();
    });
  });

  describe('updateTransactionMetadata', () => {
    it('should update transaction metadata with validation', async () => {
      const transactionId = 1;
      const metadata: Partial<TransactionMetadata> = {
        creditCardDetails: {
          merchantName: 'Updated Merchant',
          rewardPoints: 50
        }
      };

      mockTransactionRepository.updateMetadata.mockResolvedValue();

      await transactionService.updateTransactionMetadata(transactionId, metadata);

      expect(mockTransactionRepository.updateMetadata).toHaveBeenCalledWith(transactionId, metadata);
    });

    it('should reject invalid metadata', async () => {
      const transactionId = 1;
      const invalidMetadata: Partial<TransactionMetadata> = {
        smsDetails: {
          parsingConfidence: 1.5 // Invalid: > 1.0
        }
      };

      await expect(
        transactionService.updateTransactionMetadata(transactionId, invalidMetadata)
      ).rejects.toThrow('Metadata validation failed');
    });
  });

  describe('validateTransactionType', () => {
    it('should validate compatible transaction types', async () => {
      mockAccountRepository.findById.mockResolvedValue(mockCreditAccount);

      const isValid = await transactionService.validateTransactionType('credit_charge', 1);

      expect(isValid).toBe(true);
      expect(mockAccountRepository.findById).toHaveBeenCalledWith(1);
    });

    it('should reject incompatible transaction types', async () => {
      mockAccountRepository.findById.mockResolvedValue(mockCreditAccount);

      const isValid = await transactionService.validateTransactionType('loan_emi', 1);

      expect(isValid).toBe(false);
    });

    it('should return false for non-existent account', async () => {
      mockAccountRepository.findById.mockResolvedValue(null);

      const isValid = await transactionService.validateTransactionType('credit_charge', 999);

      expect(isValid).toBe(false);
    });
  });

  describe('splitLoanEMITransaction', () => {
    it('should split loan EMI into components', async () => {
      const loanTransaction: Transaction & { metadata?: TransactionMetadata } = {
        ...mockTransaction,
        transaction_type: 'loan_emi',
        amount: 10000,
        metadata: {
          loanDetails: {
            paymentType: 'regular_emi'
          }
        }
      };

      mockTransactionRepository.findByIdWithMetadata.mockResolvedValue(loanTransaction);
      mockTransactionRepository.updateMetadata.mockResolvedValue();

      await transactionService.splitLoanEMITransaction(1, 8000, 2000);

      expect(mockTransactionRepository.updateMetadata).toHaveBeenCalledWith(1, expect.objectContaining({
        loanDetails: expect.objectContaining({
          principalComponent: 8000,
          interestComponent: 2000,
          feeComponent: 0 // 10000 - 8000 - 2000
        })
      }));
    });

    it('should reject non-loan transactions', async () => {
      const creditTransaction = {
        ...mockTransaction,
        transaction_type: 'credit_charge' as const
      };

      mockTransactionRepository.findByIdWithMetadata.mockResolvedValue(creditTransaction);

      await expect(
        transactionService.splitLoanEMITransaction(1, 8000, 2000)
      ).rejects.toThrow('Can only split loan EMI transactions');
    });
  });

  describe('autoDetectTransactionType', () => {
    it('should detect credit card transaction types', async () => {
      let result = await transactionService.autoDetectTransactionType('credit', 'Payment received', 1000);
      expect(result).toBe('credit_payment');

      result = await transactionService.autoDetectTransactionType('credit', 'Interest charged', 100);
      expect(result).toBe('credit_interest');

      result = await transactionService.autoDetectTransactionType('credit', 'Annual fee', 500);
      expect(result).toBe('credit_fee');

      result = await transactionService.autoDetectTransactionType('credit', 'Purchase at store', 2000);
      expect(result).toBe('credit_charge');
    });

    it('should detect loan transaction types', async () => {
      let result = await transactionService.autoDetectTransactionType('loan', 'Prepayment processed', 50000);
      expect(result).toBe('loan_prepayment');

      result = await transactionService.autoDetectTransactionType('loan', 'Interest component', 1500);
      expect(result).toBe('loan_interest');

      result = await transactionService.autoDetectTransactionType('loan', 'Processing fee', 1000);
      expect(result).toBe('loan_fee');

      result = await transactionService.autoDetectTransactionType('loan', 'Principal amount', 8000);
      expect(result).toBe('loan_principal');

      result = await transactionService.autoDetectTransactionType('loan', 'EMI payment', 10000);
      expect(result).toBe('loan_emi');
    });

    it('should detect traditional transaction types', async () => {
      let result = await transactionService.autoDetectTransactionType('checking', 'Salary credited', 50000);
      expect(result).toBe('income');

      result = await transactionService.autoDetectTransactionType('checking', 'Transfer to savings', -10000);
      expect(result).toBe('transfer');

      result = await transactionService.autoDetectTransactionType('checking', 'Grocery shopping', -2000);
      expect(result).toBe('expense');
    });
  });

  describe('processBatchSMS', () => {
    it('should process multiple SMS messages', async () => {
      const smsMessages = [
        'HDFC Card charged Rs.1500 at Amazon',
        'SBI Loan EMI Rs.10000 debited',
        'Invalid SMS message'
      ];

      const mockSMSParsingService = require('@/business/services/SMSParsingService').SMSParsingService;
      mockSMSParsingService.prototype.parseTransactionFromSMS = jest.fn()
        .mockResolvedValueOnce({
          amount: 1500,
          transactionType: 'credit_charge',
          description: 'Purchase at Amazon',
          transactionDate: new Date('2025-08-12'),
          metadata: {},
          confidence: 0.9,
          suggestedAccountType: 'credit'
        })
        .mockResolvedValueOnce({
          amount: 10000,
          transactionType: 'loan_emi',
          description: 'Loan EMI',
          transactionDate: new Date('2025-08-12'),
          metadata: {},
          confidence: 0.9,
          suggestedAccountType: 'loan'
        })
        .mockResolvedValueOnce(null);

      mockAccountRepository.findByType
        .mockResolvedValueOnce([mockCreditAccount])
        .mockResolvedValueOnce([mockLoanAccount]);

      const mockCreditTransaction = { ...mockTransaction, id: 1 };
      const mockLoanTransaction = { ...mockTransaction, id: 2, transaction_type: 'loan_emi' as const };

      mockTransactionRepository.createWithMetadata
        .mockResolvedValueOnce(mockCreditTransaction)
        .mockResolvedValueOnce(mockLoanTransaction);

      const result = await transactionService.processBatchSMS(smsMessages);

      expect(result.totalProcessed).toBe(3);
      expect(result.successful).toHaveLength(2);
      expect(result.failed).toHaveLength(1);
      expect(result.failed[0].error).toBe('Could not parse SMS message');
    });
  });
});