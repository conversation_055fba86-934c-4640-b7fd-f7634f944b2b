import { SMSParsingService } from '@/business/services/SMSParsingService';
import { identifyBankFromSMS, parseAmountFromSMS, extractMerchantName } from '@/data/smsPatterns';

describe('SMSParsingService', () => {
  let smsParsingService: SMSParsingService;

  beforeEach(() => {
    smsParsingService = new SMSParsingService();
  });

  describe('parseTransactionFromSMS', () => {
    it('should parse credit card purchase SMS successfully', async () => {
      const smsText = 'Your HDFC Credit Card has been charged Rs.2,500.00 at AMAZON on 12-Aug-25. Available limit: Rs.47,500.00';
      
      const result = await smsParsingService.parseTransactionFromSMS(smsText);
      
      expect(result).not.toBeNull();
      expect(result?.amount).toBe(2500);
      expect(result?.transactionType).toBe('credit_charge');
      expect(result?.description).toContain('Amazon');
      expect(result?.suggestedAccountType).toBe('credit');
      expect(result?.metadata.smsDetails?.bankIdentifier).toBe('HDFC');
      expect(result?.metadata.creditCardDetails?.merchantName).toContain('AMAZON');
    });

    it('should parse credit card payment SMS successfully', async () => {
      const smsText = 'Thank you for your ICICI Credit Card payment of Rs.15,000.00 received on 12-Aug-25';
      
      const result = await smsParsingService.parseTransactionFromSMS(smsText);
      
      expect(result).not.toBeNull();
      expect(result?.amount).toBe(15000);
      expect(result?.transactionType).toBe('credit_payment');
      expect(result?.description).toBe('Credit card payment');
      expect(result?.suggestedAccountType).toBe('credit');
      expect(result?.metadata.smsDetails?.bankIdentifier).toBe('ICICI');
    });

    it('should parse loan EMI SMS successfully', async () => {
      const smsText = 'Your SBI Home Loan EMI of Rs.12,500.00 has been debited on 12-Aug-25. Outstanding balance: Rs.8,50,000.00';
      
      const result = await smsParsingService.parseTransactionFromSMS(smsText);
      
      expect(result).not.toBeNull();
      expect(result?.amount).toBe(12500);
      expect(result?.transactionType).toBe('loan_emi');
      expect(result?.description).toBe('Loan EMI payment');
      expect(result?.suggestedAccountType).toBe('loan');
      expect(result?.metadata.smsDetails?.bankIdentifier).toBe('SBI');
      expect(result?.metadata.loanDetails?.remainingPrincipalAfterPayment).toBe(850000);
    });

    it('should parse loan prepayment SMS successfully', async () => {
      const smsText = 'Your AXIS Bank loan prepayment of Rs.50,000.00 has been processed. Interest savings: Rs.2,45,000.00';
      
      const result = await smsParsingService.parseTransactionFromSMS(smsText);
      
      expect(result).not.toBeNull();
      expect(result?.amount).toBe(50000);
      expect(result?.transactionType).toBe('loan_prepayment');
      expect(result?.description).toBe('Loan prepayment');
      expect(result?.suggestedAccountType).toBe('loan');
      expect(result?.metadata.smsDetails?.bankIdentifier).toBe('AXIS');
      expect(result?.metadata.loanDetails?.paymentType).toBe('prepayment');
    });

    it('should return null for non-parseable SMS', async () => {
      const smsText = 'Hello, this is a general message with no transaction information';
      
      const result = await smsParsingService.parseTransactionFromSMS(smsText);
      
      expect(result).toBeNull();
    });

    it('should handle malformed SMS gracefully', async () => {
      const smsText = 'Invalid SMS with Rs. but no amount';
      
      const result = await smsParsingService.parseTransactionFromSMS(smsText);
      
      expect(result).toBeNull();
    });
  });

  describe('extractCreditCardDetails', () => {
    it('should extract merchant name and available credit', () => {
      const smsText = 'Spent Rs.1,200 at SWIGGY. Available credit: Rs.48,800';
      
      const details = smsParsingService.extractCreditCardDetails(smsText);
      
      expect(details.merchantName).toContain('SWIGGY');
      expect(details.availableCredit).toBe(48800);
    });

    it('should extract card number when present', () => {
      const smsText = 'Transaction on card ending 1234 for Rs.500 at Store';
      
      const details = smsParsingService.extractCreditCardDetails(smsText);
      
      expect(details.cardNumber).toBe('1234');
    });

    it('should extract reward points when mentioned', () => {
      const smsText = 'Purchase at Store. Reward points earned: 50';
      
      const details = smsParsingService.extractCreditCardDetails(smsText);
      
      expect(details.rewardPoints).toBe(50);
    });
  });

  describe('extractLoanDetails', () => {
    it('should extract EMI number and outstanding balance', () => {
      const smsText = 'EMI no. 24 of Rs.10,000 debited. Outstanding: Rs.2,50,000';
      
      const details = smsParsingService.extractLoanDetails(smsText);
      
      expect(details.emiNumber).toBe(24);
      expect(details.outstandingBalance).toBe(250000);
      expect(details.paymentType).toBe('regular_emi');
    });

    it('should detect prepayment type', () => {
      const smsText = 'Prepayment of Rs.1,00,000 processed for your loan';
      
      const details = smsParsingService.extractLoanDetails(smsText);
      
      expect(details.paymentType).toBe('prepayment');
    });

    it('should extract principal and interest components', () => {
      const smsText = 'EMI debited: Principal Rs.8,000, Interest Rs.2,000';
      
      const details = smsParsingService.extractLoanDetails(smsText);
      
      expect(details.principalComponent).toBe(8000);
      expect(details.interestComponent).toBe(2000);
    });
  });

  describe('calculateParsingConfidence', () => {
    it('should calculate higher confidence for complete data', () => {
      const smsText = 'HDFC Credit Card charged Rs.1,500 at Amazon. Ref: TXN123456';
      const extractedData = {
        amount: 1500,
        merchantName: 'Amazon',
        referenceNumber: 'TXN123456'
      };
      
      const confidence = smsParsingService.calculateParsingConfidence(smsText, extractedData, 'purchase');
      
      expect(confidence).toBeGreaterThan(0.8);
    });

    it('should calculate lower confidence for incomplete data', () => {
      const smsText = 'Transaction of some amount';
      const extractedData = {
        amount: null
      };
      
      const confidence = smsParsingService.calculateParsingConfidence(smsText, extractedData, 'purchase');
      
      expect(confidence).toBeLessThan(0.6);
    });

    it('should add confidence for bank identifier', () => {
      const smsText = 'HDFC Bank transaction of Rs.1000';
      const extractedData = { amount: 1000 };
      
      const confidence = smsParsingService.calculateParsingConfidence(smsText, extractedData, 'purchase');
      
      expect(confidence).toBeGreaterThan(0.8); // Base + amount + bank
    });
  });

  describe('identifyBankFromSMS', () => {
    it('should identify major Indian banks', () => {
      expect(smsParsingService.identifyBankFromSMS('HDFC Bank transaction')).toBe('HDFC');
      expect(smsParsingService.identifyBankFromSMS('ICICI Credit Card')).toBe('ICICI');
      expect(smsParsingService.identifyBankFromSMS('SBI Loan EMI')).toBe('SBI');
      expect(smsParsingService.identifyBankFromSMS('AXIS Bank payment')).toBe('AXIS');
      expect(smsParsingService.identifyBankFromSMS('Kotak Mahindra')).toBe('KOTAK');
    });

    it('should return null for unknown banks', () => {
      expect(smsParsingService.identifyBankFromSMS('Random Bank transaction')).toBeNull();
      expect(smsParsingService.identifyBankFromSMS('No bank mentioned')).toBeNull();
    });
  });
});

// Test helper functions from smsPatterns.ts
describe('SMS Pattern Helper Functions', () => {
  describe('identifyBankFromSMS', () => {
    it('should identify banks case-insensitively', () => {
      expect(identifyBankFromSMS('hdfc bank')).toBe('HDFC');
      expect(identifyBankFromSMS('ICICI BANK')).toBe('ICICI');
      expect(identifyBankFromSMS('Sbi Card')).toBe('SBI');
    });
  });

  describe('parseAmountFromSMS', () => {
    it('should parse amounts with commas and spaces', () => {
      expect(parseAmountFromSMS('1,234.56')).toBe(1234.56);
      expect(parseAmountFromSMS('50 000')).toBe(50000);
      expect(parseAmountFromSMS('1,00,000')).toBe(100000);
    });

    it('should handle amounts without decimal places', () => {
      expect(parseAmountFromSMS('1000')).toBe(1000);
      expect(parseAmountFromSMS('5,000')).toBe(5000);
    });
  });

  describe('extractMerchantName', () => {
    it('should extract merchant names from transaction text', () => {
      expect(extractMerchantName('Transaction at AMAZON on 12-Aug')).toBe('AMAZON');
      expect(extractMerchantName('Spent on SWIGGY avl balance')).toBe('SWIGGY');
      expect(extractMerchantName('Purchase on Flipkart.')).toBe('Flipkart');
    });

    it('should return null when no merchant name found', () => {
      expect(extractMerchantName('Transaction completed')).toBeNull();
      expect(extractMerchantName('Amount debited')).toBeNull();
    });
  });
});

// Integration tests for realistic SMS scenarios
describe('SMS Parsing Integration Tests', () => {
  let smsParsingService: SMSParsingService;

  beforeEach(() => {
    smsParsingService = new SMSParsingService();
  });

  const testCases = [
    {
      name: 'HDFC Credit Card Purchase',
      sms: 'Alert: You have spent Rs 2,150.00 on your HDFC Bank Credit Card 4532 at ZOMATO ONLINE on 12-AUG-25 at 14:30:05. Available Credit Limit: Rs 47,850.00',
      expectedType: 'credit_charge',
      expectedAmount: 2150,
      expectedBank: 'HDFC'
    },
    {
      name: 'ICICI Credit Card Payment',
      sms: 'Thank you for your ICICI Bank Credit Card payment of Rs 25,000.00 received on 12-AUG-25. Your payment will be processed within 24 hrs.',
      expectedType: 'credit_payment',
      expectedAmount: 25000,
      expectedBank: 'ICICI'
    },
    {
      name: 'SBI Home Loan EMI',
      sms: 'Dear Customer, your SBI Home Loan EMI of Rs.15,245.00 for A/c XX1234 is debited on 12-AUG-25. Outstanding Principal: Rs.12,45,000.00',
      expectedType: 'loan_emi',
      expectedAmount: 15245,
      expectedBank: 'SBI'
    },
    {
      name: 'AXIS Loan Prepayment',
      sms: 'AXIS BANK: Your Personal Loan prepayment of Rs.1,50,000.00 has been processed. Interest Savings: Rs.45,000.00. Outstanding: Rs.2,50,000.00',
      expectedType: 'loan_prepayment',
      expectedAmount: 150000,
      expectedBank: 'AXIS'
    }
  ];

  testCases.forEach(testCase => {
    it(`should parse ${testCase.name} correctly`, async () => {
      const result = await smsParsingService.parseTransactionFromSMS(testCase.sms);
      
      expect(result).not.toBeNull();
      expect(result?.transactionType).toBe(testCase.expectedType);
      expect(result?.amount).toBe(testCase.expectedAmount);
      expect(result?.metadata.smsDetails?.bankIdentifier).toBe(testCase.expectedBank);
      expect(result?.confidence).toBeGreaterThan(0.8);
      expect(result?.metadata.smsDetails?.originalText).toBe(testCase.sms);
    });
  });

  it('should handle batch of mixed SMS messages', async () => {
    const smsMessages = [
      'HDFC Card spent Rs.500 at Store',
      'SBI Loan EMI Rs.10000 debited',
      'Invalid message with no transaction data',
      'ICICI Card payment Rs.5000 received'
    ];

    const results = await Promise.all(
      smsMessages.map(sms => smsParsingService.parseTransactionFromSMS(sms))
    );

    const validResults = results.filter(result => result !== null);
    expect(validResults).toHaveLength(3); // First, second, and fourth should parse successfully
    
    expect(validResults[0]?.transactionType).toBe('credit_charge');
    expect(validResults[1]?.transactionType).toBe('loan_emi');
    expect(validResults[2]?.transactionType).toBe('credit_payment');
  });
});