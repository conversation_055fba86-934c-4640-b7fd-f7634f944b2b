import { renderHook, act } from '@testing-library/react-native';
import { Account } from '@/shared/types';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

// Create mock service instance
const mockAccountService = {
  getAllAccounts: jest.fn(),
  getAccountById: jest.fn(),
  createAccount: jest.fn(),
  updateAccount: jest.fn(),
  deleteAccount: jest.fn(),
  calculateAccountBalance: jest.fn(),
  recalculateAccountBalance: jest.fn(),
  getAccountSummary: jest.fn(),
};

// Mock the AccountService module
jest.doMock('@/business/services/AccountService', () => {
  return {
    AccountService: jest.fn().mockImplementation(() => mockAccountService),
  };
});

// Import store after setting up mocks
const { useAccountStore } = require('@/shared/stores/accountStore');

describe('accountStore', () => {
  
  const mockAccount: Account = {
    id: 1,
    name: 'Test Account',
    type: 'checking',
    balance: 1000,
    currency: 'INR',
    is_active: true,
    sync_status: 'local',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  const mockAccountSummary = {
    totalAccounts: 1,
    totalBalance: 1000,
    accountsByType: {
      checking: { count: 1, balance: 1000 },
      savings: { count: 0, balance: 0 },
      credit: { count: 0, balance: 0 },
      loan: { count: 0, balance: 0 },
      investment: { count: 0, balance: 0 },
    },
    lastUpdated: new Date('2024-01-01T00:00:00Z'),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset store state
    useAccountStore.setState({
      accounts: [],
      selectedAccount: null,
      loading: false,
      error: null,
      accountSummary: null,
      lastUpdated: null,
    });
    
    // Clear all mocks
    Object.values(mockAccountService).forEach(mock => {
      if (jest.isMockFunction(mock)) {
        mock.mockClear();
      }
    });
  });

  describe('loadAccounts', () => {
    it('should load accounts successfully', async () => {
      mockAccountService.getAllAccounts.mockResolvedValue([mockAccount]);
      
      const { result } = renderHook(() => useAccountStore());
      
      await act(async () => {
        await result.current.loadAccounts();
      });
      
      expect(result.current.accounts).toEqual([mockAccount]);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.lastUpdated).toBeDefined();
    });

    it('should handle load accounts error', async () => {
      const errorMessage = 'Failed to load accounts';
      mockAccountService.getAllAccounts.mockRejectedValue(new Error(errorMessage));
      
      const { result } = renderHook(() => useAccountStore());
      
      await act(async () => {
        await result.current.loadAccounts();
      });
      
      expect(result.current.accounts).toEqual([]);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });

    it('should set loading state during load', async () => {
      let resolvePromise: (value: Account[]) => void;
      const promise = new Promise<Account[]>((resolve) => {
        resolvePromise = resolve;
      });
      mockAccountService.getAllAccounts.mockReturnValue(promise);
      
      const { result } = renderHook(() => useAccountStore());
      
      act(() => {
        result.current.loadAccounts();
      });
      
      expect(result.current.loading).toBe(true);
      
      await act(async () => {
        resolvePromise!([mockAccount]);
        await promise;
      });
      
      expect(result.current.loading).toBe(false);
    });
  });

  describe('createAccount', () => {
    it('should create account successfully', async () => {
      const accountData = {
        name: 'New Account',
        type: 'savings' as const,
        balance: 500,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };
      
      const createdAccount = { ...mockAccount, ...accountData, id: '2' };
      mockAccountService.createAccount.mockResolvedValue(createdAccount);
      
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state
      act(() => {
        result.current.setAccounts([mockAccount]);
      });
      
      let returnedAccount: Account;
      await act(async () => {
        returnedAccount = await result.current.createAccount(accountData);
      });
      
      expect(mockAccountService.createAccount).toHaveBeenCalledWith(accountData);
      expect(result.current.accounts).toContain(createdAccount);
      expect(result.current.accounts).toHaveLength(2);
      expect(returnedAccount!).toEqual(createdAccount);
      expect(result.current.error).toBeNull();
    });

    it('should handle create account error', async () => {
      const accountData = {
        name: 'New Account',
        type: 'savings' as const,
        balance: 500,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };
      
      const errorMessage = 'Account creation failed';
      mockAccountService.createAccount.mockRejectedValue(new Error(errorMessage));
      
      const { result } = renderHook(() => useAccountStore());
      
      await act(async () => {
        try {
          await result.current.createAccount(accountData);
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.error).toBe(errorMessage);
    });
  });

  describe('updateAccount', () => {
    it('should update account successfully', async () => {
      const updateData = { name: 'Updated Account' };
      const updatedAccount = { ...mockAccount, ...updateData };
      
      mockAccountService.updateAccount.mockResolvedValue(updatedAccount);
      mockAccountService.getAccountSummary.mockResolvedValue(mockAccountSummary);
      
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state
      act(() => {
        result.current.setAccounts([mockAccount]);
      });
      
      await act(async () => {
        await result.current.updateAccount(1, updateData);
      });
      
      expect(mockAccountService.updateAccount).toHaveBeenCalledWith(1, updateData);
      expect(result.current.accounts[0]).toEqual(updatedAccount);
      expect(result.current.error).toBeNull();
    });

    it('should handle update account error', async () => {
      const updateData = { name: 'Updated Account' };
      const errorMessage = 'Update failed';
      
      mockAccountService.updateAccount.mockRejectedValue(new Error(errorMessage));
      
      const { result } = renderHook(() => useAccountStore());
      
      await act(async () => {
        try {
          await result.current.updateAccount('1', updateData);
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.error).toBe(errorMessage);
    });
  });

  describe('deleteAccount', () => {
    it('should delete account successfully', async () => {
      mockAccountService.deleteAccount.mockResolvedValue({ success: true, transactionCount: 0 });
      mockAccountService.getAccountSummary.mockResolvedValue(mockAccountSummary);
      
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state
      act(() => {
        result.current.setAccounts([mockAccount]);
      });
      
      await act(async () => {
        await result.current.deleteAccount(1);
      });
      
      expect(mockAccountService.deleteAccount).toHaveBeenCalledWith(1, undefined);
      expect(result.current.accounts).toHaveLength(0);
      expect(result.current.error).toBeNull();
    });

    it('should clear selected account if deleted', async () => {
      mockAccountService.deleteAccount.mockResolvedValue({ success: true, transactionCount: 0 });
      mockAccountService.getAccountSummary.mockResolvedValue(mockAccountSummary);
      
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state
      act(() => {
          result.current.setAccounts([mockAccount]);
          result.current.setSelectedAccount(mockAccount);
        });
      
      expect(result.current.selectedAccount?.id).toBe(1);
      
      await act(async () => {
        await result.current.deleteAccount(1);
      });
      
      expect(result.current.selectedAccount).toBeNull();
    });
  });



  describe('calculateBalance', () => {
    it('should calculate balance successfully', async () => {
      const calculatedBalance = 1500;
      mockAccountService.calculateAccountBalance.mockResolvedValue(calculatedBalance);
      
      const { result } = renderHook(() => useAccountStore());
      
      let balance: number;
      await act(async () => {
        balance = await result.current.calculateBalance(1);
      });
      
      expect(mockAccountService.calculateAccountBalance).toHaveBeenCalledWith(1);
      expect(balance!).toBe(calculatedBalance);
    });
  });

  describe('recalculateBalance', () => {
    it('should recalculate balance and update account', async () => {
      const newBalance = 1200;
      const updatedAccount = { ...mockAccount, balance: newBalance };
      
      mockAccountService.recalculateAccountBalance.mockResolvedValue(updatedAccount);
      mockAccountService.getAccountSummary.mockResolvedValue(mockAccountSummary);
      
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state
      act(() => {
        result.current.setAccounts([mockAccount]);
      });
      
      await act(async () => {
        await result.current.recalculateBalance(1);
      });
      
      expect(mockAccountService.recalculateAccountBalance).toHaveBeenCalledWith(1);
      expect(mockAccountService.getAccountSummary).toHaveBeenCalled();
      expect(result.current.accounts[0].balance).toBe(newBalance);
    });
  });

  describe('loadAccountSummary', () => {
    it('should load account summary successfully', async () => {
      mockAccountService.getAccountSummary.mockResolvedValue(mockAccountSummary);
      const { result } = renderHook(() => useAccountStore());
      
      await act(async () => {
        await result.current.loadAccountSummary();
      });
      
      expect(mockAccountService.getAccountSummary).toHaveBeenCalled();
      expect(result.current.accountSummary).toEqual(mockAccountSummary);
    });
  });

  describe('selectAccount', () => {
    it('should select account', () => {
      const { result } = renderHook(() => useAccountStore());
      
      // First add the account to the store
      act(() => {
        result.current.setAccounts([mockAccount]);
        result.current.selectAccountById(1);
      });
      
      expect(result.current.selectedAccount?.id).toBe(1);
    });

    it('should clear selection when null is passed', () => {
      const { result } = renderHook(() => useAccountStore());
      
      // First add account and select it
      act(() => {
        result.current.setAccounts([mockAccount]);
        result.current.selectAccountById(1);
      });
      
      expect(result.current.selectedAccount?.id).toBe(1);
      
      // Then clear selection
      act(() => {
        result.current.selectAccountById(null);
      });
      
      expect(result.current.selectedAccount).toBeNull();
    });
  });

  describe('setError', () => {
    it('should set and clear error', () => {
      const { result } = renderHook(() => useAccountStore());
      
      // Set error
      act(() => {
        result.current.setError('Some error');
      });
      
      expect(result.current.error).toBe('Some error');
      
      // Clear error
      act(() => {
        result.current.setError(null);
      });
      
      expect(result.current.error).toBeNull();
    });
  });

  describe('resetStore', () => {
    it('should reset store to initial state', () => {
      const { result } = renderHook(() => useAccountStore());
      
      // Set up some state
      act(() => {
        result.current.setAccounts([mockAccount]);
        result.current.setSelectedAccount(mockAccount);
        result.current.setError('Some error');
        useAccountStore.setState({ accountSummary: mockAccountSummary });
      });
      
      expect(result.current.accounts).toHaveLength(1);
      expect(result.current.selectedAccount?.id).toBe(1);
      expect(result.current.error).toBe('Some error');
      expect(result.current.accountSummary).toEqual(mockAccountSummary);
      
      act(() => {
        result.current.reset();
      });
      
      expect(result.current.accounts).toEqual([]);
      expect(result.current.selectedAccount).toBeNull();
      expect(result.current.error).toBeNull();
      expect(result.current.accountSummary).toBeNull();
      expect(result.current.loading).toBe(false);
      expect(result.current.lastUpdated).toBeNull();
    });
  });

  describe('persistence', () => {
    it('should persist accounts and selectedAccount', async () => {
      const { result } = renderHook(() => useAccountStore());
      
      act(() => {
        result.current.setAccounts([mockAccount]);
        result.current.selectAccountById(1);
      });
      
      // Simulate persistence by checking if setItem was called
      // Note: This is a simplified test as zustand persist is complex to test
      expect(result.current.accounts).toEqual([mockAccount]);
      expect(result.current.selectedAccount?.id).toBe(1);
    });
  });

  describe('refreshAccounts', () => {
    it('should refresh accounts by calling loadAccounts', async () => {
      mockAccountService.getAllAccounts.mockResolvedValue([mockAccount]);
      const { result } = renderHook(() => useAccountStore());
      
      await act(async () => {
        await result.current.refreshAccounts();
      });
      
      expect(result.current.accounts).toEqual([mockAccount]);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('transferBalance', () => {
    const mockAccount2: Account = {
      ...mockAccount,
      id: 2,
      name: 'Test Account 2',
      balance: 500,
    };

    it('should transfer balance successfully', async () => {
      const transferAmount = 200;
      const updatedFromAccount = { ...mockAccount, balance: 800, sync_status: 'local' as const };
      const updatedToAccount = { ...mockAccount2, balance: 700, sync_status: 'local' as const };
      
      mockAccountService.updateAccount
        .mockResolvedValueOnce(updatedFromAccount)
        .mockResolvedValueOnce(updatedToAccount);
      mockAccountService.getAllAccounts.mockResolvedValue([updatedFromAccount, updatedToAccount]);
      mockAccountService.getAccountSummary.mockResolvedValue(mockAccountSummary);
      
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state with both accounts
      act(() => {
        result.current.setAccounts([mockAccount, mockAccount2]);
      });
      
      await act(async () => {
        await result.current.transferBalance(1, 2, transferAmount);
      });
      
      expect(mockAccountService.updateAccount).toHaveBeenCalledWith(1, {
        balance: 800,
        sync_status: 'local'
      });
      expect(mockAccountService.updateAccount).toHaveBeenCalledWith(2, {
        balance: 700,
        sync_status: 'local'
      });
      expect(mockAccountService.getAccountSummary).toHaveBeenCalled();
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should handle account not found error', async () => {
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state with only one account
      act(() => {
        result.current.setAccounts([mockAccount]);
      });
      
      await act(async () => {
        try {
          await result.current.transferBalance(1, 999, 200);
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.error).toBe('Account not found');
      expect(result.current.loading).toBe(false);
    });

    it('should handle insufficient balance error', async () => {
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state
      act(() => {
        result.current.setAccounts([mockAccount, mockAccount2]);
      });
      
      await act(async () => {
        try {
          await result.current.transferBalance(1, 2, 1500); // More than available balance
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.error).toBe('Insufficient balance for transfer');
      expect(result.current.loading).toBe(false);
    });

    it('should handle transfer service error', async () => {
      const transferAmount = 200;
      mockAccountService.updateAccount.mockRejectedValue(new Error('Transfer failed'));
      
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state
      act(() => {
        result.current.setAccounts([mockAccount, mockAccount2]);
      });
      
      await act(async () => {
        try {
          await result.current.transferBalance(1, 2, transferAmount);
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.error).toBe('Transfer failed');
      expect(result.current.loading).toBe(false);
    });
  });

  describe('deleteAccount with transferToId', () => {
    it('should delete account and transfer to another account', async () => {
      mockAccountService.deleteAccount.mockResolvedValue({ success: true, transactionCount: 5 });
      mockAccountService.getAllAccounts.mockResolvedValue([{ ...mockAccount, id: 2 }]);
      mockAccountService.getAccountSummary.mockResolvedValue(mockAccountSummary);
      
      const { result } = renderHook(() => useAccountStore());
      
      // Set initial state with both accounts
      act(() => {
        result.current.setAccounts([mockAccount, { ...mockAccount, id: 2 }]);
      });
      
      await act(async () => {
        await result.current.deleteAccount(1, 2);
      });
      
      expect(mockAccountService.deleteAccount).toHaveBeenCalledWith(1, 2);
      expect(mockAccountService.getAccountSummary).toHaveBeenCalled();
      expect(result.current.accounts).toHaveLength(1);
      expect(result.current.error).toBeNull();
    });
  });

  describe('setSelectedAccount', () => {
    it('should set selected account directly', () => {
      const { result } = renderHook(() => useAccountStore());
      
      act(() => {
        result.current.setSelectedAccount(mockAccount);
      });
      
      expect(result.current.selectedAccount).toEqual(mockAccount);
    });

    it('should clear selected account when null', () => {
      const { result } = renderHook(() => useAccountStore());
      
      // First set an account
      act(() => {
        result.current.setSelectedAccount(mockAccount);
      });
      expect(result.current.selectedAccount).toEqual(mockAccount);
      
      // Then clear it
      act(() => {
        result.current.setSelectedAccount(null);
      });
      
      expect(result.current.selectedAccount).toBeNull();
    });
  });

  describe('setLoading', () => {
    it('should set loading state', () => {
      const { result } = renderHook(() => useAccountStore());
      
      act(() => {
        result.current.setLoading(true);
      });
      
      expect(result.current.loading).toBe(true);
      
      act(() => {
        result.current.setLoading(false);
      });
      
      expect(result.current.loading).toBe(false);
    });
  });

  describe('calculateBalance error handling', () => {
    it('should handle calculate balance error and set error state', async () => {
      const errorMessage = 'Balance calculation failed';
      mockAccountService.calculateAccountBalance.mockRejectedValue(new Error(errorMessage));
      
      const { result } = renderHook(() => useAccountStore());
      
      await act(async () => {
        try {
          await result.current.calculateBalance(1);
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.error).toBe(errorMessage);
    });
  });

  describe('loadAccounts concurrent loading prevention', () => {
    it('should prevent concurrent loading', async () => {
      let resolvePromise: (value: Account[]) => void;
      const promise = new Promise<Account[]>((resolve) => {
        resolvePromise = resolve;
      });
      mockAccountService.getAllAccounts.mockReturnValue(promise);
      
      const { result } = renderHook(() => useAccountStore());
      
      // Start first load
      act(() => {
        result.current.loadAccounts();
      });
      
      expect(result.current.loading).toBe(true);
      
      // Try to start second load while first is running
      act(() => {
        result.current.loadAccounts(); // Should be ignored
      });
      
      // getAllAccounts should only be called once
      expect(mockAccountService.getAllAccounts).toHaveBeenCalledTimes(1);
      
      // Resolve the promise
      await act(async () => {
        resolvePromise!([mockAccount]);
        await promise;
      });
      
      expect(result.current.loading).toBe(false);
      expect(result.current.accounts).toEqual([mockAccount]);
    });
  });

  describe('loadAccountSummary error handling', () => {
    it('should handle load account summary error', async () => {
      const errorMessage = 'Summary load failed';
      mockAccountService.getAccountSummary.mockRejectedValue(new Error(errorMessage));
      
      const { result } = renderHook(() => useAccountStore());
      
      await act(async () => {
        await result.current.loadAccountSummary();
      });
      
      expect(result.current.error).toBe(errorMessage);
      expect(result.current.accountSummary).toBeNull();
    });
  });

  describe('error handling', () => {
    it('should clear error when performing successful operations', async () => {
      mockAccountService.getAllAccounts.mockResolvedValue([mockAccount]);
      
      const { result } = renderHook(() => useAccountStore());
      
      // Set error first
      act(() => {
        result.current.setError('Previous error');
      });
      
      expect(result.current.error).toBe('Previous error');
      
      await act(async () => {
        await result.current.loadAccounts();
      });
      
      expect(result.current.error).toBeNull();
    });
  });
});