import { renderHook, act } from '@testing-library/react-native';
import { Category } from '@/shared/types';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

// Create mock repository instance
const mockCategoryRepository = {
  findAll: jest.fn(),
  findById: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

// Mock the CategoryRepository module
jest.mock('@/data/repositories/CategoryRepository', () => {
  return {
    CategoryRepository: jest.fn().mockImplementation(() => mockCategoryRepository),
  };
});

// Import store after setting up mocks
const { useCategoryStore } = require('@/shared/stores/categoryStore');

describe('categoryStore', () => {
  
  const mockSystemCategory: Category = {
    id: 1,
    name: 'Groceries',
    parent_id: null,
    category_type: 'expense',
    color_code: '#FF5722',
    icon_name: '🛒',
    is_system: true,
    created_at: '2024-01-01T00:00:00Z',
    sync_status: 'local',
  };

  const mockUserCategory: Category = {
    id: 2,
    name: 'Salary',
    parent_id: null,
    category_type: 'income',
    color_code: '#4CAF50',
    icon_name: '💰',
    is_system: false,
    created_at: '2024-01-01T00:00:00Z',
    sync_status: 'local',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset store state
    useCategoryStore.setState({
      categories: [],
      selectedCategory: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
    
    // Clear all mocks
    Object.values(mockCategoryRepository).forEach(mock => {
      if (jest.isMockFunction(mock)) {
        mock.mockClear();
      }
    });
  });

  describe('loadCategories', () => {
    it('should load categories successfully', async () => {
      mockCategoryRepository.findAll.mockResolvedValue([mockSystemCategory, mockUserCategory]);
      
      const { result } = renderHook(() => useCategoryStore());
      
      await act(async () => {
        await result.current.loadCategories();
      });
      
      expect(result.current.categories).toEqual([mockSystemCategory, mockUserCategory]);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.lastUpdated).toBeDefined();
    });

    it('should handle load categories error', async () => {
      const errorMessage = 'Failed to load categories';
      mockCategoryRepository.findAll.mockRejectedValue(new Error(errorMessage));
      
      const { result } = renderHook(() => useCategoryStore());
      
      await act(async () => {
        try {
          await result.current.loadCategories();
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.categories).toEqual([]);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });

    it('should set loading state during load', async () => {
      let resolvePromise: (value: Category[]) => void;
      const promise = new Promise<Category[]>((resolve) => {
        resolvePromise = resolve;
      });
      mockCategoryRepository.findAll.mockReturnValue(promise);
      
      const { result } = renderHook(() => useCategoryStore());
      
      act(() => {
        result.current.loadCategories();
      });
      
      expect(result.current.loading).toBe(true);
      
      await act(async () => {
        resolvePromise!([mockSystemCategory]);
        await promise;
      });
      
      expect(result.current.loading).toBe(false);
    });
  });

  describe('refreshCategories', () => {
    it('should refresh categories by calling loadCategories', async () => {
      mockCategoryRepository.findAll.mockResolvedValue([mockSystemCategory]);
      const { result } = renderHook(() => useCategoryStore());
      
      await act(async () => {
        await result.current.refreshCategories();
      });
      
      expect(result.current.categories).toEqual([mockSystemCategory]);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('createCategory', () => {
    it('should create category successfully', async () => {
      const categoryData = {
        name: 'New Category',
        description: 'New category description',
        icon: '🆕',
        category_type: 'expense' as const,
        is_system: false,
        is_active: true,
      };
      
      const createdCategory = { ...mockUserCategory, ...categoryData, id: 3 };
      mockCategoryRepository.create.mockResolvedValue(createdCategory);
      
      const { result } = renderHook(() => useCategoryStore());
      
      // Set initial state
      act(() => {
        result.current.setCategories([mockSystemCategory]);
      });
      
      let returnedCategory: Category;
      await act(async () => {
        returnedCategory = await result.current.createCategory(categoryData);
      });
      
      expect(mockCategoryRepository.create).toHaveBeenCalledWith(categoryData);
      expect(result.current.categories).toContain(createdCategory);
      expect(result.current.categories).toHaveLength(2);
      expect(returnedCategory!).toEqual(createdCategory);
      expect(result.current.error).toBeNull();
    });

    it('should handle create category error', async () => {
      const categoryData = {
        name: 'New Category',
        description: 'New category description',
        icon: '🆕',
        category_type: 'expense' as const,
        is_system: false,
        is_active: true,
      };
      
      const errorMessage = 'Category creation failed';
      mockCategoryRepository.create.mockRejectedValue(new Error(errorMessage));
      
      const { result } = renderHook(() => useCategoryStore());
      
      await act(async () => {
        try {
          await result.current.createCategory(categoryData);
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.error).toBe(errorMessage);
      expect(result.current.loading).toBe(false);
    });
  });

  describe('updateCategory', () => {
    it('should update category successfully', async () => {
      const updateData = { name: 'Updated Category' };
      const updatedCategory = { ...mockUserCategory, ...updateData };
      
      mockCategoryRepository.update.mockResolvedValue(updatedCategory);
      
      const { result } = renderHook(() => useCategoryStore());
      
      // Set initial state
      act(() => {
        result.current.setCategories([mockUserCategory]);
      });
      
      await act(async () => {
        await result.current.updateCategory(2, updateData);
      });
      
      expect(mockCategoryRepository.update).toHaveBeenCalledWith(2, updateData);
      expect(result.current.categories[0]).toEqual(updatedCategory);
      expect(result.current.error).toBeNull();
      expect(result.current.loading).toBe(false);
    });

    it('should handle update category error', async () => {
      const updateData = { name: 'Updated Category' };
      const errorMessage = 'Update failed';
      
      mockCategoryRepository.update.mockRejectedValue(new Error(errorMessage));
      
      const { result } = renderHook(() => useCategoryStore());
      
      await act(async () => {
        try {
          await result.current.updateCategory(2, updateData);
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.error).toBe(errorMessage);
      expect(result.current.loading).toBe(false);
    });
  });

  describe('deleteCategory', () => {
    it('should delete category successfully', async () => {
      mockCategoryRepository.delete.mockResolvedValue(true);
      
      const { result } = renderHook(() => useCategoryStore());
      
      // Set initial state
      act(() => {
        result.current.setCategories([mockSystemCategory, mockUserCategory]);
      });
      
      await act(async () => {
        await result.current.deleteCategory(2);
      });
      
      expect(mockCategoryRepository.delete).toHaveBeenCalledWith(2);
      expect(result.current.categories).toHaveLength(1);
      expect(result.current.categories[0].id).toBe(1);
      expect(result.current.error).toBeNull();
      expect(result.current.loading).toBe(false);
    });

    it('should clear selected category if deleted', async () => {
      mockCategoryRepository.delete.mockResolvedValue(true);
      
      const { result } = renderHook(() => useCategoryStore());
      
      // Set initial state
      act(() => {
        result.current.setCategories([mockSystemCategory, mockUserCategory]);
        result.current.setSelectedCategory(mockUserCategory);
      });
      
      expect(result.current.selectedCategory?.id).toBe(2);
      
      await act(async () => {
        await result.current.deleteCategory(2);
      });
      
      expect(result.current.selectedCategory).toBeNull();
    });

    it('should handle delete category error', async () => {
      const errorMessage = 'Delete failed';
      mockCategoryRepository.delete.mockRejectedValue(new Error(errorMessage));
      
      const { result } = renderHook(() => useCategoryStore());
      
      await act(async () => {
        try {
          await result.current.deleteCategory(2);
        } catch {
          // Expected to throw
        }
      });
      
      expect(result.current.error).toBe(errorMessage);
      expect(result.current.loading).toBe(false);
    });
  });

  describe('category filtering', () => {
    beforeEach(() => {
      const { result } = renderHook(() => useCategoryStore());
      act(() => {
        result.current.setCategories([mockSystemCategory, mockUserCategory]);
      });
    });

    it('should filter categories by type - expense', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      const expenseCategories = result.current.getCategoriesByType('expense');
      
      expect(expenseCategories).toHaveLength(1);
      expect(expenseCategories[0]).toEqual(mockSystemCategory);
    });

    it('should filter categories by type - income', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      const incomeCategories = result.current.getCategoriesByType('income');
      
      expect(incomeCategories).toHaveLength(1);
      expect(incomeCategories[0]).toEqual(mockUserCategory);
    });

    it('should get system categories', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      const systemCategories = result.current.getSystemCategories();
      
      expect(systemCategories).toHaveLength(1);
      expect(systemCategories[0]).toEqual(mockSystemCategory);
    });

    it('should get user categories', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      const userCategories = result.current.getUserCategories();
      
      expect(userCategories).toHaveLength(1);
      expect(userCategories[0]).toEqual(mockUserCategory);
    });
  });

  describe('category selection', () => {
    it('should set selected category directly', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      act(() => {
        result.current.setSelectedCategory(mockSystemCategory);
      });
      
      expect(result.current.selectedCategory).toEqual(mockSystemCategory);
    });

    it('should clear selected category when null', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      // First set a category
      act(() => {
        result.current.setSelectedCategory(mockSystemCategory);
      });
      expect(result.current.selectedCategory).toEqual(mockSystemCategory);
      
      // Then clear it
      act(() => {
        result.current.setSelectedCategory(null);
      });
      
      expect(result.current.selectedCategory).toBeNull();
    });

    it('should select category by id', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      // Set initial categories
      act(() => {
        result.current.setCategories([mockSystemCategory, mockUserCategory]);
        result.current.selectCategoryById(2);
      });
      
      expect(result.current.selectedCategory?.id).toBe(2);
    });

    it('should clear selection when null id is passed', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      // First set categories and select one
      act(() => {
        result.current.setCategories([mockSystemCategory, mockUserCategory]);
        result.current.selectCategoryById(2);
      });
      expect(result.current.selectedCategory?.id).toBe(2);
      
      // Then clear selection
      act(() => {
        result.current.selectCategoryById(null);
      });
      
      expect(result.current.selectedCategory).toBeNull();
    });

    it('should handle invalid category id', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      // Set initial categories
      act(() => {
        result.current.setCategories([mockSystemCategory, mockUserCategory]);
        result.current.selectCategoryById(999); // Non-existent id
      });
      
      expect(result.current.selectedCategory).toBeNull();
    });
  });

  describe('state management', () => {
    it('should set categories', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      act(() => {
        result.current.setCategories([mockSystemCategory, mockUserCategory]);
      });
      
      expect(result.current.categories).toEqual([mockSystemCategory, mockUserCategory]);
      expect(result.current.lastUpdated).toBeDefined();
    });

    it('should set loading state', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      act(() => {
        result.current.setLoading(true);
      });
      
      expect(result.current.loading).toBe(true);
      
      act(() => {
        result.current.setLoading(false);
      });
      
      expect(result.current.loading).toBe(false);
    });

    it('should set error state', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      act(() => {
        result.current.setError('Some error');
      });
      
      expect(result.current.error).toBe('Some error');
    });

    it('should clear error', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      // Set error first
      act(() => {
        result.current.setError('Some error');
      });
      expect(result.current.error).toBe('Some error');
      
      // Clear error
      act(() => {
        result.current.clearError();
      });
      
      expect(result.current.error).toBeNull();
    });

    it('should reset store to initial state', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      // Set up some state
      act(() => {
        result.current.setCategories([mockSystemCategory, mockUserCategory]);
        result.current.setSelectedCategory(mockSystemCategory);
        result.current.setError('Some error');
        result.current.setLoading(true);
      });
      
      expect(result.current.categories).toHaveLength(2);
      expect(result.current.selectedCategory?.id).toBe(1);
      expect(result.current.error).toBe('Some error');
      expect(result.current.loading).toBe(true);
      
      act(() => {
        result.current.reset();
      });
      
      expect(result.current.categories).toEqual([]);
      expect(result.current.selectedCategory).toBeNull();
      expect(result.current.error).toBeNull();
      expect(result.current.loading).toBe(false);
      expect(result.current.lastUpdated).toBeNull();
    });
  });

  describe('persistence', () => {
    it('should persist categories and selectedCategory', () => {
      const { result } = renderHook(() => useCategoryStore());
      
      act(() => {
        result.current.setCategories([mockSystemCategory]);
        result.current.setSelectedCategory(mockSystemCategory);
      });
      
      expect(result.current.categories).toEqual([mockSystemCategory]);
      expect(result.current.selectedCategory).toEqual(mockSystemCategory);
    });
  });

  describe('error handling', () => {
    it('should clear error when performing successful operations', async () => {
      mockCategoryRepository.findAll.mockResolvedValue([mockSystemCategory]);
      
      const { result } = renderHook(() => useCategoryStore());
      
      // Set error first
      act(() => {
        result.current.setError('Previous error');
      });
      
      expect(result.current.error).toBe('Previous error');
      
      await act(async () => {
        await result.current.loadCategories();
      });
      
      expect(result.current.error).toBeNull();
    });
  });
});