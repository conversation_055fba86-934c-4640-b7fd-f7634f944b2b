// Jest setup for FinVibe authentication tests

// Mock react-native modules
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock StyleSheet
jest.mock('react-native/Libraries/StyleSheet/StyleSheet', () => ({
  create: jest.fn((styles) => styles),
  flatten: jest.fn((style) => style),
  compose: jest.fn((style1, style2) => [style1, style2]),
}));

// Mock PixelRatio
jest.mock('react-native/Libraries/Utilities/PixelRatio', () => ({
  get: jest.fn(() => 2),
  getFontScale: jest.fn(() => 1),
  getPixelSizeForLayoutSize: jest.fn((size) => size * 2),
  roundToNearestPixel: jest.fn((size) => Math.round(size)),
}));

// Mock Alert for tests
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: jest.fn(),
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

// Mock react-native-keychain
jest.mock('react-native-keychain', () => ({
  setCredentials: jest.fn(() => Promise.resolve(true)),
  getCredentials: jest.fn(() => Promise.resolve({ username: 'test', password: 'test' })),
  resetCredentials: jest.fn(() => Promise.resolve(true)),
  getSupportedBiometryType: jest.fn(() => Promise.resolve('FaceID')),
  ACCESS_CONTROL: {
    BIOMETRY_ANY_OR_DEVICE_PASSCODE: 'BiometryAnyOrDevicePasscode',
    BIOMETRY_CURRENT_SET: 'BiometryCurrentSet',
  },
  AUTHENTICATION_TYPE: {
    DEVICE_PASSCODE_OR_BIOMETRICS: 'DevicePasscodeOrBiometrics',
    BIOMETRICS: 'Biometrics',
  },
  SECURITY_LEVEL: {
    SECURE_HARDWARE: 'SecureHardware',
  },
  BIOMETRY_TYPE: {
    FACE_ID: 'FaceID',
    TOUCH_ID: 'TouchID',
    FINGERPRINT: 'Fingerprint',
  },
}));

// Mock expo-local-authentication
jest.mock('expo-local-authentication', () => ({
  hasHardwareAsync: jest.fn(() => Promise.resolve(true)),
  isEnrolledAsync: jest.fn(() => Promise.resolve(true)),
  supportedAuthenticationTypesAsync: jest.fn(() => Promise.resolve([1])),
  authenticateAsync: jest.fn(() => Promise.resolve({ success: true })),
  AuthenticationType: {
    FINGERPRINT: 1,
    FACIAL_RECOGNITION: 2,
    IRIS: 3,
  },
}));

// Mock testing utilities that are actually used
global.jest = require('jest');

// Mock react-native-gesture-handler
jest.mock('react-native-gesture-handler', () => {
  const View = require('react-native/Libraries/Components/View/View');
  return {
    Swipeable: View,
    DrawerLayout: View,
    State: {},
    ScrollView: View,
    Slider: View,
    Switch: View,
    TextInput: View,
    ToolbarAndroid: View,
    ViewPagerAndroid: View,
    DrawerLayoutAndroid: View,
    WebView: View,
    NativeViewGestureHandler: View,
    TapGestureHandler: View,
    FlingGestureHandler: View,
    ForceTouchGestureHandler: View,
    LongPressGestureHandler: View,
    PanGestureHandler: View,
    PinchGestureHandler: View,
    RotationGestureHandler: View,
    RawButton: View,
    BaseButton: View,
    RectButton: View,
    BorderlessButton: View,
    FlatList: View,
    gestureHandlerRootHOC: jest.fn(component => component),
    Directions: {},
  };
});

// Mock expo-secure-store
jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn((key) => {
    if (key === 'database_key' || key === 'database_encryption_key') {
      return Promise.resolve('mocked_encryption_key_32_chars_long');
    }
    return Promise.resolve(null);
  }),
  setItemAsync: jest.fn(() => Promise.resolve()),
  deleteItemAsync: jest.fn(() => Promise.resolve()),
  isAvailableAsync: jest.fn(() => Promise.resolve(true)),
}));

// Mock expo-crypto  
jest.mock('expo-crypto', () => ({
  getRandomBytesAsync: jest.fn((size: number) => {
    const bytes = new Uint8Array(size);
    for (let i = 0; i < size; i++) {
      bytes[i] = Math.floor(Math.random() * 256);
    }
    return Promise.resolve(bytes);
  }),
}));

// Mock expo-sqlite with migration state tracking
jest.mock('expo-sqlite', () => {
  // Shared state for migration tracking
  const migrations: {id: number, name: string, batch: number, migration_time: string}[] = [];
  let migrationIdCounter = 1;
  const tables = new Set<string>();
  const indexes = new Set<string>();
  
  const createMockResult = (data: any[] = [], insertId?: number) => ({
    rows: {
      _array: data,
      length: data.length,
      item: jest.fn((index: number) => data[index] || null),
    },
    insertId: insertId,
    rowsAffected: data.length > 0 ? 1 : 0,
  });

  const mockDatabase = {
    transaction: jest.fn((callback: any, errorCallback?: any, successCallback?: any) => {
      const mockTransaction = {
        executeSql: jest.fn((sql: string, params: any[] = [], successCallback?: any, errorCallback?: any) => {
          try {
            let result = createMockResult();
            
            // Handle CREATE TABLE queries
            if (sql.toUpperCase().includes('CREATE TABLE')) {
              const tableMatch = sql.match(/CREATE TABLE (?:IF NOT EXISTS )?(\w+)/i);
              if (tableMatch) {
                tables.add(tableMatch[1]);
              }
              result = createMockResult([], 1);
            }
            
            // Handle INSERT operations for actual data (not migrations)
            else if (sql.toUpperCase().includes('INSERT INTO') && !sql.includes('knex_migrations')) {
              const insertId = Math.floor(Math.random() * 1000) + 1;
              result = createMockResult([], insertId);
            }
            
            // Handle CREATE INDEX queries
            else if (sql.toUpperCase().includes('CREATE INDEX')) {
              const indexMatch = sql.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i);
              if (indexMatch) {
                indexes.add(indexMatch[1]);
              }
              result = createMockResult([], 1);
            }
            
            // Handle INSERT into knex_migrations
            else if (sql.includes('INSERT INTO knex_migrations')) {
              const [name, batch] = params;
              const migration = {
                id: migrationIdCounter++,
                name: name,
                batch: batch,
                migration_time: new Date().toISOString()
              };
              migrations.push(migration);
              result = createMockResult([], migration.id);
            }
            
            // Handle SELECT MAX(batch) queries for migration version
            else if (sql.includes('MAX(batch)')) {
              const maxBatch = migrations.length > 0 ? Math.max(...migrations.map(m => m.batch)) : 0;
              result = createMockResult([{ version: maxBatch }]);
            }
            
            // Handle SELECT from knex_migrations
            else if (sql.includes('SELECT id, name, batch, migration_time FROM knex_migrations')) {
              result = createMockResult([...migrations]);
            }
            
            // Handle table existence checks
            else if (sql.includes("SELECT name FROM sqlite_master WHERE type='table'")) {
              if (sql.includes("AND name=")) {
                // Check for specific table
                const tableName = params[0] || '';
                if (tables.has(tableName) || tableName === 'knex_migrations') {
                  result = createMockResult([{ name: tableName }]);
                } else {
                  result = createMockResult([]);
                }
              } else {
                // List all tables
                const allTables = Array.from(tables).concat(['knex_migrations']).map(name => ({ name }));
                result = createMockResult(allTables);
              }
            }
            
            // Handle index existence checks
            else if (sql.includes("SELECT name FROM sqlite_master WHERE type='index'")) {
              const indexList = Array.from(indexes).map(name => ({ name }));
              result = createMockResult(indexList);
            }
            
            // Handle simple SELECT 1 for health checks
            else if (sql.includes('SELECT 1')) {
              result = createMockResult([{ '1': 1 }]);
            }
            
            // Handle COUNT queries 
            else if (sql.includes('COUNT(*)')) {
              result = createMockResult([{ count: 0 }]);
            }
            
            // Handle SELECT queries for account data
            else if (sql.includes('SELECT') && sql.includes('FROM accounts')) {
              if (sql.includes('WHERE id = ?')) {
                // Return mock account data for findById
                const accountId = params[0];
                result = createMockResult([{
                  id: accountId,
                  name: 'Test Account',
                  type: 'checking',
                  balance: 1000.00,
                  currency: 'INR',
                  is_active: 1,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  sync_status: 'local'
                }]);
              } else if (sql.includes('WHERE is_active = 1') || !sql.includes('WHERE')) {
                // Return mock account data for findAll
                result = createMockResult([{
                  id: 1,
                  name: 'Test Account 1',
                  type: 'checking',
                  balance: 1000.00,
                  currency: 'INR',
                  is_active: 1,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  sync_status: 'local'
                }, {
                  id: 2,
                  name: 'Test Account 2',
                  type: 'savings',
                  balance: 2000.00,
                  currency: 'INR',
                  is_active: 1,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  sync_status: 'local'
                }]);
              }
            }
            
            // Handle SELECT queries for category data
            else if (sql.includes('SELECT') && sql.includes('FROM categories')) {
              // Return mock category data
              result = createMockResult([{
                id: 1,
                name: 'Food & Dining',
                parent_id: null,
                category_type: 'expense',
                color_code: '#FF6B6B',
                icon_name: 'restaurant',
                is_system: 1,
                created_at: new Date().toISOString(),
                sync_status: 'local'
              }, {
                id: 2,
                name: 'Transportation',
                parent_id: null,
                category_type: 'expense',
                color_code: '#4ECDC4',
                icon_name: 'car',
                is_system: 1,
                created_at: new Date().toISOString(),
                sync_status: 'local'
              }]);
            }
            
            // Handle SELECT queries for transaction data
            else if (sql.includes('SELECT') && sql.includes('FROM transactions')) {
              if (sql.includes('WHERE id = ?')) {
                // Return mock transaction data for findById
                const transactionId = params[0];
                result = createMockResult([{
                  id: transactionId,
                  account_id: 1,
                  amount: 100.00,
                  description: 'Test Transaction',
                  category_id: 1,
                  transaction_type: 'expense',
                  transaction_date: new Date().toISOString(),
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  sms_source: null,
                  confidence_score: 0.95,
                  is_recurring: 0,
                  recurring_pattern: null,
                  sync_status: 'local',
                  hash: 'test_hash'
                }]);
              }
              
              // Handle findByAccount queries
              else if (sql.includes('WHERE account_id = ?') && sql.includes('ORDER BY')) {
                // Return mock transactions for findByAccount
                const mockTransactions = Array.from({ length: 5 }, (_, i) => ({
                  id: i + 1,
                  account_id: params[0],
                  amount: (i + 1) * 100.00,
                  description: `Test Transaction ${i + 1}`,
                  category_id: (i % 2) + 1,
                  transaction_type: i % 2 === 0 ? 'expense' : 'income',
                  transaction_date: new Date().toISOString(),
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  sms_source: null,
                  confidence_score: 0.95,
                  is_recurring: 0,
                  recurring_pattern: null,
                  sync_status: 'local',
                  hash: `test_hash_${i}`
                }));
                result = createMockResult(mockTransactions);
              }
              
              // Handle getAccountBalance queries (COALESCE, SUM with CASE)
              else if (sql.includes('COALESCE') && sql.includes('total_income') && sql.includes('total_expense')) {
                result = createMockResult([{
                  total_income: 1500.00,
                  total_expense: 800.00
                }]);
              }
              
              // Handle getSpendingByCategory queries (GROUP BY category_id)
              else if (sql.includes('GROUP BY category_id') && sql.includes('SUM(amount)')) {
                result = createMockResult([{
                  category_id: 1,
                  total: 400.00
                }, {
                  category_id: 2,
                  total: 200.00
                }]);
              }
              
              // Handle getMonthlyTrends queries
              else if (sql.includes('strftime') || sql.includes('DATE(')) {
                result = createMockResult([{
                  month: '2025-01',
                  income: 1500.00,
                  expense: 800.00
                }, {
                  month: '2024-12',
                  income: 1200.00,
                  expense: 900.00
                }]);
              }
              
              // Handle findByDateRange queries
              else if (sql.includes('transaction_date BETWEEN') || sql.includes('WHERE transaction_date >=')) {
                const mockTransactions = Array.from({ length: 3 }, (_, i) => ({
                  id: i + 10,
                  account_id: 1,
                  amount: (i + 1) * 150.00,
                  description: `Date Range Transaction ${i + 1}`,
                  category_id: (i % 2) + 1,
                  transaction_type: 'expense',
                  transaction_date: new Date().toISOString(),
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  sms_source: null,
                  confidence_score: 0.95,
                  is_recurring: 0,
                  recurring_pattern: null,
                  sync_status: 'local',
                  hash: `date_range_${i}`
                }));
                result = createMockResult(mockTransactions);
              }
              
              else {
                // Return empty result for other transaction queries
                result = createMockResult([]);
              }
            }
            
            // Handle DELETE from knex_migrations (for rollbacks)
            else if (sql.includes('DELETE FROM knex_migrations WHERE id = ?')) {
              const migrationId = params[0];
              const index = migrations.findIndex(m => m.id === migrationId);
              if (index !== -1) {
                migrations.splice(index, 1);
                result = createMockResult([], undefined);
                result.rowsAffected = 1;
              }
            }
            
            // Handle DROP TABLE queries (for rollbacks)
            else if (sql.toUpperCase().includes('DROP TABLE')) {
              const tableMatch = sql.match(/DROP TABLE (?:IF EXISTS )?(\w+)/i);
              if (tableMatch) {
                tables.delete(tableMatch[1]);
              }
              result = createMockResult([], 1);
            }
            
            if (successCallback) {
              successCallback(mockTransaction, result);
            }
          } catch (error) {
            if (errorCallback) {
              errorCallback(mockTransaction, error);
            }
          }
        }),
      };
      
      try {
        callback(mockTransaction);
        if (successCallback) successCallback();
      } catch (error) {
        if (errorCallback) errorCallback(error);
      }
    }),
  };

  return {
    openDatabase: jest.fn(() => mockDatabase),
    WebSQLDatabase: class MockWebSQLDatabase {
      transaction = mockDatabase.transaction;
    },
    SQLTransaction: class MockSQLTransaction {
      executeSql = jest.fn();
    },
  };
});

// Mock react-native-sqlite-storage with state tracking
jest.mock('react-native-sqlite-storage', () => {
  // Shared state across all database instances
  const migrations: {id: number, name: string, batch: number, migration_time: string}[] = [];
  let migrationIdCounter = 1;
  
  const createMockRows = (data: any[] = []) => ({
    _array: data,
    length: data.length,
    item: jest.fn((index: number) => data[index] || null)
  });
  
  const mockExecuteSql = jest.fn((sql: string, params: any[] = []) => {
    // Handle INSERT into knex_migrations
    if (sql.includes('INSERT INTO knex_migrations')) {
      const [name, batch] = params;
      const migration = {
        id: migrationIdCounter++,
        name: name,
        batch: batch,
        migration_time: new Date().toISOString()
      };
      migrations.push(migration);
      const mockRows = createMockRows();
      return Promise.resolve([{ rows: mockRows, insertId: migration.id, rowsAffected: 1 }]);
    }
    
    // Handle SELECT MAX(batch) queries (return current migration version)
    if (sql.includes('MAX(batch)')) {
      const maxBatch = migrations.length > 0 ? Math.max(...migrations.map(m => m.batch)) : 0;
      const mockRows = createMockRows([{ version: maxBatch }]);
      return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
    }
    
    // Handle SELECT from knex_migrations
    if (sql.includes('SELECT id, name, batch, migration_time FROM knex_migrations')) {
      const mockRows = createMockRows([...migrations]);
      return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
    }
    
    // Handle CREATE TABLE queries (return success)
    if (sql.toUpperCase().includes('CREATE TABLE')) {
      const mockRows = createMockRows();
      return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
    }
    
    // Handle CREATE INDEX queries (return success) 
    if (sql.toUpperCase().includes('CREATE INDEX')) {
      const mockRows = createMockRows();
      return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
    }
    
    // Check for tables existence (for migration tests)
    if (sql.includes("SELECT name FROM sqlite_master WHERE type='table'")) {
      // Return expected tables that should exist after migrations
      const tables = [
        { name: 'accounts' },
        { name: 'categories' },
        { name: 'transactions' },
        { name: 'budgets' },
        { name: 'budget_categories' },
        { name: 'sms_patterns' },
        { name: 'user_settings' },
        { name: 'due_dates' },
        { name: 'dashboard_summary' },
        { name: 'knex_migrations' }
      ];
      const mockRows = createMockRows(tables);
      return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
    }
    
    // Check for specific table existence
    if (sql.includes("SELECT name FROM sqlite_master WHERE type='table' AND name=")) {
      // Extract table name from query
      const match = sql.match(/name='(\w+)'/);
      const tableName = match ? match[1] : '';
      const expectedTables = ['accounts', 'categories', 'transactions', 'budgets', 'budget_categories', 'sms_patterns', 'user_settings', 'due_dates', 'dashboard_summary', 'knex_migrations'];
      
      if (expectedTables.includes(tableName)) {
        const mockRows = createMockRows([{ name: tableName }]);
        return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
      } else {
        const mockRows = createMockRows([]);
        return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
      }
    }
    
    // Check for indexes
    if (sql.includes("SELECT name FROM sqlite_master WHERE type='index'")) {
      const indexes = [
        { name: 'idx_accounts_type' },
        { name: 'idx_accounts_active' },
        { name: 'idx_transactions_account_date' },
        { name: 'idx_transactions_category' }
      ];
      const mockRows = createMockRows(indexes);
      return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
    }
    
    // Default response for any other queries
    const mockRows = createMockRows();
    return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
  });
  
  return {
    openDatabase: jest.fn(() => Promise.resolve({
      executeSql: mockExecuteSql,
      transaction: jest.fn((fn) => {
        const tx = {
          executeSql: jest.fn((sql, params, successCallback) => {
            // Use the same mock logic but call success callback for transaction context
            mockExecuteSql(sql, params).then(result => {
              if (successCallback) successCallback(tx, result[0]);
            });
          }),
        };
        fn(tx);
        return Promise.resolve();
      }),
      close: jest.fn(() => Promise.resolve()),
    })),
    DEBUG: jest.fn(() => {}),
    enablePromise: jest.fn(() => {}),
  };
});

// Mock crypto-js
jest.mock('crypto-js', () => ({
  SHA256: jest.fn(() => ({
    toString: jest.fn(() => 'mocked_hash'),
  })),
  lib: {
    WordArray: {
      random: jest.fn(() => ({
        toString: jest.fn(() => 'mocked_random_key'),
      })),
    },
  },
}));

// Mock Platform
jest.mock('react-native/Libraries/Utilities/Platform', () => ({
  OS: 'ios',
  Version: '14.0',
  select: jest.fn(config => config.ios || config.default),
}));

// Mock AppState
jest.mock('react-native/Libraries/AppState/AppState', () => ({
  currentState: 'active',
  addEventListener: jest.fn(() => ({ remove: jest.fn() })),
  removeEventListener: jest.fn(),
}));

// Mock Dimensions
jest.mock('react-native/Libraries/Utilities/Dimensions', () => ({
  get: jest.fn(() => ({ width: 375, height: 667 })),
  addEventListener: jest.fn(() => ({ remove: jest.fn() })),
}));

// Mock Vibration
jest.mock('react-native/Libraries/Vibration/Vibration', () => ({
  vibrate: jest.fn(),
}));

// Mock Settings/SettingsManager
jest.mock('react-native/Libraries/Settings/Settings', () => ({
  get: jest.fn((key) => null),
  set: jest.fn(),
  watchKeys: jest.fn(() => ({ remove: jest.fn() })),
}));

// Mock NativeSettingsManager 
jest.mock('react-native/Libraries/Settings/NativeSettingsManager', () => ({
  getConstants: jest.fn(() => ({})),
  setValues: jest.fn(),
  getValues: jest.fn(() => Promise.resolve({})),
}));

// Mock VirtualizedList dependencies
jest.mock('react-native/Libraries/Lists/VirtualizedList', () => {
  const React = require('react');
  const MockedVirtualizedList = React.forwardRef((props: any, ref: any) => {
    const MockView = require('react-native/Libraries/Components/View/View');
    return React.createElement(MockView, props);
  });
  MockedVirtualizedList.displayName = 'VirtualizedList';
  return MockedVirtualizedList;
});

jest.mock('react-native/Libraries/Lists/VirtualizedSectionList', () => {
  const React = require('react');
  const MockedVirtualizedSectionList = React.forwardRef((props: any, ref: any) => {
    const MockView = require('react-native/Libraries/Components/View/View');
    return React.createElement(MockView, props);
  });
  MockedVirtualizedSectionList.displayName = 'VirtualizedSectionList';
  return MockedVirtualizedSectionList;
});

// Mock NativeModules
jest.mock('react-native/Libraries/BatchedBridge/NativeModules', () => ({
  SettingsManager: {
    getConstants: jest.fn(() => ({})),
    setValues: jest.fn(),
    getValues: jest.fn(() => Promise.resolve({})),
  },
  KeychainService: {
    setCredentials: jest.fn(() => Promise.resolve(true)),
    getCredentials: jest.fn(() => Promise.resolve({ username: 'test', password: 'test' })),
    resetCredentials: jest.fn(() => Promise.resolve(true)),
  },
}));

// Global test configuration
(global as any).__DEV__ = true;

// Silence console warnings in tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};