import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ExpensesPieChart } from '../../../../presentation/components/dashboard/ExpensesPieChart';
import { ThemeProvider } from '../../../../shared/theme/ThemeProvider';

// Mock Dimensions
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Dimensions: {
    get: () => ({ width: 400, height: 800 }),
  },
}));

// Mock react-native-chart-kit
jest.mock('react-native-chart-kit', () => ({
  PieChart: ({ data, onDataPointClick }: any) => {
    const MockedChart = require('react-native').View;
    return <MockedChart testID="pie-chart" data={data} onPress={onDataPointClick} />;
  },
}));

const mockExpenses = [
  {
    category_id: 1,
    category_name: 'Food',
    category_icon: 'utensils',
    category_color: '#FF6B6B',
    total_amount: 2500,
    percentage: 50,
  },
  {
    category_id: 2,
    category_name: 'Transport',
    category_icon: 'car',
    category_color: '#4ECDC4',
    total_amount: 1500,
    percentage: 30,
  },
  {
    category_id: 3,
    category_name: 'Entertainment',
    category_icon: 'gamepad',
    category_color: '#45B7D1',
    total_amount: 1000,
    percentage: 20,
  },
];

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('ExpensesPieChart', () => {
  it('renders pie chart with expenses data', () => {
    const mockOnCategorySelect = jest.fn();

    const { getByText, getByTestId } = renderWithTheme(
      <ExpensesPieChart
        expenses={mockExpenses}
        onCategorySelect={mockOnCategorySelect}
      />
    );

    expect(getByText('Expenses by Category')).toBeTruthy();
    expect(getByText('Total Expenses')).toBeTruthy();
    expect(getByText('₹5,000.00')).toBeTruthy();
    expect(getByTestId('pie-chart')).toBeTruthy();
  });

  it('renders legend items for each expense category', () => {
    const mockOnCategorySelect = jest.fn();

    const { getByText } = renderWithTheme(
      <ExpensesPieChart
        expenses={mockExpenses}
        onCategorySelect={mockOnCategorySelect}
      />
    );

    expect(getByText('Food')).toBeTruthy();
    expect(getByText('Transport')).toBeTruthy();
    expect(getByText('Entertainment')).toBeTruthy();
    expect(getByText('₹2500')).toBeTruthy();
    expect(getByText('₹1500')).toBeTruthy();
    expect(getByText('₹1000')).toBeTruthy();
  });

  it('calls onCategorySelect when legend item is pressed', () => {
    const mockOnCategorySelect = jest.fn();

    const { getByText } = renderWithTheme(
      <ExpensesPieChart
        expenses={mockExpenses}
        onCategorySelect={mockOnCategorySelect}
      />
    );

    fireEvent.press(getByText('Food'));
    expect(mockOnCategorySelect).toHaveBeenCalledWith(1);
  });

  it('shows selected category state', () => {
    const mockOnCategorySelect = jest.fn();

    const { getByText } = renderWithTheme(
      <ExpensesPieChart
        expenses={mockExpenses}
        onCategorySelect={mockOnCategorySelect}
        selectedCategoryId={1}
      />
    );

    expect(getByText('Show All Categories')).toBeTruthy();
  });

  it('renders empty state when no expenses', () => {
    const mockOnCategorySelect = jest.fn();

    const { getByText } = renderWithTheme(
      <ExpensesPieChart
        expenses={[]}
        onCategorySelect={mockOnCategorySelect}
      />
    );

    expect(getByText('No expense data available')).toBeTruthy();
  });

  it('toggles category selection correctly', () => {
    const mockOnCategorySelect = jest.fn();

    const { getByText, rerender } = renderWithTheme(
      <ExpensesPieChart
        expenses={mockExpenses}
        onCategorySelect={mockOnCategorySelect}
        selectedCategoryId={1}
      />
    );

    // Click on selected category should deselect it
    fireEvent.press(getByText('Food'));
    expect(mockOnCategorySelect).toHaveBeenCalledWith(null);

    // Rerender with no selection
    rerender(
      <ThemeProvider>
        <ExpensesPieChart
          expenses={mockExpenses}
          onCategorySelect={mockOnCategorySelect}
          selectedCategoryId={null}
        />
      </ThemeProvider>
    );

    // Click on unselected category should select it
    fireEvent.press(getByText('Transport'));
    expect(mockOnCategorySelect).toHaveBeenCalledWith(2);
  });
});