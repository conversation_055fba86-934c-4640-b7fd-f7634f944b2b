import React from 'react';
import { render } from '@testing-library/react-native';
import { TransactionsList } from '../../../../presentation/components/dashboard/TransactionsList';
import { ThemeProvider } from '../../../../shared/theme/ThemeProvider';

const mockTransactions = [
  {
    id: 1,
    description: 'Grocery Store',
    amount: 2500,
    transaction_type: 'expense' as const,
    transaction_date: '2024-01-15',
    category_id: 1,
    account_id: 1,
  },
  {
    id: 2,
    description: 'Salary',
    amount: 50000,
    transaction_type: 'income' as const,
    transaction_date: '2024-01-01',
    category_id: 2,
    account_id: 1,
  },
];

const mockGetCategoryInfo = (categoryId: number | null) => ({
  name: categoryId === 1 ? 'Food' : categoryId === 2 ? 'Salary' : 'Uncategorized',
  icon: categoryId === 1 ? 'utensils' : categoryId === 2 ? 'dollar-sign' : 'help-circle',
  color: categoryId === 1 ? '#FF6B6B' : categoryId === 2 ? '#4ECDC4' : '#999',
});

const mockGetAccountName = (accountId: number) => `Account ${accountId}`;

const mockFormatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-IN', {
    day: '2-digit',
    month: 'short'
  });
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('TransactionsList', () => {
  it('renders transactions list with title', () => {
    const { getByText } = renderWithTheme(
      <TransactionsList
        transactions={mockTransactions}
        title="Recent Transactions"
        getCategoryInfo={mockGetCategoryInfo}
        getAccountName={mockGetAccountName}
        formatDate={mockFormatDate}
      />
    );

    expect(getByText('Recent Transactions')).toBeTruthy();
  });

  it('renders transaction items correctly', () => {
    const { getByText } = renderWithTheme(
      <TransactionsList
        transactions={mockTransactions}
        title="Recent Transactions"
        getCategoryInfo={mockGetCategoryInfo}
        getAccountName={mockGetAccountName}
        formatDate={mockFormatDate}
      />
    );

    expect(getByText('Grocery Store')).toBeTruthy();
    expect(getByText('Salary')).toBeTruthy();
    expect(getByText('Food')).toBeTruthy();
    expect(getByText('• Account 1')).toBeTruthy();
    expect(getByText('-₹2500.00')).toBeTruthy();
    expect(getByText('+₹50000.00')).toBeTruthy();
  });

  it('renders empty state when no transactions', () => {
    const { getByText } = renderWithTheme(
      <TransactionsList
        transactions={[]}
        title="Recent Transactions"
        getCategoryInfo={mockGetCategoryInfo}
        getAccountName={mockGetAccountName}
        formatDate={mockFormatDate}
      />
    );

    expect(getByText('No transactions found')).toBeTruthy();
  });

  it('renders custom empty message', () => {
    const { getByText } = renderWithTheme(
      <TransactionsList
        transactions={[]}
        title="Filtered Transactions"
        getCategoryInfo={mockGetCategoryInfo}
        getAccountName={mockGetAccountName}
        formatDate={mockFormatDate}
        emptyMessage="No filtered transactions"
      />
    );

    expect(getByText('No filtered transactions')).toBeTruthy();
  });

  it('displays transaction amounts with correct colors', () => {
    const { getByText } = renderWithTheme(
      <TransactionsList
        transactions={mockTransactions}
        title="Recent Transactions"
        getCategoryInfo={mockGetCategoryInfo}
        getAccountName={mockGetAccountName}
        formatDate={mockFormatDate}
      />
    );

    const expenseAmount = getByText('-₹2500.00');
    const incomeAmount = getByText('+₹50000.00');

    expect(expenseAmount).toBeTruthy();
    expect(incomeAmount).toBeTruthy();
  });

  it('handles null category_id correctly', () => {
    const transactionWithoutCategory = [{
      ...mockTransactions[0],
      category_id: null,
    }];

    const { getByText } = renderWithTheme(
      <TransactionsList
        transactions={transactionWithoutCategory}
        title="Recent Transactions"
        getCategoryInfo={mockGetCategoryInfo}
        getAccountName={mockGetAccountName}
        formatDate={mockFormatDate}
      />
    );

    expect(getByText('Uncategorized')).toBeTruthy();
  });
});