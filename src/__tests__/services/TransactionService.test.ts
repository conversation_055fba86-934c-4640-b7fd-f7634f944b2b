import { TransactionService } from '@/business/services/TransactionService';
import { TransactionRepository } from '@/data/repositories/TransactionRepository';
import { AccountRepository } from '@/data/repositories/AccountRepository';
import { CategoryRepository } from '@/data/repositories/CategoryRepository';
import { DatabaseService } from '@/data/database/DatabaseService';
import { Transaction, Account, Category } from '@/shared/types';

// Mock dependencies
jest.mock('@/data/repositories/TransactionRepository');
jest.mock('@/data/repositories/AccountRepository');
jest.mock('@/data/repositories/CategoryRepository');
jest.mock('@/data/database/DatabaseService');

describe('TransactionService', () => {
  let transactionService: TransactionService;
  let mockTransactionRepository: jest.Mocked<TransactionRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockCategoryRepository: jest.Mocked<CategoryRepository>;
  let mockDatabaseService: jest.Mocked<DatabaseService>;

  const mockTransaction: Transaction = {
    id: 1,
    account_id: 1,
    amount: 100.50,
    description: 'Test transaction',
    category_id: 1,
    transaction_type: 'expense',
    transaction_date: '2024-01-15',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    sms_source: null,
    confidence_score: null,
    is_recurring: false,
    recurring_pattern: null,
    sync_status: 'local',
    hash: 'abc123'
  };

  const mockAccount: Account = {
    id: 1,
    name: 'Test Account',
    type: 'checking',
    balance: 1000,
    currency: 'INR',
    is_active: true,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z',
    sync_status: 'local'
  };

  const mockCategory: Category = {
    id: 1,
    name: 'Food',
    parent_id: null,
    category_type: 'expense',
    color_code: '#FF0000',
    icon_name: 'food',
    is_system: false,
    created_at: '2024-01-01T10:00:00Z',
    sync_status: 'local'
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock DatabaseService
    mockDatabaseService = {
      getInstance: jest.fn().mockReturnThis(),
      isInitialized: jest.fn().mockReturnValue(true),
      waitForInitialization: jest.fn().mockResolvedValue(undefined),
      executeSql: jest.fn(),
      getDatabase: jest.fn().mockReturnValue({
        executeSql: jest.fn()
      })
    } as any;

    // Mock executeSql for balance-related queries - this applies to all tests
    mockDatabaseService.executeSql.mockImplementation((query: string) => {
      if (query.includes('user_settings')) {
        // Mock initial balance query - return empty result to trigger fallback
        return Promise.resolve([{ rows: { length: 0, item: () => ({}), _array: [] }, rowsAffected: 0, insertId: 0 }]);
      } else if (query.includes('SUM')) {
        // Mock transaction stats query
        return Promise.resolve([{ rows: { item: (index: number) => ({ total_income: 500, total_expense: 300 }), length: 1, _array: [{ total_income: 500, total_expense: 300 }] }, rowsAffected: 0, insertId: 0 }]);
      } else if (query.includes('COUNT')) {
        // Mock count queries
        return Promise.resolve([{ rows: { item: (index: number) => ({ total: 5 }), length: 1, _array: [{ total: 5 }] }, rowsAffected: 0, insertId: 0 }]);
      } else if (query.includes('SELECT') && query.includes('transactions')) {
        // Mock transaction queries
        return Promise.resolve([{ rows: { item: (index: number) => mockTransaction, length: 1, _array: [mockTransaction] }, rowsAffected: 0, insertId: 0 }]);
      }
      return Promise.resolve([{ rows: { length: 0, item: () => ({}), _array: [] }, rowsAffected: 0, insertId: 0 }]);
    });

    (DatabaseService.getInstance as jest.Mock).mockReturnValue(mockDatabaseService);

    // Mock repositories
    mockTransactionRepository = new TransactionRepository() as jest.Mocked<TransactionRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockCategoryRepository = new CategoryRepository() as jest.Mocked<CategoryRepository>;

    // Set up constructor mocks
    (TransactionRepository as jest.Mock).mockImplementation(() => mockTransactionRepository);
    (AccountRepository as jest.Mock).mockImplementation(() => mockAccountRepository);
    (CategoryRepository as jest.Mock).mockImplementation(() => mockCategoryRepository);

    transactionService = new TransactionService();
  });

  describe('createTransaction', () => {
    const transactionData = {
      account_id: 1,
      amount: 100.50,
      description: 'Test transaction',
      category_id: 1,
      transaction_type: 'expense' as const,
      transaction_date: '2024-01-15',
      sms_source: null,
      confidence_score: null,
      is_recurring: false,
      recurring_pattern: null,
      sync_status: 'local' as const,
      hash: ''
    };

    beforeEach(() => {
      mockAccountRepository.findById.mockResolvedValue(mockAccount);
      mockCategoryRepository.findById.mockResolvedValue(mockCategory);
      mockTransactionRepository.findDuplicates.mockResolvedValue([]);
      mockTransactionRepository.create.mockResolvedValue(mockTransaction);
      mockTransactionRepository.getAccountBalance.mockResolvedValue(1000);
      mockAccountRepository.updateBalance.mockResolvedValue(mockAccount);
    });

    it('should create a transaction successfully', async () => {
      const result = await transactionService.createTransaction(transactionData);

      expect(result).toEqual(mockTransaction);
      expect(mockTransactionRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          ...transactionData,
          hash: expect.any(String),
          sync_status: 'local'
        })
      );
      expect(mockAccountRepository.updateBalance).toHaveBeenCalledWith(1, 1200, 'set');
    });

    it('should throw error for invalid amount', async () => {
      const invalidData = { ...transactionData, amount: 0 };

      await expect(transactionService.createTransaction(invalidData))
        .rejects.toThrow('Validation failed: Amount cannot be zero');
    });

    it('should throw error for invalid account', async () => {
      mockAccountRepository.findById.mockResolvedValue(null);

      await expect(transactionService.createTransaction(transactionData))
        .rejects.toThrow('Validation failed: Invalid account ID');
    });

    it('should throw error for inactive account', async () => {
      mockAccountRepository.findById.mockResolvedValue({
        ...mockAccount,
        is_active: false
      });

      await expect(transactionService.createTransaction(transactionData))
        .rejects.toThrow('Validation failed: Cannot create transaction for inactive account');
    });

    it('should throw error for duplicate transaction', async () => {
      mockTransactionRepository.findDuplicates.mockResolvedValue([mockTransaction]);

      await expect(transactionService.createTransaction(transactionData))
        .rejects.toThrow('Duplicate transaction detected');
    });

    it('should throw error for future date', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);
      const invalidData = { 
        ...transactionData, 
        transaction_date: futureDate.toISOString().split('T')[0]
      };

      await expect(transactionService.createTransaction(invalidData))
        .rejects.toThrow('Validation failed: Transaction date cannot be in the future');
    });
  });

  describe('updateTransaction', () => {
    const updateData = {
      amount: 150.75,
      description: 'Updated transaction'
    };

    beforeEach(() => {
      mockTransactionRepository.findById.mockResolvedValue(mockTransaction);
      mockAccountRepository.findById.mockResolvedValue(mockAccount);
      mockCategoryRepository.findById.mockResolvedValue(mockCategory);
      mockTransactionRepository.update.mockResolvedValue({
        ...mockTransaction,
        ...updateData
      });
      mockTransactionRepository.getAccountBalance.mockResolvedValue(1000);
      mockAccountRepository.updateBalance.mockResolvedValue(mockAccount);
    });

    it('should update transaction successfully', async () => {
      const result = await transactionService.updateTransaction(1, updateData);

      expect(result).toEqual({
        ...mockTransaction,
        ...updateData
      });
      expect(mockTransactionRepository.update).toHaveBeenCalledWith(1, {
        ...updateData,
        sync_status: 'local'
      });
    });

    it('should throw error for non-existent transaction', async () => {
      mockTransactionRepository.findById.mockResolvedValue(null);

      await expect(transactionService.updateTransaction(1, updateData))
        .rejects.toThrow('Transaction not found');
    });

    it('should update balances for both accounts when account changes', async () => {
      const updateWithNewAccount = { ...updateData, account_id: 2 };
      const newAccount = { ...mockAccount, id: 2 };
      
      mockAccountRepository.findById
        .mockResolvedValueOnce(mockAccount) // Original account
        .mockResolvedValueOnce(newAccount); // New account
      
      mockTransactionRepository.update.mockResolvedValue({
        ...mockTransaction,
        ...updateWithNewAccount
      });

      await transactionService.updateTransaction(1, updateWithNewAccount);

      expect(mockAccountRepository.updateBalance).toHaveBeenCalledTimes(2);
      expect(mockAccountRepository.updateBalance).toHaveBeenCalledWith(1, 1200, 'set');
      expect(mockAccountRepository.updateBalance).toHaveBeenCalledWith(2, 1200, 'set');
    });
  });

  describe('deleteTransaction', () => {
    beforeEach(() => {
      mockTransactionRepository.findById.mockResolvedValue(mockTransaction);
      mockTransactionRepository.delete.mockResolvedValue(true);
      mockTransactionRepository.getAccountBalance.mockResolvedValue(1000);
      mockAccountRepository.updateBalance.mockResolvedValue(mockAccount);
    });

    it('should delete transaction successfully', async () => {
      const result = await transactionService.deleteTransaction(1);

      expect(result).toBe(true);
      expect(mockTransactionRepository.delete).toHaveBeenCalledWith(1);
      expect(mockAccountRepository.updateBalance).toHaveBeenCalledWith(1, 1200, 'set');
    });

    it('should throw error for non-existent transaction', async () => {
      mockTransactionRepository.findById.mockResolvedValue(null);

      await expect(transactionService.deleteTransaction(1))
        .rejects.toThrow('Transaction not found');
    });
  });

  describe('getTransactions', () => {
    beforeEach(() => {
      const mockExecuteSql = mockDatabaseService.executeSql as jest.Mock;
      
      // Mock count query
      mockExecuteSql
        .mockResolvedValueOnce([{
          rows: {
            item: (index: number) => ({ total: 5 }),
            length: 1,
            _array: [{ total: 5 }]
          },
          rowsAffected: 0,
          insertId: 0
        }])
        // Mock data query
        .mockResolvedValueOnce([{
          rows: {
            item: (index: number) => mockTransaction,
            length: 1,
            _array: [mockTransaction]
          },
          rowsAffected: 0,
          insertId: 0
        }]);
    });

    it('should fetch transactions with pagination', async () => {
      const result = await transactionService.getTransactions({}, 1, 20);

      expect(result).toEqual({
        transactions: [mockTransaction],
        totalCount: 5,
        page: 1,
        limit: 20,
        hasMore: true // Since offset (0) + transactions.length (1) < totalCount (5)
      });
    });

    it('should apply filters correctly', async () => {
      const filters = {
        accountId: 1,
        transactionType: 'expense' as const,
        searchQuery: 'test'
      };

      await transactionService.getTransactions(filters, 1, 20);

      const mockExecuteSql = mockDatabaseService.executeSql as jest.Mock;
      const countQuery = mockExecuteSql.mock.calls[0][0];
      const dataQuery = mockExecuteSql.mock.calls[1][0];

      expect(countQuery).toContain('WHERE account_id = ? AND transaction_type = ? AND description LIKE ?');
      expect(dataQuery).toContain('WHERE account_id = ? AND transaction_type = ? AND description LIKE ?');
    });
  });

  describe('searchTransactions', () => {
    beforeEach(() => {
      const mockExecuteSql = mockDatabaseService.executeSql as jest.Mock;
      mockExecuteSql.mockResolvedValue([{
        rows: {
          item: (index: number) => mockTransaction,
          length: 1,
          _array: [mockTransaction]
        },
        rowsAffected: 0,
        insertId: 0
      }]);
    });

    it('should search transactions by description', async () => {
      const result = await transactionService.searchTransactions('test', 10);

      expect(result).toEqual([mockTransaction]);
      
      const mockExecuteSql = mockDatabaseService.executeSql as jest.Mock;
      expect(mockExecuteSql).toHaveBeenCalledWith(
        expect.stringContaining('WHERE description LIKE ?'),
        ['%test%', 10]
      );
    });

    it('should return empty array for short queries', async () => {
      const result = await transactionService.searchTransactions('a');

      expect(result).toEqual([]);
    });
  });

  describe('suggestCategories', () => {
    beforeEach(() => {
      mockCategoryRepository.findAll.mockResolvedValue([mockCategory]);
      
      const mockExecuteSql = mockDatabaseService.executeSql as jest.Mock;
      mockExecuteSql.mockResolvedValue([{
        rows: {
          item: (index: number) => ({ category_id: 1, frequency: 3 }),
          length: 1,
          _array: [{ category_id: 1, frequency: 3 }]
        },
        rowsAffected: 0,
        insertId: 0
      }]);
    });

    it('should suggest categories based on keywords', async () => {
      const result = await transactionService.suggestCategories('food shopping');

      expect(result).toEqual([
        expect.objectContaining({
          categoryId: 1,
          categoryName: 'Food',
          reason: 'keyword_match',
          confidence: expect.any(Number)
        })
      ]);
    });

    it('should return empty array for short descriptions', async () => {
      const result = await transactionService.suggestCategories('a');

      expect(result).toEqual([]);
    });
  });

  describe('getTransactionStats', () => {
    beforeEach(() => {
      const mockExecuteSql = mockDatabaseService.executeSql as jest.Mock;
      mockExecuteSql.mockResolvedValue([{
        rows: {
          item: (index: number) => ({
            total_income: 500,
            total_expense: 300,
            transaction_count: 10
          }),
          length: 1,
          _array: [{
            total_income: 500,
            total_expense: 300,
            transaction_count: 10
          }]
        },
        rowsAffected: 0,
        insertId: 0
      }]);
    });

    it('should return transaction statistics', async () => {
      const result = await transactionService.getTransactionStats(1);

      expect(result).toEqual({
        totalIncome: 500,
        totalExpense: 300,
        netBalance: 200,
        transactionCount: 10
      });
    });
  });
});