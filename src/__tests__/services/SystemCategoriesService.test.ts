import { SystemCategoriesService } from '@/business/services/SystemCategoriesService';
import { CategoryRepository } from '@/data/repositories/CategoryRepository';
import { Category } from '@/shared/types';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock CategoryRepository
jest.mock('@/data/repositories/CategoryRepository');

describe('SystemCategoriesService', () => {
  let systemCategoriesService: SystemCategoriesService;
  let mockCategoryRepository: jest.Mocked<CategoryRepository>;
  let mockAsyncStorage: jest.Mocked<typeof AsyncStorage>;

  const mockSystemCategory: Category = {
    id: 1,
    name: 'Food & Dining',
    parent_id: null,
    category_type: 'expense',
    color_code: '#FF6B35',
    icon_name: 'restaurant',
    is_system: true,
    created_at: '2025-01-01T00:00:00Z',
    sync_status: 'local',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockCategoryRepository = new CategoryRepository() as jest.Mocked<CategoryRepository>;
    mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;
    
    systemCategoriesService = new SystemCategoriesService();
    (systemCategoriesService as any).categoryRepository = mockCategoryRepository;
  });

  describe('isSystemCategoriesInitialized', () => {
    it('should return true when categories are initialized', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('true');

      const result = await systemCategoriesService.isSystemCategoriesInitialized();

      expect(result).toBe(true);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('categories_initialized');
    });

    it('should return false when categories are not initialized', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const result = await systemCategoriesService.isSystemCategoriesInitialized();

      expect(result).toBe(false);
    });

    it('should return false on AsyncStorage error', async () => {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      const result = await systemCategoriesService.isSystemCategoriesInitialized();

      expect(result).toBe(false);
    });
  });

  describe('getSystemCategoriesVersion', () => {
    it('should return version when available', async () => {
      const version = '1.0.0';
      mockAsyncStorage.getItem.mockResolvedValue(version);

      const result = await systemCategoriesService.getSystemCategoriesVersion();

      expect(result).toBe(version);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('system_categories_version');
    });

    it('should return null when no version stored', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const result = await systemCategoriesService.getSystemCategoriesVersion();

      expect(result).toBeNull();
    });

    it('should return null on AsyncStorage error', async () => {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      const result = await systemCategoriesService.getSystemCategoriesVersion();

      expect(result).toBeNull();
    });
  });

  describe('initializeSystemCategories', () => {
    it('should initialize categories when not already initialized', async () => {
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(null) // isInitialized
        .mockResolvedValueOnce(null); // getVersion
      
      mockCategoryRepository.findByType.mockResolvedValue([]);
      mockCategoryRepository.create.mockResolvedValue(mockSystemCategory);
      mockAsyncStorage.setItem.mockResolvedValue();

      await systemCategoriesService.initializeSystemCategories();

      expect(mockCategoryRepository.create).toHaveBeenCalled();
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('categories_initialized', 'true');
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('system_categories_version', '1.0.0');
    });

    it('should skip initialization when already initialized with current version', async () => {
      mockAsyncStorage.getItem
        .mockResolvedValueOnce('true') // isInitialized
        .mockResolvedValueOnce('1.0.0'); // getVersion

      await systemCategoriesService.initializeSystemCategories();

      expect(mockCategoryRepository.create).not.toHaveBeenCalled();
    });

  });

  describe('isProtectedSystemCategory', () => {
    it('should return true for system category', async () => {
      mockCategoryRepository.findById.mockResolvedValue(mockSystemCategory);

      const result = await systemCategoriesService.isProtectedSystemCategory(1);

      expect(result).toBe(true);
      expect(mockCategoryRepository.findById).toHaveBeenCalledWith(1);
    });

    it('should return false for user category', async () => {
      const userCategory = { ...mockSystemCategory, is_system: false };
      mockCategoryRepository.findById.mockResolvedValue(userCategory);

      const result = await systemCategoriesService.isProtectedSystemCategory(1);

      expect(result).toBe(false);
    });

    it('should return false when category not found', async () => {
      mockCategoryRepository.findById.mockResolvedValue(null);

      const result = await systemCategoriesService.isProtectedSystemCategory(999);

      expect(result).toBe(false);
    });

    it('should return false on repository error', async () => {
      mockCategoryRepository.findById.mockRejectedValue(new Error('Database error'));

      const result = await systemCategoriesService.isProtectedSystemCategory(1);

      expect(result).toBe(false);
    });
  });

  describe('validateSystemCategoryDeletion', () => {
    it('should prevent deletion of system category', async () => {
      mockCategoryRepository.findById.mockResolvedValue(mockSystemCategory);

      const result = await systemCategoriesService.validateSystemCategoryDeletion(1);

      expect(result.canDelete).toBe(false);
      expect(result.reason).toContain('System categories cannot be deleted');
    });

    it('should allow deletion of user category', async () => {
      const userCategory = { ...mockSystemCategory, is_system: false };
      mockCategoryRepository.findById.mockResolvedValue(userCategory);

      const result = await systemCategoriesService.validateSystemCategoryDeletion(1);

      expect(result.canDelete).toBe(true);
      expect(result.reason).toBeUndefined();
    });
  });

  describe('archiveSystemCategory', () => {
    it('should archive system category', async () => {
      mockCategoryRepository.findById.mockResolvedValue(mockSystemCategory);
      const archivedCategory = { ...mockSystemCategory, sync_status: 'conflict' as const };
      mockCategoryRepository.update.mockResolvedValue(archivedCategory);

      const result = await systemCategoriesService.archiveSystemCategory(1);

      expect(result.sync_status).toBe('conflict');
      expect(mockCategoryRepository.update).toHaveBeenCalledWith(1, { sync_status: 'conflict' });
    });

    it('should throw error for non-system category', async () => {
      const userCategory = { ...mockSystemCategory, is_system: false };
      mockCategoryRepository.findById.mockResolvedValue(userCategory);

      await expect(systemCategoriesService.archiveSystemCategory(1)).rejects.toThrow(
        'Only system categories can be archived using this method'
      );
    });
  });

  describe('restoreSystemCategory', () => {
    it('should restore archived system category', async () => {
      const archivedCategory = { ...mockSystemCategory, sync_status: 'conflict' as const };
      mockCategoryRepository.findById.mockResolvedValue(archivedCategory);
      mockCategoryRepository.update.mockResolvedValue(mockSystemCategory);

      const result = await systemCategoriesService.restoreSystemCategory(1);

      expect(result.sync_status).toBe('local');
      expect(mockCategoryRepository.update).toHaveBeenCalledWith(1, { sync_status: 'local' });
    });

    it('should throw error when category not found', async () => {
      mockCategoryRepository.findById.mockResolvedValue(null);

      await expect(systemCategoriesService.restoreSystemCategory(1)).rejects.toThrow(
        'Category not found or not a system category'
      );
    });

    it('should throw error for non-system category', async () => {
      const userCategory = { ...mockSystemCategory, is_system: false };
      mockCategoryRepository.findById.mockResolvedValue(userCategory);

      await expect(systemCategoriesService.restoreSystemCategory(1)).rejects.toThrow(
        'Category not found or not a system category'
      );
    });
  });

  describe('getSystemCategoryUsageStats', () => {
    it('should return usage statistics', async () => {
      const systemCategories = [mockSystemCategory];
      mockCategoryRepository.findAll.mockResolvedValue(systemCategories);
      mockCategoryRepository.getCategoryUsageStats.mockResolvedValue({
        usage_count: 5,
        total_amount: 100.50,
        last_used: '2025-01-15'
      });

      const result = await systemCategoriesService.getSystemCategoryUsageStats();

      expect(result.totalSystemCategories).toBe(1);
      expect(result.usedSystemCategories).toBe(1);
      expect(result.mostUsedSystemCategory).toBe('Food & Dining');
    });

    it('should handle categories with zero usage', async () => {
      const systemCategories = [mockSystemCategory];
      mockCategoryRepository.findAll.mockResolvedValue(systemCategories);
      mockCategoryRepository.getCategoryUsageStats.mockResolvedValue({
        usage_count: 0,
        total_amount: 0,
        last_used: null
      });

      const result = await systemCategoriesService.getSystemCategoryUsageStats();

      expect(result.totalSystemCategories).toBe(1);
      expect(result.usedSystemCategories).toBe(0);
      expect(result.mostUsedSystemCategory).toBeNull();
      expect(result.leastUsedSystemCategory).toBe('Food & Dining');
    });
  });

  describe('resetSystemCategories', () => {
    it('should reset system categories', async () => {
      const systemCategories = [mockSystemCategory];
      mockCategoryRepository.findAll.mockResolvedValue(systemCategories);
      mockCategoryRepository.getCategoryUsageStats.mockResolvedValue({
        usage_count: 0,
        total_amount: 0,
        last_used: null
      });
      mockCategoryRepository.delete.mockResolvedValue(true);
      mockAsyncStorage.removeItem.mockResolvedValue();
      
      // Mock the re-initialization
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(null) // isInitialized after reset
        .mockResolvedValueOnce(null); // getVersion after reset
      mockCategoryRepository.findByType.mockResolvedValue([]);
      mockCategoryRepository.create.mockResolvedValue(mockSystemCategory);
      mockAsyncStorage.setItem.mockResolvedValue();

      await systemCategoriesService.resetSystemCategories();

      expect(mockCategoryRepository.delete).toHaveBeenCalledWith(1);
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('categories_initialized');
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('system_categories_version');
    });
  });

  describe('data access methods', () => {
    it('should return system expense categories', () => {
      const expenseCategories = systemCategoriesService.getSystemExpenseCategories();
      expect(expenseCategories.length).toBeGreaterThan(0);
      expect(expenseCategories[0].categoryType).toBe('expense');
    });

    it('should return system income categories', () => {
      const incomeCategories = systemCategoriesService.getSystemIncomeCategories();
      expect(incomeCategories.length).toBeGreaterThan(0);
      expect(incomeCategories[0].categoryType).toBe('income');
    });

    it('should return all system categories', () => {
      const allCategories = systemCategoriesService.getAllSystemCategories();
      expect(allCategories.length).toBeGreaterThan(0);
      
      const expenseCount = allCategories.filter(cat => cat.categoryType === 'expense').length;
      const incomeCount = allCategories.filter(cat => cat.categoryType === 'income').length;
      expect(expenseCount).toBeGreaterThan(0);
      expect(incomeCount).toBeGreaterThan(0);
    });
  });
});