import { CategoryService } from '@/business/services/CategoryService';
// import type { CategoryStats } from '@/business/services/CategoryService';
import { CategoryRepository } from '@/data/repositories/CategoryRepository';
import { TransactionRepository } from '@/data/repositories/TransactionRepository';
import { Category } from '@/shared/types';

// Mock the repositories
jest.mock('@/data/repositories/CategoryRepository');
jest.mock('@/data/repositories/TransactionRepository');

describe('CategoryService', () => {
  let categoryService: CategoryService;
  let mockCategoryRepository: jest.Mocked<CategoryRepository>;
  let mockTransactionRepository: jest.Mocked<TransactionRepository>;

  const mockCategory: Category = {
    id: 1,
    name: 'Food & Dining',
    parent_id: null,
    category_type: 'expense',
    color_code: '#FF6B35',
    icon_name: 'restaurant',
    is_system: true,
    created_at: '2025-01-01T00:00:00Z',
    sync_status: 'local',
  };

  const mockSubcategory: Category = {
    id: 2,
    name: 'Restaurants',
    parent_id: 1,
    category_type: 'expense',
    color_code: '#FF6B35',
    icon_name: 'restaurant',
    is_system: false,
    created_at: '2025-01-01T00:00:00Z',
    sync_status: 'local',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockCategoryRepository = new CategoryRepository() as jest.Mocked<CategoryRepository>;
    mockTransactionRepository = new TransactionRepository() as jest.Mocked<TransactionRepository>;
    
    categoryService = new CategoryService();
    
    // Set up the mocked repositories
    (categoryService as any).categoryRepository = mockCategoryRepository;
    (categoryService as any).transactionRepository = mockTransactionRepository;
  });

  describe('getAllCategories', () => {
    it('should return all categories', async () => {
      const categories = [mockCategory, mockSubcategory];
      mockCategoryRepository.findAll.mockResolvedValue(categories);

      const result = await categoryService.getAllCategories();

      expect(result).toEqual(categories);
      expect(mockCategoryRepository.findAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('getCategoryById', () => {
    it('should return category by id', async () => {
      mockCategoryRepository.findById.mockResolvedValue(mockCategory);

      const result = await categoryService.getCategoryById(1);

      expect(result).toEqual(mockCategory);
      expect(mockCategoryRepository.findById).toHaveBeenCalledWith(1);
    });

    it('should return null for non-existent category', async () => {
      mockCategoryRepository.findById.mockResolvedValue(null);

      const result = await categoryService.getCategoryById(999);

      expect(result).toBeNull();
      expect(mockCategoryRepository.findById).toHaveBeenCalledWith(999);
    });
  });

  describe('createCategory', () => {
    const newCategoryData = {
      name: 'New Category',
      parent_id: null,
      category_type: 'expense' as Category['category_type'],
      color_code: '#123456',
      icon_name: 'test',
      is_system: false,
      sync_status: 'local' as Category['sync_status'],
    };

    it('should create a category successfully', async () => {
      const createdCategory = { id: 3, created_at: '2025-01-01T00:00:00Z', ...newCategoryData };
      
      // Mock validation dependencies
      mockCategoryRepository.findByParent.mockResolvedValue([]);
      mockCategoryRepository.create.mockResolvedValue(createdCategory);

      const result = await categoryService.createCategory(newCategoryData);

      expect(result).toEqual(createdCategory);
      expect(mockCategoryRepository.create).toHaveBeenCalledWith(newCategoryData);
    });

    it('should throw error for invalid category data', async () => {
      const invalidData = {
        ...newCategoryData,
        name: '', // Invalid empty name
      };

      mockCategoryRepository.findByParent.mockResolvedValue([]);

      await expect(categoryService.createCategory(invalidData)).rejects.toThrow(
        'Category validation failed'
      );
    });

    it('should throw error for duplicate category name in same parent', async () => {
      const duplicateData = {
        ...newCategoryData,
        name: 'Existing Category',
      };

      const existingSibling = {
        ...mockCategory,
        name: 'Existing Category',
        parent_id: null,
      };

      mockCategoryRepository.findByParent.mockResolvedValue([existingSibling]);

      await expect(categoryService.createCategory(duplicateData)).rejects.toThrow(
        'Category validation failed'
      );
    });
  });

  describe('updateCategory', () => {
    it('should update category successfully', async () => {
      const updates = { name: 'Updated Name', color_code: '#654321' };
      const updatedCategory = { ...mockCategory, ...updates };
      
      mockCategoryRepository.update.mockResolvedValue(updatedCategory);

      const result = await categoryService.updateCategory(1, updates);

      expect(result).toEqual(updatedCategory);
      expect(mockCategoryRepository.update).toHaveBeenCalledWith(1, updates);
    });

    it('should validate hierarchy when updating parent', async () => {
      const updates = { parent_id: 2 };
      
      // Mock hierarchy validation - for updateCategory with parent_id = 2
      // getCategoryDepth will be called with parentId = 2  
      mockCategoryRepository.findById
        .mockResolvedValueOnce(mockSubcategory) // For getCategoryDepth(2) - category 2 has parent_id = 1
        .mockResolvedValueOnce(mockCategory);   // For getCategoryDepth(1) - category 1 has parent_id = null (depth 2, valid)
      
      // Mock findByParent calls for getCategoryAndDescendantIds during circular reference check
      mockCategoryRepository.findByParent
        .mockResolvedValueOnce([]); // No descendants for category 2
      
      mockCategoryRepository.update.mockResolvedValue({ ...mockCategory, ...updates });

      await categoryService.updateCategory(1, updates);

      expect(mockCategoryRepository.update).toHaveBeenCalledWith(1, updates);
    });
  });

  describe('deleteCategory', () => {
    it('should delete category successfully', async () => {
      const userCategory = { ...mockCategory, is_system: false };
      mockCategoryRepository.findById.mockResolvedValue(userCategory);
      mockCategoryRepository.findByParent.mockResolvedValue([]);
      mockCategoryRepository.delete.mockResolvedValue(true);

      await categoryService.deleteCategory(1);

      expect(mockCategoryRepository.delete).toHaveBeenCalledWith(1);
    });

    it('should throw error when deleting system category', async () => {
      const systemCategory = { ...mockCategory, is_system: true };
      mockCategoryRepository.findById.mockResolvedValue(systemCategory);

      await expect(categoryService.deleteCategory(1)).rejects.toThrow(
        'System categories cannot be deleted'
      );
    });

    it('should throw error when deleting category with children without reassignment', async () => {
      const userCategory = { ...mockCategory, is_system: false };
      mockCategoryRepository.findById.mockResolvedValue(userCategory);
      mockCategoryRepository.findByParent.mockResolvedValue([mockSubcategory]);

      await expect(categoryService.deleteCategory(1)).rejects.toThrow(
        'Cannot delete category with child categories'
      );
    });

    it('should reassign transactions and children when reassignToId provided', async () => {
      const userCategory = { ...mockCategory, is_system: false };
      mockCategoryRepository.findById.mockResolvedValue(userCategory);
      mockCategoryRepository.findByParent.mockResolvedValue([mockSubcategory]);
      mockCategoryRepository.update.mockResolvedValue(mockSubcategory);
      mockCategoryRepository.delete.mockResolvedValue(true);
      
      // Mock transaction reassignment
      (mockTransactionRepository as any)['db'] = {
        executeSql: jest.fn().mockResolvedValue([{ rowsAffected: 2 }]),
      };

      await categoryService.deleteCategory(1, 3);

      expect(mockCategoryRepository.update).toHaveBeenCalledWith(2, { parent_id: 3 });
      expect(mockCategoryRepository.delete).toHaveBeenCalledWith(1);
    });
  });

  describe('mergeCategories', () => {
    it('should merge categories successfully', async () => {
      const sourceIds = [2, 3];
      const targetId = 1;
      
      mockCategoryRepository.findById
        .mockResolvedValueOnce(mockCategory) // Target category
        .mockResolvedValueOnce(mockSubcategory) // Source category 1
        .mockResolvedValueOnce({ ...mockSubcategory, id: 3 }); // Source category 2
      
      mockCategoryRepository.findByParent.mockResolvedValue([]);
      mockCategoryRepository.delete.mockResolvedValue(true);
      
      // Mock transaction reassignment
      (mockTransactionRepository as any)['db'] = {
        executeSql: jest.fn().mockResolvedValue([{ rowsAffected: 1 }]),
      };

      await categoryService.mergeCategories(sourceIds, targetId);

      expect(mockCategoryRepository.delete).toHaveBeenCalledTimes(2);
      expect(mockCategoryRepository.delete).toHaveBeenCalledWith(2);
      expect(mockCategoryRepository.delete).toHaveBeenCalledWith(3);
    });

    it('should throw error for non-existent target category', async () => {
      mockCategoryRepository.findById.mockResolvedValue(null);

      await expect(categoryService.mergeCategories([2], 999)).rejects.toThrow(
        'Target category with id 999 not found'
      );
    });
  });

  describe('getCategoryStats', () => {
    it('should return category statistics', async () => {
      mockCategoryRepository.findById.mockResolvedValue(mockCategory);
      mockCategoryRepository.findByParent.mockResolvedValue([]);
      
      // Mock database query results
      (mockTransactionRepository as any)['db'] = {
        executeSql: jest.fn()
          .mockResolvedValueOnce([{ // Stats query
            rows: {
              item: jest.fn().mockReturnValue({
                totalTransactions: 5,
                totalAmount: 250.50,
                averageAmount: 50.10,
                lastTransaction: '2025-01-15',
              }),
            },
          }])
          .mockResolvedValueOnce([{ // Monthly trend query
            rows: {
              length: 2,
              item: jest.fn()
                .mockReturnValueOnce({ month: '2024-12', monthlyAmount: 100 })
                .mockReturnValueOnce({ month: '2025-01', monthlyAmount: 150.50 }),
            },
          }]),
      };

      const result = await categoryService.getCategoryStats(1);

      expect(result).toEqual({
        totalTransactions: 5,
        totalAmount: 250.50,
        averageAmount: 50.10,
        monthlyTrend: [100, 150.50],
        lastTransaction: new Date('2025-01-15'),
      });
    });

    it('should throw error for non-existent category', async () => {
      mockCategoryRepository.findById.mockResolvedValue(null);

      await expect(categoryService.getCategoryStats(999)).rejects.toThrow(
        'Category with id 999 not found'
      );
    });
  });

  describe('searchCategories', () => {
    it('should search categories by name', async () => {
      const categories = [
        mockCategory,
        { ...mockCategory, id: 2, name: 'Transportation' },
        { ...mockCategory, id: 3, name: 'Food Shopping' },
      ];
      
      mockCategoryRepository.findAll.mockResolvedValue(categories);

      const result = await categoryService.searchCategories('food');

      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Food & Dining');
      expect(result[1].name).toBe('Food Shopping');
    });

    it('should return empty array for no matches', async () => {
      mockCategoryRepository.findAll.mockResolvedValue([mockCategory]);

      const result = await categoryService.searchCategories('nonexistent');

      expect(result).toHaveLength(0);
    });
  });

  describe('validation methods', () => {
    describe('validateCategoryData', () => {
      it('should pass validation for valid data', async () => {
        const validData = {
          name: 'Valid Category',
          parent_id: null,
          category_type: 'expense' as Category['category_type'],
          color_code: '#123456',
          icon_name: 'test',
          is_system: false,
          sync_status: 'local' as Category['sync_status'],
        };

        mockCategoryRepository.findByParent.mockResolvedValue([]);

        const result = await (categoryService as any).validateCategoryData(validData);

        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should fail validation for invalid data', async () => {
        const invalidData = {
          name: '', // Empty name
          parent_id: null,
          category_type: 'invalid' as any, // Invalid type
          color_code: 'invalid-color', // Invalid color format
          icon_name: 'test',
          is_system: false,
          sync_status: 'local' as Category['sync_status'],
        };

        mockCategoryRepository.findByParent.mockResolvedValue([]);

        const result = await (categoryService as any).validateCategoryData(invalidData);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Category name is required');
        expect(result.errors).toContain('Category type must be either income or expense');
        expect(result.errors).toContain('Color code must be a valid hex color');
      });
    });
  });

  describe('initializeSystemCategories', () => {
    it('should initialize system categories', async () => {
      mockCategoryRepository.initializeSystemCategories.mockResolvedValue();

      await categoryService.initializeSystemCategories();

      expect(mockCategoryRepository.initializeSystemCategories).toHaveBeenCalledTimes(1);
    });
  });
});