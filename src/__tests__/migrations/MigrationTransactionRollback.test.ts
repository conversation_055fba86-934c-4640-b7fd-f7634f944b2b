/**
 * Transaction Rollback Test Suite for Migration System
 * 
 * Critical test to verify that when migrations fail mid-execution,
 * the database remains in a consistent state with no partial migrations applied.
 * 
 * This tests the atomic nature of our migration system.
 */

import { MigrationManager } from '../../data/migrations/MigrationManager';
import { DatabaseService } from '../../data/database/DatabaseService';

// Mock the database service
jest.mock('../../data/database/DatabaseService');

describe('Migration System - Transaction Rollback', () => {
  let migrationManager: MigrationManager;
  let mockDatabaseService: jest.Mocked<DatabaseService>;
  
  beforeEach(() => {
    // Reset singleton instance before each test
    (MigrationManager as any).instance = null;
    
    // Create mock database service
    mockDatabaseService = {
      executeSql: jest.fn(),
      transaction: jest.fn(),
      isReady: true,
      initialize: jest.fn(),
      healthCheck: jest.fn(),
      close: jest.fn()
    } as any;
    
    // Mock database service getInstance to return our mock
    (DatabaseService.getInstance as jest.Mock).mockReturnValue(mockDatabaseService);
  });

  afterEach(() => {
    // Clean up singleton
    if (migrationManager) {
      migrationManager.destroy();
    }
  });

  describe('Transaction Safety', () => {
    test('should rollback all changes when migration fails mid-execution', async () => {
      const executedOperations: string[] = [];
      // let transactionStarted = false; // Unused variable removed
      let rollbackCalled = false;
      
      // Mock version check to show database at version 0
      mockDatabaseService.executeSql.mockImplementation((sql: string, params?: any[]) => {
        if (sql.includes('PRAGMA user_version')) {
          return Promise.resolve([{ 
            rows: { 
              _array: [{ user_version: 0 }], 
              length: 1,
              item: jest.fn((index: number) => ({ user_version: 0 }))
            } 
          }] as any);
        }
        
        // Track transaction commands
        if (sql === 'BEGIN TRANSACTION') {
          executedOperations.push('BEGIN_TRANSACTION');
          return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
        }
        
        if (sql === 'ROLLBACK') {
          rollbackCalled = true;
          executedOperations.push('ROLLBACK');
          return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
        }
        
        if (sql === 'COMMIT') {
          executedOperations.push('COMMIT');
          return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
        }
        
        // Simulate failure on specific migration SQL
        if (sql.includes('CREATE TABLE budget_categories')) {
          executedOperations.push('MIGRATION_5_ATTEMPTED');
          // This should trigger the error handling in executeSingleMigrationSafely
          throw new Error('SQLITE_CONSTRAINT: table budget_categories already exists');
        }
        
        // Track other migration operations
        if (sql.includes('CREATE TABLE')) {
          if (sql.includes('accounts')) executedOperations.push('CREATE_ACCOUNTS');
          if (sql.includes('categories')) executedOperations.push('CREATE_CATEGORIES');
          if (sql.includes('transactions')) executedOperations.push('CREATE_TRANSACTIONS');
          if (sql.includes('budgets') && !sql.includes('budget_categories')) executedOperations.push('CREATE_BUDGETS');
        }
        
        // Set version operations
        if (sql.includes('PRAGMA user_version =')) {
          const version = params?.[0] || sql.match(/= (\d+)/)?.[1];
          executedOperations.push(`SET_VERSION_${version}`);
        }
        
        return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
      });
      
      migrationManager = MigrationManager.getInstance(mockDatabaseService);
      
      // Attempt to initialize - should fail on migration 5 but rollback properly
      await expect(migrationManager.initialize()).rejects.toThrow();
      
      // Verify transaction safety
      expect(executedOperations).toContain('BEGIN_TRANSACTION');
      expect(executedOperations).toContain('MIGRATION_5_ATTEMPTED');
      expect(executedOperations).toContain('ROLLBACK');
      expect(rollbackCalled).toBe(true);
      
      // Should not contain COMMIT since migration failed
      expect(executedOperations).not.toContain('COMMIT');
      
      // Database should remain at version 0 (no version update should have occurred)
      expect(executedOperations.filter(op => op.startsWith('SET_VERSION_')).length).toBe(0);
    });

    test('should handle SQL constraint violations gracefully', async () => {
      let constraintViolationDetected = false;
      let rollbackExecuted = false;
      
      // Mock version at 0
      mockDatabaseService.executeSql.mockImplementation((sql: string) => {
        if (sql.includes('PRAGMA user_version')) {
          return Promise.resolve([{ 
            rows: { 
              _array: [{ user_version: 0 }], 
              length: 1,
              item: jest.fn((index: number) => ({ user_version: 0 }))
            } 
          }] as any);
        }
        
        // Handle transaction commands
        if (sql === 'BEGIN TRANSACTION') {
          return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
        }
        
        if (sql === 'ROLLBACK') {
          rollbackExecuted = true;
          return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
        }
        
        // Simulate constraint violation on first migration
        if (sql.includes('CREATE TABLE accounts')) {
          constraintViolationDetected = true;
          throw new Error('SQLITE_ERROR: table accounts already exists');
        }
        
        return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
      });
      
      migrationManager = MigrationManager.getInstance(mockDatabaseService);
      
      // Should fail gracefully with constraint violation
      await expect(migrationManager.initialize()).rejects.toThrow();
      
      // Verify constraint violation was detected and rollback executed
      expect(constraintViolationDetected).toBe(true);
      expect(rollbackExecuted).toBe(true);
    });

    test('should handle migration timeout scenarios', async () => {
      let timeoutTriggered = false;
      
      // Mock version at 0
      mockDatabaseService.executeSql.mockImplementation((sql: string) => {
        if (sql.includes('PRAGMA user_version')) {
          return Promise.resolve([{ 
            rows: { 
              _array: [{ user_version: 0 }], 
              length: 1,
              item: jest.fn((index: number) => ({ user_version: 0 }))
            } 
          }] as any);
        }
        
        if (sql === 'BEGIN TRANSACTION' || sql === 'ROLLBACK') {
          return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
        }
        
        // Simulate timeout on first migration by taking too long
        if (sql.includes('CREATE TABLE accounts')) {
          timeoutTriggered = true;
          // Return a promise that never resolves to simulate timeout
          return new Promise(() => {}); // This will be caught by the timeout mechanism
        }
        
        return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
      });
      
      migrationManager = MigrationManager.getInstance(mockDatabaseService, { 
        migrationTimeout: 100 // Very short timeout for testing
      });
      
      // Should handle timeout gracefully
      await expect(migrationManager.initialize()).rejects.toThrow();
      
      expect(timeoutTriggered).toBe(true);
    });
  });

  describe('Recovery After Failed Migration', () => {
    test('should allow retry after failed migration', async () => {
      let attemptCount = 0;
      
      // Mock version at 0
      mockDatabaseService.executeSql.mockImplementation((sql: string) => {
        if (sql.includes('PRAGMA user_version')) {
          return Promise.resolve([{ 
            rows: { 
              _array: [{ user_version: 0 }], 
              length: 1,
              item: jest.fn((index: number) => ({ user_version: 0 }))
            } 
          }] as any);
        }
        return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
      });
      
      // Mock transaction that fails first time, succeeds second time
      mockDatabaseService.transaction.mockImplementation(async (callback) => {
        attemptCount++;
        
        const mockTx = {
          executeSql: jest.fn().mockImplementation((sql: string, params: any[], success: any, error: any) => {
            if (attemptCount === 1 && sql.includes('CREATE TABLE accounts')) {
              // First attempt fails
              const retryableError = new Error('SQLITE_BUSY: database is locked');
              if (error) {
                error(mockTx, retryableError);
              } else {
                throw retryableError;
              }
            } else {
              // Second attempt or other operations succeed
              if (success) success(mockTx, { rows: { _array: [], length: 0 } });
            }
          })
        };
        
        if (attemptCount === 1) {
          try {
            await callback(mockTx);
          } catch (err) {
            throw err; // First attempt fails
          }
        } else {
          // Second attempt succeeds
          await callback(mockTx);
        }
      });
      
      migrationManager = MigrationManager.getInstance(mockDatabaseService);
      
      // First attempt should fail
      await expect(migrationManager.initialize()).rejects.toThrow('SQLITE_BUSY');
      expect(attemptCount).toBe(1);
      
      // Create new instance for retry (simulates app restart)
      migrationManager.destroy();
      (MigrationManager as any).instance = null;
      migrationManager = MigrationManager.getInstance(mockDatabaseService);
      
      // Second attempt should succeed
      await expect(migrationManager.initialize()).resolves.toBeDefined();
      expect(attemptCount).toBe(2);
    });

    test('should maintain database consistency after multiple failures', async () => {
      const failureScenarios = [
        'SQLITE_CONSTRAINT',
        'SQLITE_BUSY', 
        'SQLITE_CORRUPT',
        'TIMEOUT_ERROR'
      ];
      
      for (let i = 0; i < failureScenarios.length; i++) {
        const scenario = failureScenarios[i];
        
        // Reset for each scenario
        if (migrationManager) {
          migrationManager.destroy();
        }
        (MigrationManager as any).instance = null;
        
        // Mock version at 0
        mockDatabaseService.executeSql.mockImplementation((sql: string) => {
          if (sql.includes('PRAGMA user_version')) {
            return Promise.resolve([{ 
              rows: { 
                _array: [{ user_version: 0 }], 
                length: 1,
                item: jest.fn((index: number) => ({ user_version: 0 }))
              } 
            }] as any);
          }
          return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
        });
        
        // Mock transaction with specific failure
        mockDatabaseService.transaction.mockImplementation(async (callback) => {
          throw new Error(scenario + ': Simulated database failure');
        });
        
        migrationManager = MigrationManager.getInstance(mockDatabaseService);
        
        // Should fail with expected error
        await expect(migrationManager.initialize()).rejects.toThrow(scenario);
        
        // Database should remain at version 0
        const status = await migrationManager.getSystemStatus();
        expect(status.currentVersion).toBe(0);
        expect(status.pendingCount).toBe(15); // All migrations still pending
        
        // System should report as unhealthy but recoverable
        // (Note: exact health check logic depends on implementation)
      }
    });
  });

  describe('Partial Migration State Detection', () => {
    test('should detect and handle partial migration states correctly', async () => {
      // Mock a scenario where database version is updated but not all operations completed
      let versionUpdateCalled = false;
      
      mockDatabaseService.executeSql.mockImplementation((sql: string) => {
        if (sql.includes('PRAGMA user_version')) {
          // Return version 3 to simulate partial state
          return Promise.resolve([{ 
            rows: { 
              _array: [{ user_version: 3 }], 
              length: 1,
              item: jest.fn((index: number) => ({ user_version: 3 }))
            } 
          }] as any);
        }
        return Promise.resolve([{ rows: { _array: [], length: 0 } }] as any);
      });
      
      // Mock transaction that tracks version updates
      mockDatabaseService.transaction.mockImplementation(async (callback) => {
        const mockTx = {
          executeSql: jest.fn().mockImplementation((sql: string, params: any[], success: any) => {
            if (sql.includes('PRAGMA user_version')) {
              versionUpdateCalled = true;
            }
            if (success) success(mockTx, { rows: { _array: [], length: 0 } });
          })
        };
        
        await callback(mockTx);
      });
      
      migrationManager = MigrationManager.getInstance(mockDatabaseService);
      
      // Get pending migrations - should only be versions 4-15
      const pendingMigrations = await migrationManager.getPendingMigrations();
      expect(pendingMigrations).toHaveLength(12); // Versions 4-15
      expect(pendingMigrations.map(m => m.version)).toEqual([4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]);
      
      // Initialize should continue from version 3
      await migrationManager.initialize();
      
      // Should have updated version
      expect(versionUpdateCalled).toBe(true);
    });
  });
});