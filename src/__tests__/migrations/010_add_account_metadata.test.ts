import knex, { Knex } from 'knex';

describe('Migration 010: Add Account Metadata', () => {
  let db: Knex;
  const migration = require('../../data/database/migrations/010_add_account_metadata.js');

  beforeEach(async () => {
    // Create in-memory SQLite database
    db = knex({
      client: 'sqlite3',
      connection: {
        filename: ':memory:'
      },
      useNullAsDefault: true
    });

    // Create accounts table first (dependency)
    await db.schema.createTable('accounts', function(table) {
      table.increments('id').primary();
      table.string('name').notNullable();
      table.enum('type', ['checking', 'savings', 'credit', 'loan', 'investment']).notNullable();
      table.decimal('balance', 15, 2).defaultTo(0.00);
      table.string('currency').defaultTo('INR');
      table.boolean('is_active').defaultTo(true);
      table.timestamp('created_at').defaultTo(db.fn.now());
      table.timestamp('updated_at').defaultTo(db.fn.now());
      table.enum('sync_status', ['local', 'synced', 'pending', 'conflict']).defaultTo('local');
    });

    // Insert test accounts
    await db('accounts').insert([
      { id: 1, name: 'Test Credit Card', type: 'credit', balance: -5000 },
      { id: 2, name: 'Test Loan', type: 'loan', balance: -250000 },
      { id: 3, name: 'Test Checking', type: 'checking', balance: 10000 }
    ]);
  });

  afterEach(async () => {
    await db.destroy();
  });

  describe('Migration Up', () => {
    it('should create account_metadata table with correct structure', async () => {
      await migration.up(db);

      const hasTable = await db.schema.hasTable('account_metadata');
      expect(hasTable).toBe(true);

      // Check table structure
      const columnInfo = await db('account_metadata').columnInfo();
      
      // Verify key columns exist
      expect(columnInfo.id).toBeDefined();
      expect(columnInfo.account_id).toBeDefined();
      expect(columnInfo.credit_limit).toBeDefined();
      expect(columnInfo.outstanding_balance).toBeDefined();
      expect(columnInfo.principal_amount).toBeDefined();
      expect(columnInfo.emi_amount).toBeDefined();
      expect(columnInfo.interest_rate).toBeDefined();
      expect(columnInfo.created_at).toBeDefined();
      expect(columnInfo.updated_at).toBeDefined();
    });

    it('should create proper indexes', async () => {
      await migration.up(db);

      // SQLite doesn't have a direct way to check indexes, but we can test that they work
      // by inserting data and querying
      await db('account_metadata').insert({
        account_id: 1,
        credit_limit: 50000,
        outstanding_balance: 15000,
        payment_due_date: '2024-02-15T00:00:00.000Z'
      });

      // Query using indexed column should work efficiently
      const result = await db('account_metadata').where('account_id', 1);
      expect(result).toHaveLength(1);
      expect(result[0].credit_limit).toBe(50000);
    });

    it('should enforce foreign key constraint', async () => {
      await migration.up(db);

      // Should be able to insert with valid account_id
      await expect(db('account_metadata').insert({
        account_id: 1,
        credit_limit: 50000
      })).resolves.toBeDefined();

      // Should fail with invalid account_id (if foreign key constraints are enabled)
      try {
        await db('account_metadata').insert({
          account_id: 999, // Non-existent account
          credit_limit: 50000
        });
        // If we reach here, foreign keys might not be enforced in test env
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    it('should enforce unique constraint on account_id', async () => {
      await migration.up(db);

      // First insert should succeed
      await db('account_metadata').insert({
        account_id: 1,
        credit_limit: 50000
      });

      // Second insert with same account_id should fail
      await expect(db('account_metadata').insert({
        account_id: 1,
        credit_limit: 60000
      })).rejects.toThrow();
    });

    it('should allow nullable metadata fields', async () => {
      await migration.up(db);

      // Should be able to insert with minimal required fields
      await expect(db('account_metadata').insert({
        account_id: 1
      })).resolves.toBeDefined();

      const result = await db('account_metadata').where('account_id', 1);
      expect(result).toHaveLength(1);
      expect(result[0].credit_limit).toBeNull();
      expect(result[0].emi_amount).toBeNull();
    });

    it('should set default values correctly', async () => {
      await migration.up(db);

      await db('account_metadata').insert({
        account_id: 1,
        credit_limit: 50000
      });

      const result = await db('account_metadata').where('account_id', 1).first();
      expect(result.auto_calculate).toBe(1); // SQLite stores boolean as 1/0
      expect(result.created_at).toBeDefined();
      expect(result.updated_at).toBeDefined();
    });
  });

  describe('Migration Down', () => {
    it('should drop account_metadata table', async () => {
      // Run up migration first
      await migration.up(db);
      expect(await db.schema.hasTable('account_metadata')).toBe(true);

      // Run down migration
      await migration.down(db);
      expect(await db.schema.hasTable('account_metadata')).toBe(false);
    });

    it('should not affect existing accounts table', async () => {
      await migration.up(db);
      await migration.down(db);

      // Accounts table should still exist
      expect(await db.schema.hasTable('accounts')).toBe(true);
      
      // Data should still be there
      const accounts = await db('accounts');
      expect(accounts).toHaveLength(3);
    });
  });

  describe('Data Integrity', () => {
    it('should handle various data types correctly', async () => {
      await migration.up(db);

      const testData = {
        account_id: 1,
        credit_limit: 50000.50,
        outstanding_balance: 15000.75,
        interest_rate: 8.25,
        tenure: 60,
        payment_due_date: '2024-02-15T00:00:00.000Z',
        auto_calculate: false
      };

      await db('account_metadata').insert(testData);
      const result = await db('account_metadata').where('account_id', 1).first();

      expect(parseFloat(result.credit_limit)).toBe(50000.50);
      expect(parseFloat(result.outstanding_balance)).toBe(15000.75);
      expect(parseFloat(result.interest_rate)).toBe(8.25);
      expect(result.tenure).toBe(60);
      expect(result.payment_due_date).toBe('2024-02-15T00:00:00.000Z');
      expect(result.auto_calculate).toBe(0); // false = 0 in SQLite
    });
  });
});