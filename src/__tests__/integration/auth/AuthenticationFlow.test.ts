import { AuthenticationManager } from '../../../platform/biometric/AuthenticationManager';
import { BiometricAuthService } from '../../../platform/biometric/BiometricAuthService';
import { KeychainService } from '../../../platform/biometric/KeychainService';

// Mock dependencies
jest.mock('../../../platform/biometric/BiometricAuthService');
jest.mock('../../../platform/biometric/KeychainService');

const mockBiometricAuth = BiometricAuthService as jest.Mocked<typeof BiometricAuthService>;
const mockKeychain = KeychainService as jest.Mocked<typeof KeychainService>;

describe('Authentication Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Biometric Authentication Flow', () => {
    it('should complete full biometric authentication successfully', async () => {
      // Setup: Biometric available and working
      mockBiometricAuth.detectCapabilities.mockResolvedValue({
        isAvailable: true,
        supportedTypes: [1], // Fingerprint
        hasHardware: true,
        isEnrolled: true,
      });

      mockBiometricAuth.authenticate.mockResolvedValue({
        success: true,
      });

      // Test authentication
      const result = await AuthenticationManager.authenticate('biometric');

      expect(result.success).toBe(true);
      expect(mockBiometricAuth.detectCapabilities).toHaveBeenCalled();
      expect(mockBiometricAuth.authenticate).toHaveBeenCalledWith('Unlock FinVibe');
    });

    it('should handle biometric setup and authentication flow', async () => {
      // Setup biometric
      mockBiometricAuth.detectCapabilities.mockResolvedValue({
        isAvailable: true,
        supportedTypes: [1],
        hasHardware: true,
        isEnrolled: true,
      });

      const setupResult = await AuthenticationManager.setupAuthMethod('biometric');
      expect(setupResult.success).toBe(true);

      // Authenticate
      mockBiometricAuth.authenticate.mockResolvedValue({ success: true });
      const authResult = await AuthenticationManager.authenticate('biometric');
      expect(authResult.success).toBe(true);
    });

    it('should fallback gracefully when biometric fails', async () => {
      mockBiometricAuth.detectCapabilities.mockResolvedValue({
        isAvailable: false,
        supportedTypes: [],
        hasHardware: false,
        isEnrolled: false,
      });

      const result = await AuthenticationManager.authenticate('biometric');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Biometric authentication not available');
    });
  });

  describe('PIN Authentication Flow', () => {
    it('should complete full PIN setup and authentication', async () => {
      // Setup PIN
      mockKeychain.storePIN.mockResolvedValue(true);
      const setupResult = await AuthenticationManager.setupAuthMethod('pin', '1234');
      expect(setupResult.success).toBe(true);
      expect(mockKeychain.storePIN).toHaveBeenCalledWith('1234');

      // Authenticate with correct PIN
      mockKeychain.verifyPIN.mockResolvedValue(true);
      const authResult = await AuthenticationManager.authenticate('pin', '1234');
      expect(authResult.success).toBe(true);
      expect(mockKeychain.verifyPIN).toHaveBeenCalledWith('1234');
    });

    it('should reject incorrect PIN', async () => {
      // Setup PIN
      mockKeychain.storePIN.mockResolvedValue(true);
      await AuthenticationManager.setupAuthMethod('pin', '1234');

      // Try with wrong PIN
      mockKeychain.verifyPIN.mockResolvedValue(false);
      const authResult = await AuthenticationManager.authenticate('pin', '5678');
      
      expect(authResult.success).toBe(false);
      expect(authResult.error).toBe('Incorrect PIN');
    });

    it('should validate PIN format during setup', async () => {
      const result = await AuthenticationManager.setupAuthMethod('pin', '12'); // Too short
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('PIN must be at least 4 digits');
    });
  });

  describe('Pattern Authentication Flow', () => {
    it('should complete full pattern setup and authentication', async () => {
      const testPattern = [1, 2, 5, 4];

      // Setup pattern
      mockKeychain.storePattern.mockResolvedValue(true);
      const setupResult = await AuthenticationManager.setupAuthMethod('pattern', testPattern);
      expect(setupResult.success).toBe(true);
      expect(mockKeychain.storePattern).toHaveBeenCalledWith(testPattern);

      // Authenticate with correct pattern
      mockKeychain.verifyPattern.mockResolvedValue(true);
      const authResult = await AuthenticationManager.authenticate('pattern', testPattern);
      expect(authResult.success).toBe(true);
      expect(mockKeychain.verifyPattern).toHaveBeenCalledWith(testPattern);
    });

    it('should reject incorrect pattern', async () => {
      const correctPattern = [1, 2, 5, 4];
      const wrongPattern = [1, 3, 6, 9];

      // Setup pattern
      mockKeychain.storePattern.mockResolvedValue(true);
      await AuthenticationManager.setupAuthMethod('pattern', correctPattern);

      // Try with wrong pattern
      mockKeychain.verifyPattern.mockResolvedValue(false);
      const authResult = await AuthenticationManager.authenticate('pattern', wrongPattern);
      
      expect(authResult.success).toBe(false);
      expect(authResult.error).toBe('Incorrect pattern');
    });

    it('should validate pattern length during setup', async () => {
      const result = await AuthenticationManager.setupAuthMethod('pattern', [1, 2]); // Too short
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Pattern must connect at least 4 dots');
    });
  });

  describe('Failed Attempts and Lockout', () => {
    beforeEach(() => {
      // Mock the getAuthenticationState to return a clean state
      jest.spyOn(AuthenticationManager, 'getAuthenticationState').mockResolvedValue({
        isAuthenticated: false,
        authMethod: 'pin',
        lastAuthTime: 0,
        failedAttempts: 0,
        lockoutUntil: null,
      });
    });

    it('should track failed attempts correctly', async () => {
      mockKeychain.verifyPIN.mockResolvedValue(false);

      // First failed attempt
      const result1 = await AuthenticationManager.authenticate('pin', 'wrong');
      expect(result1.success).toBe(false);

      // Second failed attempt  
      const result2 = await AuthenticationManager.authenticate('pin', 'wrong');
      expect(result2.success).toBe(false);

      // Third failed attempt should trigger lockout warning
      const result3 = await AuthenticationManager.authenticate('pin', 'wrong');
      expect(result3.success).toBe(false);
    });

    it('should implement progressive lockout delays', async () => {
      // Mock state with 3 failed attempts (should trigger 30-second lockout)
      jest.spyOn(AuthenticationManager, 'getAuthenticationState').mockResolvedValue({
        isAuthenticated: false,
        authMethod: 'pin',
        lastAuthTime: 0,
        failedAttempts: 3,
        lockoutUntil: Date.now() + 30000, // 30 seconds from now
      });

      const result = await AuthenticationManager.authenticate('pin', '1234');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Too many failed attempts');
    });

    it('should allow authentication after lockout expires', async () => {
      // Mock state with expired lockout
      jest.spyOn(AuthenticationManager, 'getAuthenticationState').mockResolvedValue({
        isAuthenticated: false,
        authMethod: 'pin',
        lastAuthTime: 0,
        failedAttempts: 3,
        lockoutUntil: Date.now() - 1000, // 1 second ago (expired)
      });

      mockKeychain.verifyPIN.mockResolvedValue(true);
      const result = await AuthenticationManager.authenticate('pin', '1234');
      
      expect(result.success).toBe(true);
    });

    it('should reset failed attempts after successful authentication', async () => {
      // Mock successful authentication
      mockKeychain.verifyPIN.mockResolvedValue(true);
      const result = await AuthenticationManager.authenticate('pin', '1234');
      
      expect(result.success).toBe(true);
      // Verify that handleSuccessfulAuth would be called (which resets attempts)
    });
  });

  describe('Method Switching', () => {
    it('should allow switching from PIN to biometric', async () => {
      // Setup PIN first
      mockKeychain.storePIN.mockResolvedValue(true);
      await AuthenticationManager.setupAuthMethod('pin', '1234');

      // Switch to biometric
      mockBiometricAuth.detectCapabilities.mockResolvedValue({
        isAvailable: true,
        supportedTypes: [1],
        hasHardware: true,
        isEnrolled: true,
      });

      const result = await AuthenticationManager.setupAuthMethod('biometric');
      expect(result.success).toBe(true);
    });

    it('should allow switching from biometric to PIN when biometric becomes unavailable', async () => {
      // Setup biometric first
      mockBiometricAuth.detectCapabilities.mockResolvedValue({
        isAvailable: true,
        supportedTypes: [1],
        hasHardware: true,
        isEnrolled: true,
      });
      await AuthenticationManager.setupAuthMethod('biometric');

      // Switch to PIN
      mockKeychain.storePIN.mockResolvedValue(true);
      const result = await AuthenticationManager.setupAuthMethod('pin', '1234');
      expect(result.success).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle keychain service errors gracefully', async () => {
      mockKeychain.storePIN.mockRejectedValue(new Error('Keychain error'));

      const result = await AuthenticationManager.setupAuthMethod('pin', '1234');
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to setup authentication method');
    });

    it('should handle biometric service errors gracefully', async () => {
      mockBiometricAuth.detectCapabilities.mockRejectedValue(new Error('Biometric error'));

      const result = await AuthenticationManager.setupAuthMethod('biometric');
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to setup authentication method');
    });

    it('should handle authentication system errors', async () => {
      mockKeychain.verifyPIN.mockRejectedValue(new Error('System error'));

      const result = await AuthenticationManager.authenticate('pin', '1234');
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Authentication system error');
    });
  });

  describe('Security Edge Cases', () => {
    it('should reject empty credentials', async () => {
      const pinResult = await AuthenticationManager.authenticate('pin', '');
      expect(pinResult.success).toBe(false);
      expect(pinResult.error).toBe('Invalid PIN format');

      const patternResult = await AuthenticationManager.authenticate('pattern', []);
      expect(patternResult.success).toBe(false);
      expect(patternResult.error).toBe('Pattern too short');
    });

    it('should handle concurrent authentication attempts', async () => {
      mockKeychain.verifyPIN.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(true), 100))
      );

      // Start multiple authentication attempts simultaneously
      const promises = [
        AuthenticationManager.authenticate('pin', '1234'),
        AuthenticationManager.authenticate('pin', '1234'),
        AuthenticationManager.authenticate('pin', '1234'),
      ];

      const results = await Promise.all(promises);
      
      // All should complete without interference
      results.forEach(result => expect(result.success).toBe(true));
    });

    it('should handle rapid successive failed attempts', async () => {
      mockKeychain.verifyPIN.mockResolvedValue(false);

      // Rapid fire failed attempts
      const promises = Array(5).fill(null).map(() => 
        AuthenticationManager.authenticate('pin', 'wrong')
      );

      const results = await Promise.all(promises);
      
      // All should fail, and lockout should be triggered
      results.forEach(result => expect(result.success).toBe(false));
    });
  });
});