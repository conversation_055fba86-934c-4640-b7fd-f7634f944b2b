/**
 * Feedback Service Tests
 * Story 1.9: Anonymous Feedback and Feature Requests
 */

import { FeedbackService } from '../../business/services/FeedbackService';
import { DeviceFingerprintService } from '../../shared/utils/deviceFingerprint';
import { FeedbackFormData } from '../../shared/types/feedback';

// Mock dependencies
jest.mock('../../data/database/DatabaseService');
jest.mock('../../shared/utils/deviceFingerprint');

describe('FeedbackService', () => {
  let feedbackService: FeedbackService;
  let mockDeviceService: jest.Mocked<DeviceFingerprintService>;

  beforeEach(() => {
    feedbackService = FeedbackService.getInstance();
    mockDeviceService = DeviceFingerprintService.getInstance() as jest.Mocked<DeviceFingerprintService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('submitFeedback', () => {
    const validFeedbackData: FeedbackFormData = {
      category: 'bug_report',
      title: 'Test Bug Report',
      description: 'This is a test bug report description.',
      priorityLevel: 'medium',
      rewardEligible: false,
      contactInfo: undefined,
      screenshotPath: undefined
    };

    it('should validate required fields', async () => {
      const invalidFeedback = { ...validFeedbackData, title: '' };
      
      await expect(feedbackService.submitFeedback(invalidFeedback))
        .rejects.toThrow('Validation failed');
    });

    it('should require contact info when reward eligible', async () => {
      const rewardEligibleFeedback = { 
        ...validFeedbackData, 
        rewardEligible: true,
        contactInfo: undefined
      };
      
      await expect(feedbackService.submitFeedback(rewardEligibleFeedback))
        .rejects.toThrow('Contact info required for reward eligibility');
    });

    it('should validate email format for contact info', async () => {
      const invalidEmailFeedback = { 
        ...validFeedbackData, 
        rewardEligible: true,
        contactInfo: 'invalid-email'
      };
      
      await expect(feedbackService.submitFeedback(invalidEmailFeedback))
        .rejects.toThrow('Valid email address required');
    });

    it('should validate title length', async () => {
      const longTitleFeedback = { 
        ...validFeedbackData, 
        title: 'A'.repeat(101) // Too long
      };
      
      await expect(feedbackService.submitFeedback(longTitleFeedback))
        .rejects.toThrow('Title must be less than 100 characters');
    });

    it('should validate description length', async () => {
      const longDescFeedback = { 
        ...validFeedbackData, 
        description: 'A'.repeat(2001) // Too long
      };
      
      await expect(feedbackService.submitFeedback(longDescFeedback))
        .rejects.toThrow('Description must be less than 2000 characters');
    });
  });

  describe('device ID handling', () => {
    it('should get device ID from fingerprint service', async () => {
      mockDeviceService.getDeviceId.mockResolvedValue('test-device-id');
      
      const deviceId = await mockDeviceService.getDeviceId();
      expect(deviceId).toBe('test-device-id');
      expect(mockDeviceService.getDeviceId).toHaveBeenCalledTimes(1);
    });
  });

  describe('voting system', () => {
    const voteData = {
      featureRequestId: 'feature-123',
      voteValue: 1 as const
    };

    it('should allow voting on feature requests', async () => {
      mockDeviceService.getDeviceId.mockResolvedValue('test-device-id');
      
      // Mock that no existing vote exists
      jest.spyOn(feedbackService, 'getUserVote').mockResolvedValue(null);
      
      // This would normally interact with the database
      // For now, we just test the service exists and can be called
      expect(feedbackService.submitVote).toBeDefined();
    });
  });

  describe('early access tokens', () => {
    it('should check early access availability', async () => {
      mockDeviceService.getDeviceId.mockResolvedValue('test-device-id');
      
      // Test that the method exists and can be called
      expect(feedbackService.hasEarlyAccess).toBeDefined();
    });
  });

  describe('sync operations', () => {
    it('should track pending sync count', async () => {
      // Test that sync counting method exists
      expect(feedbackService.getPendingSyncCount).toBeDefined();
    });

    it('should handle failed sync operations', async () => {
      // Test that failed sync handling exists
      expect(feedbackService.getFailedSyncOperations).toBeDefined();
    });
  });
});

describe('DeviceFingerprintService', () => {
  let deviceService: DeviceFingerprintService;

  beforeEach(() => {
    deviceService = DeviceFingerprintService.getInstance();
  });

  it('should generate unique device IDs', async () => {
    const deviceId = await deviceService.getDeviceId();
    
    expect(deviceId).toBeDefined();
    expect(deviceId).toMatch(/^fv_/); // Should start with 'fv_'
    expect(deviceId.length).toBeGreaterThan(10); // Should be reasonably long
  });

  it('should validate device ID format', () => {
    expect(deviceService.isValidDeviceId('fv_abc123')).toBe(false); // Too short
    expect(deviceService.isValidDeviceId('invalid_format')).toBe(false); // Wrong prefix
    expect(deviceService.isValidDeviceId('fv_' + 'a'.repeat(32))).toBe(true); // Valid format
  });

  it('should generate device fingerprint', async () => {
    const fingerprint = await deviceService.getDeviceFingerprint();
    
    expect(fingerprint).toBeDefined();
    expect(fingerprint.deviceId).toBeDefined();
    expect(fingerprint.platform).toBeDefined();
    expect(fingerprint.createdAt).toBeInstanceOf(Date);
  });
});