import {
  tokenizeTransactionDescription,
  calculateDescriptionSimilarity,
  extractMerchantNames,
  findMatchingKeywords,
  calculateCategoryConfidence,
  normalizeConfidenceScores
} from '@/shared/utils/categorization';

describe('Categorization Utils', () => {
  describe('tokenizeTransactionDescription', () => {
    it('should tokenize a simple transaction description', () => {
      const result = tokenizeTransactionDescription('Restaurant Dinner Payment');
      
      expect(result.originalText).toBe('Restaurant Dinner Payment');
      expect(result.cleanedText).toBe('restaurant dinner payment');
      expect(result.tokens).toEqual(['restaurant', 'dinner', 'payment']);
      expect(result.merchantTokens).toEqual(['restaurant', 'dinner', 'payment']);
    });

    it('should handle complex transaction descriptions with numbers and dates', () => {
      const result = tokenizeTransactionDescription('UBER*TRIP 123.45 on 12/25/2023 SQ*COFFEE');
      
      expect(result.cleanedText).toBe('uber trip 123.45 on 12 25 2023 sq coffee');
      expect(result.tokens).toContain('uber');
      expect(result.tokens).toContain('trip');
      expect(result.tokens).toContain('coffee');
      expect(result.amountTokens).toContain('123.45');
    });

    it('should filter out stop words', () => {
      const result = tokenizeTransactionDescription('Payment to the restaurant for dinner');
      
      expect(result.tokens).not.toContain('to');
      expect(result.tokens).not.toContain('the');
      expect(result.tokens).not.toContain('for');
      expect(result.tokens).toContain('payment');
      expect(result.tokens).toContain('restaurant');
      expect(result.tokens).toContain('dinner');
    });

    it('should handle empty and whitespace strings', () => {
      const result1 = tokenizeTransactionDescription('');
      const result2 = tokenizeTransactionDescription('   ');
      
      expect(result1.originalText).toBe('');
      expect(result1.tokens).toEqual([]);
      expect(result2.originalText).toBe('');
      expect(result2.tokens).toEqual([]);
    });

    it('should identify merchant tokens correctly', () => {
      const result = tokenizeTransactionDescription('AMAZON PRIME SQ*STARBUCKS');
      
      expect(result.merchantTokens).toContain('amazon');
      expect(result.merchantTokens).toContain('prime');
      expect(result.merchantTokens).toContain('starbucks');
    });
  });

  describe('calculateDescriptionSimilarity', () => {
    it('should return 1.0 for identical token arrays', () => {
      const tokens1 = ['restaurant', 'dinner'];
      const tokens2 = ['restaurant', 'dinner'];
      
      const similarity = calculateDescriptionSimilarity(tokens1, tokens2);
      expect(similarity).toBe(1.0);
    });

    it('should return 0.0 for completely different token arrays', () => {
      const tokens1 = ['restaurant', 'dinner'];
      const tokens2 = ['uber', 'taxi'];
      
      const similarity = calculateDescriptionSimilarity(tokens1, tokens2);
      expect(similarity).toBe(0.0);
    });

    it('should return 0.33 for partially overlapping token arrays', () => {
      const tokens1 = ['restaurant', 'dinner'];
      const tokens2 = ['restaurant', 'lunch'];
      
      const similarity = calculateDescriptionSimilarity(tokens1, tokens2);
      expect(similarity).toBeCloseTo(0.33, 2); // 1 intersection, 3 union
    });

    it('should handle empty arrays', () => {
      const tokens1: string[] = [];
      const tokens2: string[] = [];
      const tokens3 = ['restaurant'];
      
      expect(calculateDescriptionSimilarity(tokens1, tokens2)).toBe(1.0);
      expect(calculateDescriptionSimilarity(tokens1, tokens3)).toBe(0.0);
      expect(calculateDescriptionSimilarity(tokens3, tokens1)).toBe(0.0);
    });
  });

  describe('extractMerchantNames', () => {
    it('should extract all caps merchant names', () => {
      const merchants = extractMerchantNames('AMAZON PRIME MEMBERSHIP');
      
      expect(merchants).toContain('amazon');
      expect(merchants).toContain('prime');
      expect(merchants).toContain('membership');
    });

    it('should extract merchant names with common suffixes', () => {
      const merchants = extractMerchantNames('STARBUCKS CORP payment');
      
      expect(merchants).toContain('starbucks corp');
    });

    it('should extract merchant names with special patterns', () => {
      const merchants = extractMerchantNames('SQ*STARBUCKS COFFEE123');
      
      expect(merchants).toContain('sq*starbucks');
      expect(merchants).toContain('coffee123');
    });

    it('should handle descriptions without clear merchant names', () => {
      const merchants = extractMerchantNames('cash payment for lunch');
      
      expect(merchants.length).toBeGreaterThanOrEqual(0);
      // Should not crash and should return some tokens
    });

    it('should remove duplicates', () => {
      const merchants = extractMerchantNames('AMAZON AMAZON PRIME');
      
      const amazonCount = merchants.filter(m => m === 'amazon').length;
      expect(amazonCount).toBeLessThanOrEqual(1);
    });
  });

  describe('findMatchingKeywords', () => {
    it('should find food-related keywords', () => {
      const tokens = ['restaurant', 'dinner', 'food'];
      const matches = findMatchingKeywords(tokens, 'expense');
      
      expect(matches.length).toBeGreaterThan(0);
      expect(matches.some(m => m.keyword === 'restaurant')).toBe(true);
      expect(matches.some(m => m.keyword === 'food')).toBe(true);
      expect(matches.some(m => m.keyword === 'dinner')).toBe(true);
    });

    it('should find transportation-related keywords', () => {
      const tokens = ['uber', 'taxi', 'ride'];
      const matches = findMatchingKeywords(tokens, 'expense');
      
      expect(matches.length).toBeGreaterThan(0);
      expect(matches.some(m => m.keyword === 'uber')).toBe(true);
      expect(matches.some(m => m.keyword === 'taxi')).toBe(true);
    });

    it('should find income-related keywords for income transactions', () => {
      const tokens = ['salary', 'pay', 'freelance'];
      const matches = findMatchingKeywords(tokens, 'income');
      
      expect(matches.length).toBeGreaterThan(0);
      expect(matches.some(m => m.keyword === 'salary')).toBe(true);
      expect(matches.some(m => m.keyword === 'freelance')).toBe(true);
    });

    it('should not find income keywords for expense transactions', () => {
      const tokens = ['salary', 'pay'];
      const matches = findMatchingKeywords(tokens, 'expense');
      
      expect(matches.some(m => m.keyword === 'salary')).toBe(false);
    });

    it('should sort matches by relevance', () => {
      const tokens = ['restaurant', 'food', 'dinner'];
      const matches = findMatchingKeywords(tokens, 'expense');
      
      if (matches.length > 1) {
        for (let i = 1; i < matches.length; i++) {
          const prevRelevance = matches[i - 1].weight * matches[i - 1].matchCount;
          const currRelevance = matches[i].weight * matches[i].matchCount;
          expect(prevRelevance).toBeGreaterThanOrEqual(currRelevance);
        }
      }
    });

    it('should handle tokens with no matches', () => {
      const tokens = ['unknown', 'random', 'words'];
      const matches = findMatchingKeywords(tokens, 'expense');
      
      expect(matches).toEqual([]);
    });
  });

  describe('calculateCategoryConfidence', () => {
    it('should calculate confidence based on keyword matches', () => {
      const keywordMatches = [
        { weight: 0.9, matchCount: 2 },
        { weight: 0.8, matchCount: 1 }
      ];
      
      const confidence = calculateCategoryConfidence(keywordMatches);
      
      expect(confidence).toBeGreaterThan(0);
      expect(confidence).toBeLessThanOrEqual(0.95);
    });

    it('should include historical frequency in calculation', () => {
      const keywordMatches = [{ weight: 0.5, matchCount: 1 }];
      
      const confidenceWithoutHistory = calculateCategoryConfidence(keywordMatches, 0);
      const confidenceWithHistory = calculateCategoryConfidence(keywordMatches, 0.8);
      
      expect(confidenceWithHistory).toBeGreaterThan(confidenceWithoutHistory);
    });

    it('should include amount and merchant relevance', () => {
      const keywordMatches = [{ weight: 0.5, matchCount: 1 }];
      
      const baseConfidence = calculateCategoryConfidence(keywordMatches);
      const enhancedConfidence = calculateCategoryConfidence(
        keywordMatches, 0, 0.7, 0.6
      );
      
      expect(enhancedConfidence).toBeGreaterThan(baseConfidence);
    });

    it('should cap confidence at 0.95', () => {
      const keywordMatches = [
        { weight: 1.0, matchCount: 10 },
        { weight: 1.0, matchCount: 10 }
      ];
      
      const confidence = calculateCategoryConfidence(
        keywordMatches, 1.0, 1.0, 1.0
      );
      
      expect(confidence).toBeLessThanOrEqual(0.95);
    });

    it('should ensure minimum confidence of 0.1', () => {
      const keywordMatches = [{ weight: 0.01, matchCount: 1 }];
      
      const confidence = calculateCategoryConfidence(keywordMatches);
      
      expect(confidence).toBeGreaterThanOrEqual(0.1);
    });
  });

  describe('normalizeConfidenceScores', () => {
    it('should normalize scores to 0.1-0.9 range', () => {
      const suggestions = [
        { confidence: 0.3 },
        { confidence: 0.7 },
        { confidence: 0.5 }
      ];
      
      const normalized = normalizeConfidenceScores(suggestions);
      
      expect(normalized.every(s => s.confidence >= 0.1)).toBe(true);
      expect(normalized.every(s => s.confidence <= 0.9)).toBe(true);
    });

    it('should maintain relative ordering', () => {
      const suggestions = [
        { confidence: 0.8 },
        { confidence: 0.6 },
        { confidence: 0.4 }
      ];
      
      const normalized = normalizeConfidenceScores(suggestions);
      
      expect(normalized[0].confidence).toBeGreaterThan(normalized[1].confidence);
      expect(normalized[1].confidence).toBeGreaterThan(normalized[2].confidence);
    });

    it('should handle empty array', () => {
      const normalized = normalizeConfidenceScores([]);
      expect(normalized).toEqual([]);
    });

    it('should handle single item', () => {
      const suggestions = [{ confidence: 0.5 }];
      const normalized = normalizeConfidenceScores(suggestions);
      
      expect(normalized).toEqual(suggestions);
    });

    it('should handle all equal scores', () => {
      const suggestions = [
        { confidence: 0.5 },
        { confidence: 0.5 },
        { confidence: 0.5 }
      ];
      
      const normalized = normalizeConfidenceScores(suggestions);
      
      expect(normalized).toEqual(suggestions);
    });
  });
});