import { AccountValidationUtils } from '@/shared/utils/accountValidation';
import { AccountMetadata, AccountType } from '@/shared/types';

describe('AccountValidationUtils', () => {
  describe('validateAccountMetadata', () => {
    describe('Credit card validation', () => {
      it('should validate credit card metadata successfully', () => {
        const metadata: AccountMetadata = {
          creditLimit: 50000,
          outstandingBalance: 15000,
          minimumPaymentDue: 1500,
          paymentDueDate: '2024-01-15T00:00:00.000Z',
          autoCalculate: true
        };

        const result = AccountValidationUtils.validateAccountMetadata('credit', metadata);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should fail validation for negative credit limit', () => {
        const metadata: AccountMetadata = {
          creditLimit: -1000
        };

        const result = AccountValidationUtils.validateAccountMetadata('credit', metadata);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Credit limit must be positive for credit card accounts');
      });

      it('should fail validation for negative outstanding balance', () => {
        const metadata: AccountMetadata = {
          outstandingBalance: -500
        };

        const result = AccountValidationUtils.validateAccountMetadata('credit', metadata);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Outstanding balance cannot be negative');
      });

      it('should fail validation for invalid payment due date', () => {
        const metadata: AccountMetadata = {
          paymentDueDate: '2024-01-15' // Missing time component
        };

        const result = AccountValidationUtils.validateAccountMetadata('credit', metadata);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Payment due date must be a valid ISO date string');
      });
    });

    describe('Loan validation', () => {
      it('should validate loan metadata successfully', () => {
        const metadata: AccountMetadata = {
          principalAmount: 500000,
          currentPrincipal: 350000,
          interestRate: 8.5,
          emiAmount: 6000,
          tenure: 60,
          remainingTenure: 42,
          nextEmiDate: '2024-02-01T00:00:00.000Z',
          loanStartDate: '2021-01-01T00:00:00.000Z',
          autoCalculate: true
        };

        const result = AccountValidationUtils.validateAccountMetadata('loan', metadata);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should fail validation for negative principal amount', () => {
        const metadata: AccountMetadata = {
          principalAmount: -100000
        };

        const result = AccountValidationUtils.validateAccountMetadata('loan', metadata);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Principal amount must be positive for loan accounts');
      });

      it('should fail validation for invalid interest rate', () => {
        const metadata: AccountMetadata = {
          interestRate: 150 // Over 100%
        };

        const result = AccountValidationUtils.validateAccountMetadata('loan', metadata);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Interest rate must be between 0-100% for loan accounts');
      });

      it('should fail validation for negative EMI amount', () => {
        const metadata: AccountMetadata = {
          emiAmount: -500
        };

        const result = AccountValidationUtils.validateAccountMetadata('loan', metadata);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('EMI amount must be positive for loan accounts');
      });
    });
  });

  describe('calculateCreditFields', () => {
    it('should calculate available credit and utilization correctly', () => {
      const metadata: AccountMetadata = {
        creditLimit: 50000,
        outstandingBalance: 15000
      };

      const calculated = AccountValidationUtils.calculateCreditFields(metadata);
      expect(calculated.availableCredit).toBe(35000);
      expect(calculated.creditUtilization).toBe(30);
    });

    it('should handle missing values gracefully', () => {
      const metadata: AccountMetadata = {
        creditLimit: 50000
        // Missing outstandingBalance
      };

      const calculated = AccountValidationUtils.calculateCreditFields(metadata);
      expect(calculated.availableCredit).toBeUndefined();
      expect(calculated.creditUtilization).toBeUndefined();
    });
  });

  describe('isValidISODate', () => {
    it('should validate correct ISO date strings', () => {
      expect(AccountValidationUtils.isValidISODate('2024-01-15T00:00:00.000Z')).toBe(true);
      expect(AccountValidationUtils.isValidISODate('2024-01-15T10:30:45.123Z')).toBe(true);
    });

    it('should reject invalid date strings', () => {
      expect(AccountValidationUtils.isValidISODate('2024-01-15')).toBe(false);
      expect(AccountValidationUtils.isValidISODate('invalid-date')).toBe(false);
      expect(AccountValidationUtils.isValidISODate('2024-13-32T00:00:00.000Z')).toBe(false);
    });
  });

  describe('isAccountTypeSupported', () => {
    it('should return true for supported account types', () => {
      expect(AccountValidationUtils.isAccountTypeSupported('checking')).toBe(true);
      expect(AccountValidationUtils.isAccountTypeSupported('credit')).toBe(true);
      expect(AccountValidationUtils.isAccountTypeSupported('loan')).toBe(true);
    });

    it('should return false for unsupported account types', () => {
      expect(AccountValidationUtils.isAccountTypeSupported('unsupported' as AccountType)).toBe(false);
    });
  });

  describe('requiresMetadata', () => {
    it('should return true for credit and loan accounts', () => {
      expect(AccountValidationUtils.requiresMetadata('credit')).toBe(true);
      expect(AccountValidationUtils.requiresMetadata('loan')).toBe(true);
    });

    it('should return false for basic account types', () => {
      expect(AccountValidationUtils.requiresMetadata('checking')).toBe(false);
      expect(AccountValidationUtils.requiresMetadata('savings')).toBe(false);
      expect(AccountValidationUtils.requiresMetadata('investment')).toBe(false);
    });
  });
});