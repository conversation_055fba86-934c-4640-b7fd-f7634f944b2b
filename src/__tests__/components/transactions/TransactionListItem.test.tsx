import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { TransactionListItem } from '@/presentation/components/transactions/TransactionListItem';
import { Transaction, Account, Category } from '@/shared/types';

describe('TransactionListItem', () => {
  const mockTransaction: Transaction = {
    id: 1,
    account_id: 1,
    amount: 100.50,
    description: 'Restaurant dinner',
    category_id: 1,
    transaction_type: 'expense',
    transaction_date: '2024-01-15',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    sms_source: null,
    confidence_score: 0.8,
    is_recurring: false,
    recurring_pattern: null,
    sync_status: 'local',
    hash: 'abc123'
  };

  const mockIncomeTransaction: Transaction = {
    ...mockTransaction,
    id: 2,
    amount: 2000,
    description: 'Salary payment',
    category_id: 5,
    transaction_type: 'income',
    sms_source: 'SMS',
    is_recurring: true,
  };

  const mockAccounts: Account[] = [
    {
      id: 1,
      name: 'Main Account',
      type: 'checking',
      balance: 1000,
      currency: 'INR',
      is_active: true,
      created_at: '2024-01-01T10:00:00Z',
      updated_at: '2024-01-01T10:00:00Z',
      sync_status: 'local'
    },
  ];

  const mockCategories: Category[] = [
    {
      id: 1,
      name: 'Food & Dining',
      parent_id: null,
      category_type: 'expense',
      color_code: '#FF6B6B',
      icon_name: '🍕',
      is_system: true,
      created_at: '2024-01-01T10:00:00Z',
      sync_status: 'local'
    },
    {
      id: 5,
      name: 'Salary',
      parent_id: null,
      category_type: 'income',
      color_code: '#58D68D',
      icon_name: '💰',
      is_system: true,
      created_at: '2024-01-01T10:00:00Z',
      sync_status: 'local'
    },
  ];

  describe('Rendering', () => {
    it('should render expense transaction correctly', () => {
      const { getByText, getByTestId } = render(
        <TransactionListItem
          transaction={mockTransaction}
          categories={mockCategories}
          accounts={mockAccounts}
        />
      );

      expect(getByText('Restaurant dinner')).toBeTruthy();
      expect(getByText('-₹100.50')).toBeTruthy();
      expect(getByText('Food & Dining')).toBeTruthy();
      expect(getByText('🍕')).toBeTruthy();
      expect(getByTestId('transaction-item-1')).toBeTruthy();
    });

    it('should render income transaction correctly', () => {
      const { getByText } = render(
        <TransactionListItem
          transaction={mockIncomeTransaction}
          categories={mockCategories}
          accounts={mockAccounts}
        />
      );

      expect(getByText('Salary payment')).toBeTruthy();
      expect(getByText('+₹2000.00')).toBeTruthy();
      expect(getByText('Salary')).toBeTruthy();
      expect(getByText('💰')).toBeTruthy();
    });

    it('should show account name when showAccount is true', () => {
      const { getByText } = render(
        <TransactionListItem
          transaction={mockTransaction}
          categories={mockCategories}
          accounts={mockAccounts}
          showAccount={true}
        />
      );

      expect(getByText(' • Main Account')).toBeTruthy();
    });

    it('should not show account name when showAccount is false', () => {
      const { queryByText } = render(
        <TransactionListItem
          transaction={mockTransaction}
          categories={mockCategories}
          accounts={mockAccounts}
          showAccount={false}
        />
      );

      expect(queryByText(' • Main Account')).toBeFalsy();
    });

    it('should show SMS indicator for SMS transactions', () => {
      const { getByText } = render(
        <TransactionListItem
          transaction={mockIncomeTransaction}
          categories={mockCategories}
          accounts={mockAccounts}
        />
      );

      expect(getByText(' • SMS')).toBeTruthy();
    });

    it('should show recurring indicator for recurring transactions', () => {
      const { getByText } = render(
        <TransactionListItem
          transaction={mockIncomeTransaction}
          categories={mockCategories}
          accounts={mockAccounts}
        />
      );

      expect(getByText('🔄')).toBeTruthy();
    });

    it('should handle missing category gracefully', () => {
      const transactionWithoutCategory = {
        ...mockTransaction,
        category_id: null
      };

      const { getByText } = render(
        <TransactionListItem
          transaction={transactionWithoutCategory}
          categories={mockCategories}
          accounts={mockAccounts}
        />
      );

      expect(getByText('Uncategorized')).toBeTruthy();
      expect(getByText('❓')).toBeTruthy();
    });

    it('should handle unknown category', () => {
      const transactionWithUnknownCategory = {
        ...mockTransaction,
        category_id: 999
      };

      const { getByText } = render(
        <TransactionListItem
          transaction={transactionWithUnknownCategory}
          categories={mockCategories}
          accounts={mockAccounts}
        />
      );

      expect(getByText('Unknown')).toBeTruthy();
      expect(getByText('❓')).toBeTruthy();
    });

    it('should handle unknown account', () => {
      const transactionWithUnknownAccount = {
        ...mockTransaction,
        account_id: 999
      };

      const { getByText } = render(
        <TransactionListItem
          transaction={transactionWithUnknownAccount}
          categories={mockCategories}
          accounts={mockAccounts}
          showAccount={true}
        />
      );

      expect(getByText(' • Unknown Account')).toBeTruthy();
    });
  });

  describe('Date Formatting', () => {
    it('should show "Today" for today\'s transactions', () => {
      const todayTransaction = {
        ...mockTransaction,
        transaction_date: new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString()
      };

      const { getByText } = render(
        <TransactionListItem
          transaction={todayTransaction}
          categories={mockCategories}
        />
      );

      // Should show "Today" or relative time like "Just now", "1h ago", etc.
      const dateElement = getByText(/Today|Just now|\d+[hm] ago/);
      expect(dateElement).toBeTruthy();
    });

    it('should show "Yesterday" for yesterday\'s transactions', () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const yesterdayTransaction = {
        ...mockTransaction,
        transaction_date: yesterday.toISOString().split('T')[0]
      };

      const { getByText } = render(
        <TransactionListItem
          transaction={yesterdayTransaction}
          categories={mockCategories}
        />
      );

      expect(getByText('Yesterday')).toBeTruthy();
    });

    it('should show formatted date for older transactions', () => {
      const { getByText } = render(
        <TransactionListItem
          transaction={mockTransaction}
          categories={mockCategories}
        />
      );

      // Should show "15 Jan 2024" for the test date
      expect(getByText('15 Jan 2024')).toBeTruthy();
    });
  });

  describe('Status Indicators', () => {
    it('should show sync status when pending', () => {
      const pendingTransaction = {
        ...mockTransaction,
        sync_status: 'pending' as const
      };

      const { getByText } = render(
        <TransactionListItem
          transaction={pendingTransaction}
          categories={mockCategories}
        />
      );

      expect(getByText('Syncing...')).toBeTruthy();
    });

    it('should show conflict status', () => {
      const conflictTransaction = {
        ...mockTransaction,
        sync_status: 'conflict' as const
      };

      const { getByText } = render(
        <TransactionListItem
          transaction={conflictTransaction}
          categories={mockCategories}
        />
      );

      expect(getByText('Conflict')).toBeTruthy();
    });

    it('should show low confidence score', () => {
      const lowConfidenceTransaction = {
        ...mockTransaction,
        confidence_score: 0.5
      };

      const { getByText } = render(
        <TransactionListItem
          transaction={lowConfidenceTransaction}
          categories={mockCategories}
        />
      );

      expect(getByText('50% match')).toBeTruthy();
    });

    it('should not show confidence score if high', () => {
      const highConfidenceTransaction = {
        ...mockTransaction,
        confidence_score: 0.9
      };

      const { queryByText } = render(
        <TransactionListItem
          transaction={highConfidenceTransaction}
          categories={mockCategories}
        />
      );

      expect(queryByText('90% match')).toBeFalsy();
    });
  });

  describe('User Interactions', () => {
    it('should call onPress when item is pressed', () => {
      const mockOnPress = jest.fn();
      const { getByTestId } = render(
        <TransactionListItem
          transaction={mockTransaction}
          onPress={mockOnPress}
          categories={mockCategories}
        />
      );

      fireEvent.press(getByTestId('transaction-item-1'));
      expect(mockOnPress).toHaveBeenCalledWith(mockTransaction);
    });

    it('should call onLongPress when item is long pressed', () => {
      const mockOnLongPress = jest.fn();
      const { getByTestId } = render(
        <TransactionListItem
          transaction={mockTransaction}
          onLongPress={mockOnLongPress}
          categories={mockCategories}
        />
      );

      fireEvent(getByTestId('transaction-item-1'), 'longPress');
      expect(mockOnLongPress).toHaveBeenCalledWith(mockTransaction);
    });
  });

  describe('Amount Formatting', () => {
    it('should format expense amounts with negative prefix', () => {
      const { getByText } = render(
        <TransactionListItem
          transaction={mockTransaction}
          categories={mockCategories}
        />
      );

      expect(getByText('-₹100.50')).toBeTruthy();
    });

    it('should format income amounts with positive prefix', () => {
      const { getByText } = render(
        <TransactionListItem
          transaction={mockIncomeTransaction}
          categories={mockCategories}
        />
      );

      expect(getByText('+₹2000.00')).toBeTruthy();
    });

    it('should format transfer amounts correctly', () => {
      const transferTransaction = {
        ...mockTransaction,
        transaction_type: 'transfer' as const,
        amount: 500
      };

      const { getByText } = render(
        <TransactionListItem
          transaction={transferTransaction}
          categories={mockCategories}
        />
      );

      expect(getByText('-₹500.00')).toBeTruthy();
    });
  });
});