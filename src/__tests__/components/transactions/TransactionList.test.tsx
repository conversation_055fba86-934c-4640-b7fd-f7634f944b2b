import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { TransactionList } from '@/presentation/components/transactions/TransactionList';
import { TransactionService } from '@/business/services/TransactionService';
import { Transaction } from '@/shared/types';

// Mock dependencies
jest.mock('@/business/services/TransactionService');
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: jest.fn(),
}));

// Mock React Native components that cause issues in testing
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    FlatList: ({ data, renderItem, ListEmptyComponent, ListFooterComponent, onEndReached, testID, refreshControl }: any) => {
      const MockedFlatList = require('react-native').View;
      return (
        <MockedFlatList testID={testID}>
          {data?.length ? data.map((item: any) => renderItem({ item })) : ListEmptyComponent?.()}
          {ListFooterComponent?.()}
        </MockedFlatList>
      );
    },
    RefreshControl: ({ children }: any) => children,
    ActivityIndicator: ({ testID }: any) => {
      const MockedActivityIndicator = require('react-native').View;
      return <MockedActivityIndicator testID={testID} />;
    },
  };
});

describe('TransactionList', () => {
  const mockTransactions: Transaction[] = [
    {
      id: 1,
      account_id: 1,
      amount: 100.50,
      description: 'Restaurant dinner',
      category_id: 1,
      transaction_type: 'expense',
      transaction_date: '2024-01-15',
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-15T10:00:00Z',
      sms_source: null,
      confidence_score: 0.8,
      is_recurring: false,
      recurring_pattern: null,
      sync_status: 'local',
      hash: 'abc123'
    },
    {
      id: 2,
      account_id: 1,
      amount: 2000,
      description: 'Salary payment',
      category_id: 5,
      transaction_type: 'income',
      transaction_date: '2024-01-14',
      created_at: '2024-01-14T09:00:00Z',
      updated_at: '2024-01-14T09:00:00Z',
      sms_source: 'SMS',
      confidence_score: null,
      is_recurring: true,
      recurring_pattern: null,
      sync_status: 'synced',
      hash: 'def456'
    },
  ];

  let mockTransactionService: jest.Mocked<TransactionService>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock TransactionService
    mockTransactionService = {
      getTransactions: jest.fn(),
    } as any;

    (TransactionService as jest.Mock).mockImplementation(() => mockTransactionService);

    // Default successful response
    mockTransactionService.getTransactions.mockResolvedValue({
      transactions: mockTransactions,
      totalCount: 2,
      page: 1,
      limit: 20,
      hasMore: false
    });
  });

  describe('Rendering', () => {
    it('should render transaction list', async () => {
      const { getByTestId } = render(<TransactionList />);

      await waitFor(() => {
        expect(getByTestId('transaction-list')).toBeTruthy();
      });
    });

    it('should render search input when showSearch is true', () => {
      const { getByTestId } = render(<TransactionList showSearch={true} />);

      expect(getByTestId('search-input')).toBeTruthy();
    });

    it('should not render search input when showSearch is false', () => {
      const { queryByTestId } = render(<TransactionList showSearch={false} />);

      expect(queryByTestId('search-input')).toBeFalsy();
    });

    it('should render filters when showFilters is true', () => {
      const { getByText } = render(<TransactionList showFilters={true} />);

      expect(getByText('Filters:')).toBeTruthy();
      expect(getByText('Expenses')).toBeTruthy();
      expect(getByText('Income')).toBeTruthy();
    });

    it('should render empty state when no transactions', async () => {
      mockTransactionService.getTransactions.mockResolvedValue({
        transactions: [],
        totalCount: 0,
        page: 1,
        limit: 20,
        hasMore: false
      });

      const { getByText } = render(<TransactionList />);

      await waitFor(() => {
        expect(getByText('No transactions found')).toBeTruthy();
        expect(getByText('Your transactions will appear here')).toBeTruthy();
      });
    });

    it('should render loading indicator initially', () => {
      const { getByText } = render(<TransactionList />);

      expect(getByText('Loading transactions...')).toBeTruthy();
    });
  });

  describe('Search Functionality', () => {
    it('should trigger search when typing in search input', async () => {
      const { getByTestId } = render(<TransactionList showSearch={true} />);

      const searchInput = getByTestId('search-input');
      fireEvent.changeText(searchInput, 'restaurant');

      // Wait for debounce
      await waitFor(() => {
        expect(mockTransactionService.getTransactions).toHaveBeenCalledWith(
          expect.objectContaining({
            searchQuery: 'restaurant'
          }),
          1,
          20
        );
      }, { timeout: 1000 });
    });

    it('should clear search when clear button is pressed', async () => {
      const { getByTestId } = render(<TransactionList showSearch={true} />);

      const searchInput = getByTestId('search-input');
      fireEvent.changeText(searchInput, 'test');

      await waitFor(() => {
        expect(getByTestId('clear-search')).toBeTruthy();
      });

      fireEvent.press(getByTestId('clear-search'));

      await waitFor(() => {
        expect(searchInput.props.value).toBe('');
      });
    });
  });

  describe('Filter Functionality', () => {
    it('should filter by expense type', async () => {
      const { getByText } = render(<TransactionList showFilters={true} />);

      const expenseFilter = getByText('Expenses');
      fireEvent.press(expenseFilter);

      await waitFor(() => {
        expect(mockTransactionService.getTransactions).toHaveBeenCalledWith(
          expect.objectContaining({
            transactionType: 'expense'
          }),
          1,
          20
        );
      });
    });

    it('should filter by income type', async () => {
      const { getByText } = render(<TransactionList showFilters={true} />);

      const incomeFilter = getByText('Income');
      fireEvent.press(incomeFilter);

      await waitFor(() => {
        expect(mockTransactionService.getTransactions).toHaveBeenCalledWith(
          expect.objectContaining({
            transactionType: 'income'
          }),
          1,
          20
        );
      });
    });

    it('should clear all filters when clear button is pressed', async () => {
      const { getByText } = render(<TransactionList showFilters={true} />);

      // Apply a filter first
      fireEvent.press(getByText('Expenses'));

      await waitFor(() => {
        expect(getByText('Clear')).toBeTruthy();
      });

      fireEvent.press(getByText('Clear'));

      await waitFor(() => {
        expect(mockTransactionService.getTransactions).toHaveBeenCalledWith(
          expect.not.objectContaining({
            transactionType: expect.anything()
          }),
          1,
          20
        );
      });
    });
  });

  describe('Transaction Items', () => {
    it('should call onTransactionPress when transaction is tapped', async () => {
      const mockOnPress = jest.fn();
      const { getByTestId } = render(
        <TransactionList onTransactionPress={mockOnPress} />
      );

      await waitFor(() => {
        const transactionItem = getByTestId('transaction-item-1');
        fireEvent.press(transactionItem);
        expect(mockOnPress).toHaveBeenCalledWith(mockTransactions[0]);
      });
    });

    it('should call onTransactionLongPress when transaction is long pressed', async () => {
      const mockOnLongPress = jest.fn();
      const { getByTestId } = render(
        <TransactionList onTransactionLongPress={mockOnLongPress} />
      );

      await waitFor(() => {
        const transactionItem = getByTestId('transaction-item-1');
        fireEvent(transactionItem, 'longPress');
        expect(mockOnLongPress).toHaveBeenCalledWith(mockTransactions[0]);
      });
    });

    it('should display transaction details correctly', async () => {
      const { getByText } = render(<TransactionList />);

      await waitFor(() => {
        expect(getByText('Restaurant dinner')).toBeTruthy();
        expect(getByText('-₹100.50')).toBeTruthy();
        expect(getByText('Food & Dining')).toBeTruthy();
        
        expect(getByText('Salary payment')).toBeTruthy();
        expect(getByText('+₹2000.00')).toBeTruthy();
        expect(getByText('Salary')).toBeTruthy();
      });
    });
  });

  describe('Pagination', () => {
    it('should load more transactions when scrolled to end', async () => {
      mockTransactionService.getTransactions
        .mockResolvedValueOnce({
          transactions: mockTransactions,
          totalCount: 10,
          page: 1,
          limit: 20,
          hasMore: true
        })
        .mockResolvedValueOnce({
          transactions: [mockTransactions[0]], // Additional transaction
          totalCount: 10,
          page: 2,
          limit: 20,
          hasMore: false
        });

      const { getByTestId } = render(<TransactionList />);

      await waitFor(() => {
        const transactionList = getByTestId('transaction-list');
        fireEvent(transactionList, 'onEndReached');
      });

      await waitFor(() => {
        expect(mockTransactionService.getTransactions).toHaveBeenCalledTimes(2);
        expect(mockTransactionService.getTransactions).toHaveBeenNthCalledWith(2,
          expect.anything(),
          2,
          20
        );
      });
    });

    it('should show loading footer when has more transactions', async () => {
      mockTransactionService.getTransactions.mockResolvedValue({
        transactions: mockTransactions,
        totalCount: 50,
        page: 1,
        limit: 20,
        hasMore: true
      });

      const { getByText } = render(<TransactionList />);

      await waitFor(() => {
        expect(getByText('Loading more...')).toBeTruthy();
      });
    });
  });

  describe('Error Handling', () => {
    it('should display error message when loading fails', async () => {
      mockTransactionService.getTransactions.mockRejectedValue(
        new Error('Network error')
      );

      const { getByText } = render(<TransactionList />);

      await waitFor(() => {
        expect(getByText('Failed to load transactions')).toBeTruthy();
        expect(getByText('Network error')).toBeTruthy();
        expect(getByText('Retry')).toBeTruthy();
      });
    });

    it('should retry loading when retry button is pressed', async () => {
      mockTransactionService.getTransactions
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          transactions: mockTransactions,
          totalCount: 2,
          page: 1,
          limit: 20,
          hasMore: false
        });

      const { getByText } = render(<TransactionList />);

      await waitFor(() => {
        expect(getByText('Retry')).toBeTruthy();
      });

      fireEvent.press(getByText('Retry'));

      await waitFor(() => {
        expect(getByText('Restaurant dinner')).toBeTruthy();
      });
    });
  });

  describe('Account Filtering', () => {
    it('should filter transactions by account when accountId is provided', async () => {
      render(<TransactionList accountId={1} />);

      await waitFor(() => {
        expect(mockTransactionService.getTransactions).toHaveBeenCalledWith(
          expect.objectContaining({
            accountId: 1
          }),
          1,
          20
        );
      });
    });
  });
});