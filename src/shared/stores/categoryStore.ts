import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Category } from '@/shared/types';
import { CategoryService } from '@/business/services/CategoryService';
import type { CategoryStats, CategoryTree } from '@/business/services/CategoryService';

interface CategoryState {
  categories: Category[];
  expenseCategories: Category[];
  incomeCategories: Category[];
  loading: boolean;
  error: string | null;
  selectedCategory: Category | null;
  lastUpdated: Date | null;
  categoryStats: Record<number, CategoryStats>;
  categoryHierarchy: CategoryTree[];
}

interface CategoryActions {
  // Data loading
  loadCategories: () => Promise<void>;
  refreshCategories: () => Promise<void>;
  initializeSystemCategories: () => Promise<void>;
  
  // Category CRUD operations
  createCategory: (categoryData: Omit<Category, 'id' | 'created_at'>) => Promise<Category>;
  updateCategory: (id: number, updates: Partial<Category>) => Promise<void>;
  deleteCategory: (id: number, reassignToId?: number) => Promise<void>;
  mergeCategories: (sourceIds: number[], targetId: number) => Promise<void>;
  archiveCategory: (id: number) => Promise<void>;
  
  // Category filtering and selection
  getCategoriesByType: (type: 'income' | 'expense') => Category[];
  getSystemCategories: () => Category[];
  getUserCategories: () => Category[];
  getRootCategories: () => Category[];
  getSubcategories: (parentId: number) => Category[];
  setSelectedCategory: (category: Category | null) => void;
  selectCategoryById: (id: number | null) => void;
  
  // Category analytics and statistics
  getCategoryStats: (categoryId: number) => Promise<CategoryStats>;
  loadCategoryHierarchy: () => Promise<void>;
  getCategoryHierarchy: () => CategoryTree[];
  searchCategories: (query: string) => Category[];
  getTopCategories: (type: 'income' | 'expense', limit?: number) => Promise<(Category & {usage_count: number})[]>;
  
  // Internal state management
  setCategories: (categories: Category[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
}

type CategoryStore = CategoryState & CategoryActions;

const initialState: CategoryState = {
  categories: [],
  expenseCategories: [],
  incomeCategories: [],
  loading: false,
  error: null,
  selectedCategory: null,
  lastUpdated: null,
  categoryStats: {},
  categoryHierarchy: [],
};

// Create single service instance to avoid repeated instantiation
const categoryService = new CategoryService();

export const useCategoryStore = create<CategoryStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Data loading
      loadCategories: async () => {
        set({ loading: true, error: null });
        
        try {
          const categories = await categoryService.getAllCategories();
          
          const expenseCategories = categories.filter(cat => cat.category_type === 'expense');
          const incomeCategories = categories.filter(cat => cat.category_type === 'income');
          
          set({ 
            categories,
            expenseCategories,
            incomeCategories,
            loading: false,
            lastUpdated: new Date(),
            error: null 
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load categories';
          set({ 
            error: errorMessage, 
            loading: false 
          });
          throw new Error(errorMessage);
        }
      },

      initializeSystemCategories: async () => {
        set({ loading: true, error: null });
        
        try {
          await categoryService.initializeSystemCategories();
          await get().loadCategories();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to initialize system categories';
          set({ 
            error: errorMessage, 
            loading: false 
          });
          throw new Error(errorMessage);
        }
      },

      refreshCategories: async () => {
        const { loadCategories } = get();
        await loadCategories();
      },

      // Category CRUD operations
      createCategory: async (categoryData) => {
        set({ loading: true, error: null });
        
        try {
          const newCategory = await categoryService.createCategory(categoryData);
          
          await get().loadCategories();
          
          return newCategory;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create category';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      updateCategory: async (id, updates) => {
        set({ loading: true, error: null });
        
        try {
          await categoryService.updateCategory(id, updates);
          
          await get().loadCategories();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update category';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      deleteCategory: async (id, reassignToId) => {
        set({ loading: true, error: null });
        
        try {
          await categoryService.deleteCategory(id, reassignToId);
          
          const { selectedCategory } = get();
          if (selectedCategory?.id === id) {
            set({ selectedCategory: null });
          }
          
          await get().loadCategories();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete category';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      mergeCategories: async (sourceIds, targetId) => {
        set({ loading: true, error: null });
        
        try {
          await categoryService.mergeCategories(sourceIds, targetId);
          
          await get().loadCategories();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to merge categories';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      archiveCategory: async (id) => {
        set({ loading: true, error: null });
        
        try {
          await categoryService.archiveCategory(id);
          
          await get().loadCategories();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to archive category';
          set({ error: errorMessage, loading: false });
          throw new Error(errorMessage);
        }
      },

      // Category filtering and selection
      getCategoriesByType: (type) => {
        const { expenseCategories, incomeCategories } = get();
        return type === 'expense' ? expenseCategories : incomeCategories;
      },

      getSystemCategories: () => {
        const { categories } = get();
        return categories.filter(cat => cat.is_system);
      },

      getUserCategories: () => {
        const { categories } = get();
        return categories.filter(cat => !cat.is_system);
      },

      getRootCategories: () => {
        const { categories } = get();
        return categories.filter(cat => cat.parent_id === null);
      },

      getSubcategories: (parentId) => {
        const { categories } = get();
        return categories.filter(cat => cat.parent_id === parentId);
      },

      setSelectedCategory: (category) => {
        set({ selectedCategory: category });
      },

      selectCategoryById: (id) => {
        const { categories } = get();
        const category = id ? categories.find(cat => cat.id === id) || null : null;
        set({ selectedCategory: category });
      },

      // Category analytics and statistics
      getCategoryStats: async (categoryId) => {
        const stats = await categoryService.getCategoryStats(categoryId);
        
        const { categoryStats } = get();
        set({
          categoryStats: {
            ...categoryStats,
            [categoryId]: stats,
          },
        });
        
        return stats;
      },

      loadCategoryHierarchy: async () => {
        set({ loading: true, error: null });
        
        try {
          const categoryHierarchy = await categoryService.getCategoryHierarchyWithStats();
          
          set({
            categoryHierarchy,
            loading: false,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load category hierarchy';
          set({ 
            error: errorMessage, 
            loading: false 
          });
          throw new Error(errorMessage);
        }
      },

      getCategoryHierarchy: () => {
        const { categoryHierarchy } = get();
        return categoryHierarchy;
      },

      searchCategories: (query) => {
        const { categories } = get();
        const lowerQuery = query.toLowerCase();
        
        return categories.filter(category =>
          category.name.toLowerCase().includes(lowerQuery)
        );
      },

      getTopCategories: async (type, limit = 5) => {
        return categoryService.getTopCategories(type, limit);
      },

      // Internal state management
      setCategories: (categories) => {
        const expenseCategories = categories.filter(cat => cat.category_type === 'expense');
        const incomeCategories = categories.filter(cat => cat.category_type === 'income');
        
        set({ 
          categories, 
          expenseCategories,
          incomeCategories,
          lastUpdated: new Date() 
        });
      },

      setLoading: (loading) => {
        set({ loading });
      },

      setError: (error) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'category-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        categories: state.categories,
        expenseCategories: state.expenseCategories,
        incomeCategories: state.incomeCategories,
        selectedCategory: state.selectedCategory,
        lastUpdated: state.lastUpdated,
        categoryStats: state.categoryStats,
      }),
    }
  )
);