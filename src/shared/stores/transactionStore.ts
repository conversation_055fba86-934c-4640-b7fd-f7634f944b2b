import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Transaction, CategorySuggestion } from '@/shared/types';
import { TransactionService } from '@/business/services/TransactionService';
import { CategorySuggestionService } from '@/business/services/CategorySuggestionService';

// Types for search and filtering
export interface TransactionFilters {
  searchQuery?: string;
  accountId?: number;
  categoryId?: number;
  transactionType?: 'income' | 'expense' | 'transfer';
  dateFrom?: string;
  dateTo?: string;
  syncStatus?: 'local' | 'synced' | 'pending' | 'conflict';
}

export interface PaginationInfo {
  page: number;
  limit: number;
  totalCount: number;
  hasMore: boolean;
}

export interface TransactionOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  transactionId?: number;
  originalData?: Transaction;
  newData?: Partial<Transaction>;
  timestamp: number;
  status: 'pending' | 'success' | 'error';
  error?: string;
}

interface TransactionState {
  // Core data
  transactions: Transaction[];
  selectedTransactions: Set<number>;
  
  // Loading states
  loading: boolean;
  loadingMore: boolean;
  refreshing: boolean;
  
  // Error handling
  error: string | null;
  
  // Search and filtering
  searchQuery: string;
  filters: TransactionFilters;
  
  // Pagination
  pagination: PaginationInfo;
  
  // Optimistic operations
  pendingOperations: Map<string, TransactionOperation>;
  
  // Category suggestions
  categorySuggestions: CategorySuggestion[];
  suggestionsLoading: boolean;
}

interface TransactionActions {
  // CRUD Operations with Optimistic Updates
  createTransaction: (transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'hash'>) => Promise<Transaction>;
  updateTransaction: (id: number, updates: Partial<Transaction>) => Promise<Transaction>;
  deleteTransaction: (id: number) => Promise<void>;
  
  // Bulk Operations
  deleteSelectedTransactions: () => Promise<void>;
  selectTransaction: (id: number) => void;
  deselectTransaction: (id: number) => void;
  selectAllTransactions: () => void;
  clearSelection: () => void;
  
  // Data Loading
  loadTransactions: (options?: { accountId?: number; refresh?: boolean }) => Promise<void>;
  loadMoreTransactions: () => Promise<void>;
  refreshTransactions: () => Promise<void>;
  
  // Search and Filtering
  setSearchQuery: (query: string) => void;
  setFilters: (filters: Partial<TransactionFilters>) => void;
  clearFilters: () => void;
  searchTransactions: (query: string) => Promise<void>;
  
  // Category Suggestions
  getSuggestions: (description: string, amount?: number) => Promise<CategorySuggestion[]>;
  
  // Utility Methods
  getFilteredTransactions: () => Transaction[];
  getTotalAmount: (type?: 'income' | 'expense') => number;
  getTransactionById: (id: number) => Transaction | undefined;
  
  // State Management
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
  
  // Internal Operations (for optimistic updates)
  _addPendingOperation: (operation: TransactionOperation) => void;
  _completePendingOperation: (operationId: string, success: boolean, error?: string) => void;
  _retryFailedOperations: () => Promise<void>;
}

type TransactionStore = TransactionState & TransactionActions;

const initialState: TransactionState = {
  transactions: [],
  selectedTransactions: new Set(),
  loading: false,
  loadingMore: false,
  refreshing: false,
  error: null,
  searchQuery: '',
  filters: {},
  pagination: {
    page: 1,
    limit: 20,
    totalCount: 0,
    hasMore: false,
  },
  pendingOperations: new Map(),
  categorySuggestions: [],
  suggestionsLoading: false,
};

// Default service instances
let transactionService = new TransactionService();
let categorySuggestionService = new CategorySuggestionService();

// Function to set services for testing
export const setTransactionStoreServices = (
  txService: TransactionService,
  catService: CategorySuggestionService
) => {
  transactionService = txService;
  categorySuggestionService = catService;
};

export const useTransactionStore = create<TransactionStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // CRUD Operations with Optimistic Updates
      createTransaction: async (transactionData): Promise<Transaction> => {
        const operationId = `create_${Date.now()}`;
        const optimisticTransaction: Transaction = {
          ...transactionData,
          id: -Date.now(), // Temporary negative ID
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          hash: `temp_${Date.now()}`,
          sync_status: 'pending'
        };

        // Optimistic update
        set(state => ({ 
          transactions: [optimisticTransaction, ...state.transactions],
          error: null 
        }));

        // Add pending operation
        get()._addPendingOperation({
          id: operationId,
          type: 'create',
          newData: transactionData,
          timestamp: Date.now(),
          status: 'pending'
        });

        try {
          const createdTransaction = await transactionService.createTransaction(transactionData);
          
          // Replace optimistic transaction with real one
          set(state => ({
            transactions: state.transactions.map(t => 
              t.id === optimisticTransaction.id ? createdTransaction : t
            )
          }));

          get()._completePendingOperation(operationId, true);
          return createdTransaction;
          
        } catch (error) {
          // Revert optimistic update
          set(state => ({
            transactions: state.transactions.filter(t => t.id !== optimisticTransaction.id),
            error: error instanceof Error ? error.message : 'Failed to create transaction'
          }));

          get()._completePendingOperation(operationId, false, error instanceof Error ? error.message : 'Unknown error');
          throw error;
        }
      },

      updateTransaction: async (id, updates): Promise<Transaction> => {
        const operationId = `update_${id}_${Date.now()}`;
        const originalTransaction = get().transactions.find(t => t.id === id);
        
        if (!originalTransaction) {
          throw new Error('Transaction not found');
        }

        // Optimistic update
        const optimisticTransaction = { ...originalTransaction, ...updates, sync_status: 'pending' as const };
        set(state => ({
          transactions: state.transactions.map(t => 
            t.id === id ? optimisticTransaction : t
          ),
          error: null
        }));

        get()._addPendingOperation({
          id: operationId,
          type: 'update',
          transactionId: id,
          originalData: originalTransaction,
          newData: updates,
          timestamp: Date.now(),
          status: 'pending'
        });

        try {
          const updatedTransaction = await transactionService.updateTransaction(id, updates);
          
          set(state => ({
            transactions: state.transactions.map(t => 
              t.id === id ? updatedTransaction : t
            )
          }));

          get()._completePendingOperation(operationId, true);
          return updatedTransaction;
          
        } catch (error) {
          // Revert optimistic update
          set(state => ({
            transactions: state.transactions.map(t => 
              t.id === id ? originalTransaction : t
            ),
            error: error instanceof Error ? error.message : 'Failed to update transaction'
          }));

          get()._completePendingOperation(operationId, false, error instanceof Error ? error.message : 'Unknown error');
          throw error;
        }
      },

      deleteTransaction: async (id): Promise<void> => {
        const operationId = `delete_${id}_${Date.now()}`;
        const originalTransaction = get().transactions.find(t => t.id === id);
        
        if (!originalTransaction) {
          throw new Error('Transaction not found');
        }

        // Optimistic update
        set(state => ({
          transactions: state.transactions.filter(t => t.id !== id),
          selectedTransactions: new Set([...state.selectedTransactions].filter(selectedId => selectedId !== id)),
          error: null
        }));

        get()._addPendingOperation({
          id: operationId,
          type: 'delete',
          transactionId: id,
          originalData: originalTransaction,
          timestamp: Date.now(),
          status: 'pending'
        });

        try {
          await transactionService.deleteTransaction(id);
          get()._completePendingOperation(operationId, true);
          
        } catch (error) {
          // Revert optimistic update
          set(state => ({
            transactions: [...state.transactions, originalTransaction].sort((a, b) => 
              new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime()
            ),
            error: error instanceof Error ? error.message : 'Failed to delete transaction'
          }));

          get()._completePendingOperation(operationId, false, error instanceof Error ? error.message : 'Unknown error');
          throw error;
        }
      },

      // Bulk Operations
      deleteSelectedTransactions: async (): Promise<void> => {
        const { selectedTransactions } = get();
        if (selectedTransactions.size === 0) return;

        const deletePromises = Array.from(selectedTransactions).map(id => 
          get().deleteTransaction(id)
        );

        try {
          await Promise.all(deletePromises);
          set({ selectedTransactions: new Set() });
        } catch (error) {
          // Individual delete operations handle their own rollbacks
          throw error;
        }
      },

      selectTransaction: (id): void => {
        set(state => ({
          selectedTransactions: new Set([...state.selectedTransactions, id])
        }));
      },

      deselectTransaction: (id): void => {
        set(state => {
          const newSelection = new Set(state.selectedTransactions);
          newSelection.delete(id);
          return { selectedTransactions: newSelection };
        });
      },

      selectAllTransactions: (): void => {
        const allIds = get().getFilteredTransactions().map(t => t.id);
        set({ selectedTransactions: new Set(allIds) });
      },

      clearSelection: (): void => {
        set({ selectedTransactions: new Set() });
      },

      // Data Loading
      loadTransactions: async (options = {}): Promise<void> => {
        const { accountId, refresh = false } = options;
        const { filters, searchQuery } = get();

        if (refresh) {
          set({ refreshing: true, error: null });
        } else {
          set({ loading: true, error: null });
        }

        try {
          const searchFilters: TransactionFilters = {
            ...filters,
            ...(searchQuery && { searchQuery }),
            ...(accountId && { accountId }),
            ...(filters.accountId && { accountId: filters.accountId }),
          };

          const result = await transactionService.getTransactions(searchFilters, 1, get().pagination.limit);
          
          set({
            transactions: result.transactions,
            pagination: {
              page: result.page,
              limit: result.limit,
              totalCount: result.totalCount,
              hasMore: result.hasMore,
            },
            loading: false,
            refreshing: false,
            error: null,
          });
          
        } catch (error) {
          set({
            loading: false,
            refreshing: false,
            error: error instanceof Error ? error.message : 'Failed to load transactions'
          });
          throw error;
        }
      },

      loadMoreTransactions: async (): Promise<void> => {
        const { pagination, filters, searchQuery, loadingMore } = get();
        
        if (loadingMore || !pagination.hasMore) return;

        set({ loadingMore: true, error: null });

        try {
          const searchFilters: TransactionFilters = {
            ...filters,
            ...(searchQuery && { searchQuery }),
          };

          const result = await transactionService.getTransactions(
            searchFilters, 
            pagination.page + 1, 
            pagination.limit
          );
          
          set(state => ({
            transactions: [...state.transactions, ...result.transactions],
            pagination: {
              page: result.page,
              limit: result.limit,
              totalCount: result.totalCount,
              hasMore: result.hasMore,
            },
            loadingMore: false,
            error: null,
          }));
          
        } catch (error) {
          set({
            loadingMore: false,
            error: error instanceof Error ? error.message : 'Failed to load more transactions'
          });
          throw error;
        }
      },

      refreshTransactions: async (): Promise<void> => {
        return get().loadTransactions({ refresh: true });
      },

      // Search and Filtering
      setSearchQuery: (query): void => {
        set({ searchQuery: query });
      },

      setFilters: (newFilters): void => {
        set(state => ({
          filters: { ...state.filters, ...newFilters }
        }));
      },

      clearFilters: (): void => {
        set({
          filters: {},
          searchQuery: '',
        });
      },

      searchTransactions: async (query): Promise<void> => {
        set({ searchQuery: query });
        return get().loadTransactions({ refresh: true });
      },

      // Category Suggestions
      getSuggestions: async (description, amount): Promise<CategorySuggestion[]> => {
        set({ suggestionsLoading: true });

        try {
          const suggestions = await categorySuggestionService.suggestCategories(description, amount);
          set({ 
            categorySuggestions: suggestions,
            suggestionsLoading: false 
          });
          return suggestions;
          
        } catch (error) {
          set({ 
            suggestionsLoading: false,
            error: error instanceof Error ? error.message : 'Failed to get suggestions'
          });
          return [];
        }
      },

      // Utility Methods
      getFilteredTransactions: (): Transaction[] => {
        const { transactions, filters, searchQuery } = get();
        
        return transactions.filter(transaction => {
          // Text search
          if (searchQuery) {
            const query = searchQuery.toLowerCase();
            const matchesDescription = transaction.description.toLowerCase().includes(query);
            const matchesAmount = transaction.amount.toString().includes(query);
            
            if (!matchesDescription && !matchesAmount) {
              return false;
            }
          }

          // Filters
          if (filters.accountId && transaction.account_id !== filters.accountId) {
            return false;
          }
          if (filters.categoryId && transaction.category_id !== filters.categoryId) {
            return false;
          }
          if (filters.transactionType && transaction.transaction_type !== filters.transactionType) {
            return false;
          }
          if (filters.dateFrom && transaction.transaction_date < filters.dateFrom) {
            return false;
          }
          if (filters.dateTo && transaction.transaction_date > filters.dateTo) {
            return false;
          }
          if (filters.syncStatus && transaction.sync_status !== filters.syncStatus) {
            return false;
          }
          
          return true;
        });
      },

      getTotalAmount: (type): number => {
        const transactions = get().getFilteredTransactions();
        
        return transactions
          .filter(t => !type || t.transaction_type === type)
          .reduce((sum, t) => {
            if (t.transaction_type === 'income') {
              return sum + t.amount;
            } else if (t.transaction_type === 'expense') {
              return sum - t.amount;
            }
            return sum; // transfers don't affect total
          }, 0);
      },

      getTransactionById: (id): Transaction | undefined => {
        return get().transactions.find(t => t.id === id);
      },

      // State Management
      setError: (error): void => {
        set({ error });
      },

      clearError: (): void => {
        set({ error: null });
      },

      reset: (): void => {
        set({
          ...initialState,
          selectedTransactions: new Set(),
          pendingOperations: new Map(),
        });
      },

      // Internal Operations
      _addPendingOperation: (operation): void => {
        set(state => ({
          pendingOperations: new Map(state.pendingOperations).set(operation.id, operation)
        }));
      },

      _completePendingOperation: (operationId, success, error): void => {
        set(state => {
          const newOperations = new Map(state.pendingOperations);
          const operation = newOperations.get(operationId);
          
          if (operation) {
            newOperations.set(operationId, {
              ...operation,
              status: success ? 'success' : 'error',
              ...(error && { error }),
            });
          }
          
          return { pendingOperations: newOperations };
        });

        // Clean up completed operations after a delay
        setTimeout(() => {
          set(state => {
            const newOperations = new Map(state.pendingOperations);
            newOperations.delete(operationId);
            return { pendingOperations: newOperations };
          });
        }, 5000);
      },

      _retryFailedOperations: async (): Promise<void> => {
        const { pendingOperations } = get();
        const failedOperations = Array.from(pendingOperations.values())
          .filter(op => op.status === 'error');

        for (const operation of failedOperations) {
          try {
            switch (operation.type) {
              case 'create':
                if (operation.newData) {
                  await get().createTransaction(operation.newData as any);
                }
                break;
              case 'update':
                if (operation.transactionId && operation.newData) {
                  await get().updateTransaction(operation.transactionId, operation.newData);
                }
                break;
              case 'delete':
                if (operation.transactionId) {
                  await get().deleteTransaction(operation.transactionId);
                }
                break;
            }
          } catch (error) {
            // Individual operations will handle their own errors
            console.error('Retry failed for operation:', operation.id, error);
          }
        }
      },
    }),
    {
      name: 'transaction-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        transactions: state.transactions,
        filters: state.filters,
        searchQuery: state.searchQuery,
        // Don't persist temporary UI states like loading, selections, etc.
      }),
    }
  )
);