import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppSettings } from '@/shared/types';

interface SettingsState extends AppSettings {
  loading: boolean;
  error: string | null;
}

interface SettingsActions {
  updateSettings: (updates: Partial<AppSettings>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

type SettingsStore = SettingsState & SettingsActions;

const initialState: SettingsState = {
  currency: 'INR',
  language: 'en',
  theme: 'system',
  biometric_enabled: false,
  sync_enabled: false,
  notification_enabled: true,
  loading: false,
  error: null,
};

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set) => ({
      ...initialState,

      updateSettings: (updates: Partial<AppSettings>): void => {
        set(state => ({ 
          ...state,
          ...updates,
          error: null 
        }));
      },

      setLoading: (loading: boolean): void => {
        set({ loading });
      },

      setError: (error: string | null): void => {
        set({ error });
      },

      reset: (): void => {
        set(initialState);
      },
    }),
    {
      name: 'settings-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        currency: state.currency,
        language: state.language,
        theme: state.theme,
        biometric_enabled: state.biometric_enabled,
        sync_enabled: state.sync_enabled,
        notification_enabled: state.notification_enabled,
      }),
    }
  )
);