import { create } from 'zustand';
import { Account } from '@/shared/types';
import { MockAccountService, AccountSummary } from '@/business/services/MockAccountService';

interface AccountState {
  accounts: Account[];
  loading: boolean;
  error: string | null;
  selectedAccount: Account | null;
  accountSummary: AccountSummary | null;
  lastUpdated: Date | null;
}

interface AccountActions {
  // Data loading
  loadAccounts: () => Promise<void>;
  refreshAccounts: () => Promise<void>;
  
  // Account CRUD operations
  createAccount: (accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'>) => Promise<Account>;
  updateAccount: (id: number, updates: Partial<Account>) => Promise<void>;
  deleteAccount: (id: number, transferToId?: number) => Promise<void>;
  
  // Balance operations
  calculateBalance: (accountId: number) => Promise<number>;
  recalculateBalance: (accountId: number) => Promise<void>;
  transferBalance: (fromAccountId: number, toAccountId: number, amount: number) => Promise<void>;
  
  // Account summary
  loadAccountSummary: () => Promise<void>;
  
  // Selection and UI state
  setSelectedAccount: (account: Account | null) => void;
  selectAccountById: (id: number | null) => void;
  
  // Internal state management
  setAccounts: (accounts: Account[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

type AccountStore = AccountState & AccountActions;

const initialState: AccountState = {
  accounts: [],
  loading: false,
  error: null,
  selectedAccount: null,
  accountSummary: null,
  lastUpdated: null,
};

// Create service instance
const accountService = new MockAccountService();

export const useAccountStore = create<AccountStore>()((set, get) => ({
  ...initialState,

  // Data loading
  loadAccounts: async () => {
    console.log('🔧 MockAccountStore: loadAccounts called');
    
    try {
      // BATCH ALL STATE UPDATES INTO ONE SET CALL
      set({ loading: true, error: null });
      
      const accounts = await accountService.getAllAccounts();
      
      // BATCH THE FINAL STATE UPDATE 
      set({ 
        accounts, 
        loading: false, 
        lastUpdated: new Date() 
      });
      
      console.log('✅ MockAccountStore: Accounts loaded successfully', accounts);
    } catch (error) {
      console.error('❌ MockAccountStore: Failed to load accounts:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load accounts';
      
      // BATCH ERROR STATE UPDATE
      set({ 
        loading: false, 
        error: errorMessage 
      });
    }
  },

  refreshAccounts: async () => {
    console.log('🔧 MockAccountStore: refreshAccounts called');
    const { loadAccounts } = get();
    await loadAccounts();
  },

  // Account CRUD operations
  createAccount: async (accountData) => {
    console.log('🔧 MockAccountStore: createAccount called', accountData);
    
    try {
      // BATCH: Set loading state
      set({ loading: true, error: null });
      
      // Validate account data
      const validation = accountService.validateAccount(accountData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }
      
      const newAccount = await accountService.createAccount(accountData);
      
      // Reload accounts to get updated state
      const accounts = await accountService.getAllAccounts();
      
      // BATCH: Update all state at once
      set({ 
        accounts, 
        loading: false, 
        lastUpdated: new Date() 
      });
      
      console.log('✅ MockAccountStore: Account created successfully', newAccount);
      return newAccount;
    } catch (error) {
      console.error('❌ MockAccountStore: Failed to create account:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create account';
      
      // BATCH: Error state
      set({ 
        loading: false, 
        error: errorMessage 
      });
      throw error;
    }
  },

  updateAccount: async (id, updates) => {
    console.log('🔧 MockAccountStore: updateAccount called', id, updates);
    const { setLoading, setError, loadAccounts } = get();
    
    try {
      setLoading(true);
      setError(null);
      
      await accountService.updateAccount(id, updates);
      
      // Reload accounts to update state
      await loadAccounts();
      
      console.log('✅ MockAccountStore: Account updated successfully');
    } catch (error) {
      console.error('❌ MockAccountStore: Failed to update account:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update account';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  },

  deleteAccount: async (id, transferToId) => {
    console.log('🔧 MockAccountStore: deleteAccount called', id, transferToId);
    const { setLoading, setError, loadAccounts } = get();
    
    try {
      setLoading(true);
      setError(null);
      
      await accountService.deleteAccount(id, transferToId);
      
      // Reload accounts to update state
      await loadAccounts();
      
      console.log('✅ MockAccountStore: Account deleted successfully');
    } catch (error) {
      console.error('❌ MockAccountStore: Failed to delete account:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete account';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  },

  // Balance operations
  calculateBalance: async (accountId) => {
    console.log('🔧 MockAccountStore: calculateBalance called', accountId);
    try {
      return await accountService.calculateBalance(accountId);
    } catch (error) {
      console.error('❌ MockAccountStore: Failed to calculate balance:', error);
      return 0;
    }
  },

  recalculateBalance: async (accountId) => {
    console.log('🔧 MockAccountStore: recalculateBalance called', accountId);
    const { loadAccounts } = get();
    
    try {
      // In mock implementation, just reload accounts
      await loadAccounts();
    } catch (error) {
      console.error('❌ MockAccountStore: Failed to recalculate balance:', error);
    }
  },

  transferBalance: async (fromAccountId, toAccountId, amount) => {
    console.log('🔧 MockAccountStore: transferBalance called', fromAccountId, toAccountId, amount);
    const { setLoading, setError, loadAccounts } = get();
    
    try {
      setLoading(true);
      setError(null);
      
      await accountService.transferBalance(fromAccountId, toAccountId, amount);
      
      // Reload accounts to update state
      await loadAccounts();
      
      console.log('✅ MockAccountStore: Balance transferred successfully');
    } catch (error) {
      console.error('❌ MockAccountStore: Failed to transfer balance:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to transfer balance';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  },

  // Account summary
  loadAccountSummary: async () => {
    console.log('🔧 MockAccountStore: loadAccountSummary called');
    const { setError } = get();
    
    try {
      setError(null);
      
      const summary = await accountService.getAccountSummary();
      set({ accountSummary: summary });
      
      console.log('✅ MockAccountStore: Account summary loaded', summary);
    } catch (error) {
      console.error('❌ MockAccountStore: Failed to load account summary:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load account summary';
      setError(errorMessage);
    }
  },

  // Selection and UI state
  setSelectedAccount: (account) => {
    console.log('🔧 MockAccountStore: setSelectedAccount called', account);
    set({ selectedAccount: account });
  },

  selectAccountById: (id) => {
    console.log('🔧 MockAccountStore: selectAccountById called', id);
    const { accounts } = get();
    
    if (id === null) {
      set({ selectedAccount: null });
      return;
    }
    
    const account = accounts.find(acc => acc.id === id);
    set({ selectedAccount: account || null });
  },

  // Internal state management
  setAccounts: (accounts) => {
    console.log('🔧 MockAccountStore: setAccounts called', accounts.length, 'accounts');
    set({ accounts });
  },

  setLoading: (loading) => {
    set({ loading });
  },

  setError: (error) => {
    set({ error });
  },

  reset: () => {
    console.log('🔧 MockAccountStore: reset called');
    set(initialState);
  },
}));