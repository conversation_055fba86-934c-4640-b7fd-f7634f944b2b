/**
 * Feedback Store
 * Story 1.9: Anonymous Feedback and Feature Requests
 * 
 * Zustand store for feedback state management with offline-first approach
 * Manages feedback submissions, votes, early access tokens, and sync status
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FeedbackService } from '../../business/services/FeedbackService';
import {
  FeedbackSubmission,
  FeatureVote,
  EarlyAccessToken,
  FeedbackFormData,
  VoteSubmissionData,
  FeedbackStatusUpdate,
  FeatureRequestWithVotes
} from '../types/feedback';

export interface FeedbackStore {
  // Local state
  feedback: FeedbackSubmission[];
  featureRequests: FeatureRequestWithVotes[];
  userVotes: FeatureVote[];
  earlyAccessTokens: EarlyAccessToken[];
  
  // UI state
  loading: boolean;
  error: string | null;
  submitting: boolean;
  
  // Sync state
  syncStatus: 'idle' | 'syncing' | 'error';
  lastSyncAt: Date | null;
  pendingSyncCount: number;
  
  // Connection state
  isConnected: boolean;
  
  // Draft state
  draftFeedback: Partial<FeedbackFormData> | null;
  
  // Actions - Feedback Management
  submitFeedback: (feedback: FeedbackFormData) => Promise<void>;
  loadUserFeedback: () => Promise<void>;
  refreshFeedback: () => Promise<void>;
  
  // Actions - Voting
  submitVote: (voteData: VoteSubmissionData) => Promise<void>;
  loadUserVotes: () => Promise<void>;
  loadFeatureRequests: () => Promise<void>;
  
  // Actions - Early Access
  loadEarlyAccessTokens: () => Promise<void>;
  checkEarlyAccess: (tokenType: 'contributor' | 'community_wave') => Promise<boolean>;
  
  // Actions - Sync Management
  syncToSupabase: () => Promise<void>;
  handleStatusUpdate: (update: FeedbackStatusUpdate) => void;
  retryFailedSync: () => Promise<void>;
  
  // Actions - Draft Management
  saveDraftFeedback: (draft: Partial<FeedbackFormData>) => void;
  loadDraftFeedback: () => void;
  clearDraftFeedback: () => void;
  
  // Actions - UI Management
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
  setConnected: (connected: boolean) => void;
  
  // Actions - Cleanup
  clearStore: () => void;
}

export const useFeedbackStore = create<FeedbackStore>()(
  persist(
    (set, get) => ({
      // Initial state
      feedback: [],
      featureRequests: [],
      userVotes: [],
      earlyAccessTokens: [],
      loading: false,
      error: null,
      submitting: false,
      syncStatus: 'idle',
      lastSyncAt: null,
      pendingSyncCount: 0,
      isConnected: true,
      draftFeedback: null,

      // Feedback Management Actions
      submitFeedback: async (formData: FeedbackFormData) => {
        const service = FeedbackService.getInstance();
        
        try {
          set({ submitting: true, error: null });
          
          const feedback = await service.submitFeedback(formData);
          
          set(state => ({
            feedback: [feedback, ...state.feedback],
            submitting: false,
            pendingSyncCount: state.pendingSyncCount + 1,
            draftFeedback: null // Clear draft after successful submission
          }));
          
          // Trigger sync if connected
          if (get().isConnected) {
            get().syncToSupabase();
          }
          
        } catch (error) {
          console.error('Failed to submit feedback:', error);
          set({ 
            submitting: false, 
            error: error instanceof Error ? error.message : 'Failed to submit feedback'
          });
          throw error;
        }
      },

      loadUserFeedback: async () => {
        const service = FeedbackService.getInstance();
        
        try {
          set({ loading: true, error: null });
          
          const feedback = await service.getUserFeedback();
          const pendingCount = await service.getPendingSyncCount();
          
          set({ 
            feedback, 
            loading: false,
            pendingSyncCount: pendingCount
          });
          
        } catch (error) {
          console.error('Failed to load feedback:', error);
          set({ 
            loading: false, 
            error: 'Failed to load feedback'
          });
        }
      },

      refreshFeedback: async () => {
        await get().loadUserFeedback();
      },

      // Voting Actions
      submitVote: async (voteData: VoteSubmissionData) => {
        const service = FeedbackService.getInstance();
        
        try {
          set({ loading: true, error: null });
          
          const vote = await service.submitVote(voteData);
          
          set(state => {
            // Update or add vote to user votes
            const existingVoteIndex = state.userVotes.findIndex(
              v => v.featureRequestId === voteData.featureRequestId
            );
            
            let updatedVotes: FeatureVote[];
            if (existingVoteIndex !== -1) {
              updatedVotes = [...state.userVotes];
              updatedVotes[existingVoteIndex] = vote;
            } else {
              updatedVotes = [vote, ...state.userVotes];
            }
            
            // Update feature request vote count locally
            const updatedFeatureRequests = state.featureRequests.map(fr => {
              if (fr.id === voteData.featureRequestId) {
                const previousVote = state.userVotes.find(v => v.featureRequestId === fr.id);
                let voteChange = voteData.voteValue;
                
                // If user had previous vote, subtract it first
                if (previousVote) {
                  voteChange = voteData.voteValue - previousVote.voteValue;
                }
                
                return {
                  ...fr,
                  totalVotes: fr.totalVotes + voteChange,
                  userVote: voteData.voteValue
                };
              }
              return fr;
            });
            
            return {
              userVotes: updatedVotes,
              featureRequests: updatedFeatureRequests,
              loading: false,
              pendingSyncCount: state.pendingSyncCount + 1
            };
          });
          
          // Trigger sync if connected
          if (get().isConnected) {
            get().syncToSupabase();
          }
          
        } catch (error) {
          console.error('Failed to submit vote:', error);
          set({ 
            loading: false, 
            error: 'Failed to submit vote'
          });
          throw error;
        }
      },

      loadUserVotes: async () => {
        const service = FeedbackService.getInstance();
        
        try {
          const votes = await service.getUserVotes();
          set({ userVotes: votes });
        } catch (error) {
          console.error('Failed to load user votes:', error);
          set({ error: 'Failed to load votes' });
        }
      },

      loadFeatureRequests: async () => {
        // This would typically load from Supabase or cache
        // For now, just clear loading state
        set({ loading: false });
        
        // TODO: Implement feature request loading from Supabase
        console.log('Feature request loading not implemented yet');
      },

      // Early Access Actions
      loadEarlyAccessTokens: async () => {
        const service = FeedbackService.getInstance();
        
        try {
          const tokens = await service.getEarlyAccessTokens();
          set({ earlyAccessTokens: tokens });
        } catch (error) {
          console.error('Failed to load early access tokens:', error);
          set({ error: 'Failed to load early access tokens' });
        }
      },

      checkEarlyAccess: async (tokenType: 'contributor' | 'community_wave') => {
        const service = FeedbackService.getInstance();
        
        try {
          return await service.hasEarlyAccess(tokenType);
        } catch (error) {
          console.error('Failed to check early access:', error);
          return false;
        }
      },

      // Sync Management Actions
      syncToSupabase: async () => {
        // TODO: Implement Supabase sync when Task 6 is completed
        set({ syncStatus: 'syncing' });
        
        try {
          // Placeholder for sync logic
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          set({ 
            syncStatus: 'idle',
            lastSyncAt: new Date(),
            pendingSyncCount: 0
          });
          
        } catch (error) {
          console.error('Sync failed:', error);
          set({ 
            syncStatus: 'error',
            error: 'Sync failed'
          });
        }
      },

      handleStatusUpdate: (update: FeedbackStatusUpdate) => {
        set(state => ({
          feedback: state.feedback.map(f => {
            if (f.supabaseId === update.id) {
              return {
                ...f,
                status: update.status,
                adminFeedback: update.adminFeedback || f.adminFeedback
              };
            }
            return f;
          })
        }));
      },

      retryFailedSync: async () => {
        const service = FeedbackService.getInstance();
        
        try {
          const failedOperations = await service.getFailedSyncOperations();
          
          if (failedOperations.length > 0) {
            await get().syncToSupabase();
          }
          
        } catch (error) {
          console.error('Failed to retry sync:', error);
          set({ error: 'Failed to retry sync' });
        }
      },

      // Draft Management Actions
      saveDraftFeedback: (draft: Partial<FeedbackFormData>) => {
        set({ draftFeedback: draft });
      },

      loadDraftFeedback: () => {
        // Draft is automatically loaded from persistence
      },

      clearDraftFeedback: () => {
        set({ draftFeedback: null });
      },

      // UI Management Actions
      setError: (error: string | null) => {
        set({ error });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setConnected: (connected: boolean) => {
        set({ isConnected: connected });
        
        // Auto-sync when connection is restored
        if (connected && get().pendingSyncCount > 0) {
          get().syncToSupabase();
        }
      },

      // Cleanup Actions
      clearStore: () => {
        set({
          feedback: [],
          featureRequests: [],
          userVotes: [],
          earlyAccessTokens: [],
          loading: false,
          error: null,
          submitting: false,
          syncStatus: 'idle',
          lastSyncAt: null,
          pendingSyncCount: 0,
          draftFeedback: null
        });
      }
    }),
    {
      name: 'feedback-store',
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist essential data, not loading states
      partialize: (state) => ({
        feedback: state.feedback,
        userVotes: state.userVotes,
        earlyAccessTokens: state.earlyAccessTokens,
        lastSyncAt: state.lastSyncAt,
        pendingSyncCount: state.pendingSyncCount,
        draftFeedback: state.draftFeedback
      }),
      // Rehydrate sync status after load
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.loading = false;
          state.submitting = false;
          state.syncStatus = 'idle';
          state.error = null;
          state.isConnected = true;
        }
      }
    }
  )
);

// Selector hooks for optimized re-renders
export const useFeedbackSubmissions = () => useFeedbackStore(state => state.feedback);
export const useFeatureRequests = () => useFeedbackStore(state => state.featureRequests);
export const useUserVotes = () => useFeedbackStore(state => state.userVotes);
export const useEarlyAccessTokens = () => useFeedbackStore(state => state.earlyAccessTokens);
export const useFeedbackLoading = () => useFeedbackStore(state => state.loading);
export const useFeedbackError = () => useFeedbackStore(state => state.error);
export const useFeedbackSyncStatus = () => useFeedbackStore(state => ({
  status: state.syncStatus,
  lastSyncAt: state.lastSyncAt,
  pendingCount: state.pendingSyncCount
}));
export const useDraftFeedback = () => useFeedbackStore(state => state.draftFeedback);