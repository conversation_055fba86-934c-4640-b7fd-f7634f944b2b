import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthenticationManager, AuthMethod } from '../../platform/biometric/AuthenticationManager';
import { BiometricAuthService } from '../../platform/biometric/BiometricAuthService';
import { AppState } from 'react-native';

export interface AuthState {
  // Authentication status
  isAuthenticated: boolean;
  isLocked: boolean;
  authMethod: AuthMethod;
  
  // Timeout and locking
  lockTimeout: number; // seconds: 0=immediate, 60=1min, 300=5min, 900=15min
  lastActiveTime: number;
  lockoutUntil: number | null;
  
  // Failed attempts tracking
  failedAttempts: number;
  maxFailedAttempts: number;
  
  // Onboarding and setup
  onboardingCompleted: boolean;
  biometricEnabled: boolean;
  
  // App state management
  appState: string;
  backgroundTime: number | null;
}

export interface AuthActions {
  // Authentication actions
  authenticate: (credential?: string | number[]) => Promise<boolean>;
  lock: () => void;
  unlock: () => void;
  logout: () => Promise<void>;
  
  // Setup actions
  setAuthMethod: (method: AuthMethod, credential?: string | number[]) => Promise<boolean>;
  setupBiometric: () => Promise<boolean>;
  
  // Configuration actions
  setLockTimeout: (seconds: number) => Promise<void>;
  setBiometricEnabled: (enabled: boolean) => Promise<void>;
  completeOnboarding: () => Promise<void>;
  
  // State management
  updateLastActiveTime: () => void;
  resetFailedAttempts: () => void;
  incrementFailedAttempts: () => void;
  checkLockTimeout: () => boolean;
  
  // App state handling
  handleAppStateChange: (nextAppState: string) => void;
  handleBackground: () => void;
  handleForeground: () => void;
  
  // Persistence actions
  hydrate: () => Promise<void>;
  reset: () => Promise<void>;
}

export type AuthStore = AuthState & AuthActions;

const TIMEOUT_OPTIONS = {
  IMMEDIATE: 0,
  ONE_MINUTE: 60,
  FIVE_MINUTES: 300,
  FIFTEEN_MINUTES: 900,
} as const;

const MAX_FAILED_ATTEMPTS = 6;
const LOCKOUT_DURATIONS = [0, 0, 0, 30000, 60000, 300000]; // 0, 0, 0, 30s, 1min, 5min in ms

const initialState: AuthState = {
  isAuthenticated: false,
  isLocked: true,
  authMethod: 'none',
  lockTimeout: TIMEOUT_OPTIONS.FIVE_MINUTES,
  lastActiveTime: Date.now(),
  lockoutUntil: null,
  failedAttempts: 0,
  maxFailedAttempts: MAX_FAILED_ATTEMPTS,
  onboardingCompleted: false,
  biometricEnabled: false,
  appState: AppState.currentState,
  backgroundTime: null,
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Authentication actions
      authenticate: async (credential?: string | number[]) => {
        const state = get();
        
        try {
          // Check lockout
          if (state.lockoutUntil && Date.now() < state.lockoutUntil) {
            return false;
          }

          const result = await AuthenticationManager.authenticate(
            state.authMethod,
            credential,
            { bypassLockout: false }
          );

          if (result.success) {
            set({
              isAuthenticated: true,
              isLocked: false,
              failedAttempts: 0,
              lockoutUntil: null,
              lastActiveTime: Date.now(),
            });
            return true;
          } else {
            // Handle failed attempt
            const newFailedAttempts = state.failedAttempts + 1;
            let lockoutUntil: number | null = null;

            if (newFailedAttempts >= 3) {
              const lockoutIndex = Math.min(newFailedAttempts - 1, LOCKOUT_DURATIONS.length - 1);
              const lockoutDuration = LOCKOUT_DURATIONS[lockoutIndex];
              lockoutUntil = lockoutDuration > 0 ? Date.now() + lockoutDuration : null;
            }

            set({
              failedAttempts: newFailedAttempts,
              lockoutUntil,
            });
            return false;
          }
        } catch (error) {
          console.error('Authentication error:', error);
          get().incrementFailedAttempts();
          return false;
        }
      },

      lock: () => {
        set({
          isAuthenticated: false,
          isLocked: true,
          lastActiveTime: Date.now(),
        });
      },

      unlock: () => {
        set({
          isAuthenticated: true,
          isLocked: false,
          lastActiveTime: Date.now(),
        });
      },

      logout: async () => {
        try {
          // Clear any stored auth data if needed
          await AuthenticationManager.resetFailedAttempts();
          
          set({
            ...initialState,
            onboardingCompleted: get().onboardingCompleted, // Keep onboarding state
            authMethod: get().authMethod, // Keep auth method
            lockTimeout: get().lockTimeout, // Keep timeout preference
          });
        } catch (error) {
          console.error('Logout error:', error);
        }
      },

      // Setup actions
      setAuthMethod: async (method: AuthMethod, credential?: string | number[]) => {
        try {
          const result = await AuthenticationManager.setupAuthMethod(method, credential);
          
          if (result.success) {
            set({
              authMethod: method,
              biometricEnabled: method === 'biometric',
            });
            return true;
          }
          return false;
        } catch (error) {
          console.error('Setup auth method error:', error);
          return false;
        }
      },

      setupBiometric: async () => {
        try {
          const capabilities = await BiometricAuthService.detectCapabilities();
          
          if (capabilities.isAvailable) {
            const result = await BiometricAuthService.authenticate('Setup biometric authentication');
            
            if (result.success) {
              set({
                authMethod: 'biometric',
                biometricEnabled: true,
              });
              return true;
            }
          }
          return false;
        } catch (error) {
          console.error('Biometric setup error:', error);
          return false;
        }
      },

      // Configuration actions
      setLockTimeout: async (seconds: number) => {
        set({ lockTimeout: seconds });
      },

      setBiometricEnabled: async (enabled: boolean) => {
        const state = get();
        
        if (enabled && state.authMethod !== 'biometric') {
          const success = await get().setupBiometric();
          if (!success) return;
        }
        
        set({ biometricEnabled: enabled });
      },

      completeOnboarding: async () => {
        set({ onboardingCompleted: true });
      },

      // State management
      updateLastActiveTime: () => {
        set({ lastActiveTime: Date.now() });
      },

      resetFailedAttempts: () => {
        set({
          failedAttempts: 0,
          lockoutUntil: null,
        });
      },

      incrementFailedAttempts: () => {
        const state = get();
        const newFailedAttempts = state.failedAttempts + 1;
        let lockoutUntil: number | null = null;

        if (newFailedAttempts >= 3) {
          const lockoutIndex = Math.min(newFailedAttempts - 1, LOCKOUT_DURATIONS.length - 1);
          const lockoutDuration = LOCKOUT_DURATIONS[lockoutIndex];
          lockoutUntil = lockoutDuration > 0 ? Date.now() + lockoutDuration : null;
        }

        set({
          failedAttempts: newFailedAttempts,
          lockoutUntil,
        });
      },

      checkLockTimeout: () => {
        const state = get();
        
        if (!state.isAuthenticated || state.lockTimeout === 0) {
          return false;
        }

        const timeSinceLastActive = Date.now() - state.lastActiveTime;
        const timeoutMs = state.lockTimeout * 1000;
        
        if (timeSinceLastActive >= timeoutMs) {
          get().lock();
          return true;
        }
        
        return false;
      },

      // App state handling
      handleAppStateChange: (nextAppState: string) => {
        const currentAppState = get().appState;
        
        if (currentAppState === 'active' && nextAppState.match(/inactive|background/)) {
          get().handleBackground();
        } else if (currentAppState.match(/inactive|background/) && nextAppState === 'active') {
          get().handleForeground();
        }
        
        set({ appState: nextAppState });
      },

      handleBackground: () => {
        const state = get();
        
        set({ backgroundTime: Date.now() });
        
        // Lock immediately if timeout is set to immediate
        if (state.lockTimeout === TIMEOUT_OPTIONS.IMMEDIATE && state.isAuthenticated) {
          get().lock();
        }
      },

      handleForeground: () => {
        const state = get();
        
        if (state.backgroundTime) {
          const timeInBackground = Date.now() - state.backgroundTime;
          const timeoutMs = state.lockTimeout * 1000;
          
          // Check if we should lock due to timeout
          if (timeInBackground >= timeoutMs && state.isAuthenticated) {
            get().lock();
          }
        }
        
        set({ backgroundTime: null });
        get().updateLastActiveTime();
      },

      // Persistence actions
      hydrate: async () => {
        // This will be called after the store is rehydrated from persistence
        // Check if we should be locked due to time passed
        get().checkLockTimeout();
        
        // Update app state
        set({ appState: AppState.currentState });
      },

      reset: async () => {
        set({
          ...initialState,
          appState: AppState.currentState,
        });
      },
    }),
    {
      name: 'auth-store',
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist certain fields
      partialize: (state) => ({
        authMethod: state.authMethod,
        lockTimeout: state.lockTimeout,
        onboardingCompleted: state.onboardingCompleted,
        biometricEnabled: state.biometricEnabled,
        failedAttempts: state.failedAttempts,
        lockoutUntil: state.lockoutUntil,
      }),
      onRehydrateStorage: () => (state) => {
        // Called after rehydration is complete
        if (state) {
          state.hydrate();
        }
      },
    }
  )
);

// Timeout options for UI
export const TIMEOUT_OPTIONS_UI = [
  { label: 'Immediately', value: TIMEOUT_OPTIONS.IMMEDIATE },
  { label: '1 minute', value: TIMEOUT_OPTIONS.ONE_MINUTE },
  { label: '5 minutes', value: TIMEOUT_OPTIONS.FIVE_MINUTES },
  { label: '15 minutes', value: TIMEOUT_OPTIONS.FIFTEEN_MINUTES },
] as const;

// Helper functions
export const getRemainingLockoutTime = (lockoutUntil: number | null): number => {
  if (!lockoutUntil || Date.now() >= lockoutUntil) {
    return 0;
  }
  return Math.ceil((lockoutUntil - Date.now()) / 1000);
};

export const formatLockoutTime = (seconds: number): string => {
  if (seconds <= 0) return '';
  
  if (seconds < 60) {
    return `${seconds} seconds`;
  }
  
  const minutes = Math.ceil(seconds / 60);
  return `${minutes} minute${minutes > 1 ? 's' : ''}`;
};