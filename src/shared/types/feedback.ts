/**
 * Feedback and Feature Request Types
 * Story 1.9: Anonymous Feedback and Feature Requests
 * 
 * Type definitions for feedback system including local SQLite and Supabase cloud interfaces
 */

// Local SQLite interfaces
export interface FeedbackSubmission {
  id: number;
  supabaseId: string | undefined;
  category: 'bug_report' | 'feature_request' | 'general_feedback' | 'praise_complaint';
  title: string;
  description: string;
  priorityLevel: 'low' | 'medium' | 'high' | 'critical';
  deviceId: string;
  contactInfo: string | undefined; // encrypted
  rewardEligible: boolean;
  screenshotPath: string | undefined;
  submittedAt: Date;
  syncStatus: 'pending' | 'synced' | 'failed' | 'conflict';
  lastSyncedAt: Date | undefined;
  status: 'submitted' | 'acknowledged' | 'in_review' | 'implemented' | 'rejected';
  adminFeedback: string | undefined;
  version: number;
}

export interface FeatureVote {
  id: number;
  supabaseId: string | undefined;
  featureRequestId: string;
  deviceId: string;
  voteValue: 1 | -1;
  votedAt: Date;
  syncStatus: 'pending' | 'synced' | 'failed';
  lastSyncedAt: Date | undefined;
}

export interface EarlyAccessToken {
  id: number;
  supabaseId: string | undefined;
  deviceId: string;
  tokenType: 'contributor' | 'community_wave';
  grantedFor: string; // feedback_id or feature_id
  expiresAt: Date;
  createdAt: Date;
  syncStatus: 'pending' | 'synced' | 'failed';
  isActive: boolean;
}

// Supabase cloud interfaces  
export interface SupabaseFeedbackSubmission {
  id: string;
  category: 'bug_report' | 'feature_request' | 'general_feedback' | 'praise_complaint';
  title: string;
  description: string;
  priority_level: 'low' | 'medium' | 'high' | 'critical';
  device_id: string;
  contact_info?: string;
  reward_eligible: boolean;
  screenshot_url?: string;
  submitted_at: string;
  status: 'submitted' | 'acknowledged' | 'in_review' | 'implemented' | 'rejected';
  admin_feedback?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  version: number;
  created_at: string;
  updated_at: string;
}

export interface SupabaseFeatureVote {
  id: string;
  feedback_id: string;
  device_id: string;
  vote_value: 1 | -1;
  voted_at: string;
}

export interface SupabaseEarlyAccessToken {
  id: string;
  device_id: string;
  token_type: 'contributor' | 'community_wave';
  granted_for: string;
  expires_at: string;
  created_at: string;
  is_active: boolean;
}

// Sync operation interfaces
export interface SyncOperation {
  id: number;
  operationType: 'INSERT' | 'UPDATE' | 'DELETE';
  tableName: string;
  localId: number;
  payload: any;
  retryCount: number;
  createdAt: Date;
  lastAttemptAt: Date | undefined;
  errorMessage: string | undefined;
}

export interface SyncResult {
  localId: number;
  success: boolean;
  supabaseId?: string;
  error?: string;
}

// Status update interface for real-time updates
export interface FeedbackStatusUpdate {
  id: string;
  status: 'submitted' | 'acknowledged' | 'in_review' | 'implemented' | 'rejected';
  adminFeedback?: string;
  reviewedAt?: Date;
}

// Form submission interfaces
export interface FeedbackFormData {
  category: FeedbackSubmission['category'];
  title: string;
  description: string;
  priorityLevel: FeedbackSubmission['priorityLevel'];
  rewardEligible: boolean;
  contactInfo: string | undefined;
  screenshotPath: string | undefined;
}

export interface VoteSubmissionData {
  featureRequestId: string;
  voteValue: 1 | -1;
}

// API response interfaces
export interface FeedbackApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export interface FeatureRequestWithVotes {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  totalVotes: number;
  userVote?: 1 | -1;
  createdAt: Date;
  updatedAt: Date;
}

// Configuration interfaces
export interface FeedbackConfig {
  maxRetryAttempts: number;
  syncIntervalMs: number;
  maxScreenshotSizeMB: number;
  supportedImageFormats: string[];
  maxFeedbackLength: number;
  maxTitleLength: number;
}

// Device fingerprinting interface
export interface DeviceFingerprint {
  deviceId: string;
  platform: string;
  version: string;
  model?: string;
  isEmulator: boolean;
  createdAt: Date;
}

// Validation error interface
export interface FeedbackValidationError {
  field: keyof FeedbackFormData;
  message: string;
  code: string;
}

// Statistics interface for admin dashboard
export interface FeedbackStats {
  totalFeedback: number;
  byCategory: Record<FeedbackSubmission['category'], number>;
  byStatus: Record<FeedbackSubmission['status'], number>;
  byPriority: Record<FeedbackSubmission['priorityLevel'], number>;
  rewardEligibleCount: number;
  averageResponseTime: number; // in hours
  topFeatureRequests: FeatureRequestWithVotes[];
}

// Export utility type for creating new feedback submissions
export type CreateFeedbackSubmission = Omit<
  FeedbackSubmission, 
  'id' | 'syncStatus' | 'version' | 'submittedAt' | 'lastSyncedAt' | 'status' | 'adminFeedback' | 'supabaseId'
>;

export type CreateFeatureVote = Omit<
  FeatureVote,
  'id' | 'syncStatus' | 'votedAt' | 'lastSyncedAt'
>;

export type CreateEarlyAccessToken = Omit<
  EarlyAccessToken,
  'id' | 'syncStatus' | 'createdAt' | 'isActive'
>;