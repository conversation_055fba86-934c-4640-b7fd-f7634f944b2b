/**
 * Account Balance Calculator Types
 * 
 * Types for account-specific balance calculations and validation
 * Supporting different account types: credit cards, loans, checking, savings, investment
 */

import { AccountType } from './index';
import { Transaction } from './index';
import { Account } from './index';

/**
 * Interface for account balance calculation results
 * Contains both display balance and actual balance with metadata
 */
export interface AccountBalance {
  /** Balance to display to user (e.g., available credit for credit cards) */
  displayBalance: number;
  
  /** Actual underlying balance (e.g., outstanding balance for credit cards) */
  actualBalance: number;
  
  /** Account-specific metadata for the calculation */
  metadata: Record<string, any>;
  
  /** When this balance was calculated */
  lastCalculated: Date;
  
  /** Type of calculation performed */
  calculationType: 'checking' | 'savings' | 'credit' | 'loan' | 'investment';
}

/**
 * Interface for transaction validation results
 * Used by calculators to validate transactions against account rules
 */
export interface ValidationResult {
  /** Whether the transaction is valid */
  isValid: boolean;
  
  /** Array of error messages */
  errors: string[];
  
  /** Array of warning messages */
  warnings: string[];
  
  /** Suggested corrections for invalid transactions */
  suggestedCorrections?: Partial<Transaction> | undefined;
}

/**
 * Amortization schedule entry for loan calculations
 */
export interface AmortizationSchedule {
  /** EMI month number */
  month: number;
  
  /** EMI amount for this month */
  emi: number;
  
  /** Principal component of EMI */
  principalComponent: number;
  
  /** Interest component of EMI */
  interestComponent: number;
  
  /** Remaining principal after this EMI */
  remainingPrincipal: number;
}

/**
 * EMI component split result
 */
export interface EMIComponents {
  /** Principal component amount */
  principal: number;
  
  /** Interest component amount */
  interest: number;
}

/**
 * Credit card summary information
 */
export interface CreditCardSummary {
  /** Available credit remaining */
  availableCredit: number;
  
  /** Outstanding balance owed */
  outstandingBalance: number;
  
  /** Credit utilization percentage */
  creditUtilization: number;
  
  /** Minimum payment due */
  minimumPaymentDue: number;
  
  /** Credit limit */
  creditLimit: number;
  
  /** Next payment due date */
  paymentDueDate?: string;
}

/**
 * Loan summary information
 */
export interface LoanSummary {
  /** Remaining principal amount */
  remainingPrincipal: number;
  
  /** Next EMI due date */
  nextEMIDate: string;
  
  /** Monthly EMI amount */
  emiAmount: number;
  
  /** Remaining tenure in months */
  remainingTenure: number;
  
  /** Original loan amount */
  originalPrincipal: number;
  
  /** Total interest paid till date */
  totalInterestPaid: number;
}

/**
 * Calculator performance metrics
 */
export interface CalculatorMetrics {
  /** Time taken for calculation in milliseconds */
  calculationTime: number;
  
  /** Number of transactions processed */
  transactionCount: number;
  
  /** Calculator type used */
  calculatorType: string;
  
  /** Any performance warnings */
  warnings: string[];
}