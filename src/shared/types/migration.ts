/**
 * Migration System Types
 * 
 * Core TypeScript interfaces for the new dynamic migration system.
 * Provides type safety, validation, and comprehensive error handling.
 */

import type { DatabaseService } from '../../data/database/DatabaseService';

/**
 * Core migration definition interface
 */
export interface Migration {
  /** Unique sequential version number */
  version: number;
  
  /** Human-readable migration name (kebab-case recommended) */
  name: string;
  
  /** SQL statements to execute for migration */
  up: string;
  
  /** Optional rollback SQL statements */
  down?: string;
  
  /** Optional pre-flight validation function */
  validate?: (db: DatabaseService) => Promise<void>;
  
  /** Optional post-migration verification function */
  verify?: (db: DatabaseService) => Promise<boolean>;
  
  /** Migration metadata */
  metadata?: MigrationMetadata;
}

/**
 * Migration metadata for enhanced tracking
 */
export interface MigrationMetadata {
  /** Migration description */
  description?: string;
  
  /** Expected execution time in milliseconds */
  estimatedDuration?: number;
  
  /** Whether migration affects large datasets */
  isDataHeavy?: boolean;
  
  /** Required database features/extensions */
  requirements?: string[];
  
  /** Migration author/creator */
  author?: string;
  
  /** Creation timestamp */
  createdAt?: string;
  
  /** Breaking change indicator */
  isBreaking?: boolean;
}

/**
 * Migration execution status
 */
export type MigrationStatus = 
  | 'pending'     // Not yet executed
  | 'running'     // Currently executing
  | 'completed'   // Successfully executed
  | 'failed'      // Failed during execution
  | 'rolled_back' // Successfully rolled back
  | 'skipped';    // Skipped due to conditions

/**
 * Migration execution result
 */
export interface MigrationResult {
  /** Migration that was executed */
  migration: Migration;
  
  /** Execution status */
  status: MigrationStatus;
  
  /** Execution start time */
  startTime: Date;
  
  /** Execution end time */
  endTime?: Date;
  
  /** Duration in milliseconds */
  duration?: number;
  
  /** Error information if failed */
  error?: MigrationError;
  
  /** Memory usage statistics */
  memoryStats?: MigrationMemoryStats;
}

/**
 * Memory usage statistics for performance monitoring
 */
export interface MigrationMemoryStats {
  /** Memory usage before migration (MB) */
  beforeMB: number;
  
  /** Peak memory usage during migration (MB) */
  peakMB: number;
  
  /** Memory usage after migration (MB) */
  afterMB: number;
  
  /** Memory freed by garbage collection (MB) */
  freedMB?: number;
}

/**
 * Migration system status overview
 */
export interface MigrationSystemStatus {
  /** Current database version */
  currentVersion: number;
  
  /** Latest available migration version */
  latestVersion: number;
  
  /** Number of pending migrations */
  pendingCount: number;
  
  /** List of pending migrations */
  pendingMigrations: Migration[];
  
  /** Migration execution history */
  executionHistory: MigrationResult[];
  
  /** System health status */
  isHealthy: boolean;
  
  /** Last successful migration timestamp */
  lastMigrationAt?: Date;
  
  /** Performance metrics */
  performanceMetrics?: MigrationPerformanceMetrics;
}

/**
 * Performance metrics for monitoring
 */
export interface MigrationPerformanceMetrics {
  /** Average execution time per migration (ms) */
  averageExecutionTime: number;
  
  /** Total migrations executed */
  totalMigrations: number;
  
  /** Success rate (0-1) */
  successRate: number;
  
  /** Peak memory usage across all migrations (MB) */
  peakMemoryUsage: number;
  
  /** Database size before/after migrations */
  databaseSizeStats?: {
    beforeMB: number;
    afterMB: number;
    growthMB: number;
  };
}

/**
 * Migration error types and details
 */
export interface MigrationError {
  /** Error type classification */
  type: MigrationErrorType;
  
  /** Error message */
  message: string;
  
  /** SQL that caused the error */
  failedSQL?: string;
  
  /** Original error from database */
  originalError?: Error;
  
  /** Recovery suggestions */
  recoverySuggestions?: string[];
  
  /** Whether error is recoverable */
  isRecoverable: boolean;
  
  /** Error context */
  context?: MigrationErrorContext;
}

/**
 * Migration error classification
 */
export type MigrationErrorType =
  | 'sql_syntax'        // SQL syntax error
  | 'constraint_violation' // Database constraint violation
  | 'permission_denied' // Insufficient permissions
  | 'timeout'          // Migration timed out
  | 'memory_limit'     // Memory limit exceeded
  | 'disk_space'       // Insufficient disk space
  | 'corruption'       // Database corruption detected
  | 'version_conflict' // Version mismatch
  | 'validation_failed' // Pre-flight validation failed
  | 'rollback_failed'  // Rollback operation failed
  | 'unknown';         // Unclassified error

/**
 * Error context for debugging
 */
export interface MigrationErrorContext {
  /** Migration version that failed */
  migrationVersion: number;
  
  /** Database version before migration */
  databaseVersionBefore: number;
  
  /** Execution environment */
  environment: 'development' | 'production' | 'test';
  
  /** Device information */
  deviceInfo?: {
    platform: string;
    osVersion: string;
    appVersion: string;
    availableMemoryMB: number;
    availableStorageMB: number;
  };
  
  /** Concurrent operations */
  concurrentOperations?: string[];
}

/**
 * Migration discovery result
 */
export interface MigrationDiscoveryResult {
  /** All discovered migrations */
  migrations: Migration[];
  
  /** Discovery warnings */
  warnings: string[];
  
  /** Validation errors */
  errors: string[];
  
  /** Discovery timestamp */
  discoveredAt: Date;
  
  /** Total files scanned */
  filesScanned: number;
}

/**
 * Migration validation result
 */
export interface MigrationValidationResult {
  /** Whether migration is valid */
  isValid: boolean;
  
  /** Validation errors */
  errors: string[];
  
  /** Validation warnings */
  warnings: string[];
  
  /** Safety score (0-1) */
  safetyScore: number;
  
  /** Performance impact assessment */
  performanceImpact: 'low' | 'medium' | 'high';
  
  /** Estimated execution time (ms) */
  estimatedDuration: number;
  
  /** Required resources */
  resourceRequirements: MigrationResourceRequirements;
}

/**
 * Resource requirements for migration execution
 */
export interface MigrationResourceRequirements {
  /** Estimated memory usage (MB) */
  memoryMB: number;
  
  /** Estimated disk space needed (MB) */
  diskSpaceMB: number;
  
  /** Estimated execution time (ms) */
  executionTimeMS: number;
  
  /** Whether migration blocks database access */
  blocksDatabase: boolean;
  
  /** Required SQLite features */
  sqliteFeatures: string[];
}

/**
 * Migration configuration options
 */
export interface MigrationConfig {
  /** Migration files directory path */
  migrationsPath: string;
  
  /** Timeout for individual migrations (ms) */
  migrationTimeout: number;
  
  /** Memory limit for migrations (MB) */
  memoryLimitMB: number;
  
  /** Whether to enable rollback on failure */
  enableRollback: boolean;
  
  /** Whether to enable pre-flight validation */
  enableValidation: boolean;
  
  /** Whether to collect performance metrics */
  enableMetrics: boolean;
  
  /** Development mode settings */
  development?: {
    /** Enable hot reload support */
    enableHotReload: boolean;
    
    /** Allow destructive operations */
    allowDestructive: boolean;
    
    /** Enable debug logging */
    enableDebugLog: boolean;
  };
  
  /** Production mode settings */
  production?: {
    /** Enable backup before major migrations */
    enableBackup: boolean;
    
    /** Maximum allowed downtime (ms) */
    maxDowntimeMS: number;
    
    /** Enable monitoring integration */
    enableMonitoring: boolean;
  };
}

/**
 * Migration manager events
 */
export type MigrationEvent = 
  | 'migration_started'
  | 'migration_completed'  
  | 'migration_failed'
  | 'migration_rolled_back'
  | 'all_migrations_completed'
  | 'validation_failed'
  | 'progress_updated';

/**
 * Migration event payload
 */
export interface MigrationEventPayload {
  /** Event type */
  event: MigrationEvent;
  
  /** Migration involved */
  migration?: Migration;
  
  /** Migration result */
  result?: MigrationResult;
  
  /** Progress information */
  progress?: MigrationProgress;
  
  /** Error information */
  error?: MigrationError;
  
  /** Event timestamp */
  timestamp: Date;
}

/**
 * Migration progress information
 */
export interface MigrationProgress {
  /** Current migration being executed */
  currentMigration: number;
  
  /** Total migrations to execute */
  totalMigrations: number;
  
  /** Progress percentage (0-100) */
  percentage: number;
  
  /** Current step description */
  currentStep: string;
  
  /** Estimated time remaining (ms) */
  estimatedTimeRemainingMS?: number;
  
  /** Completed migrations */
  completedMigrations: number[];
  
  /** Failed migrations */
  failedMigrations: number[];
}

/**
 * Migration file info for auto-discovery
 */
export interface MigrationFileInfo {
  /** File path */
  filePath: string;
  
  /** Extracted version from filename */
  version: number;
  
  /** Extracted name from filename */
  name: string;
  
  /** File modification time */
  modifiedAt: Date;
  
  /** Whether file follows naming convention */
  isValidNaming: boolean;
  
  /** File size in bytes */
  sizeBytes: number;
}

/**
 * Migration batch for group execution
 */
export interface MigrationBatch {
  /** Batch identifier */
  batchId: string;
  
  /** Migrations in this batch */
  migrations: Migration[];
  
  /** Whether batch can be executed in parallel */
  canExecuteInParallel: boolean;
  
  /** Batch execution order */
  order: number;
  
  /** Batch dependencies */
  dependencies?: string[];
}