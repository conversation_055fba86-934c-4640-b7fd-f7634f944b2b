import { AccountMetadata } from './accountMetadata';

export interface Account {
  id: number;
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'loan' | 'investment';
  balance: number; // Keep for backward compatibility
  currency: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  sync_status: 'local' | 'synced' | 'pending' | 'conflict';
  
  // New field for account-specific metadata
  metadata?: AccountMetadata;
}

// Enhanced Transaction Types
export type TransactionType = 
  // Existing types
  | 'income' 
  | 'expense' 
  | 'transfer'
  
  // Credit Card Types
  | 'credit_payment'      // Credit card payment
  | 'credit_charge'       // Credit card purchase/charge
  | 'credit_interest'     // Credit card interest charges
  | 'credit_fee'          // Credit card fees (annual, late payment, etc.)
  
  // Loan Types  
  | 'loan_emi'           // Loan EMI payment
  | 'loan_interest'      // Interest component of payment
  | 'loan_principal'     // Principal component of payment
  | 'loan_fee'           // Loan processing fees
  | 'loan_prepayment';   // Loan prepayment

// Transaction Metadata for account-specific details
export interface TransactionMetadata {
  // Credit Card specific
  creditCardDetails?: {
    merchantName?: string;
    merchantCategory?: string;
    rewardPoints?: number;
    availableCreditAfterTransaction?: number;
    installmentInfo?: {
      isInstallment: boolean;
      installmentNumber?: number;
      totalInstallments?: number;
    };
  };
  
  // Loan specific
  loanDetails?: {
    principalComponent?: number;
    interestComponent?: number;
    feeComponent?: number;
    remainingPrincipalAfterPayment?: number;
    emiNumber?: number;
    paymentType?: 'regular_emi' | 'prepayment' | 'part_payment';
  };
  
  // SMS parsing metadata
  smsDetails?: {
    originalText?: string;
    extractedFields?: Record<string, any>;
    parsingConfidence?: number; // 0-1 score
    patternMatched?: string;
    bankIdentifier?: string;
  };
  
  // Common fields
  lastUpdated?: string;
  source?: 'sms' | 'manual' | 'import';
}

export interface Transaction {
  id: number;
  account_id: number;
  amount: number;
  description: string;
  category_id: number | null;
  transaction_type: TransactionType;
  transaction_date: string;
  created_at: string;
  updated_at: string;
  sms_source: string | null;
  confidence_score: number | null;
  is_recurring: boolean;
  recurring_pattern: string | null;
  sync_status: 'local' | 'synced' | 'pending' | 'conflict';
  hash: string;
  
  // New field for account-specific metadata
  metadata?: TransactionMetadata;
}

export interface Category {
  id: number;
  name: string;
  parent_id: number | null;
  category_type: 'income' | 'expense';
  color_code: string;
  icon_name: string;
  is_system: boolean;
  created_at: string;
  sync_status: 'local' | 'synced' | 'pending' | 'conflict';
}

export interface DatabaseError {
  code: string;
  message: string;
  details?: string;
}

export interface AppSettings {
  currency: string;
  language: string;
  theme: 'light' | 'dark' | 'system';
  biometric_enabled: boolean;
  sync_enabled: boolean;
  notification_enabled: boolean;
}

export interface CategorySuggestion {
  categoryId: number;
  categoryName: string;
  confidence: number;
  reason: 'keyword_match' | 'frequent_pattern' | 'amount_pattern' | 'ml_suggestion' | 'merchant_pattern';
}

export interface TransactionValidationResult {
  isValid: boolean;
  errors: string[];
}

// Re-export types from accountMetadata for convenience
export type { AccountMetadata, AccountType, ValidationResult, AccountCalculatedFields } from './accountMetadata';

// Re-export types from feedback for convenience
export type { 
  FeedbackSubmission, 
  FeatureVote, 
  EarlyAccessToken,
  FeedbackFormData,
  VoteSubmissionData,
  FeedbackStatusUpdate,
  FeatureRequestWithVotes,
  DeviceFingerprint
} from './feedback';

// Re-export types from migration system
export type {
  Migration,
  MigrationMetadata,
  MigrationStatus,
  MigrationResult,
  MigrationSystemStatus,
  MigrationError,
  MigrationErrorType,
  MigrationConfig,
  MigrationEvent,
  MigrationEventPayload,
  MigrationProgress,
  MigrationValidationResult,
  MigrationDiscoveryResult,
  MigrationFileInfo,
  MigrationBatch,
  MigrationPerformanceMetrics,
  MigrationMemoryStats
} from './migration';