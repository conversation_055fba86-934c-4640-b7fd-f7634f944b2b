export interface AccountMetadata {
  // Credit Card Specific Fields
  creditLimit?: number;
  outstandingBalance?: number;
  availableCredit?: number; // Calculated field: creditLimit - outstandingBalance
  minimumPaymentDue?: number;
  paymentDueDate?: string; // ISO date format
  lastStatementDate?: string;
  creditUtilization?: number; // Calculated: (outstandingBalance / creditLimit) * 100
  
  // Loan Specific Fields
  principalAmount?: number; // Original loan amount
  currentPrincipal?: number; // Remaining principal
  interestRate?: number; // Annual interest rate percentage
  emiAmount?: number; // Monthly EMI amount
  tenure?: number; // Total tenure in months
  remainingTenure?: number; // Remaining months
  nextEmiDate?: string; // Next EMI due date
  loanStartDate?: string; // Loan disbursement date
  totalInterestPaid?: number; // Interest paid till date
  
  // Common Fields
  lastUpdated?: string;
  autoCalculate?: boolean; // Whether to auto-calculate derived fields
}

export type AccountType = 'checking' | 'savings' | 'credit' | 'loan' | 'investment';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface AccountCalculatedFields {
  availableCredit: number;
  creditUtilization: number;
}