import { AccountMetadata, AccountType, ValidationResult } from '../types/accountMetadata';

export class AccountValidationUtils {
  static validateAccountMetadata(accountType: AccountType, metadata: AccountMetadata): ValidationResult {
    const errors: string[] = [];
    
    // Credit card specific validation
    if (accountType === 'credit') {
      if (metadata.creditLimit !== undefined && metadata.creditLimit <= 0) {
        errors.push('Credit limit must be positive for credit card accounts');
      }
      
      if (metadata.outstandingBalance !== undefined && metadata.outstandingBalance < 0) {
        errors.push('Outstanding balance cannot be negative');
      }
      
      if (metadata.minimumPaymentDue !== undefined && metadata.minimumPaymentDue < 0) {
        errors.push('Minimum payment due cannot be negative');
      }
      
      if (metadata.paymentDueDate && !this.isValidISODate(metadata.paymentDueDate)) {
        errors.push('Payment due date must be a valid ISO date string');
      }
      
      if (metadata.lastStatementDate && !this.isValidISODate(metadata.lastStatementDate)) {
        errors.push('Last statement date must be a valid ISO date string');
      }
    }
    
    // Loan specific validation
    if (accountType === 'loan') {
      if (metadata.principalAmount !== undefined && metadata.principalAmount <= 0) {
        errors.push('Principal amount must be positive for loan accounts');
      }
      
      if (metadata.currentPrincipal !== undefined && metadata.currentPrincipal < 0) {
        errors.push('Current principal cannot be negative');
      }
      
      if (metadata.interestRate !== undefined && (metadata.interestRate < 0 || metadata.interestRate > 100)) {
        errors.push('Interest rate must be between 0-100% for loan accounts');
      }
      
      if (metadata.emiAmount !== undefined && metadata.emiAmount <= 0) {
        errors.push('EMI amount must be positive for loan accounts');
      }
      
      if (metadata.tenure !== undefined && metadata.tenure <= 0) {
        errors.push('Tenure must be positive for loan accounts');
      }
      
      if (metadata.remainingTenure !== undefined && metadata.remainingTenure < 0) {
        errors.push('Remaining tenure cannot be negative');
      }
      
      if (metadata.nextEmiDate && !this.isValidISODate(metadata.nextEmiDate)) {
        errors.push('Next EMI date must be a valid ISO date string');
      }
      
      if (metadata.loanStartDate && !this.isValidISODate(metadata.loanStartDate)) {
        errors.push('Loan start date must be a valid ISO date string');
      }
      
      if (metadata.totalInterestPaid !== undefined && metadata.totalInterestPaid < 0) {
        errors.push('Total interest paid cannot be negative');
      }
    }
    
    // Common field validation
    if (metadata.lastUpdated && !this.isValidISODate(metadata.lastUpdated)) {
      errors.push('Last updated must be a valid ISO date string');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  static calculateCreditFields(metadata: AccountMetadata): Partial<AccountMetadata> {
    const calculated: Partial<AccountMetadata> = {};
    
    if (metadata.creditLimit !== undefined && metadata.outstandingBalance !== undefined) {
      calculated.availableCredit = metadata.creditLimit - metadata.outstandingBalance;
      calculated.creditUtilization = (metadata.outstandingBalance / metadata.creditLimit) * 100;
    }
    
    return calculated;
  }
  
  static isValidISODate(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime()) && dateString.includes('T');
  }
  
  static isAccountTypeSupported(accountType: AccountType): boolean {
    return ['checking', 'savings', 'credit', 'loan', 'investment'].includes(accountType);
  }
  
  static requiresMetadata(accountType: AccountType): boolean {
    return accountType === 'credit' || accountType === 'loan';
  }
}