import { Logger } from './Logger';

export type ErrorCategory = 
  | 'database'
  | 'network'
  | 'validation'
  | 'authentication'
  | 'permission'
  | 'sms_processing'
  | 'ml_inference'
  | 'file_system'
  | 'unknown';

export interface AppError extends Error {
  category: ErrorCategory;
  code: string;
  isRetryable: boolean;
  metadata?: Record<string, any>;
  originalError?: Error;
}

export interface ErrorRecoveryStrategy {
  canRecover: (error: AppError) => boolean;
  recover: (error: AppError) => Promise<void>;
  maxRetries: number;
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private logger: Logger;
  private recoveryStrategies: ErrorRecoveryStrategy[] = [];

  private constructor() {
    this.logger = Logger.getInstance();
    this.setupDefaultRecoveryStrategies();
  }

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  private setupDefaultRecoveryStrategies(): void {
    // Database retry strategy
    this.addRecoveryStrategy({
      canRecover: (error: AppError) => 
        error.category === 'database' && error.code === 'DB_TIMEOUT',
      recover: async (error: AppError) => {
        this.logger.info('Attempting database recovery', 'error-recovery');
        // Wait a bit and let connection pool recover
        await new Promise(resolve => setTimeout(resolve, 1000));
      },
      maxRetries: 3
    });

    // Network retry strategy
    this.addRecoveryStrategy({
      canRecover: (error: AppError) => 
        error.category === 'network' && error.isRetryable,
      recover: async (error: AppError) => {
        this.logger.info('Attempting network recovery', 'error-recovery');
        await new Promise(resolve => setTimeout(resolve, 2000));
      },
      maxRetries: 3
    });
  }

  public addRecoveryStrategy(strategy: ErrorRecoveryStrategy): void {
    this.recoveryStrategies.push(strategy);
  }

  public createError(
    message: string,
    category: ErrorCategory,
    code: string,
    isRetryable: boolean = false,
    metadata?: Record<string, any>,
    originalError?: Error
  ): AppError {
    const error = new Error(message) as AppError;
    error.category = category;
    error.code = code;
    error.isRetryable = isRetryable;
    if (metadata !== undefined) error.metadata = metadata;
    if (originalError !== undefined) error.originalError = originalError;
    
    return error;
  }

  public async handleError(error: Error | AppError, context?: string): Promise<boolean> {
    const appError = this.normalizeError(error);
    
    // Log the error
    this.logger.error(
      appError.message,
      appError.category,
      {
        code: appError.code,
        isRetryable: appError.isRetryable,
        context,
        metadata: appError.metadata,
        originalError: appError.originalError?.message
      },
      appError
    );

    // Attempt recovery if possible
    if (appError.isRetryable) {
      const recovered = await this.attemptRecovery(appError);
      if (recovered) {
        this.logger.info(
          `Successfully recovered from error: ${appError.code}`,
          'error-recovery'
        );
        return true;
      }
    }

    // Handle graceful degradation
    this.handleGracefulDegradation(appError);
    
    return false;
  }

  private normalizeError(error: Error | AppError): AppError {
    if (this.isAppError(error)) {
      return error;
    }

    // Convert standard errors to AppError
    let category: ErrorCategory = 'unknown';
    let code = 'UNKNOWN_ERROR';
    let isRetryable = false;

    // Classify common errors
    if (error.message.includes('database') || error.message.includes('SQL')) {
      category = 'database';
      code = 'DB_ERROR';
      isRetryable = true;
    } else if (error.message.includes('network') || error.message.includes('fetch')) {
      category = 'network';
      code = 'NETWORK_ERROR';
      isRetryable = true;
    } else if (error.message.includes('permission')) {
      category = 'permission';
      code = 'PERMISSION_DENIED';
      isRetryable = false;
    }

    const appError = new Error(error.message) as AppError;
    appError.category = category;
    appError.code = code;
    appError.isRetryable = isRetryable;
    appError.originalError = error;
    
    return appError;
  }

  private isAppError(error: Error | AppError): error is AppError {
    return 'category' in error && 'code' in error && 'isRetryable' in error;
  }

  private async attemptRecovery(error: AppError, retryCount = 0): Promise<boolean> {
    const strategy = this.recoveryStrategies.find(s => s.canRecover(error));
    
    if (!strategy || retryCount >= strategy.maxRetries) {
      return false;
    }

    try {
      await strategy.recover(error);
      return true;
    } catch (recoveryError) {
      this.logger.warn(
        `Recovery attempt ${retryCount + 1} failed for ${error.code}`,
        'error-recovery',
        { recoveryError: (recoveryError as Error).message }
      );

      // Recursive retry with backoff
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      return this.attemptRecovery(error, retryCount + 1);
    }
  }

  private handleGracefulDegradation(error: AppError): void {
    switch (error.category) {
      case 'database':
        this.logger.warn(
          'Database error - enabling offline mode',
          'graceful-degradation'
        );
        // Could emit event to enable offline mode
        break;

      case 'network':
        this.logger.warn(
          'Network error - using cached data',
          'graceful-degradation'
        );
        // Could emit event to use cached data
        break;

      case 'sms_processing':
        this.logger.warn(
          'SMS processing error - falling back to manual entry',
          'graceful-degradation'
        );
        // Could show manual entry form
        break;

      case 'ml_inference':
        this.logger.warn(
          'ML inference error - using rule-based categorization',
          'graceful-degradation'
        );
        // Could fallback to simple rule-based categorization
        break;

      default:
        this.logger.warn(
          `Unhandled error category: ${error.category}`,
          'graceful-degradation'
        );
    }
  }

  // Specialized error creation methods
  public createDatabaseError(message: string, code: string, metadata?: Record<string, any>): AppError {
    return this.createError(message, 'database', code, true, metadata);
  }

  public createNetworkError(message: string, code: string, metadata?: Record<string, any>): AppError {
    return this.createError(message, 'network', code, true, metadata);
  }

  public createValidationError(message: string, field: string, value?: any): AppError {
    return this.createError(
      message, 
      'validation', 
      'VALIDATION_FAILED', 
      false, 
      { field, value }
    );
  }

  public createPermissionError(message: string, permission: string): AppError {
    return this.createError(
      message,
      'permission',
      'PERMISSION_DENIED',
      false,
      { permission }
    );
  }

  public createSMSProcessingError(message: string, smsContent?: string): AppError {
    return this.createError(
      message,
      'sms_processing',
      'SMS_PARSE_FAILED',
      true,
      { smsLength: smsContent?.length }
    );
  }

  // Error reporting and analytics
  public getErrorStatistics(): {
    totalErrors: number;
    errorsByCategory: Record<ErrorCategory, number>;
    recoveredErrors: number;
    recentErrors: AppError[];
  } {
    const logs = this.logger.getLogs('error');
    const errorsByCategory: Record<ErrorCategory, number> = {
      database: 0,
      network: 0,
      validation: 0,
      authentication: 0,
      permission: 0,
      sms_processing: 0,
      ml_inference: 0,
      file_system: 0,
      unknown: 0
    };

    const recoveredErrors = this.logger.getLogs('info').filter(
      log => log.category === 'error-recovery'
    ).length;

    logs.forEach(log => {
      const category = (log.metadata?.category as ErrorCategory) || 'unknown';
      if (category in errorsByCategory) {
        errorsByCategory[category]++;
      }
    });

    return {
      totalErrors: logs.length,
      errorsByCategory,
      recoveredErrors,
      recentErrors: logs.slice(-10) as unknown as AppError[]
    };
  }

  public async testErrorHandling(): Promise<void> {
    this.logger.info('Running error handling tests', 'testing');

    // Test different error categories
    const testErrors: AppError[] = [
      this.createDatabaseError('Test database timeout', 'DB_TIMEOUT'),
      this.createNetworkError('Test network failure', 'NETWORK_TIMEOUT'),
      this.createValidationError('Test validation failure', 'email'),
      this.createPermissionError('Test permission denied', 'SMS_READ'),
      this.createSMSProcessingError('Test SMS parsing failure')
    ];

    for (const error of testErrors) {
      const recovered = await this.handleError(error, 'error-handling-test');
      this.logger.info(
        `Test error ${error.code}: ${recovered ? 'recovered' : 'not recovered'}`,
        'testing'
      );
    }
  }
}