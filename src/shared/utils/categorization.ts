/**
 * Utility functions for transaction categorization and text processing
 */

export interface TokenizedTransaction {
  originalText: string;
  cleanedText: string;
  tokens: string[];
  merchantTokens: string[];
  amountTokens: string[];
  dateTokens: string[];
}

export interface CategoryKeyword {
  keyword: string;
  weight: number;
  categoryIds: number[];
}

/**
 * Tokenize and clean transaction description for categorization
 */
export function tokenizeTransactionDescription(description: string): TokenizedTransaction {
  const originalText = description.trim();
  
  // Clean the text: remove special characters, convert to lowercase
  const cleanedText = originalText
    .toLowerCase()
    .replace(/[^\w\s.-]/g, ' ') // Keep alphanumeric, spaces, dots, and hyphens
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  // Split into tokens
  const allTokens = cleanedText.split(' ').filter(token => token.length > 1);
  
  // Categorize tokens
  const tokens = allTokens.filter(token => 
    !isNumericToken(token) && 
    !isDateToken(token) && 
    !isCommonStopWord(token)
  );
  
  const merchantTokens = allTokens.filter(token => 
    token.length > 3 && 
    !isNumericToken(token) && 
    !isDateToken(token) &&
    !isCommonStopWord(token)
  );
  
  const amountTokens = allTokens.filter(isNumericToken);
  const dateTokens = allTokens.filter(isDateToken);

  return {
    originalText,
    cleanedText,
    tokens,
    merchantTokens,
    amountTokens,
    dateTokens
  };
}

/**
 * Check if a token represents a numeric value
 */
function isNumericToken(token: string): boolean {
  return /^\d+\.?\d*$/.test(token) || /^\d+[,]\d+\.?\d*$/.test(token);
}

/**
 * Check if a token represents a date
 */
function isDateToken(token: string): boolean {
  return /^\d{1,2}[/-]\d{1,2}[/-]\d{2,4}$/.test(token) ||
         /^\d{1,2}[/-]\d{1,2}$/.test(token) ||
         /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)$/i.test(token);
}

/**
 * Check if a token is a common stop word that should be filtered out
 */
function isCommonStopWord(token: string): boolean {
  const stopWords = [
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
    'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
    'before', 'after', 'above', 'below', 'between', 'among', 'within',
    'without', 'across', 'against', 'towards', 'per', 'via', 'near',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
    'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
    'can', 'must', 'shall', 'this', 'that', 'these', 'those', 'my', 'your',
    'his', 'her', 'its', 'our', 'their', 'me', 'you', 'him', 'her', 'us', 'them'
  ];
  
  return stopWords.includes(token);
}

/**
 * Calculate similarity between two tokenized descriptions
 */
export function calculateDescriptionSimilarity(
  tokens1: string[], 
  tokens2: string[]
): number {
  if (tokens1.length === 0 && tokens2.length === 0) return 1.0;
  if (tokens1.length === 0 || tokens2.length === 0) return 0.0;

  // Jaccard similarity: intersection / union
  const set1 = new Set(tokens1);
  const set2 = new Set(tokens2);
  
  const intersection = new Set([...set1].filter(x => set2.has(x)));
  const union = new Set([...set1, ...set2]);
  
  return intersection.size / union.size;
}

/**
 * Extract potential merchant names from transaction description
 */
export function extractMerchantNames(description: string): string[] {
  const tokenized = tokenizeTransactionDescription(description);
  
  // Look for patterns that might indicate merchant names
  const merchantPatterns = [
    // All caps words (e.g., "AMAZON", "WALMART")
    /\b[A-Z]{2,}\b/g,
    // Words followed by common merchant suffixes
    /\b\w+(?:\s+(?:INC|LLC|CORP|CO|LTD|PVT|PRIVATE|LIMITED))\b/gi,
    // Common patterns in transaction descriptions
    /\b\w+\*\w+/g, // e.g., "SQ*STARBUCKS"
    /\b[A-Z]\w+\d+/g // e.g., "UBER123"
  ];
  
  const merchants: string[] = [];
  
  for (const pattern of merchantPatterns) {
    const matches = description.match(pattern);
    if (matches) {
      merchants.push(...matches.map(m => m.trim().toLowerCase()));
    }
  }
  
  // Also include the longest tokens as potential merchant names
  const longTokens = tokenized.merchantTokens
    .filter(token => token.length > 4)
    .slice(0, 2); // Take first 2 longest tokens
  
  merchants.push(...longTokens);
  
  // Remove duplicates and return
  return [...new Set(merchants)];
}

/**
 * Predefined category keywords with weights
 */
export const CATEGORY_KEYWORDS: Record<string, CategoryKeyword[]> = {
  'food_dining': [
    { keyword: 'restaurant', weight: 0.9, categoryIds: [1] },
    { keyword: 'food', weight: 0.8, categoryIds: [1] },
    { keyword: 'dining', weight: 0.8, categoryIds: [1] },
    { keyword: 'meal', weight: 0.7, categoryIds: [1] },
    { keyword: 'lunch', weight: 0.7, categoryIds: [1] },
    { keyword: 'dinner', weight: 0.7, categoryIds: [1] },
    { keyword: 'breakfast', weight: 0.7, categoryIds: [1] },
    { keyword: 'cafe', weight: 0.8, categoryIds: [1] },
    { keyword: 'coffee', weight: 0.6, categoryIds: [1] },
    { keyword: 'pizza', weight: 0.8, categoryIds: [1] },
    { keyword: 'burger', weight: 0.8, categoryIds: [1] },
    { keyword: 'grocery', weight: 0.7, categoryIds: [1] },
    { keyword: 'supermarket', weight: 0.8, categoryIds: [1] },
    { keyword: 'market', weight: 0.6, categoryIds: [1] },
  ],
  'transportation': [
    { keyword: 'uber', weight: 0.9, categoryIds: [2] },
    { keyword: 'lyft', weight: 0.9, categoryIds: [2] },
    { keyword: 'taxi', weight: 0.9, categoryIds: [2] },
    { keyword: 'cab', weight: 0.8, categoryIds: [2] },
    { keyword: 'metro', weight: 0.8, categoryIds: [2] },
    { keyword: 'bus', weight: 0.8, categoryIds: [2] },
    { keyword: 'train', weight: 0.8, categoryIds: [2] },
    { keyword: 'fuel', weight: 0.8, categoryIds: [2] },
    { keyword: 'gas', weight: 0.8, categoryIds: [2] },
    { keyword: 'petrol', weight: 0.8, categoryIds: [2] },
    { keyword: 'parking', weight: 0.7, categoryIds: [2] },
    { keyword: 'toll', weight: 0.8, categoryIds: [2] },
    { keyword: 'highway', weight: 0.6, categoryIds: [2] },
  ],
  'shopping': [
    { keyword: 'amazon', weight: 0.9, categoryIds: [3] },
    { keyword: 'flipkart', weight: 0.9, categoryIds: [3] },
    { keyword: 'myntra', weight: 0.8, categoryIds: [3] },
    { keyword: 'shopping', weight: 0.8, categoryIds: [3] },
    { keyword: 'store', weight: 0.6, categoryIds: [3] },
    { keyword: 'mall', weight: 0.7, categoryIds: [3] },
    { keyword: 'purchase', weight: 0.7, categoryIds: [3] },
    { keyword: 'buy', weight: 0.6, categoryIds: [3] },
    { keyword: 'clothes', weight: 0.7, categoryIds: [3] },
    { keyword: 'clothing', weight: 0.7, categoryIds: [3] },
    { keyword: 'apparel', weight: 0.7, categoryIds: [3] },
    { keyword: 'fashion', weight: 0.6, categoryIds: [3] },
  ],
  'bills_utilities': [
    { keyword: 'electricity', weight: 0.9, categoryIds: [4] },
    { keyword: 'water', weight: 0.8, categoryIds: [4] },
    { keyword: 'internet', weight: 0.8, categoryIds: [4] },
    { keyword: 'phone', weight: 0.7, categoryIds: [4] },
    { keyword: 'mobile', weight: 0.7, categoryIds: [4] },
    { keyword: 'recharge', weight: 0.8, categoryIds: [4] },
    { keyword: 'bill', weight: 0.7, categoryIds: [4] },
    { keyword: 'insurance', weight: 0.8, categoryIds: [4] },
    { keyword: 'premium', weight: 0.7, categoryIds: [4] },
    { keyword: 'policy', weight: 0.7, categoryIds: [4] },
  ],
  'income': [
    { keyword: 'salary', weight: 0.9, categoryIds: [5] },
    { keyword: 'wage', weight: 0.8, categoryIds: [5] },
    { keyword: 'pay', weight: 0.7, categoryIds: [5] },
    { keyword: 'payroll', weight: 0.8, categoryIds: [5] },
    { keyword: 'income', weight: 0.8, categoryIds: [5] },
    { keyword: 'bonus', weight: 0.8, categoryIds: [5] },
    { keyword: 'freelance', weight: 0.9, categoryIds: [6] },
    { keyword: 'consulting', weight: 0.8, categoryIds: [6] },
    { keyword: 'project', weight: 0.6, categoryIds: [6] },
    { keyword: 'contract', weight: 0.7, categoryIds: [6] },
  ]
};

/**
 * Find matching keywords in transaction description
 */
export function findMatchingKeywords(
  tokens: string[], 
  categoryType: 'income' | 'expense' = 'expense'
): { keyword: string; weight: number; categoryIds: number[]; matchCount: number }[] {
  const matches: { keyword: string; weight: number; categoryIds: number[]; matchCount: number }[] = [];
  
  // Filter keyword groups by category type
  const relevantGroups = categoryType === 'income' 
    ? ['income']
    : ['food_dining', 'transportation', 'shopping', 'bills_utilities'];
  
  for (const groupName of relevantGroups) {
    const keywords = CATEGORY_KEYWORDS[groupName] || [];
    
    for (const keywordData of keywords) {
      const matchCount = tokens.filter(token => 
        token.includes(keywordData.keyword) || keywordData.keyword.includes(token)
      ).length;
      
      if (matchCount > 0) {
        matches.push({
          ...keywordData,
          matchCount
        });
      }
    }
  }
  
  // Sort by relevance score (weight * matchCount)
  return matches.sort((a, b) => (b.weight * b.matchCount) - (a.weight * a.matchCount));
}

/**
 * Calculate confidence score for category suggestion
 */
export function calculateCategoryConfidence(
  keywordMatches: { weight: number; matchCount: number }[],
  historicalFrequency: number = 0,
  amountRelevance: number = 0,
  merchantRelevance: number = 0
): number {
  // Base score from keyword matching
  const keywordScore = keywordMatches.reduce(
    (sum, match) => sum + (match.weight * match.matchCount), 
    0
  ) / Math.max(1, keywordMatches.length);
  
  // Combined confidence score
  const confidence = Math.min(0.95, 
    (keywordScore * 0.4) + 
    (historicalFrequency * 0.3) + 
    (amountRelevance * 0.2) + 
    (merchantRelevance * 0.1)
  );
  
  return Math.max(0.1, confidence);
}

/**
 * Normalize confidence scores for a list of suggestions
 */
export function normalizeConfidenceScores(
  suggestions: { confidence: number }[]
): { confidence: number }[] {
  if (suggestions.length === 0) return suggestions;
  
  const maxConfidence = Math.max(...suggestions.map(s => s.confidence));
  const minConfidence = Math.min(...suggestions.map(s => s.confidence));
  
  if (maxConfidence === minConfidence) {
    return suggestions; // All scores are the same
  }
  
  // Normalize to 0.1 - 0.9 range
  return suggestions.map(suggestion => ({
    ...suggestion,
    confidence: 0.1 + (0.8 * (suggestion.confidence - minConfidence) / (maxConfidence - minConfidence))
  }));
}