/**
 * Maps icon names to valid Material Icons from @expo/vector-icons
 * This ensures consistent icon rendering across the app
 */

import { MaterialIcons } from '@expo/vector-icons';

export type MaterialIconName = keyof typeof MaterialIcons.glyphMap;

/**
 * Map of common icon names to valid Material Icons
 */
export const ICON_NAME_MAP: Record<string, MaterialIconName> = {
  // Finance & Money
  'briefcase': 'work',
  'card': 'credit-card',
  'cash': 'attach-money',
  'trending-up': 'trending-up',
  'trending-down': 'trending-down',
  'pie-chart': 'pie-chart',
  'bar-chart': 'bar-chart',
  'analytics': 'analytics',
  'wallet': 'account-balance-wallet',
  'credit-card': 'credit-card',
  'bank': 'account-balance',

  // Food & Dining
  'restaurant': 'restaurant',
  'fast-food': 'fastfood',
  'cafe': 'local-cafe',
  'wine': 'wine-bar',
  'pizza': 'local-pizza',
  'fish': 'set-meal',
  'apple': 'fastfood',
  'nutrition': 'eco',
  'restaurant-outline': 'restaurant-menu',

  // Transportation
  'car': 'directions-car',
  'bus': 'directions-bus',
  'train': 'train',
  'airplane': 'flight',
  'bicycle': 'directions-bike',
  'walk': 'directions-walk',
  'boat': 'directions-boat',
  'subway': 'subway',
  'car-outline': 'directions-car',
  'gas-station': 'local-gas-station',

  // Home & Utilities
  'home': 'home',
  'home-outline': 'home',
  'build': 'build',
  'flash': 'flash-on',
  'water': 'opacity',
  'flame': 'local-fire-department',
  'wifi': 'wifi',
  'tv': 'tv',
  'bed': 'hotel',
  'sofa': 'weekend',
  'kitchen': 'kitchen',

  // Healthcare
  'medical': 'medical-services',
  'fitness': 'fitness-center',
  'heart': 'favorite',
  'pulse': 'favorite',
  'medical-outline': 'local-hospital',
  'thermometer': 'device-thermostat',
  'bandage': 'healing',
  'glasses': 'visibility',
  'dental': 'sentiment-satisfied',

  // Shopping & Retail
  'shopping-bag': 'shopping-bag',
  'shopping-cart': 'shopping-cart',
  'storefront': 'store',
  'pricetag': 'label',
  'gift': 'card-giftcard',
  'shirt': 'checkroom',
  'watch': 'watch',
  'diamond': 'star',
  'bag': 'work',

  // Entertainment
  'music': 'music-note',
  'movie': 'movie',
  'game-controller': 'sports-esports',
  'headset': 'headset',
  'camera': 'camera-alt',
  'tv-outline': 'live-tv',
  'radio': 'radio',
  'ticket': 'confirmation-number',
  'theater': 'theaters',
  'sports': 'sports',

  // Education & Work
  'book': 'menu-book',
  'school': 'school',
  'library': 'local-library',
  'pencil': 'edit',
  'laptop': 'laptop',
  'desktop': 'desktop-windows',
  'tablet': 'tablet',
  'phone-portrait': 'smartphone',
  'calculator': 'calculate',

  // Personal Care
  'scissors': 'content-cut',
  'brush': 'brush',
  'flower': 'local-florist',
  'leaf': 'eco',
  'spa': 'spa',
  'fitness-outline': 'fitness-center',
  'body': 'accessibility',
  'eyedrop': 'colorize',
  'hand-left': 'pan-tool',

  // Travel & Tourism
  'airplane-outline': 'flight-takeoff',
  'map': 'map',
  'compass': 'explore',
  'globe': 'public',
  'location': 'location-on',
  'camera-outline': 'photo-camera',
  'backpack': 'backpack',
  'tent': 'outdoor-grill',
  'mountain': 'landscape',

  // Miscellaneous
  'shield': 'security',
  'shield-check': 'verified-user',
  'umbrella': 'beach-access',
  'sunny': 'wb-sunny',
  'cloud': 'cloud',
  'star': 'star',
  'heart-outline': 'favorite-border',
  'people': 'people',
  'person': 'person',
  'calendar': 'calendar-today',
  'time': 'access-time',
  'alarm': 'alarm',
  'notifications': 'notifications',
  'mail': 'mail',
  'call': 'call',

  // Default/fallback
  'ellipse': 'circle',
  'tag': 'label'
};

/**
 * Get a valid Material Icon name for a given icon string
 * @param iconName - The icon name to map
 * @returns Valid Material Icon name
 */
export const getMaterialIconName = (iconName: string | null | undefined): MaterialIconName => {
  if (!iconName) return 'circle';
  
  const mappedIcon = ICON_NAME_MAP[iconName.toLowerCase()];
  if (mappedIcon) return mappedIcon;
  
  // If no mapping found, try to use the icon name directly if it's valid
  if (iconName in MaterialIcons.glyphMap) {
    return iconName as MaterialIconName;
  }
  
  // Fallback to default icon
  return 'circle';
};

/**
 * Check if an icon name is a valid Material Icon
 * @param iconName - The icon name to check
 * @returns Whether the icon name is valid
 */
export const isValidMaterialIcon = (iconName: string): boolean => {
  return iconName in MaterialIcons.glyphMap;
};