/**
 * Unified icon mapping system for @expo/vector-icons
 * Supports multiple icon families with intelligent fallbacks
 */

import { 
  MaterialIcons, 
  Ionicons 
} from '@expo/vector-icons';

// Safely import optional icon families
let FontAwesome5: any;
let Feather: any;

try {
  FontAwesome5 = require('@expo/vector-icons/FontAwesome5').default;
} catch {
  console.warn('FontAwesome5 not available in @expo/vector-icons');
}

try {
  Feather = require('@expo/vector-icons/Feather').default;
} catch {
  console.warn('Feather not available in @expo/vector-icons');
}

// Supported icon families
export type IconFamily = 'MaterialIcons' | 'Ionicons' | 'FontAwesome5' | 'Feather';

// Type definitions for icon names from each family
export type MaterialIconName = keyof typeof MaterialIcons.glyphMap;
export type IoniconName = keyof typeof Ionicons.glyphMap;
export type FontAwesome5Name = string; // Simplified since it's optional
export type FeatherName = string; // Simplified since it's optional

// Icon configuration interface
export interface IconConfig {
  family: IconFamily;
  name: MaterialIconName | IoniconName | FontAwesome5Name | FeatherName;
}

/**
 * Comprehensive icon mapping with multiple family support
 * Priority: Ionicons > MaterialIcons > FontAwesome5 > Feather
 */
export const UNIFIED_ICON_MAP: Record<string, IconConfig> = {
  // Finance & Money
  'briefcase': { family: 'Ionicons', name: 'briefcase-outline' },
  'briefcase-outline': { family: 'Ionicons', name: 'briefcase-outline' },
  'card': { family: 'Ionicons', name: 'card-outline' },
  'card-outline': { family: 'Ionicons', name: 'card-outline' },
  'cash': { family: 'Ionicons', name: 'cash' },
  'trending-up': { family: 'Ionicons', name: 'trending-up' },
  'trending-down': { family: 'Ionicons', name: 'trending-down' },
  'pie-chart': { family: 'Ionicons', name: 'pie-chart' },
  'bar-chart': { family: 'Ionicons', name: 'bar-chart' },
  'analytics': { family: 'Ionicons', name: 'analytics' },
  'wallet': { family: 'Ionicons', name: 'wallet-outline' },
  'wallet-outline': { family: 'Ionicons', name: 'wallet-outline' },
  'credit-card': { family: 'Ionicons', name: 'card' },
  'bank': { family: 'Ionicons', name: 'business' },

  // Food & Dining
  'restaurant': { family: 'Ionicons', name: 'restaurant' },
  'restaurant-outline': { family: 'Ionicons', name: 'restaurant-outline' },
  'fast-food': { family: 'Ionicons', name: 'fast-food' },
  'cafe': { family: 'Ionicons', name: 'cafe' },
  'wine': { family: 'Ionicons', name: 'wine' },
  'pizza': { family: 'Ionicons', name: 'pizza' },
  'fish': { family: 'Ionicons', name: 'fish' },
  'apple': { family: 'Ionicons', name: 'nutrition' },
  'nutrition': { family: 'Ionicons', name: 'nutrition' },

  // Transportation
  'car': { family: 'Ionicons', name: 'car' },
  'car-outline': { family: 'Ionicons', name: 'car-outline' },
  'bus': { family: 'Ionicons', name: 'bus' },
  'train': { family: 'Ionicons', name: 'train' },
  'airplane': { family: 'Ionicons', name: 'airplane' },
  'airplane-outline': { family: 'Ionicons', name: 'airplane-outline' },
  'bicycle': { family: 'Ionicons', name: 'bicycle' },
  'walk': { family: 'Ionicons', name: 'walk' },
  'boat': { family: 'Ionicons', name: 'boat' },
  'subway': { family: 'Ionicons', name: 'train' },
  'gas-station': { family: 'Ionicons', name: 'car' },

  // Home & Utilities
  'home': { family: 'Ionicons', name: 'home' },
  'home-outline': { family: 'Ionicons', name: 'home-outline' },
  'build': { family: 'Ionicons', name: 'build' },
  'flash': { family: 'Ionicons', name: 'flash' },
  'flash-outline': { family: 'Ionicons', name: 'flash-outline' },
  'water': { family: 'Ionicons', name: 'water' },
  'flame': { family: 'Ionicons', name: 'flame' },
  'wifi': { family: 'Ionicons', name: 'wifi' },
  'tv': { family: 'Ionicons', name: 'tv' },
  'tv-outline': { family: 'Ionicons', name: 'tv-outline' },
  'bed': { family: 'Ionicons', name: 'bed' },
  'bed-outline': { family: 'Ionicons', name: 'bed-outline' },

  // Healthcare
  'medical': { family: 'Ionicons', name: 'medical' },
  'medical-outline': { family: 'Ionicons', name: 'medical-outline' },
  'fitness': { family: 'Ionicons', name: 'fitness' },
  'fitness-outline': { family: 'Ionicons', name: 'fitness-outline' },
  'heart': { family: 'Ionicons', name: 'heart' },
  'heart-outline': { family: 'Ionicons', name: 'heart-outline' },
  'pulse': { family: 'Ionicons', name: 'pulse' },
  'thermometer': { family: 'Ionicons', name: 'thermometer' },
  'thermometer-outline': { family: 'Ionicons', name: 'thermometer-outline' },
  'glasses': { family: 'Ionicons', name: 'glasses' },

  // Shopping & Retail
  'shopping-bag': { family: 'Ionicons', name: 'bag' },
  'bag': { family: 'Ionicons', name: 'bag' },
  'bag-outline': { family: 'Ionicons', name: 'bag-outline' },
  'shopping-cart': { family: 'Ionicons', name: 'cart' },
  'cart': { family: 'Ionicons', name: 'cart' },
  'cart-outline': { family: 'Ionicons', name: 'cart-outline' },
  'storefront': { family: 'Ionicons', name: 'storefront' },
  'storefront-outline': { family: 'Ionicons', name: 'storefront-outline' },
  'pricetag': { family: 'Ionicons', name: 'pricetag' },
  'pricetag-outline': { family: 'Ionicons', name: 'pricetag-outline' },
  'gift': { family: 'Ionicons', name: 'gift' },
  'gift-outline': { family: 'Ionicons', name: 'gift-outline' },
  'shirt': { family: 'Ionicons', name: 'shirt' },
  'shirt-outline': { family: 'Ionicons', name: 'shirt-outline' },
  'watch': { family: 'Ionicons', name: 'watch' },
  'watch-outline': { family: 'Ionicons', name: 'watch-outline' },
  'diamond': { family: 'Ionicons', name: 'diamond' },
  'diamond-outline': { family: 'Ionicons', name: 'diamond-outline' },

  // Entertainment
  'music': { family: 'Ionicons', name: 'musical-notes' },
  'musical-notes': { family: 'Ionicons', name: 'musical-notes' },
  'musical-notes-outline': { family: 'Ionicons', name: 'musical-notes-outline' },
  'movie': { family: 'Ionicons', name: 'film' },
  'film': { family: 'Ionicons', name: 'film' },
  'film-outline': { family: 'Ionicons', name: 'film-outline' },
  'game-controller': { family: 'Ionicons', name: 'game-controller' },
  'game-controller-outline': { family: 'Ionicons', name: 'game-controller-outline' },
  'headset': { family: 'Ionicons', name: 'headset' },
  'headset-outline': { family: 'Ionicons', name: 'headset-outline' },
  'camera': { family: 'Ionicons', name: 'camera' },
  'camera-outline': { family: 'Ionicons', name: 'camera-outline' },
  'radio': { family: 'Ionicons', name: 'radio' },
  'radio-outline': { family: 'Ionicons', name: 'radio-outline' },
  'ticket': { family: 'Ionicons', name: 'ticket' },
  'ticket-outline': { family: 'Ionicons', name: 'ticket-outline' },

  // Education & Work
  'book': { family: 'Ionicons', name: 'book' },
  'book-outline': { family: 'Ionicons', name: 'book-outline' },
  'school': { family: 'Ionicons', name: 'school' },
  'school-outline': { family: 'Ionicons', name: 'school-outline' },
  'library': { family: 'Ionicons', name: 'library' },
  'library-outline': { family: 'Ionicons', name: 'library-outline' },
  'pencil': { family: 'Ionicons', name: 'pencil' },
  'pencil-outline': { family: 'Ionicons', name: 'pencil-outline' },
  'laptop': { family: 'Ionicons', name: 'laptop' },
  'laptop-outline': { family: 'Ionicons', name: 'laptop-outline' },
  'desktop': { family: 'Ionicons', name: 'desktop' },
  'desktop-outline': { family: 'Ionicons', name: 'desktop-outline' },
  'tablet': { family: 'Ionicons', name: 'tablet-portrait' },
  'tablet-portrait': { family: 'Ionicons', name: 'tablet-portrait' },
  'tablet-portrait-outline': { family: 'Ionicons', name: 'tablet-portrait-outline' },
  'phone': { family: 'Ionicons', name: 'phone-portrait' },
  'phone-portrait': { family: 'Ionicons', name: 'phone-portrait' },
  'phone-portrait-outline': { family: 'Ionicons', name: 'phone-portrait-outline' },
  'calculator': { family: 'Ionicons', name: 'calculator' },
  'calculator-outline': { family: 'Ionicons', name: 'calculator-outline' },

  // Personal Care
  'scissors': { family: 'Ionicons', name: 'cut' },
  'cut': { family: 'Ionicons', name: 'cut' },
  'cut-outline': { family: 'Ionicons', name: 'cut-outline' },
  'brush': { family: 'Ionicons', name: 'brush' },
  'brush-outline': { family: 'Ionicons', name: 'brush-outline' },
  'flower': { family: 'Ionicons', name: 'flower' },
  'flower-outline': { family: 'Ionicons', name: 'flower-outline' },
  'leaf': { family: 'Ionicons', name: 'leaf' },
  'leaf-outline': { family: 'Ionicons', name: 'leaf-outline' },
  'spa': { family: 'Ionicons', name: 'flower-outline' },
  'body': { family: 'Ionicons', name: 'body' },
  'body-outline': { family: 'Ionicons', name: 'body-outline' },
  'face-woman-shimmer-outline': { family: 'Ionicons', name: 'person-outline' }, // Fallback for missing icon

  // Travel & Tourism
  'map': { family: 'Ionicons', name: 'map' },
  'map-outline': { family: 'Ionicons', name: 'map-outline' },
  'compass': { family: 'Ionicons', name: 'compass' },
  'compass-outline': { family: 'Ionicons', name: 'compass-outline' },
  'globe': { family: 'Ionicons', name: 'globe' },
  'globe-outline': { family: 'Ionicons', name: 'globe-outline' },
  'location': { family: 'Ionicons', name: 'location' },
  'location-outline': { family: 'Ionicons', name: 'location-outline' },
  'backpack': { family: 'Ionicons', name: 'bag' },
  'tent': { family: 'Ionicons', name: 'home' },
  'mountain': { family: 'Ionicons', name: 'triangle' },

  // Miscellaneous
  'shield': { family: 'Ionicons', name: 'shield' },
  'shield-outline': { family: 'Ionicons', name: 'shield-outline' },
  'shield-check': { family: 'Ionicons', name: 'shield-checkmark' },
  'shield-checkmark': { family: 'Ionicons', name: 'shield-checkmark' },
  'shield-checkmark-outline': { family: 'Ionicons', name: 'shield-checkmark-outline' },
  'umbrella': { family: 'Ionicons', name: 'umbrella' },
  'umbrella-outline': { family: 'Ionicons', name: 'umbrella-outline' },
  'sunny': { family: 'Ionicons', name: 'sunny' },
  'sunny-outline': { family: 'Ionicons', name: 'sunny-outline' },
  'cloud': { family: 'Ionicons', name: 'cloud' },
  'cloud-outline': { family: 'Ionicons', name: 'cloud-outline' },
  'cloud-offline': { family: 'Ionicons', name: 'cloud-offline' },
  'cloud-offline-outline': { family: 'Ionicons', name: 'cloud-offline-outline' },
  'star': { family: 'Ionicons', name: 'star' },
  'star-outline': { family: 'Ionicons', name: 'star-outline' },
  'people': { family: 'Ionicons', name: 'people' },
  'people-outline': { family: 'Ionicons', name: 'people-outline' },
  'person': { family: 'Ionicons', name: 'person' },
  'person-outline': { family: 'Ionicons', name: 'person-outline' },
  'calendar': { family: 'Ionicons', name: 'calendar' },
  'calendar-outline': { family: 'Ionicons', name: 'calendar-outline' },
  'time': { family: 'Ionicons', name: 'time' },
  'time-outline': { family: 'Ionicons', name: 'time-outline' },
  'alarm': { family: 'Ionicons', name: 'alarm' },
  'alarm-outline': { family: 'Ionicons', name: 'alarm-outline' },
  'notifications': { family: 'Ionicons', name: 'notifications' },
  'notifications-outline': { family: 'Ionicons', name: 'notifications-outline' },
  'mail': { family: 'Ionicons', name: 'mail' },
  'mail-outline': { family: 'Ionicons', name: 'mail-outline' },
  'call': { family: 'Ionicons', name: 'call' },
  'call-outline': { family: 'Ionicons', name: 'call-outline' },
  'receipt': { family: 'Ionicons', name: 'receipt' },
  'receipt-outline': { family: 'Ionicons', name: 'receipt-outline' },

  // Additional common icons that might be missing
  'fast-food-outline': { family: 'Ionicons', name: 'fast-food' },
  'cafe-outline': { family: 'Ionicons', name: 'cafe' },
  'wine-outline': { family: 'Ionicons', name: 'wine' },
  'pizza-outline': { family: 'Ionicons', name: 'pizza' },
  'fish-outline': { family: 'Ionicons', name: 'fish' },
  'nutrition-outline': { family: 'Ionicons', name: 'nutrition' },
  'bus-outline': { family: 'Ionicons', name: 'bus' },
  'train-outline': { family: 'Ionicons', name: 'train' },
  'walk-outline': { family: 'Ionicons', name: 'walk' },
  'boat-outline': { family: 'Ionicons', name: 'boat' },
  'build-outline': { family: 'Ionicons', name: 'build' },
  'water-outline': { family: 'Ionicons', name: 'water' },
  'flame-outline': { family: 'Ionicons', name: 'flame' },
  'wifi-outline': { family: 'Ionicons', name: 'wifi' },
  'pulse-outline': { family: 'Ionicons', name: 'pulse' },
  'glasses-outline': { family: 'Ionicons', name: 'glasses' },
  'bandage-outline': { family: 'Ionicons', name: 'bandage' },
  'football-outline': { family: 'Ionicons', name: 'football' },
  'backpack-outline': { family: 'Ionicons', name: 'bag' },

  // Transaction & Transfer
  'swap-horizontal': { family: 'Ionicons', name: 'swap-horizontal' },
  'swap-horizontal-outline': { family: 'Ionicons', name: 'swap-horizontal-outline' },

  // Default/fallback
  'ellipse': { family: 'Ionicons', name: 'ellipse' },
  'ellipse-outline': { family: 'Ionicons', name: 'ellipse-outline' },
  'circle': { family: 'Ionicons', name: 'ellipse' },
  'tag': { family: 'Ionicons', name: 'pricetag' },
};

/**
 * Get icon configuration for a given icon name
 * @param iconName - The icon name to resolve
 * @returns IconConfig with family and icon name, or default fallback
 */
export const getIconConfig = (iconName: string | null | undefined): IconConfig => {
  if (!iconName) {
    return { family: 'Ionicons', name: 'ellipse-outline' };
  }
  
  const lowerName = iconName.toLowerCase();
  const config = UNIFIED_ICON_MAP[lowerName];
  
  if (config) {
    return config;
  }
  
  // Try direct lookup in each family with safety checks
  if (Ionicons?.glyphMap && iconName in Ionicons.glyphMap) {
    return { family: 'Ionicons', name: iconName as IoniconName };
  }
  
  // Try without -outline suffix if the icon doesn't exist
  if (iconName.endsWith('-outline')) {
    const baseIconName = iconName.replace('-outline', '');
    if (Ionicons?.glyphMap && baseIconName in Ionicons.glyphMap) {
      return { family: 'Ionicons', name: baseIconName as IoniconName };
    }
  }
  
  // Try with -outline suffix if the base icon doesn't exist
  if (!iconName.endsWith('-outline')) {
    const outlineIconName = iconName + '-outline';
    if (Ionicons?.glyphMap && outlineIconName in Ionicons.glyphMap) {
      return { family: 'Ionicons', name: outlineIconName as IoniconName };
    }
  }
  
  if (MaterialIcons?.glyphMap && iconName in MaterialIcons.glyphMap) {
    return { family: 'MaterialIcons', name: iconName as MaterialIconName };
  }
  
  if (FontAwesome5?.glyphMap && iconName in FontAwesome5.glyphMap) {
    return { family: 'FontAwesome5', name: iconName as FontAwesome5Name };
  }
  
  if (Feather?.glyphMap && iconName in Feather.glyphMap) {
    return { family: 'Feather', name: iconName as FeatherName };
  }
  
  // Default fallback
  console.log(`⚠️ Icon not found, using fallback: ${iconName} -> ellipse-outline`);
  return { family: 'Ionicons', name: 'ellipse-outline' };
};

/**
 * Check if an icon name is valid in any supported family
 * @param iconName - The icon name to check
 * @returns Whether the icon exists in any family
 */
export const isValidIcon = (iconName: string): boolean => {
  const config = getIconConfig(iconName);
  return config.family !== 'Ionicons' || config.name !== 'ellipse-outline' || iconName === 'ellipse-outline';
};

/**
 * Get the icon component for rendering
 * @param iconName - The icon name to resolve
 * @returns Object with component and props for rendering
 */
export const getIconComponent = (iconName: string | null | undefined) => {
  const config = getIconConfig(iconName);
  
  switch (config.family) {
    case 'MaterialIcons':
      if (MaterialIcons) {
        return {
          component: MaterialIcons,
          name: config.name,
          family: 'MaterialIcons'
        };
      }
      break;
    case 'FontAwesome5':
      if (FontAwesome5) {
        return {
          component: FontAwesome5,
          name: config.name,
          family: 'FontAwesome5'
        };
      }
      break;
    case 'Feather':
      if (Feather) {
        return {
          component: Feather,
          name: config.name,
          family: 'Feather'
        };
      }
      break;
    case 'Ionicons':
    default:
      // Use the actual icon name from config, or fallback to ellipse-outline
      return {
        component: Ionicons,
        name: config.family === 'Ionicons' ? config.name : 'ellipse-outline',
        family: 'Ionicons'
      };
  }
  
  // Final fallback if specific family not available
  return {
    component: Ionicons,
    name: 'ellipse-outline',
    family: 'Ionicons'
  };
};

// Legacy exports for backward compatibility
export const getMaterialIconName = (iconName: string | null | undefined): MaterialIconName => {
  const config = getIconConfig(iconName);
  if (config.family === 'MaterialIcons') {
    return config.name as MaterialIconName;
  }
  // Fallback to a safe MaterialIcon
  return 'circle';
};

export const isValidMaterialIcon = (iconName: string): boolean => {
  return MaterialIcons?.glyphMap ? iconName in MaterialIcons.glyphMap : false;
};