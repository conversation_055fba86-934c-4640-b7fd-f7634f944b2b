import { TransactionMetadata } from '../types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validates transaction metadata for completeness and consistency
 */
export function validateTransactionMetadata(metadata: Partial<TransactionMetadata>): ValidationResult {
  const errors: string[] = [];
  
  // Credit Card Details Validation
  if (metadata.creditCardDetails) {
    const { creditCardDetails } = metadata;
    
    // Merchant name validation
    if (creditCardDetails.merchantName !== undefined && creditCardDetails.merchantName.trim().length === 0) {
      errors.push('Merchant name cannot be empty');
    }
    
    // Reward points validation
    if (creditCardDetails.rewardPoints !== undefined && creditCardDetails.rewardPoints < 0) {
      errors.push('Reward points cannot be negative');
    }
    
    // Available credit validation
    if (creditCardDetails.availableCreditAfterTransaction !== undefined && creditCardDetails.availableCreditAfterTransaction < 0) {
      errors.push('Available credit after transaction cannot be negative');
    }
    
    // Installment info validation
    if (creditCardDetails.installmentInfo) {
      const { installmentInfo } = creditCardDetails;
      
      if (installmentInfo.isInstallment) {
        if (installmentInfo.installmentNumber !== undefined && installmentInfo.installmentNumber < 1) {
          errors.push('Installment number must be positive');
        }
        
        if (installmentInfo.totalInstallments !== undefined && installmentInfo.totalInstallments < 1) {
          errors.push('Total installments must be positive');
        }
        
        if (
          installmentInfo.installmentNumber !== undefined && 
          installmentInfo.totalInstallments !== undefined && 
          installmentInfo.installmentNumber > installmentInfo.totalInstallments
        ) {
          errors.push('Installment number cannot be greater than total installments');
        }
      }
    }
  }
  
  // Loan Details Validation
  if (metadata.loanDetails) {
    const { loanDetails } = metadata;
    
    // Principal component validation
    if (loanDetails.principalComponent !== undefined && loanDetails.principalComponent < 0) {
      errors.push('Principal component cannot be negative');
    }
    
    // Interest component validation
    if (loanDetails.interestComponent !== undefined && loanDetails.interestComponent < 0) {
      errors.push('Interest component cannot be negative');
    }
    
    // Fee component validation
    if (loanDetails.feeComponent !== undefined && loanDetails.feeComponent < 0) {
      errors.push('Fee component cannot be negative');
    }
    
    // Remaining principal validation
    if (loanDetails.remainingPrincipalAfterPayment !== undefined && loanDetails.remainingPrincipalAfterPayment < 0) {
      errors.push('Remaining principal after payment cannot be negative');
    }
    
    // EMI number validation
    if (loanDetails.emiNumber !== undefined && loanDetails.emiNumber < 1) {
      errors.push('EMI number must be positive');
    }
    
    // Payment type validation
    const validPaymentTypes = ['regular_emi', 'prepayment', 'part_payment'];
    if (loanDetails.paymentType && !validPaymentTypes.includes(loanDetails.paymentType)) {
      errors.push(`Invalid payment type. Must be one of: ${validPaymentTypes.join(', ')}`);
    }
  }
  
  // SMS Details Validation
  if (metadata.smsDetails) {
    const { smsDetails } = metadata;
    
    // Parsing confidence validation
    if (smsDetails.parsingConfidence !== undefined) {
      if (smsDetails.parsingConfidence < 0 || smsDetails.parsingConfidence > 1) {
        errors.push('Parsing confidence must be between 0 and 1');
      }
    }
    
    // Original text validation
    if (smsDetails.originalText !== undefined && smsDetails.originalText.trim().length === 0) {
      errors.push('Original SMS text cannot be empty');
    }
    
    // Bank identifier validation
    if (smsDetails.bankIdentifier !== undefined && smsDetails.bankIdentifier.trim().length === 0) {
      errors.push('Bank identifier cannot be empty');
    }
  }
  
  // Source validation
  const validSources = ['sms', 'manual', 'import'];
  if (metadata.source && !validSources.includes(metadata.source)) {
    errors.push(`Invalid source. Must be one of: ${validSources.join(', ')}`);
  }
  
  // Last updated validation
  if (metadata.lastUpdated) {
    const lastUpdatedDate = new Date(metadata.lastUpdated);
    if (isNaN(lastUpdatedDate.getTime())) {
      errors.push('Invalid lastUpdated date format');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates credit card specific metadata
 */
export function validateCreditCardMetadata(creditCardDetails: NonNullable<TransactionMetadata['creditCardDetails']>): ValidationResult {
  const errors: string[] = [];
  
  // Merchant name is required for credit card transactions
  if (!creditCardDetails.merchantName || creditCardDetails.merchantName.trim().length === 0) {
    errors.push('Merchant name is required for credit card transactions');
  }
  
  // Validate merchant name format (basic validation)
  if (creditCardDetails.merchantName && creditCardDetails.merchantName.length > 100) {
    errors.push('Merchant name cannot exceed 100 characters');
  }
  
  // Validate reward points
  if (creditCardDetails.rewardPoints !== undefined && creditCardDetails.rewardPoints < 0) {
    errors.push('Reward points cannot be negative');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates loan specific metadata
 */
export function validateLoanMetadata(loanDetails: NonNullable<TransactionMetadata['loanDetails']>): ValidationResult {
  const errors: string[] = [];
  
  // Validate that the sum of components doesn't exceed reasonable bounds
  const totalComponents = (loanDetails.principalComponent || 0) + 
                         (loanDetails.interestComponent || 0) + 
                         (loanDetails.feeComponent || 0);
  
  if (totalComponents < 0) {
    errors.push('Total loan components cannot be negative');
  }
  
  // Validate payment type
  if (loanDetails.paymentType) {
    const validTypes = ['regular_emi', 'prepayment', 'part_payment'];
    if (!validTypes.includes(loanDetails.paymentType)) {
      errors.push(`Invalid payment type: ${loanDetails.paymentType}`);
    }
  }
  
  // EMI number should be positive for regular EMI payments
  if (loanDetails.paymentType === 'regular_emi' && loanDetails.emiNumber !== undefined && loanDetails.emiNumber < 1) {
    errors.push('EMI number must be positive for regular EMI payments');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates SMS parsing specific metadata
 */
export function validateSMSParsingMetadata(smsDetails: NonNullable<TransactionMetadata['smsDetails']>): ValidationResult {
  const errors: string[] = [];
  
  // Parsing confidence is required and must be valid
  if (smsDetails.parsingConfidence === undefined) {
    errors.push('Parsing confidence is required for SMS parsed transactions');
  } else if (smsDetails.parsingConfidence < 0 || smsDetails.parsingConfidence > 1) {
    errors.push('Parsing confidence must be between 0 and 1');
  }
  
  // Original text is required for SMS transactions
  if (!smsDetails.originalText || smsDetails.originalText.trim().length === 0) {
    errors.push('Original SMS text is required for SMS parsed transactions');
  }
  
  // Bank identifier should be provided for better transaction categorization
  if (!smsDetails.bankIdentifier || smsDetails.bankIdentifier.trim().length === 0) {
    errors.push('Bank identifier is recommended for better transaction categorization');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sanitizes and normalizes transaction metadata
 */
export function sanitizeTransactionMetadata(metadata: Partial<TransactionMetadata>): Partial<TransactionMetadata> {
  const sanitized = { ...metadata };
  
  // Sanitize credit card details
  if (sanitized.creditCardDetails) {
    if (sanitized.creditCardDetails.merchantName) {
      sanitized.creditCardDetails.merchantName = sanitized.creditCardDetails.merchantName.trim();
    }
    if (sanitized.creditCardDetails.merchantCategory) {
      sanitized.creditCardDetails.merchantCategory = sanitized.creditCardDetails.merchantCategory.trim().toLowerCase();
    }
  }
  
  // Sanitize SMS details
  if (sanitized.smsDetails) {
    if (sanitized.smsDetails.bankIdentifier) {
      sanitized.smsDetails.bankIdentifier = sanitized.smsDetails.bankIdentifier.trim().toUpperCase();
    }
    if (sanitized.smsDetails.patternMatched) {
      sanitized.smsDetails.patternMatched = sanitized.smsDetails.patternMatched.trim();
    }
  }
  
  // Ensure timestamps are properly formatted
  if (sanitized.lastUpdated) {
    try {
      sanitized.lastUpdated = new Date(sanitized.lastUpdated).toISOString();
    } catch (error) {
      delete sanitized.lastUpdated;
    }
  }
  
  return sanitized;
}