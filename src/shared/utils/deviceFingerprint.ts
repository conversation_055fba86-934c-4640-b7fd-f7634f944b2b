/**
 * Device Fingerprinting Service
 * Story 1.9: Anonymous Feedback and Feature Requests
 * 
 * Generates anonymous device identifiers for feedback tracking
 * Ensures privacy while preventing duplicate submissions and voting fraud
 */

import { Platform } from 'react-native';
import { KeychainService } from '../../platform/biometric/KeychainService';
// CryptoJS removed - using expo-crypto instead
import AsyncStorage from '@react-native-async-storage/async-storage';
import { DeviceFingerprint } from '../types/feedback';

const DEVICE_ID_KEYCHAIN_KEY = 'finvibe_device_fingerprint';
const DEVICE_ID_STORAGE_KEY = 'device_id_backup';

export class DeviceFingerprintService {
  private static instance: DeviceFingerprintService;
  private deviceId: string | null = null;

  private constructor() {
    // Constructor is empty for now
  }

  public static getInstance(): DeviceFingerprintService {
    if (!DeviceFingerprintService.instance) {
      DeviceFingerprintService.instance = new DeviceFingerprintService();
    }
    return DeviceFingerprintService.instance;
  }

  /**
   * Generate anonymous device identifier
   * Combines device characteristics without personal information
   */
  public async getDeviceId(): Promise<string> {
    if (this.deviceId) {
      return this.deviceId;
    }

    // Try to load existing device ID from secure storage
    try {
      const credentials = await KeychainService.getCredentials({ service: DEVICE_ID_KEYCHAIN_KEY });
      if (credentials && credentials.password) {
        this.deviceId = credentials.password; // Store device ID as password
        return this.deviceId;
      }
    } catch (error) {
      console.warn('Failed to load device ID from keychain, trying fallback:', error);
      // Try fallback storage
      try {
        const fallbackId = await this.loadDeviceIdFallback();
        if (fallbackId) {
          this.deviceId = fallbackId;
          return this.deviceId;
        }
      } catch (fallbackError) {
        console.warn('Failed to load device ID from fallback storage:', fallbackError);
      }
    }

    // Generate new device ID if none exists
    this.deviceId = await this.generateNewDeviceId();
    
    // Store in secure keychain with fallback
    try {
      await KeychainService.setCredentials(DEVICE_ID_KEYCHAIN_KEY, this.deviceId);
    } catch (error) {
      console.warn('Failed to store device ID in keychain, using fallback:', error);
      // Fallback to AsyncStorage if keychain fails
      try {
        await this.storeDeviceIdFallback(this.deviceId);
      } catch (fallbackError) {
        console.error('Failed to store device ID in fallback storage:', fallbackError);
        // Continue anyway - the app should work even without persistent device ID
      }
    }

    return this.deviceId;
  }

  /**
   * Generate a new anonymous device identifier
   * Uses device characteristics but no personal data
   */
  private async generateNewDeviceId(): Promise<string> {
    try {
      const Crypto = require('expo-crypto');
      
      // Generate secure random salt using expo-crypto
      const saltBytes = await Crypto.getRandomBytesAsync(8);
      const salt = saltBytes.map((b: number) => b.toString(36)).join('').substring(0, 15);
      
      // Simplified device characteristics without external library dependencies
      const deviceCharacteristics = {
        platform: Platform.OS,
        version: Platform.Version.toString(),
        // Add timestamp to ensure uniqueness even on identical devices
        timestamp: Date.now(),
        // Add secure random salt for additional uniqueness
        salt
      };

      // Create hash from device characteristics using expo-crypto
      const deviceString = JSON.stringify(deviceCharacteristics);
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        deviceString
      );
      
      // Return first 32 characters for reasonable length
      return `fv_${hash.substring(0, 32)}`;
    } catch (error) {
      console.error('Error generating device fingerprint:', error);
      // Fallback to timestamp-based ID if device info fails
      return `fv_fallback_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    }
  }

  /**
   * Get complete device fingerprint for analytics/debugging
   */
  public async getDeviceFingerprint(): Promise<DeviceFingerprint> {
    const deviceId = await this.getDeviceId();
    
    return {
      deviceId,
      platform: Platform.OS,
      version: Platform.Version.toString(),
      isEmulator: false, // Simplified - would need device info library for actual detection
      createdAt: new Date()
    };
  }

  /**
   * Validate device ID format
   */
  public isValidDeviceId(deviceId: string): boolean {
    // Device IDs should start with 'fv_' and be at least 35 characters
    return deviceId.startsWith('fv_') && deviceId.length >= 35;
  }

  /**
   * Reset device ID (for testing or privacy reset)
   */
  public async resetDeviceId(): Promise<string> {
    this.deviceId = null;
    
    // Clear from keychain
    try {
      await KeychainService.removeCredentials(DEVICE_ID_KEYCHAIN_KEY);
    } catch (error) {
      console.warn('Failed to remove device ID from keychain:', error);
    }

    // Clear fallback storage
    try {
      await AsyncStorage.removeItem(DEVICE_ID_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to remove device ID from fallback storage:', error);
    }

    // Generate new ID
    return await this.getDeviceId();
  }

  /**
   * Fallback storage for device ID if keychain fails
   */
  private async storeDeviceIdFallback(deviceId: string): Promise<void> {
    try {
      await AsyncStorage.setItem(DEVICE_ID_STORAGE_KEY, deviceId);
    } catch (error) {
      console.error('Failed to store device ID in fallback storage:', error);
    }
  }

  /**
   * Load device ID from fallback storage
   */
  private async loadDeviceIdFallback(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(DEVICE_ID_STORAGE_KEY);
    } catch (error) {
      console.error('Failed to load device ID from fallback storage:', error);
      return null;
    }
  }

  /**
   * Get device anonymization hash for additional privacy
   * Used when sharing device statistics without revealing actual device ID
   */
  public async getAnonymizedHash(): Promise<string> {
    try {
      const Crypto = require('expo-crypto');
      const deviceId = await this.getDeviceId();
      const salt = 'finvibe_anonymization_salt_v1';
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        deviceId + salt
      );
      return hash.substring(0, 16);
    } catch (error) {
      console.error('Error generating anonymized hash:', error);
      // Fallback to simple string manipulation
      const deviceId = await this.getDeviceId();
      return deviceId.substring(0, 16);
    }
  }

  /**
   * Check if device ID has been generated (for onboarding flow)
   */
  public async hasDeviceId(): Promise<boolean> {
    if (this.deviceId) {
      return true;
    }

    try {
      const credentials = await KeychainService.getCredentials({ service: DEVICE_ID_KEYCHAIN_KEY });
      return !!credentials;
    } catch (error) {
      // Try fallback storage
      const fallbackId = await this.loadDeviceIdFallback();
      return !!fallbackId;
    }
  }
}