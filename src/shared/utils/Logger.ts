// import * as FileSystem from 'expo-file-system'; // Reserved for future file logging

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  category: string;
  metadata?: Record<string, any> | undefined;
  stackTrace?: string | undefined;
}

interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFileOutput: boolean;
  maxLogEntries: number;
  enableAnonymousReporting: boolean;
}

export class Logger {
  private static instance: Logger;
  private logs: LogEntry[] = [];
  private config: LoggerConfig = {
    level: 'info',
    enableConsole: true,
    enableFileOutput: true,
    maxLogEntries: 1000,
    enableAnonymousReporting: false, // User preference
  };
  private flushTimer?: NodeJS.Timeout | null;

  private constructor() {
    console.log('📊 Logger: Initializing...');
    this.setupErrorHandlers();
    this.setupPeriodicFlush();
    console.log('📊 Logger: Initialization complete');
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public configure(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('📊 Logger: Configured with:', {
      level: this.config.level,
      enableFileOutput: this.config.enableFileOutput,
      enableConsole: this.config.enableConsole
    });
  }

  private setupErrorHandlers(): void {
    try {
      // React Native error handling using ErrorUtils
      if (typeof ErrorUtils !== 'undefined' && ErrorUtils.setGlobalHandler) {
        const originalHandler = ErrorUtils.getGlobalHandler && ErrorUtils.getGlobalHandler();
        
        ErrorUtils.setGlobalHandler((error, isFatal) => {
          this.error('Unhandled Error', 'global', {
            message: error.message,
            stack: error.stack,
            isFatal: isFatal
          });
          
          // Call original handler if it exists
          if (originalHandler) {
            originalHandler(error, isFatal);
          }
        });
      }

      // Handle unhandled promise rejections for React Native
      if (typeof global !== 'undefined') {
        const globalAny = global as any;
        // const originalHandler = globalAny.HermesInternal?.hasPromiseRejectionHandler?.() || null;
        
        // Set up promise rejection tracking for React Native
        // Note: React Native doesn't support window.addEventListener
        // Using global tracking instead if available
        if (typeof globalAny.HermesInternal !== 'undefined') {
          // For React Native with Hermes engine
          console.log('📊 Logger: Hermes engine detected, using native promise rejection tracking');
        }
      }
    } catch (error) {
      // If error handler setup fails, log to console but don't break initialization
      console.warn('Failed to setup error handlers:', error);
    }
  }

  private setupPeriodicFlush(): void {
    // Flush logs every 5 seconds to ensure timely file writing
    this.flushTimer = setInterval(() => {
      if (this.logQueue.length > 0) {
        this.flushLogsToFile();
      }
    }, 5000);
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    const configLevelIndex = levels.indexOf(this.config.level);
    const logLevelIndex = levels.indexOf(level);
    return logLevelIndex >= configLevelIndex;
  }

  private addLogEntry(level: LogLevel, message: string, category: string, metadata?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const logEntry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      category,
      metadata,
      stackTrace: error?.stack
    };

    // Add to memory logs
    this.logs.push(logEntry);

    // Maintain max log entries
    if (this.logs.length > this.config.maxLogEntries) {
      this.logs = this.logs.slice(-this.config.maxLogEntries);
    }

    // Console output
    if (this.config.enableConsole) {
      this.outputToConsole(logEntry);
    }

    // File output (would be implemented with file system access)
    if (this.config.enableFileOutput) {
      this.outputToFile(logEntry);
    }

    // Anonymous error reporting for errors and warnings
    if (this.config.enableAnonymousReporting && (level === 'error' || level === 'warn')) {
      this.reportAnonymously(logEntry);
    }
  }

  private outputToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const logMessage = `[${timestamp}] [${entry.level.toUpperCase()}] [${entry.category}] ${entry.message}`;

    switch (entry.level) {
      case 'debug':
        console.debug(logMessage, entry.metadata);
        break;
      case 'info':
        console.info(logMessage, entry.metadata);
        break;
      case 'warn':
        console.warn(logMessage, entry.metadata);
        break;
      case 'error':
        console.error(logMessage, entry.metadata);
        if (entry.stackTrace) {
          console.error('Stack trace:', entry.stackTrace);
        }
        break;
    }
  }

  private outputToFile(entry: LogEntry): void {
    // Use expo-file-system for actual file writing to logs/llm_debug
    const logLine = JSON.stringify({
      timestamp: entry.timestamp.toISOString(),
      level: entry.level,
      category: entry.category,
      message: entry.message,
      metadata: entry.metadata,
      stackTrace: entry.stackTrace
    });

    // Queue the log for file writing - async operation handled by LogAggregatorService
    this.queueLogForFile(logLine);
  }

  private logQueue: string[] = [];
  private isWritingLogs = false;

  private queueLogForFile(logLine: string): void {
    this.logQueue.push(logLine);
    
    // Trigger flush if queue is getting large
    if (this.logQueue.length >= 10) {
      this.flushLogsToFile();
    }
  }

  private async flushLogsToFile(): Promise<void> {
    if (this.isWritingLogs || this.logQueue.length === 0) {
      return;
    }

    this.isWritingLogs = true;
    console.log(`📊 Logger: Starting HTTP flush of ${this.logQueue.length} log entries`);

    try {
      // Prepare logs to send
      const logsToWrite = [...this.logQueue];
      this.logQueue = [];

      // Send each log entry to the bridge server
      const LOG_BRIDGE_URL = 'http://localhost:8083/log';
      console.log(`📊 Logger: Sending to bridge server: ${LOG_BRIDGE_URL}`);

      const promises = logsToWrite.map(async (logLine) => {
        try {
          const logEntry = JSON.parse(logLine);
          
          const response = await fetch(LOG_BRIDGE_URL, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(logEntry),
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          return { success: true, entry: logEntry };
        } catch (error) {
          console.error('❌ Failed to send log entry:', error);
          return { success: false, entry: logLine, error };
        }
      });

      const results = await Promise.all(promises);
      const successful = results.filter(r => r.success).length;
      const failed = results.length - successful;

      if (successful > 0) {
        console.log(`✅ FILE_LOG: Successfully sent ${successful} log entries to bridge server`);
      }
      
      if (failed > 0) {
        console.warn(`⚠️ FILE_LOG: Failed to send ${failed} log entries`);
        // Put failed entries back in queue for retry
        const failedEntries = results
          .filter(r => !r.success)
          .map(r => typeof r.entry === 'string' ? r.entry : JSON.stringify(r.entry));
        this.logQueue.unshift(...failedEntries);
      }

    } catch (error) {
      // If HTTP bridge fails, fall back to console logging
      console.error('❌ FILE_LOG_ERROR: Failed to send logs to bridge server:', error);
      console.error('❌ FILE_LOG_ERROR: Error details:', {
        name: (error as Error).name,
        message: (error as Error).message,
        stack: (error as Error).stack
      });
      console.log('📋 FILE_LOG_FALLBACK: Bridge server not available, logs will be console-only');
      
      // Don't put logs back in queue if bridge server is down
      this.logQueue = [];
    } finally {
      this.isWritingLogs = false;
      console.log('📊 Logger: HTTP flush operation completed');
    }
  }

  private reportAnonymously(entry: LogEntry): void {
    if (!this.config.enableAnonymousReporting) {
      return;
    }

    // Anonymous error reporting - sanitize any personal information
    const anonymousReport = {
      timestamp: entry.timestamp.toISOString(),
      level: entry.level,
      category: entry.category,
      message: this.sanitizeMessage(entry.message),
      metadata: this.sanitizeMetadata(entry.metadata),
      stackTrace: entry.stackTrace,
      appVersion: '1.0.0',
      platform: 'react-native'
    };

    // In production, this would send to analytics service
    console.log('ANONYMOUS_REPORT:', anonymousReport);
  }

  private sanitizeMessage(message: string): string {
    // Remove potential personal information from log messages
    return message
      .replace(/\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, '[CARD-REDACTED]') // Credit card numbers
      .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL-REDACTED]') // Email addresses
      .replace(/\b\d{10,12}\b/g, '[PHONE-REDACTED]') // Phone numbers
      .replace(/₹[\d,]+\.?\d*/g, '[AMOUNT-REDACTED]'); // Currency amounts
  }

  private sanitizeMetadata(metadata?: Record<string, any>): Record<string, any> | undefined {
    if (!metadata) {
      return undefined;
    }

    const sanitized: Record<string, any> = {};
    for (const [key, value] of Object.entries(metadata)) {
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeMessage(value);
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = '[OBJECT-REDACTED]';
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  // Public logging methods
  public debug(message: string, category: string = 'app', metadata?: Record<string, any>): void {
    this.addLogEntry('debug', message, category, metadata);
  }

  public info(message: string, category: string = 'app', metadata?: Record<string, any>): void {
    this.addLogEntry('info', message, category, metadata);
  }

  public warn(message: string, category: string = 'app', metadata?: Record<string, any>): void {
    this.addLogEntry('warn', message, category, metadata);
  }

  public error(message: string, category: string = 'app', metadata?: Record<string, any>, error?: Error): void {
    this.addLogEntry('error', message, category, metadata, error);
  }

  // Specialized logging methods
  public logDatabaseOperation(operation: string, table: string, executionTime: number, success: boolean): void {
    const level = success ? 'info' : 'error';
    const message = `Database ${operation} on ${table} ${success ? 'completed' : 'failed'}`;
    
    this.addLogEntry(level, message, 'database', {
      operation,
      table,
      executionTime,
      success
    });
  }

  public logPerformanceMetric(metric: string, value: number, threshold?: number): void {
    const level = threshold && value > threshold ? 'warn' : 'info';
    const message = `Performance metric: ${metric} = ${value}${threshold ? ` (threshold: ${threshold})` : ''}`;
    
    this.addLogEntry(level, message, 'performance', {
      metric,
      value,
      threshold,
      exceeded: threshold ? value > threshold : false
    });
  }

  public logUserAction(action: string, screen: string, metadata?: Record<string, any>): void {
    this.addLogEntry('info', `User action: ${action} on ${screen}`, 'user-action', {
      action,
      screen,
      ...metadata
    });
  }

  public logSMSProcessing(success: boolean, confidence?: number, error?: string): void {
    const level = success ? 'info' : 'error';
    const message = `SMS processing ${success ? 'successful' : 'failed'}`;
    
    this.addLogEntry(level, message, 'sms', {
      success,
      confidence,
      error
    });
  }

  // Query and management methods
  public getLogs(level?: LogLevel, category?: string, limit: number = 100): LogEntry[] {
    let filteredLogs = this.logs;

    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }

    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    return filteredLogs.slice(-limit);
  }

  public getLogsSince(since: Date): LogEntry[] {
    return this.logs.filter(log => log.timestamp >= since);
  }

  public exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  public clearLogs(): void {
    this.logs = [];
    this.info('Logs cleared', 'logger');
  }

  public cleanup(): void {
    // Clear the periodic flush timer
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    
    // Flush any remaining logs
    if (this.logQueue.length > 0) {
      this.flushLogsToFile();
    }
  }

  public async manualFlush(): Promise<string> {
    // Force flush logs and return the expected file path in project directory
    await this.flushLogsToFile();
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    return `logs/llm_debug/app-${dateStr}.log (in project root)`;
  }

  public getLogStats(): {
    total: number;
    byLevel: Record<LogLevel, number>;
    byCategory: Record<string, number>;
    oldestLog?: Date | undefined;
    newestLog?: Date | undefined;
  } {
    const byLevel: Record<LogLevel, number> = {
      debug: 0,
      info: 0,
      warn: 0,
      error: 0
    };

    const byCategory: Record<string, number> = {};

    this.logs.forEach(log => {
      byLevel[log.level]++;
      byCategory[log.category] = (byCategory[log.category] || 0) + 1;
    });

    return {
      total: this.logs.length,
      byLevel,
      byCategory,
      oldestLog: this.logs.length > 0 ? this.logs[0].timestamp : undefined,
      newestLog: this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : undefined
    };
  }

  // Helper for structured error reporting
  public createErrorContext(operation: string, context: Record<string, any>): Record<string, any> {
    return {
      operation,
      timestamp: new Date().toISOString(),
      context: this.sanitizeMetadata(context)
    };
  }
}