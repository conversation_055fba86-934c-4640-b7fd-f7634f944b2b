import { TransactionType } from '../types';
import { Account } from '../types';

// Account type to transaction type compatibility mapping
export const ACCOUNT_TRANSACTION_COMPATIBILITY: Record<Account['type'], TransactionType[]> = {
  'credit': ['credit_payment', 'credit_charge', 'credit_interest', 'credit_fee'],
  'loan': ['loan_emi', 'loan_interest', 'loan_principal', 'loan_fee', 'loan_prepayment'],
  'checking': ['income', 'expense', 'transfer'],
  'savings': ['income', 'expense', 'transfer'],
  'investment': ['income', 'expense', 'transfer']
};

// Credit card specific transaction types
export const CREDIT_CARD_TRANSACTION_TYPES: TransactionType[] = [
  'credit_payment', 
  'credit_charge', 
  'credit_interest', 
  'credit_fee'
];

// Loan specific transaction types
export const LOAN_TRANSACTION_TYPES: TransactionType[] = [
  'loan_emi', 
  'loan_interest', 
  'loan_principal', 
  'loan_fee', 
  'loan_prepayment'
];

// Traditional account transaction types
export const TRADITIONAL_TRANSACTION_TYPES: TransactionType[] = [
  'income', 
  'expense', 
  'transfer'
];

/**
 * Validates if a transaction type is compatible with an account type
 * @param transactionType The transaction type to validate
 * @param accountType The account type to validate against
 * @returns true if compatible, false otherwise
 */
export function validateTransactionType(
  transactionType: TransactionType, 
  accountType: Account['type']
): boolean {
  const compatibleTypes = ACCOUNT_TRANSACTION_COMPATIBILITY[accountType];
  return compatibleTypes.includes(transactionType);
}

/**
 * Gets all valid transaction types for a given account type
 * @param accountType The account type
 * @returns Array of valid transaction types
 */
export function getValidTransactionTypes(accountType: Account['type']): TransactionType[] {
  return ACCOUNT_TRANSACTION_COMPATIBILITY[accountType] || [];
}

/**
 * Checks if a transaction type is credit card related
 * @param transactionType The transaction type to check
 * @returns true if credit card related
 */
export function isCreditCardTransaction(transactionType: TransactionType): boolean {
  return CREDIT_CARD_TRANSACTION_TYPES.includes(transactionType);
}

/**
 * Checks if a transaction type is loan related
 * @param transactionType The transaction type to check
 * @returns true if loan related
 */
export function isLoanTransaction(transactionType: TransactionType): boolean {
  return LOAN_TRANSACTION_TYPES.includes(transactionType);
}

/**
 * Determines the default transaction type for an account type
 * @param accountType The account type
 * @returns Default transaction type for the account
 */
export function getDefaultTransactionType(accountType: Account['type']): TransactionType {
  switch (accountType) {
    case 'credit':
      return 'credit_charge';
    case 'loan':
      return 'loan_emi';
    case 'checking':
    case 'savings':
    case 'investment':
    default:
      return 'expense';
  }
}

/**
 * Gets the account types that are compatible with a transaction type
 * @param transactionType The transaction type
 * @returns Array of compatible account types
 */
export function getCompatibleAccountTypes(transactionType: TransactionType): Account['type'][] {
  const compatibleTypes: Account['type'][] = [];
  
  for (const [accountType, transactionTypes] of Object.entries(ACCOUNT_TRANSACTION_COMPATIBILITY)) {
    if (transactionTypes.includes(transactionType)) {
      compatibleTypes.push(accountType as Account['type']);
    }
  }
  
  return compatibleTypes;
}

/**
 * Validates transaction type and amount compatibility for account type
 * @param transactionType The transaction type
 * @param amount The transaction amount
 * @param accountType The account type
 * @returns Validation result with error message if invalid
 */
export function validateTransactionCompatibility(
  transactionType: TransactionType,
  amount: number,
  accountType: Account['type']
): { isValid: boolean; error?: string } {
  // Basic transaction type compatibility
  if (!validateTransactionType(transactionType, accountType)) {
    return {
      isValid: false,
      error: `Transaction type '${transactionType}' is not compatible with account type '${accountType}'`
    };
  }
  
  // Credit card specific validations
  if (accountType === 'credit') {
    if (transactionType === 'credit_charge' && amount < 0) {
      return {
        isValid: false,
        error: 'Credit card charges must be positive amounts'
      };
    }
    if (transactionType === 'credit_payment' && amount < 0) {
      return {
        isValid: false,
        error: 'Credit card payments must be positive amounts'
      };
    }
  }
  
  // Loan specific validations  
  if (accountType === 'loan') {
    if (LOAN_TRANSACTION_TYPES.includes(transactionType) && amount < 0) {
      return {
        isValid: false,
        error: 'Loan transaction amounts must be positive'
      };
    }
  }
  
  // General amount validations
  if (amount <= 0) {
    return {
      isValid: false,
      error: 'Transaction amount must be greater than zero'
    };
  }
  
  return { isValid: true };
}