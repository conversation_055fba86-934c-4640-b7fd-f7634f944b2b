/**
 * Utility functions for consistent icon handling across the application
 */

// Predefined color palette for fallback icons with good text visibility
const FALLBACK_COLORS = [
  '#4A90E2', // Blue
  '#7ED321', // Green
  '#F5A623', // Orange
  '#D0021B', // Red
  '#9013FE', // Purple
  '#50E3C2', // Teal
  '#B8E986', // Light Green
  '#FF6B6B', // Coral
  '#4ECDC4', // Turquoise
  '#45B7D1', // Sky Blue
  '#96CEB4', // Mint
  '#FFEAA7', // Light Yellow
  '#DDA0DD', // Plum
  '#98D8C8', // Aqua
  '#F7DC6F', // Pale Gold
];

/**
 * Generate a consistent color based on text input
 * @param text - The text to generate color from
 * @returns Hex color string
 */
export const getConsistentColor = (text: string): string => {
  if (!text || text.length === 0) {
    return FALLBACK_COLORS[0];
  }
  
  // Create a simple hash from the text
  let hash = 0;
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Use absolute value and modulo to get a valid index
  const colorIndex = Math.abs(hash) % FALLBACK_COLORS.length;
  return FALLBACK_COLORS[colorIndex];
};

/**
 * Get the first letter of a text (uppercase)
 * @param text - The text to get first letter from
 * @returns First letter in uppercase
 */
export const getFirstLetter = (text: string): string => {
  if (!text || text.length === 0) {
    return '?';
  }
  
  // Handle special characters and emojis - get first meaningful character
  const trimmed = text.trim();
  if (trimmed.length === 0) {
    return '?';
  }
  
  // Extract first alphabetic character
  const match = trimmed.match(/[a-zA-Z]/);
  if (match) {
    return match[0].toUpperCase();
  }
  
  // If no alphabetic character found, use first character
  return trimmed[0].toUpperCase();
};

/**
 * Generate fallback icon data for components
 * @param text - The text to generate fallback icon from
 * @returns Object with letter and background color
 */
export const generateFallbackIcon = (text: string) => {
  const letter = getFirstLetter(text);
  const backgroundColor = getConsistentColor(text);
  
  return {
    letter,
    backgroundColor,
    textColor: '#FFFFFF', // Always white for good contrast
  };
};

/**
 * Check if a string is an emoji
 * @param text - The text to check
 * @returns Boolean indicating if text contains emoji
 */
export const isEmoji = (text: string): boolean => {
  if (!text) return false;
  
  // Simple emoji detection - checks for common emoji unicode ranges
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
  return emojiRegex.test(text);
};

/**
 * Get icon to display - emoji if available, unified icon system otherwise, fallback icon data as last resort
 * @param iconText - The icon text (could be emoji or regular text)
 * @param name - The name to generate fallback from
 * @returns Icon display data
 */
export const getIconDisplay = (iconText: string | undefined | null, name: string) => {
  // If we have an icon and it's an emoji, use it
  if (iconText && isEmoji(iconText)) {
    return {
      type: 'emoji' as const,
      emoji: iconText,
    };
  }
  
  // If we have an icon name, use the unified icon system
  if (iconText && iconText.trim()) {
    return {
      type: 'unified_icon' as const,
      iconName: iconText,
    };
  }
  
  // Otherwise generate fallback
  const fallback = generateFallbackIcon(name);
  return {
    type: 'fallback' as const,
    ...fallback,
  };
};;