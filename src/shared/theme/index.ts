import { TextStyle, ViewStyle } from 'react-native';

export interface FinVibeTheme {
  colors: {
    // Primary colors
    primary: string;
    primaryLight: string;
    primaryDark: string;
    
    // Semantic colors
    success: string;
    warning: string;
    error: string;
    info: string;
    
    // Transaction types
    income: string;
    expense: string;
    transfer: string;
    
    // Background colors
    background: string;
    surface: string;
    card: string;
    
    // Text colors
    text: string;
    textSecondary: string;
    textDisabled: string;
    
    // Border colors
    border: string;
    borderLight: string;
    
    // Status indicators
    synced: string;
    pending: string;
    offline: string;
    conflict: string;
  };
  
  typography: {
    h1: TextStyle;
    h2: TextStyle;
    h3: TextStyle;
    h4: TextStyle;
    h5: TextStyle;
    body: TextStyle;
    caption: TextStyle;
    button: TextStyle;
  };
  
  spacing: {
    xs: number; // 4
    sm: number; // 8
    md: number; // 16
    lg: number; // 24
    xl: number; // 32
    xxl: number; // 48
  };
  
  borderRadius: {
    sm: number; // 4
    md: number; // 8
    lg: number; // 12
    xl: number; // 16
  };
  
  shadows: {
    sm: ViewStyle;
    md: ViewStyle;
    lg: ViewStyle;
  };
}

export const lightTheme: FinVibeTheme = {
  colors: {
    primary: '#4A90E2',
    primaryLight: '#7BB3F0',
    primaryDark: '#2E5C8A',
    
    success: '#58D68D',
    warning: '#F39C12',
    error: '#FF6B6B',
    info: '#5DADE2',
    
    income: '#58D68D',
    expense: '#FF6B6B',
    transfer: '#F39C12',
    
    background: '#FFFFFF',
    surface: '#F8F9FA',
    card: '#FFFFFF',
    
    text: '#2C3E50',
    textSecondary: '#7F8C8D',
    textDisabled: '#BDC3C7',
    
    border: '#E5E5E5',
    borderLight: '#F0F0F0',
    
    synced: '#58D68D',
    pending: '#F39C12',
    offline: '#95A5A6',
    conflict: '#FF6B6B',
  },
  typography: {
    h1: { fontSize: 32, fontWeight: '700', lineHeight: 40 },
    h2: { fontSize: 24, fontWeight: '600', lineHeight: 32 },
    h3: { fontSize: 18, fontWeight: '600', lineHeight: 24 },
    h4: { fontSize: 16, fontWeight: '600', lineHeight: 22 },
    h5: { fontSize: 14, fontWeight: '600', lineHeight: 20 },
    body: { fontSize: 16, fontWeight: '400', lineHeight: 24 },
    caption: { fontSize: 14, fontWeight: '400', lineHeight: 20 },
    button: { fontSize: 16, fontWeight: '600', lineHeight: 20 },
  },
  spacing: {
    xs: 4,
    sm: 8, 
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 8,
    },
  },
};

export const darkTheme: FinVibeTheme = {
  ...lightTheme,
  colors: {
    ...lightTheme.colors,
    background: '#1A1A1A',
    surface: '#2C2C2C',
    card: '#2C2C2C',
    text: '#FFFFFF',
    textSecondary: '#B0B0B0',
    textDisabled: '#666666',
    border: '#404040',
    borderLight: '#333333',
  },
};

// Export styled utilities
export { createStyles, createCommonStyles, useCommonStyles } from './createStyles';