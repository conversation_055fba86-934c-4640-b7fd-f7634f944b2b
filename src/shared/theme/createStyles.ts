import { StyleSheet } from 'react-native';
import { FinVibeTheme } from './index';

/**
 * Creates themed styles using the current theme
 * This utility helps maintain consistency across components
 * while avoiding inline styles everywhere
 */
export const createStyles = <T extends StyleSheet.NamedStyles<T>>(
  styleFactory: (theme: FinVibeTheme) => T
) => {
  return (theme: FinVibeTheme): T => {
    return StyleSheet.create(styleFactory(theme));
  };
};

/**
 * Common styled components that can be reused across the app
 * Aligned with component specifications in docs/design/component-specifications.md
 */
export const createCommonStyles = (theme: FinVibeTheme) => StyleSheet.create({
  // Container styles
  card: {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.xs,
    ...theme.shadows.sm,
  },
  
  cardSelected: {
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  
  cardElevated: {
    ...theme.shadows.md,
  },
  
  cardOutlined: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: 'transparent',
    elevation: 0,
  },
  
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  surface: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
  },
  
  // Layout styles
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  rowSpaceBetween: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  column: {
    flexDirection: 'column',
  },
  
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  flex1: {
    flex: 1,
  },
  
  // Button styles - Base and variants
  buttonBase: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.sm + theme.spacing.xs, // 12
    paddingHorizontal: theme.spacing.md,
    minHeight: 48, // WCAG AA touch target
    gap: theme.spacing.xs,
  },
  
  buttonPrimary: {
    backgroundColor: theme.colors.primary,
  },
  
  buttonSecondary: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  
  buttonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  
  buttonGhost: {
    backgroundColor: 'transparent',
  },
  
  buttonDanger: {
    backgroundColor: theme.colors.error,
  },
  
  buttonDisabled: {
    opacity: 0.5,
  },
  
  // Button size variants
  buttonSm: {
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    minHeight: 40,
  },
  
  buttonMd: {
    paddingVertical: theme.spacing.sm + theme.spacing.xs, // 12
    paddingHorizontal: theme.spacing.md,
    minHeight: 48,
  },
  
  buttonLg: {
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    minHeight: 56,
  },
  
  // Text styles
  textPrimary: {
    ...theme.typography.body,
    color: theme.colors.text,
  },
  
  textSecondary: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
  },
  
  textDisabled: {
    ...theme.typography.caption,
    color: theme.colors.textDisabled,
  },
  
  textBold: {
    fontWeight: '600',
  },
  
  textTitle: {
    ...theme.typography.h3,
    color: theme.colors.text,
    fontWeight: '600',
  },
  
  textSubtitle: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
  },
  
  // Form input styles
  inputBase: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    minHeight: 48,
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.text,
  },
  
  inputFocused: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.card,
  },
  
  inputError: {
    borderColor: theme.colors.error,
    backgroundColor: theme.colors.card,
  },
  
  inputDisabled: {
    opacity: 0.6,
    backgroundColor: theme.colors.borderLight,
  },
  
  inputLabel: {
    ...theme.typography.caption,
    color: theme.colors.text,
    fontWeight: '500',
    marginBottom: theme.spacing.xs,
  },
  
  // Icon container styles
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  
  iconContainerSmall: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  
  iconContainerMedium: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  
  // Status indicator styles
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  statusIcon: {
    fontSize: 12,
    marginRight: theme.spacing.xs,
  },
  
  statusText: {
    ...theme.typography.caption,
    fontSize: 12,
    fontWeight: '500',
  },
  
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: theme.spacing.xs,
  },
  
  statusBadge: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 18,
    minHeight: 18,
    paddingHorizontal: theme.spacing.xs,
  },
  
  // Progress styles
  progressContainer: {
    height: 6,
    backgroundColor: theme.colors.borderLight,
    borderRadius: 3,
    overflow: 'hidden',
  },
  
  progressBar: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 3,
  },
  
  progressText: {
    ...theme.typography.caption,
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text,
    textAlign: 'right',
    marginTop: theme.spacing.xs,
  },
  
  // Selection indicator
  selectionIndicator: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  selectionCheckmark: {
    color: theme.colors.card,
    fontSize: 14,
    fontWeight: 'bold',
  },
  
  // Border styles
  borderLeft: {
    borderLeftWidth: 4,
  },
  
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  
  borderRadius: {
    borderRadius: theme.borderRadius.md,
  },
  
  borderRadiusLg: {
    borderRadius: theme.borderRadius.lg,
  },
  
  // Spacing utilities
  marginXs: { margin: theme.spacing.xs },
  marginSm: { margin: theme.spacing.sm },
  marginMd: { margin: theme.spacing.md },
  marginLg: { margin: theme.spacing.lg },
  marginXl: { margin: theme.spacing.xl },
  
  paddingXs: { padding: theme.spacing.xs },
  paddingSm: { padding: theme.spacing.sm },
  paddingMd: { padding: theme.spacing.md },
  paddingLg: { padding: theme.spacing.lg },
  paddingXl: { padding: theme.spacing.xl },
  
  marginBottomXs: { marginBottom: theme.spacing.xs },
  marginBottomSm: { marginBottom: theme.spacing.sm },
  marginBottomMd: { marginBottom: theme.spacing.md },
  
  // Status colors utilities
  successColor: { color: theme.colors.success },
  errorColor: { color: theme.colors.error },
  warningColor: { color: theme.colors.warning },
  infoColor: { color: theme.colors.info },
  
  // Background color utilities  
  successBackground: { backgroundColor: theme.colors.success },
  errorBackground: { backgroundColor: theme.colors.error },
  warningBackground: { backgroundColor: theme.colors.warning },
  infoBackground: { backgroundColor: theme.colors.info },
  
  // Transaction type colors
  incomeColor: { color: theme.colors.income },
  expenseColor: { color: theme.colors.expense },
  transferColor: { color: theme.colors.transfer },
  
  // Transaction background colors
  incomeBackground: { backgroundColor: theme.colors.income },
  expenseBackground: { backgroundColor: theme.colors.expense },
  transferBackground: { backgroundColor: theme.colors.transfer },
});

/**
 * Hook to get common themed styles
 */
export const useCommonStyles = (theme: FinVibeTheme) => {
  return createCommonStyles(theme);
};