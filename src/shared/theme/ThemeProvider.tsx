import React, { createContext, useContext, useMemo } from 'react';
import { useColorScheme } from 'react-native';
import { FinVibeTheme, lightTheme, darkTheme } from './index';

interface ThemeContextType {
  theme: FinVibeTheme;
  isDark: boolean;
  toggleTheme?: () => void;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  isDark: false,
});

interface ThemeProviderProps {
  children: React.ReactNode;
  forceDark?: boolean;
  forceLight?: boolean;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  forceDark = false, 
  forceLight = false 
}) => {
  const systemColorScheme = useColorScheme();
  
  const isDark = useMemo(() => {
    if (forceLight) return false;
    if (forceDark) return true;
    return systemColorScheme === 'dark';
  }, [systemColorScheme, forceDark, forceLight]);
  
  const theme = useMemo(() => isDark ? darkTheme : lightTheme, [isDark]);
  
  const contextValue = useMemo(() => ({
    theme,
    isDark,
  }), [theme, isDark]);

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): FinVibeTheme => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context.theme;
};

export const useThemeContext = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
};