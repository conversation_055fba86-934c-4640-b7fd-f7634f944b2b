import { Category } from '@/shared/types';

export interface SystemCategoryData {
  name: string;
  categoryType: Category['category_type'];
  colorCode: string;
  iconName: string;
  description?: string;
}

export const SYSTEM_EXPENSE_CATEGORIES: SystemCategoryData[] = [
  {
    name: 'Food & Dining',
    categoryType: 'expense',
    colorCode: '#FF6B35',
    iconName: 'restaurant-outline',
    description: 'Restaurants, groceries, and food delivery'
  },
  {
    name: 'Transportation',
    categoryType: 'expense',
    colorCode: '#4A90E2',
    iconName: 'car-outline',
    description: 'Gas, public transport, rideshares, and parking'
  },
  {
    name: 'Utilities',
    categoryType: 'expense',
    colorCode: '#7ED321',
    iconName: 'home-outline',
    description: 'Electricity, water, gas, internet, and phone bills'
  },
  {
    name: 'Entertainment',
    categoryType: 'expense',
    colorCode: '#9013FE',
    iconName: 'musical-notes-outline',
    description: 'Movies, games, streaming services, and events'
  },
  {
    name: 'Healthcare',
    categoryType: 'expense',
    colorCode: '#F5A623',
    iconName: 'medical-outline',
    description: 'Medical bills, prescriptions, and health insurance'
  },
  {
    name: 'Shopping',
    categoryType: 'expense',
    colorCode: '#50C878',
    iconName: 'bag-outline',
    description: 'Clothing, electronics, and general retail purchases'
  },
  {
    name: 'Education',
    categoryType: 'expense',
    colorCode: '#FF9500',
    iconName: 'book-outline',
    description: 'Tuition, books, courses, and educational materials'
  },
  {
    name: 'Personal Care',
    categoryType: 'expense',
    colorCode: '#FF2D92',
    iconName: 'face-woman-shimmer-outline',
    description: 'Haircuts, cosmetics, spa, and personal grooming'
  },
  {
    name: 'Insurance',
    categoryType: 'expense',
    colorCode: '#34C759',
    iconName: 'shield-outline',
    description: 'Life, auto, home, and health insurance premiums'
  },
  {
    name: 'Taxes',
    categoryType: 'expense',
    colorCode: '#8B4513',
    iconName: 'receipt-outline',
    description: 'Income tax, property tax, and other tax payments'
  },
  {
    name: 'Gifts & Donations',
    categoryType: 'expense',
    colorCode: '#FF69B4',
    iconName: 'gift-outline',
    description: 'Charitable donations, gifts, and special occasions'
  },
  {
    name: 'Travel',
    categoryType: 'expense',
    colorCode: '#20B2AA',
    iconName: 'airplane-outline',
    description: 'Hotels, flights, vacation expenses, and travel insurance'
  }
];

export const SYSTEM_INCOME_CATEGORIES: SystemCategoryData[] = [
  {
    name: 'Salary',
    categoryType: 'income',
    colorCode: '#34C759',
    iconName: 'briefcase-outline',
    description: 'Regular employment income and wages'
  },
  {
    name: 'Freelance',
    categoryType: 'income',
    colorCode: '#007AFF',
    iconName: 'laptop-outline',
    description: 'Contract work, consulting, and gig economy income'
  },
  {
    name: 'Investments',
    categoryType: 'income',
    colorCode: '#FF9500',
    iconName: 'trending-up-outline',
    description: 'Dividends, capital gains, and investment returns'
  },
  {
    name: 'Business',
    categoryType: 'income',
    colorCode: '#5856D6',
    iconName: 'storefront-outline',
    description: 'Business profits, sales revenue, and entrepreneurial income'
  },
  {
    name: 'Rental',
    categoryType: 'income',
    colorCode: '#00CED1',
    iconName: 'home-outline',
    description: 'Property rental income and real estate earnings'
  },
  {
    name: 'Government Benefits',
    categoryType: 'income',
    colorCode: '#32D74B',
    iconName: 'shield-checkmark-outline',
    description: 'Social security, unemployment benefits, and government aid'
  },
  {
    name: 'Other Income',
    categoryType: 'income',
    colorCode: '#32D74B',
    iconName: 'add-circle-outline',
    description: 'Miscellaneous income, bonuses, and unexpected earnings'
  }
];

export const ALL_SYSTEM_CATEGORIES: SystemCategoryData[] = [
  ...SYSTEM_EXPENSE_CATEGORIES,
  ...SYSTEM_INCOME_CATEGORIES
];

export const CATEGORY_ICONS = [
  // Finance & Money
  'briefcase-outline', 'card-outline', 'cash-outline', 'trending-up-outline', 'trending-down-outline', 'pie-chart-outline',
  'bar-chart-outline', 'analytics-outline', 'wallet-outline', 'card-outline', 'business-outline',
  
  // Food & Dining
  'restaurant-outline', 'fast-food-outline', 'cafe-outline', 'wine-outline', 'pizza-outline', 'fish-outline',
  'nutrition-outline', 'restaurant-outline',
  
  // Transportation
  'car-outline', 'bus-outline', 'train-outline', 'airplane-outline', 'bicycle-outline', 'walk-outline',
  'boat-outline', 'car-outline',
  
  // Home & Utilities
  'home-outline', 'build-outline', 'flash-outline', 'water-outline', 'flame-outline',
  'wifi-outline', 'tv-outline', 'bed-outline', 'home-outline',
  
  // Healthcare
  'medical-outline', 'fitness-outline', 'heart-outline', 'pulse-outline', 'medical-outline',
  'thermometer-outline', 'bandage-outline', 'glasses-outline',
  
  // Shopping & Retail
  'bag-outline', 'cart-outline', 'storefront-outline', 'pricetag-outline',
  'gift-outline', 'shirt-outline', 'watch-outline', 'diamond-outline', 'bag-outline',
  
  // Entertainment
  'musical-notes-outline', 'film-outline', 'game-controller-outline', 'headset-outline', 'camera-outline',
  'tv-outline', 'radio-outline', 'ticket-outline', 'football-outline',
  
  // Education & Work
  'book-outline', 'school-outline', 'library-outline', 'pencil-outline', 'laptop-outline',
  'desktop-outline', 'tablet-portrait-outline', 'phone-portrait-outline', 'calculator-outline',
  
  // Personal Care
  'cut-outline', 'brush-outline', 'flower-outline', 'leaf-outline', 'fitness-outline',
  'body-outline', 'hand-left-outline',
  
  // Travel & Tourism
  'airplane-outline', 'map-outline', 'compass-outline', 'globe-outline', 'location-outline',
  'camera-outline', 'backpack-outline', 'home-outline',
  
  // Miscellaneous
  'shield-outline', 'shield-checkmark-outline', 'umbrella-outline', 'sunny-outline', 'cloud-outline',
  'star-outline', 'heart-outline', 'people-outline', 'person-outline', 'calendar-outline',
  'time-outline', 'alarm-outline', 'notifications-outline', 'mail-outline', 'call-outline',
  'chatbox-outline', 'document-outline', 'folder-outline', 'archive-outline', 'trash-outline'
];

export const CATEGORY_COLORS = [
  // Primary colors
  '#FF6B35', '#4A90E2', '#7ED321', '#9013FE', '#F5A623',
  '#50C878', '#FF9500', '#FF2D92', '#34C759', '#007AFF',
  '#5856D6', '#32D74B', '#00CED1', '#20B2AA', '#FF69B4',
  
  // Secondary colors
  '#8B4513', '#2E8B57', '#4682B4', '#9ACD32', '#FF4500',
  '#DA70D6', '#40E0D0', '#EE82EE', '#F0E68C', '#DDA0DD',
  
  // Muted colors
  '#708090', '#778899', '#B0C4DE', '#D3D3D3', '#F5F5DC',
  '#FFF8DC', '#FFEFD5', '#FFE4E1', '#F0FFF0', '#F5FFFA'
];

export function getDefaultIconForCategory(categoryName: string): string {
  const lowerName = categoryName.toLowerCase();
  
  if (lowerName.includes('food') || lowerName.includes('dining') || lowerName.includes('restaurant')) {
    return 'restaurant-outline';
  }
  if (lowerName.includes('transport') || lowerName.includes('car') || lowerName.includes('gas')) {
    return 'car-outline';
  }
  if (lowerName.includes('home') || lowerName.includes('util') || lowerName.includes('rent')) {
    return 'home-outline';
  }
  if (lowerName.includes('entertain') || lowerName.includes('music') || lowerName.includes('movie')) {
    return 'musical-notes-outline';
  }
  if (lowerName.includes('health') || lowerName.includes('medical') || lowerName.includes('doctor')) {
    return 'medical-outline';
  }
  if (lowerName.includes('shop') || lowerName.includes('retail') || lowerName.includes('store')) {
    return 'bag-outline';
  }
  if (lowerName.includes('education') || lowerName.includes('school') || lowerName.includes('book')) {
    return 'book-outline';
  }
  if (lowerName.includes('personal') || lowerName.includes('care') || lowerName.includes('beauty')) {
    return 'cut-outline';
  }
  if (lowerName.includes('salary') || lowerName.includes('work') || lowerName.includes('job')) {
    return 'briefcase-outline';
  }
  if (lowerName.includes('invest') || lowerName.includes('stock') || lowerName.includes('trade')) {
    return 'trending-up-outline';
  }
  if (lowerName.includes('business') || lowerName.includes('company')) {
    return 'storefront-outline';
  }
  if (lowerName.includes('freelance') || lowerName.includes('contract')) {
    return 'laptop-outline';
  }
  if (lowerName.includes('travel') || lowerName.includes('vacation') || lowerName.includes('trip')) {
    return 'airplane-outline';
  }
  
  // Default fallback
  return 'ellipse-outline';
}

export function getDefaultColorForCategoryType(categoryType: Category['category_type']): string {
  return categoryType === 'income' ? '#34C759' : '#FF6B35';
}

export function validateSystemCategoryData(categoryData: SystemCategoryData): boolean {
  return !!(
    categoryData.name &&
    categoryData.categoryType &&
    ['income', 'expense'].includes(categoryData.categoryType) &&
    categoryData.colorCode &&
    /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(categoryData.colorCode) &&
    categoryData.iconName
  );
}