import * as SQLite from 'expo-sqlite';
import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';
import { ALL_SYSTEM_CATEGORIES } from '../systemCategories';

interface DatabaseConfig {
  name: string;
  version: string;
  displayName: string;
  size: number;
}

export class DatabaseService {
  private static instance: DatabaseService;
  private db: SQLite.WebSQLDatabase | null = null;
  private initializationPromise: Promise<void> | null = null;
  private isReady: boolean = false;
  private migrationsStarted: boolean = false;
  private queryCache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 30000; // 30 seconds
  private readonly config: DatabaseConfig = {
    name: 'finvibe.db',
    version: '1.0',
    displayName: 'FinVibe Database',
    size: 200000,
  };

  private constructor() {
    // Expo SQLite doesn't need these calls
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  private async getOrCreateDatabaseKey(): Promise<string> {
    try {
      let dbKey = await SecureStore.getItemAsync('database_key');
      
      if (!dbKey) {
        const randomBytes = await Crypto.getRandomBytesAsync(32); // 256 bits / 8 = 32 bytes
        dbKey = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
        await SecureStore.setItemAsync('database_key', dbKey);
      }
      
      return dbKey;
    } catch (error) {
      console.error('Error managing database key:', error);
      throw new Error('Failed to initialize database encryption key');
    }
  }

  public async initialize(): Promise<void> {
    // If already initialized, return silently
    if (this.isReady && this.db) {
      return;
    }

    // If initialization is in progress, wait for it
    if (this.initializationPromise) {
      console.log('Database initialization in progress, waiting...');
      return this.initializationPromise;
    }

    // Start initialization
    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  private async performInitialization(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log('🔧 Starting database initialization...');
      
      // Open database connection using Expo SQLite
      this.db = SQLite.openDatabase(this.config.name);
      console.log(`✅ Database connection opened (${Date.now() - startTime}ms)`);

      // Skip encryption for now - focus on getting basic functionality working
      // TODO: Re-enable encryption after basic functionality is verified
      
      // Verify database is accessible
      const accessStart = Date.now();
      await this.execSqlAsync('SELECT 1', []);
      console.log(`✅ Database accessibility verified (${Date.now() - accessStart}ms)`);
      
      // Create basic tables and indexes first
      const tablesStart = Date.now();
      await this.createTables();
      console.log(`✅ Database tables created (${Date.now() - tablesStart}ms)`);
      
      const indexesStart = Date.now();
      await this.createIndexes();
      console.log(`✅ Database indexes created (${Date.now() - indexesStart}ms)`);
      
      // Initialize system categories
      const categoriesStart = Date.now();
      await this.initializeSystemData();
      console.log(`✅ System data initialized (${Date.now() - categoriesStart}ms)`);
      
      this.isReady = true;
      const totalTime = Date.now() - startTime;
      console.log(`✅ Database initialized successfully (Total: ${totalTime}ms)`);
      
      // Warn if initialization took too long
      if (totalTime > 5000) {
        console.warn(`⚠️  Database initialization took ${totalTime}ms - consider optimization`);
      }
      
      // Run migrations asynchronously but track completion
      this.runMigrationsAsync();
    } catch (error) {
      const totalTime = Date.now() - startTime;
      console.error(`❌ Database initialization failed after ${totalTime}ms:`, error);
      this.isReady = false;
      this.db = null;
      throw new Error(`Database initialization failed: ${error}`);
    }
  }

  private migrationsCompleted: boolean = false;
  private migrationPromise: Promise<void> | null = null;

  private async runMigrationsAsync(): Promise<void> {
    // Only run migrations once
    if (this.migrationsStarted) {
      return this.migrationPromise || Promise.resolve();
    }
    this.migrationsStarted = true;

    // Store the migration promise for waiting
    this.migrationPromise = new Promise<void>((resolve) => {
      // Run migrations with minimal delay to avoid blocking
      setTimeout(async () => {
        try {
          // Use the dynamic migration system
          const { MigrationManager } = require('../migrations');
          const migrationManager = MigrationManager.getInstance(this);
          
          const status = await migrationManager.initialize();
          console.log(`✅ Migration system ready. Version: ${status.currentVersion}, Pending: ${status.pendingCount}`);
          this.migrationsCompleted = true;
        } catch (error) {
          console.error('💥 Migration system initialization failed:', error);
          console.warn('⚠️  Database will continue with basic schema only');
          this.migrationsCompleted = true; // Mark as completed even if failed
        } finally {
          resolve();
        }
      }, 50); // Very short delay
    });

    return this.migrationPromise;
  }

  public async waitForMigrations(): Promise<void> {
    if (this.migrationsCompleted) {
      return;
    }
    if (this.migrationPromise) {
      await this.migrationPromise;
    }
  }

  private async createTables(): Promise<void> {
    const createTablesSQL = [
      // Accounts table
      `CREATE TABLE IF NOT EXISTS accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('checking', 'savings', 'credit', 'loan', 'investment')),
        balance DECIMAL(15,2) DEFAULT 0.00,
        currency TEXT DEFAULT 'INR',
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'local' CHECK (sync_status IN ('local', 'synced', 'pending', 'conflict'))
      )`,
      
      // Categories table
      `CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        parent_id INTEGER,
        category_type TEXT NOT NULL CHECK (category_type IN ('income', 'expense')),
        color_code TEXT DEFAULT '#4A90E2',
        icon_name TEXT DEFAULT 'category',
        is_system BOOLEAN DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'local',
        FOREIGN KEY (parent_id) REFERENCES categories(id)
      )`,
      
      // Transactions table
      `CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        description TEXT NOT NULL,
        category_id INTEGER,
        transaction_type TEXT NOT NULL CHECK (transaction_type IN ('income', 'expense', 'transfer')),
        transaction_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sms_source TEXT,
        confidence_score DECIMAL(3,2),
        is_recurring BOOLEAN DEFAULT 0,
        recurring_pattern TEXT,
        sync_status TEXT DEFAULT 'local',
        hash TEXT UNIQUE,
        FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id)
      )`,
      
      // User settings table
      `CREATE TABLE IF NOT EXISTS user_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // SMS patterns table
      `CREATE TABLE IF NOT EXISTS sms_patterns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pattern TEXT NOT NULL,
        pattern_type TEXT NOT NULL,
        confidence DECIMAL(3,2) DEFAULT 0.0,
        usage_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    for (const sql of createTablesSQL) {
      await this.execSqlAsync(sql, []);
    }
  }

  private async createIndexes(): Promise<void> {
    const indexesSQL = [
      'CREATE INDEX IF NOT EXISTS idx_transactions_account_date ON transactions(account_id, transaction_date DESC)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category_id)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_type_date ON transactions(transaction_type, transaction_date DESC)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_hash ON transactions(hash)',
      'CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(type)',
      'CREATE INDEX IF NOT EXISTS idx_accounts_active ON accounts(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(category_type)',
      'CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_id)',
    ];

    for (const sql of indexesSQL) {
      await this.execSqlAsync(sql, []);
    }
  }

  private async initializeSystemData(): Promise<void> {
    console.log('🔧 Initializing system data...');
    // Initialize system categories directly without going through repository
    // to avoid circular dependency during database initialization
    await this.createSystemCategories();
    console.log('✅ System data initialization completed');
  }

  private async createSystemCategories(): Promise<void> {
    console.log('📦 Creating system categories...');

    try {
      // Check if any system categories exist (faster single query)
      const existingCount = await this.execSqlAsync(
        'SELECT COUNT(*) as count FROM categories WHERE is_system = 1',
        []
      );

      if (existingCount.rows.item(0).count > 0) {
        console.log('• System categories already exist, skipping initialization');
        return;
      }

      // Batch insert all categories in a single transaction
      const now = new Date().toISOString();
      const insertSQL = `INSERT INTO categories (name, parent_id, category_type, color_code, icon_name, is_system, created_at, sync_status)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
      
      // Use database transaction for atomic batch insert
      await new Promise<void>((resolve, reject) => {
        this.db!.transaction(
          (tx) => {
            for (const cat of ALL_SYSTEM_CATEGORIES) {
              tx.executeSql(
                insertSQL,
                [
                  cat.name,
                  null,
                  cat.categoryType,
                  cat.colorCode,
                  cat.iconName,
                  1, // is_system = true
                  now,
                  'local'
                ],
                () => {}, // Success callback (empty for batch)
                (_, error) => {
                  console.error(`Failed to create system category ${cat.name}:`, error);
                  return false; // Continue with other inserts
                }
              );
            }
          },
          (error) => reject(error),
          () => {
            console.log(`✅ Created ${ALL_SYSTEM_CATEGORIES.length} system categories in batch`);
            resolve();
          }
        );
      });
      
    } catch (error) {
      console.error('Failed to create system categories:', error);
      throw error;
    }
  }

  private execSqlAsync(sql: string, params: any[] = []): Promise<SQLite.SQLResultSet> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }
      
      this.db.transaction(tx => {
        tx.executeSql(
          sql, 
          params,
          (_, result) => resolve(result),
          (_, error) => {
            console.error('Database query error:', error, 'SQL:', sql, 'Params:', params);
            reject(error);
            return false;
          }
        );
      });
    });
  }

  public async executeSql(sql: string, params: any[] = []): Promise<SQLite.SQLResultSet[]> {
    if (!this.isReady || !this.db) {
      throw new Error('Database not initialized or not ready');
    }

    // Check cache for SELECT queries only
    const isSelectQuery = sql.trim().toUpperCase().startsWith('SELECT');
    let cacheKey = '';
    
    if (isSelectQuery) {
      cacheKey = `${sql}|${JSON.stringify(params)}`;
      const cached = this.queryCache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.CACHE_DURATION) {
        // Return cached result for static data
        if (sql.includes('FROM categories') || sql.includes('FROM accounts') || sql.includes('COUNT(*)')) {
          return [cached.data];
        }
      }
    }

    const startTime = Date.now();
    
    try {
      const result = await this.execSqlAsync(sql, params);
      const duration = Date.now() - startTime;
      
      // Cache SELECT queries for static and frequently accessed data
      if (isSelectQuery) {
        // Cache categories, accounts, and count queries aggressively
        if (sql.includes('FROM categories') || 
            sql.includes('FROM accounts') || 
            sql.includes('COUNT(*)') ||
            duration < 100) { // Cache fast queries
          this.queryCache.set(cacheKey, {
            data: result,
            timestamp: Date.now()
          });
        }
      }
      
      // Performance monitoring
      if (duration > 1000) {
        console.warn(`🐌 Very slow query detected (${duration}ms): ${sql.substring(0, 100)}...`);
      } else if (duration > 500) {
        console.warn(`⏱️  Slow query detected (${duration}ms): ${sql.substring(0, 100)}...`);
      }
      
      return [result];
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ SQL execution error (${duration}ms):`, { sql, params, error });
      throw error;
    }
  }

  /**
   * Execute SQL without automatic transaction wrapping - for migration system
   * @param sql SQL statement to execute
   * @param params Parameters for the SQL statement
   * @returns Promise resolving to SQL result
   */
  /**
   * Execute SQL without automatic transaction wrapping - for migration system
   * Uses direct tx.executeSql instead of wrapping in another transaction
   * @param sql SQL statement to execute
   * @param params Parameters for the SQL statement
   * @returns Promise resolving to SQL result
   */
  public async executeSqlDirect(sql: string, params: any[] = []): Promise<SQLite.SQLResultSet> {
    if (!this.isReady || !this.db) {
      throw new Error('Database not initialized or not ready');
    }

    const startTime = Date.now();
    
    try {
      // For migration system, we need to handle SQL differently
      // This method should only be used during migrations
      const result = await this.execSqlAsync(sql, params);
      
      const duration = Date.now() - startTime;
      
      // Performance monitoring
      if (duration > 1000) {
        console.warn(`🐌 Very slow direct query detected (${duration}ms): ${sql.substring(0, 100)}...`);
      } else if (duration > 500) {
        console.warn(`⏱️  Slow direct query detected (${duration}ms): ${sql.substring(0, 100)}...`);
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ SQL direct execution error (${duration}ms):`, { sql, params, error });
      throw error;
    }
  }

  public async transaction(fn: (tx: SQLite.SQLTransaction) => Promise<void>): Promise<void> {
    if (!this.isReady || !this.db) {
      throw new Error('Database not initialized or not ready');
    }

    return new Promise((resolve, reject) => {
      this.db!.transaction(
        fn,
        (error: any) => reject(error),
        () => resolve()
      );
    });
  }

  public clearCache(table?: string): void {
    if (table) {
      // Clear cache for specific table
      for (const [key] of this.queryCache) {
        if (key.includes(`FROM ${table}`)) {
          this.queryCache.delete(key);
        }
      }
    } else {
      // Clear all cache
      this.queryCache.clear();
    }
  }

  public async close(): Promise<void> {
    if (this.db) {
      // Expo SQLite WebSQLDatabase doesn't have a close method in the current API
      // The database will be closed automatically when the app terminates
      this.db = null;
      this.isReady = false;
      this.queryCache.clear();
    }
  }

  public isInitialized(): boolean {
    return this.isReady && this.db !== null;
  }

  public async waitForInitialization(): Promise<void> {
    if (this.isReady) return;
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  // Health check method
  public async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy', details: any }> {
    try {
      if (!this.db) {
        return { status: 'unhealthy', details: 'Database not initialized' };
      }

      const startTime = Date.now();
      await this.executeSql('SELECT 1');
      const queryTime = Date.now() - startTime;

      const [accountCount] = await this.executeSql('SELECT COUNT(*) as count FROM accounts');
      const [transactionCount] = await this.executeSql('SELECT COUNT(*) as count FROM transactions');

      return {
        status: 'healthy',
        details: {
          queryTime: `${queryTime}ms`,
          accountCount: accountCount.rows.item(0).count,
          transactionCount: transactionCount.rows.item(0).count,
          database: this.config.name,
        }
      };
    } catch (error) {
      return { status: 'unhealthy', details: error };
    }
  }
}