import { Account, AccountMetadata } from '@/shared/types';
import { BaseRepository } from './BaseRepository';

export class AccountRepository extends BaseRepository<Account> {
  private migrationsAwaited: boolean = false;
  private metadataTableEnsured: boolean = false;
  
  async findById(id: number): Promise<Account | null> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM accounts WHERE id = ? AND is_active = 1',
      [id]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows.item(0) as Account;
  }

  async findAll(): Promise<Account[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM accounts WHERE is_active = 1 ORDER BY created_at DESC'
    );

    const accounts: Account[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      accounts.push(result.rows.item(i) as Account);
    }

    return accounts;
  }

  async findByType(type: Account['type']): Promise<Account[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM accounts WHERE type = ? AND is_active = 1 ORDER BY created_at DESC',
      [type]
    );

    const accounts: Account[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      accounts.push(result.rows.item(i) as Account);
    }

    return accounts;
  }

  async create(accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'>): Promise<Account> {
    await this.ensureInitialized();
    
    const now = this.getCurrentTimestamp();
    
    const [result] = await this.db.executeSql(
      `INSERT INTO accounts (name, type, balance, currency, is_active, created_at, updated_at, sync_status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        accountData.name,
        accountData.type,
        accountData.balance,
        accountData.currency || 'INR',
        accountData.is_active ? 1 : 0,
        now,
        now,
        accountData.sync_status || 'local'
      ]
    );

    const insertId = result.insertId;
    if (insertId === undefined) {
      throw new Error('Failed to create account - no insert ID returned');
    }

    const newAccount = await this.findById(insertId);
    if (!newAccount) {
      throw new Error('Failed to create account');
    }

    return newAccount;
  }

  async update(id: number, updates: Partial<Account>): Promise<Account> {
    await this.ensureInitialized();
    
    const existingAccount = await this.findById(id);
    if (!existingAccount) {
      throw new Error(`Account with id ${id} not found`);
    }

    const now = this.getCurrentTimestamp();
    const setClause: string[] = [];
    const values: any[] = [];

    if (updates.name !== undefined) {
      setClause.push('name = ?');
      values.push(updates.name);
    }
    if (updates.type !== undefined) {
      setClause.push('type = ?');
      values.push(updates.type);
    }
    if (updates.balance !== undefined) {
      setClause.push('balance = ?');
      values.push(updates.balance);
    }
    if (updates.currency !== undefined) {
      setClause.push('currency = ?');
      values.push(updates.currency);
    }
    if (updates.is_active !== undefined) {
      setClause.push('is_active = ?');
      values.push(updates.is_active ? 1 : 0);
    }
    if (updates.sync_status !== undefined) {
      setClause.push('sync_status = ?');
      values.push(updates.sync_status);
    }

    setClause.push('updated_at = ?');
    values.push(now);
    values.push(id);

    await this.db.executeSql(
      `UPDATE accounts SET ${setClause.join(', ')} WHERE id = ?`,
      values
    );

    const updatedAccount = await this.findById(id);
    if (!updatedAccount) {
      throw new Error('Failed to retrieve updated account');
    }

    return updatedAccount;
  }

  async delete(id: number): Promise<boolean> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'UPDATE accounts SET is_active = 0, updated_at = ? WHERE id = ?',
      [this.getCurrentTimestamp(), id]
    );

    return result.rowsAffected > 0;
  }

  async updateBalance(id: number, amount: number, operation: 'add' | 'subtract' | 'set'): Promise<Account> {
    await this.ensureInitialized();
    
    let sql: string;
    let params: any[];
    const now = this.getCurrentTimestamp();

    switch (operation) {
      case 'add':
        sql = 'UPDATE accounts SET balance = balance + ?, updated_at = ? WHERE id = ?';
        params = [amount, now, id];
        break;
      case 'subtract':
        sql = 'UPDATE accounts SET balance = balance - ?, updated_at = ? WHERE id = ?';
        params = [amount, now, id];
        break;
      case 'set':
        sql = 'UPDATE accounts SET balance = ?, updated_at = ? WHERE id = ?';
        params = [amount, now, id];
        break;
      default:
        throw new Error('Invalid balance operation');
    }

    await this.db.executeSql(sql, params);
    
    const updatedAccount = await this.findById(id);
    if (!updatedAccount) {
      throw new Error('Failed to retrieve updated account');
    }

    return updatedAccount;
  }

  async getTotalBalance(): Promise<number> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT SUM(balance) as total FROM accounts WHERE is_active = 1 AND type != "credit"'
    );

    return result.rows.item(0).total || 0;
  }

  async getBalanceByType(): Promise<Record<Account['type'], number>> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT type, SUM(balance) as total FROM accounts WHERE is_active = 1 GROUP BY type'
    );

    const balancesByType: Record<Account['type'], number> = {
      checking: 0,
      savings: 0,
      credit: 0,
      loan: 0,
      investment: 0,
    };

    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      balancesByType[row.type as Account['type']] = row.total || 0;
    }

    return balancesByType;
  }

  // Account Metadata CRUD Methods
  private async ensureAccountMetadataReady(): Promise<void> {
    // Only wait for migrations once
    if (!this.migrationsAwaited) {
      await this.db.waitForMigrations();
      this.migrationsAwaited = true;
    }
    
    // Only ensure table creation once
    if (!this.metadataTableEnsured) {
      await this.ensureAccountMetadataTable();
      this.metadataTableEnsured = true;
    }
  }

  private async ensureAccountMetadataTable(): Promise<void> {
    // Create account_metadata table if it doesn't exist (fallback mechanism)
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS account_metadata (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        credit_limit DECIMAL(15, 2),
        outstanding_balance DECIMAL(15, 2),
        minimum_payment_due DECIMAL(15, 2),
        payment_due_date TEXT,
        last_statement_date TEXT,
        principal_amount DECIMAL(15, 2),
        current_principal DECIMAL(15, 2),
        interest_rate DECIMAL(5, 2),
        emi_amount DECIMAL(15, 2),
        tenure INTEGER,
        remaining_tenure INTEGER,
        next_emi_date TEXT,
        loan_start_date TEXT,
        total_interest_paid DECIMAL(15, 2),
        last_updated TEXT,
        auto_calculate BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE,
        UNIQUE (account_id)
      )
    `;
    
    try {
      await this.db.executeSql(createTableSQL, []);
      
      // Create indexes
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_account_metadata_account_id ON account_metadata (account_id)',
        'CREATE INDEX IF NOT EXISTS idx_account_metadata_payment_due_date ON account_metadata (payment_due_date)',
        'CREATE INDEX IF NOT EXISTS idx_account_metadata_next_emi_date ON account_metadata (next_emi_date)'
      ];
      
      for (const indexSQL of indexes) {
        await this.db.executeSql(indexSQL, []);
      }
    } catch (error) {
      console.error('Failed to create account_metadata table fallback:', error);
      // Don't throw - let the original operation proceed and fail with a clearer error
    }
  }

  async createAccountMetadata(accountId: number, metadata: AccountMetadata): Promise<void> {
    await this.ensureInitialized();
    
    // Ensure migrations and table creation (optimized - only runs once)
    await this.ensureAccountMetadataReady();
    
    const now = new Date().toISOString();
    
    await this.db.executeSql(
      `INSERT INTO account_metadata (
        account_id, credit_limit, outstanding_balance, minimum_payment_due,
        payment_due_date, last_statement_date, principal_amount, current_principal,
        interest_rate, emi_amount, tenure, remaining_tenure, next_emi_date,
        loan_start_date, total_interest_paid, last_updated, auto_calculate,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        accountId,
        metadata.creditLimit || null,
        metadata.outstandingBalance || null,
        metadata.minimumPaymentDue || null,
        metadata.paymentDueDate || null,
        metadata.lastStatementDate || null,
        metadata.principalAmount || null,
        metadata.currentPrincipal || null,
        metadata.interestRate || null,
        metadata.emiAmount || null,
        metadata.tenure || null,
        metadata.remainingTenure || null,
        metadata.nextEmiDate || null,
        metadata.loanStartDate || null,
        metadata.totalInterestPaid || null,
        metadata.lastUpdated || now,
        metadata.autoCalculate !== undefined ? (metadata.autoCalculate ? 1 : 0) : 1,
        now,
        now
      ]
    );
  }

  async updateAccountMetadata(accountId: number, metadata: Partial<AccountMetadata>): Promise<void> {
    await this.ensureInitialized();
    
    // Ensure migrations and table creation (optimized - only runs once)
    await this.ensureAccountMetadataReady();
    
    const now = new Date().toISOString();
    const setClause: string[] = [];
    const values: any[] = [];

    if (metadata.creditLimit !== undefined) {
      setClause.push('credit_limit = ?');
      values.push(metadata.creditLimit);
    }
    if (metadata.outstandingBalance !== undefined) {
      setClause.push('outstanding_balance = ?');
      values.push(metadata.outstandingBalance);
    }
    if (metadata.minimumPaymentDue !== undefined) {
      setClause.push('minimum_payment_due = ?');
      values.push(metadata.minimumPaymentDue);
    }
    if (metadata.paymentDueDate !== undefined) {
      setClause.push('payment_due_date = ?');
      values.push(metadata.paymentDueDate);
    }
    if (metadata.lastStatementDate !== undefined) {
      setClause.push('last_statement_date = ?');
      values.push(metadata.lastStatementDate);
    }
    if (metadata.principalAmount !== undefined) {
      setClause.push('principal_amount = ?');
      values.push(metadata.principalAmount);
    }
    if (metadata.currentPrincipal !== undefined) {
      setClause.push('current_principal = ?');
      values.push(metadata.currentPrincipal);
    }
    if (metadata.interestRate !== undefined) {
      setClause.push('interest_rate = ?');
      values.push(metadata.interestRate);
    }
    if (metadata.emiAmount !== undefined) {
      setClause.push('emi_amount = ?');
      values.push(metadata.emiAmount);
    }
    if (metadata.tenure !== undefined) {
      setClause.push('tenure = ?');
      values.push(metadata.tenure);
    }
    if (metadata.remainingTenure !== undefined) {
      setClause.push('remaining_tenure = ?');
      values.push(metadata.remainingTenure);
    }
    if (metadata.nextEmiDate !== undefined) {
      setClause.push('next_emi_date = ?');
      values.push(metadata.nextEmiDate);
    }
    if (metadata.loanStartDate !== undefined) {
      setClause.push('loan_start_date = ?');
      values.push(metadata.loanStartDate);
    }
    if (metadata.totalInterestPaid !== undefined) {
      setClause.push('total_interest_paid = ?');
      values.push(metadata.totalInterestPaid);
    }
    if (metadata.lastUpdated !== undefined) {
      setClause.push('last_updated = ?');
      values.push(metadata.lastUpdated);
    }
    if (metadata.autoCalculate !== undefined) {
      setClause.push('auto_calculate = ?');
      values.push(metadata.autoCalculate ? 1 : 0);
    }

    if (setClause.length === 0) {
      return; // No updates to make
    }

    setClause.push('updated_at = ?');
    values.push(now);
    values.push(accountId);

    await this.db.executeSql(
      `UPDATE account_metadata SET ${setClause.join(', ')} WHERE account_id = ?`,
      values
    );
  }

  async getAccountMetadata(accountId: number): Promise<AccountMetadata | null> {
    await this.ensureInitialized();
    
    // Ensure migrations and table creation (optimized - only runs once)
    await this.ensureAccountMetadataReady();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM account_metadata WHERE account_id = ?',
      [accountId]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows.item(0);
    
    return {
      creditLimit: row.credit_limit,
      outstandingBalance: row.outstanding_balance,
      minimumPaymentDue: row.minimum_payment_due,
      paymentDueDate: row.payment_due_date,
      lastStatementDate: row.last_statement_date,
      principalAmount: row.principal_amount,
      currentPrincipal: row.current_principal,
      interestRate: row.interest_rate,
      emiAmount: row.emi_amount,
      tenure: row.tenure,
      remainingTenure: row.remaining_tenure,
      nextEmiDate: row.next_emi_date,
      loanStartDate: row.loan_start_date,
      totalInterestPaid: row.total_interest_paid,
      lastUpdated: row.last_updated,
      autoCalculate: row.auto_calculate === 1,
    };
  }

  async getAccountWithMetadata(accountId: number): Promise<Account | null> {
    await this.ensureInitialized();
    
    const account = await this.findById(accountId);
    if (!account) {
      return null;
    }

    const metadata = await this.getAccountMetadata(accountId);
    if (metadata) {
      return {
        ...account,
        metadata
      };
    }
    return account;
  }

  async deleteAccountMetadata(accountId: number): Promise<void> {
    await this.ensureInitialized();
    
    // Ensure migrations and table creation (optimized - only runs once)
    await this.ensureAccountMetadataReady();
    
    await this.db.executeSql(
      'DELETE FROM account_metadata WHERE account_id = ?',
      [accountId]
    );
  }

  // Utility Methods
  async getCreditCardAccounts(): Promise<Account[]> {
    return await this.findByType('credit');
  }

  async getLoanAccounts(): Promise<Account[]> {
    return await this.findByType('loan');
  }

  async getAccountsByType(type: Account['type']): Promise<Account[]> {
    return await this.findByType(type);
  }

  // Enhanced delete method that also deletes metadata
  async deleteWithMetadata(id: number): Promise<boolean> {
    await this.ensureInitialized();
    
    // Delete metadata first (foreign key constraint will handle this automatically due to CASCADE)
    const deleteResult = await this.delete(id);
    
    return deleteResult;
  }
}