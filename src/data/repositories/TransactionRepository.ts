import { Transaction, TransactionMetadata } from '@/shared/types';
import { BaseRepository } from './BaseRepository';

export class TransactionRepository extends BaseRepository<Transaction> {

  async findById(id: number): Promise<Transaction | null> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions WHERE id = ?',
      [id]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows.item(0) as Transaction;
  }

  async findAll(): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions ORDER BY transaction_date DESC, created_at DESC'
    );

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }

  async findByAccount(accountId: number, limit?: number): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    let sql = 'SELECT * FROM transactions WHERE account_id = ? ORDER BY transaction_date DESC, created_at DESC';
    const params: any[] = [accountId];

    if (limit) {
      sql += ' LIMIT ?';
      params.push(limit);
    }

    const [result] = await this.db.executeSql(sql, params);

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }

  async findByDateRange(startDate: string, endDate: string): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions WHERE transaction_date BETWEEN ? AND ? ORDER BY transaction_date DESC',
      [startDate, endDate]
    );

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }

  async findByCategory(categoryId: number): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions WHERE category_id = ? ORDER BY transaction_date DESC',
      [categoryId]
    );

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }

  async create(transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'hash'>): Promise<Transaction> {
    await this.ensureInitialized();
    
    const now = this.getCurrentTimestamp();
    
    // Generate hash for duplicate detection
    const hashData = `${transactionData.account_id}-${transactionData.amount}-${transactionData.description}-${transactionData.transaction_date}`;
    const hash = this.generateHash(hashData);

    const [result] = await this.db.executeSql(
      `INSERT INTO transactions (
        account_id, amount, description, category_id, transaction_type, 
        transaction_date, created_at, updated_at, sms_source, confidence_score,
        is_recurring, recurring_pattern, sync_status, hash
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        transactionData.account_id,
        transactionData.amount,
        transactionData.description,
        transactionData.category_id,
        transactionData.transaction_type,
        transactionData.transaction_date,
        now,
        now,
        transactionData.sms_source,
        transactionData.confidence_score,
        transactionData.is_recurring ? 1 : 0,
        transactionData.recurring_pattern,
        transactionData.sync_status || 'local',
        hash
      ]
    );

    const insertId = result.insertId;
    if (insertId === undefined) {
      throw new Error('Failed to create transaction - no insert ID returned');
    }

    const newTransaction = await this.findById(insertId);
    if (!newTransaction) {
      throw new Error('Failed to create transaction');
    }

    return newTransaction;
  }

  async update(id: number, updates: Partial<Transaction>): Promise<Transaction> {
    await this.ensureInitialized();
    
    const existingTransaction = await this.findById(id);
    if (!existingTransaction) {
      throw new Error(`Transaction with id ${id} not found`);
    }

    const now = this.getCurrentTimestamp();
    const setClause: string[] = [];
    const values: any[] = [];

    if (updates.account_id !== undefined) {
      setClause.push('account_id = ?');
      values.push(updates.account_id);
    }
    if (updates.amount !== undefined) {
      setClause.push('amount = ?');
      values.push(updates.amount);
    }
    if (updates.description !== undefined) {
      setClause.push('description = ?');
      values.push(updates.description);
    }
    if (updates.category_id !== undefined) {
      setClause.push('category_id = ?');
      values.push(updates.category_id);
    }
    if (updates.transaction_type !== undefined) {
      setClause.push('transaction_type = ?');
      values.push(updates.transaction_type);
    }
    if (updates.transaction_date !== undefined) {
      setClause.push('transaction_date = ?');
      values.push(updates.transaction_date);
    }
    if (updates.sms_source !== undefined) {
      setClause.push('sms_source = ?');
      values.push(updates.sms_source);
    }
    if (updates.confidence_score !== undefined) {
      setClause.push('confidence_score = ?');
      values.push(updates.confidence_score);
    }
    if (updates.is_recurring !== undefined) {
      setClause.push('is_recurring = ?');
      values.push(updates.is_recurring ? 1 : 0);
    }
    if (updates.recurring_pattern !== undefined) {
      setClause.push('recurring_pattern = ?');
      values.push(updates.recurring_pattern);
    }
    if (updates.sync_status !== undefined) {
      setClause.push('sync_status = ?');
      values.push(updates.sync_status);
    }

    setClause.push('updated_at = ?');
    values.push(now);
    values.push(id);

    await this.db.executeSql(
      `UPDATE transactions SET ${setClause.join(', ')} WHERE id = ?`,
      values
    );

    const updatedTransaction = await this.findById(id);
    if (!updatedTransaction) {
      throw new Error('Failed to retrieve updated transaction');
    }

    return updatedTransaction;
  }

  async delete(id: number): Promise<boolean> {
    await this.ensureInitialized();
    
    console.log(`TransactionRepository: Attempting to delete transaction with id: ${id}`);
    
    try {
      const [result] = await this.db.executeSql(
        'DELETE FROM transactions WHERE id = ?',
        [id]
      );

      console.log(`TransactionRepository: Delete result - rowsAffected: ${result.rowsAffected}`);
      
      const success = result.rowsAffected > 0;
      
      if (!success) {
        console.warn(`TransactionRepository: No rows affected when deleting transaction ${id}`);
      }
      
      return success;
    } catch (error) {
      console.error(`TransactionRepository: Error deleting transaction ${id}:`, error);
      throw error;
    }
  }

  async getAccountBalance(accountId: number): Promise<number> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      `SELECT 
        COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as total_income,
        COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as total_expense
       FROM transactions WHERE account_id = ?`,
      [accountId]
    );

    const row = result.rows.item(0);
    return row.total_income - row.total_expense;
  }

  async getSpendingByCategory(accountId: number, startDate: string, endDate: string): Promise<{category_id: number, total: number}[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      `SELECT category_id, SUM(amount) as total 
       FROM transactions 
       WHERE account_id = ? AND transaction_type = 'expense' 
       AND transaction_date BETWEEN ? AND ?
       GROUP BY category_id 
       ORDER BY total DESC`,
      [accountId, startDate, endDate]
    );

    const spending: {category_id: number, total: number}[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      spending.push(result.rows.item(i));
    }

    return spending;
  }

  async getMonthlyTrends(accountId: number, months: number = 12): Promise<{month: string, income: number, expense: number}[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      `SELECT 
        strftime('%Y-%m', transaction_date) as month,
        COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as income,
        COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as expense
       FROM transactions 
       WHERE account_id = ? 
       AND transaction_date >= date('now', '-${months} months')
       GROUP BY strftime('%Y-%m', transaction_date)
       ORDER BY month DESC`,
      [accountId]
    );

    const trends: {month: string, income: number, expense: number}[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      trends.push(result.rows.item(i));
    }

    return trends;
  }

  // ===== TRANSACTION METADATA METHODS =====

  /**
   * Creates transaction metadata record
   */
  async createMetadata(transactionId: number, metadata: Partial<TransactionMetadata>): Promise<void> {
    await this.ensureInitialized();
    
    const now = this.getCurrentTimestamp();
    
    await this.db.executeSql(
      `INSERT INTO transaction_metadata (
        transaction_id, principal_component, interest_component, fee_component,
        remaining_principal_after_payment, emi_number, payment_type,
        merchant_name, merchant_category, reward_points, available_credit_after_transaction,
        installment_info, original_sms_text, parsing_confidence, pattern_matched,
        bank_identifier, extracted_fields, source, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        transactionId,
        metadata.loanDetails?.principalComponent || null,
        metadata.loanDetails?.interestComponent || null,
        metadata.loanDetails?.feeComponent || null,
        metadata.loanDetails?.remainingPrincipalAfterPayment || null,
        metadata.loanDetails?.emiNumber || null,
        metadata.loanDetails?.paymentType || null,
        metadata.creditCardDetails?.merchantName || null,
        metadata.creditCardDetails?.merchantCategory || null,
        metadata.creditCardDetails?.rewardPoints || null,
        metadata.creditCardDetails?.availableCreditAfterTransaction || null,
        metadata.creditCardDetails?.installmentInfo ? JSON.stringify(metadata.creditCardDetails.installmentInfo) : null,
        metadata.smsDetails?.originalText || null,
        metadata.smsDetails?.parsingConfidence || null,
        metadata.smsDetails?.patternMatched || null,
        metadata.smsDetails?.bankIdentifier || null,
        metadata.smsDetails?.extractedFields ? JSON.stringify(metadata.smsDetails.extractedFields) : null,
        metadata.source || 'sms',
        now,
        now
      ]
    );
  }

  /**
   * Updates transaction metadata
   */
  async updateMetadata(transactionId: number, metadata: Partial<TransactionMetadata>): Promise<void> {
    await this.ensureInitialized();
    
    // Build dynamic update query based on provided fields
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    
    if (metadata.loanDetails?.principalComponent !== undefined) {
      updateFields.push('principal_component = ?');
      updateValues.push(metadata.loanDetails.principalComponent);
    }
    if (metadata.loanDetails?.interestComponent !== undefined) {
      updateFields.push('interest_component = ?');
      updateValues.push(metadata.loanDetails.interestComponent);
    }
    if (metadata.loanDetails?.feeComponent !== undefined) {
      updateFields.push('fee_component = ?');
      updateValues.push(metadata.loanDetails.feeComponent);
    }
    if (metadata.loanDetails?.remainingPrincipalAfterPayment !== undefined) {
      updateFields.push('remaining_principal_after_payment = ?');
      updateValues.push(metadata.loanDetails.remainingPrincipalAfterPayment);
    }
    if (metadata.loanDetails?.emiNumber !== undefined) {
      updateFields.push('emi_number = ?');
      updateValues.push(metadata.loanDetails.emiNumber);
    }
    if (metadata.loanDetails?.paymentType !== undefined) {
      updateFields.push('payment_type = ?');
      updateValues.push(metadata.loanDetails.paymentType);
    }
    if (metadata.creditCardDetails?.merchantName !== undefined) {
      updateFields.push('merchant_name = ?');
      updateValues.push(metadata.creditCardDetails.merchantName);
    }
    if (metadata.creditCardDetails?.merchantCategory !== undefined) {
      updateFields.push('merchant_category = ?');
      updateValues.push(metadata.creditCardDetails.merchantCategory);
    }
    if (metadata.creditCardDetails?.rewardPoints !== undefined) {
      updateFields.push('reward_points = ?');
      updateValues.push(metadata.creditCardDetails.rewardPoints);
    }
    if (metadata.creditCardDetails?.availableCreditAfterTransaction !== undefined) {
      updateFields.push('available_credit_after_transaction = ?');
      updateValues.push(metadata.creditCardDetails.availableCreditAfterTransaction);
    }
    if (metadata.creditCardDetails?.installmentInfo !== undefined) {
      updateFields.push('installment_info = ?');
      updateValues.push(metadata.creditCardDetails.installmentInfo ? JSON.stringify(metadata.creditCardDetails.installmentInfo) : null);
    }
    if (metadata.smsDetails?.originalText !== undefined) {
      updateFields.push('original_sms_text = ?');
      updateValues.push(metadata.smsDetails.originalText);
    }
    if (metadata.smsDetails?.parsingConfidence !== undefined) {
      updateFields.push('parsing_confidence = ?');
      updateValues.push(metadata.smsDetails.parsingConfidence);
    }
    if (metadata.smsDetails?.patternMatched !== undefined) {
      updateFields.push('pattern_matched = ?');
      updateValues.push(metadata.smsDetails.patternMatched);
    }
    if (metadata.smsDetails?.bankIdentifier !== undefined) {
      updateFields.push('bank_identifier = ?');
      updateValues.push(metadata.smsDetails.bankIdentifier);
    }
    if (metadata.smsDetails?.extractedFields !== undefined) {
      updateFields.push('extracted_fields = ?');
      updateValues.push(metadata.smsDetails.extractedFields ? JSON.stringify(metadata.smsDetails.extractedFields) : null);
    }
    if (metadata.source !== undefined) {
      updateFields.push('source = ?');
      updateValues.push(metadata.source);
    }
    
    if (updateFields.length === 0) {
      return; // Nothing to update
    }
    
    // Always update the updated_at timestamp
    updateFields.push('updated_at = ?');
    updateValues.push(this.getCurrentTimestamp());
    
    updateValues.push(transactionId);
    
    await this.db.executeSql(
      `UPDATE transaction_metadata SET ${updateFields.join(', ')} WHERE transaction_id = ?`,
      updateValues
    );
  }

  /**
   * Gets transaction metadata by transaction ID
   */
  async getMetadata(transactionId: number): Promise<TransactionMetadata | null> {
    await this.ensureInitialized();
    
    const [results] = await this.db.executeSql(
      `SELECT * FROM transaction_metadata WHERE transaction_id = ?`,
      [transactionId]
    );
    
    if (results.rows.length === 0) {
      return null;
    }
    
    const row = results.rows.item(0);
    
    // Parse JSON fields
    const metadata = {
      ...row,
      installment_info: row.installment_info ? JSON.parse(row.installment_info) : null,
      extracted_fields: row.extracted_fields ? JSON.parse(row.extracted_fields) : null
    };
    
    return metadata;
  }

  /**
   * Deletes transaction metadata
   */
  async deleteMetadata(transactionId: number): Promise<void> {
    await this.ensureInitialized();
    
    await this.db.executeSql(
      `DELETE FROM transaction_metadata WHERE transaction_id = ?`,
      [transactionId]
    );
  }

  /**
   * Creates transaction with metadata in a single operation
   */
  async createWithMetadata(
    transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'hash'>, 
    metadata?: TransactionMetadata
  ): Promise<Transaction> {
    await this.ensureInitialized();
    
    // Create the transaction first
    const transaction = await this.create(transactionData);
    
    // Add metadata if provided
    if (metadata && transaction.id) {
      await this.createMetadata(transaction.id, metadata);
    }
    
    return transaction;
  }

  /**
   * Gets transaction with its metadata
   */
  async findByIdWithMetadata(id: number): Promise<(Transaction & { metadata?: TransactionMetadata }) | null> {
    await this.ensureInitialized();
    
    const transaction = await this.findById(id);
    if (!transaction) {
      return null;
    }
    
    const metadata = await this.getMetadata(id);
    
    return {
      ...transaction,
      ...(metadata ? { metadata } : {})
    };
  }

  /**
   * Finds all transactions with their metadata for an account
   */
  async findByAccountWithMetadata(accountId: number, limit = 50, offset = 0): Promise<(Transaction & { metadata?: TransactionMetadata })[]> {
    await this.ensureInitialized();
    
    const [results] = await this.db.executeSql(
      `SELECT 
        t.*,
        tm.principal_component, tm.interest_component, tm.fee_component,
        tm.remaining_principal_after_payment, tm.emi_number, tm.payment_type,
        tm.merchant_name, tm.merchant_category, tm.reward_points,
        tm.available_credit_after_transaction, tm.installment_info,
        tm.original_sms_text, tm.parsing_confidence, tm.pattern_matched,
        tm.bank_identifier, tm.extracted_fields, tm.source as metadata_source
      FROM transactions t
      LEFT JOIN transaction_metadata tm ON t.id = tm.transaction_id
      WHERE t.account_id = ?
      ORDER BY t.transaction_date DESC, t.created_at DESC
      LIMIT ? OFFSET ?`,
      [accountId, limit, offset]
    );
    
    const transactions: (Transaction & { metadata?: TransactionMetadata })[] = [];
    
    for (let i = 0; i < results.rows.length; i++) {
      const row = results.rows.item(i);
      
      // Build transaction object
      const transaction: Transaction = {
        id: row.id,
        account_id: row.account_id,
        amount: row.amount,
        description: row.description,
        category_id: row.category_id,
        transaction_type: row.transaction_type,
        transaction_date: row.transaction_date,
        created_at: row.created_at,
        updated_at: row.updated_at,
        sms_source: row.sms_source,
        confidence_score: row.confidence_score,
        is_recurring: Boolean(row.is_recurring),
        recurring_pattern: row.recurring_pattern,
        sync_status: row.sync_status,
        hash: row.hash
      };
      
      // Build metadata object if it exists
      let metadata: TransactionMetadata | undefined = undefined;
      if (row.principal_component !== null || row.merchant_name !== null || row.original_sms_text !== null) {
        metadata = {
          source: row.metadata_source || 'sms',
          lastUpdated: new Date().toISOString()
        };
        
        // Add loan details if present
        if (row.principal_component !== null || row.emi_number !== null) {
          metadata.loanDetails = {};
          if (row.principal_component !== null) metadata.loanDetails.principalComponent = row.principal_component;
          if (row.interest_component !== null) metadata.loanDetails.interestComponent = row.interest_component;
          if (row.fee_component !== null) metadata.loanDetails.feeComponent = row.fee_component;
          if (row.remaining_principal_after_payment !== null) metadata.loanDetails.remainingPrincipalAfterPayment = row.remaining_principal_after_payment;
          if (row.emi_number !== null) metadata.loanDetails.emiNumber = row.emi_number;
          if (row.payment_type !== null) metadata.loanDetails.paymentType = row.payment_type;
        }
        
        // Add credit card details if present
        if (row.merchant_name !== null || row.reward_points !== null) {
          metadata.creditCardDetails = {};
          if (row.merchant_name !== null) metadata.creditCardDetails.merchantName = row.merchant_name;
          if (row.merchant_category !== null) metadata.creditCardDetails.merchantCategory = row.merchant_category;
          if (row.reward_points !== null) metadata.creditCardDetails.rewardPoints = row.reward_points;
          if (row.available_credit_after_transaction !== null) metadata.creditCardDetails.availableCreditAfterTransaction = row.available_credit_after_transaction;
          if (row.installment_info !== null) metadata.creditCardDetails.installmentInfo = JSON.parse(row.installment_info);
        }
        
        // Add SMS details if present
        if (row.original_sms_text !== null || row.parsing_confidence !== null) {
          metadata.smsDetails = {};
          if (row.original_sms_text !== null) metadata.smsDetails.originalText = row.original_sms_text;
          if (row.parsing_confidence !== null) metadata.smsDetails.parsingConfidence = row.parsing_confidence;
          if (row.pattern_matched !== null) metadata.smsDetails.patternMatched = row.pattern_matched;
          if (row.bank_identifier !== null) metadata.smsDetails.bankIdentifier = row.bank_identifier;
          if (row.extracted_fields !== null) metadata.smsDetails.extractedFields = JSON.parse(row.extracted_fields);
        }
      }
      
      transactions.push({
        ...transaction,
        ...(metadata ? { metadata } : {})
      });
    }
    
    return transactions;
  }

  async findDuplicates(hash: string): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions WHERE hash = ?',
      [hash]
    );

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }
}