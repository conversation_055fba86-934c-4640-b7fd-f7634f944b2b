import { Category } from '@/shared/types';
import { BaseRepository } from './BaseRepository';

export class CategoryRepository extends BaseRepository<Category> {

  async findById(id: number): Promise<Category | null> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM categories WHERE id = ?',
      [id]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows.item(0) as Category;
  }

  async findAll(): Promise<Category[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM categories ORDER BY category_type, name'
    );

    const categories: Category[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      categories.push(result.rows.item(i) as Category);
    }

    return categories;
  }

  async findByType(type: Category['category_type']): Promise<Category[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM categories WHERE category_type = ? ORDER BY name',
      [type]
    );

    const categories: Category[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      categories.push(result.rows.item(i) as Category);
    }

    return categories;
  }

  async findByParent(parentId: number | null): Promise<Category[]> {
    await this.ensureInitialized();
    
    let sql: string;
    let params: any[];

    if (parentId === null) {
      sql = 'SELECT * FROM categories WHERE parent_id IS NULL ORDER BY name';
      params = [];
    } else {
      sql = 'SELECT * FROM categories WHERE parent_id = ? ORDER BY name';
      params = [parentId];
    }

    const [result] = await this.db.executeSql(sql, params);

    const categories: Category[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      categories.push(result.rows.item(i) as Category);
    }

    return categories;
  }

  async create(categoryData: Omit<Category, 'id' | 'created_at'>): Promise<Category> {
    await this.ensureInitialized();
    
    const now = this.getCurrentTimestamp();
    
    const [result] = await this.db.executeSql(
      `INSERT INTO categories (name, parent_id, category_type, color_code, icon_name, is_system, created_at, sync_status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        categoryData.name,
        categoryData.parent_id,
        categoryData.category_type,
        categoryData.color_code || '#4A90E2',
        categoryData.icon_name || 'category',
        categoryData.is_system ? 1 : 0,
        now,
        categoryData.sync_status || 'local'
      ]
    );

    const insertId = result.insertId;
    if (insertId === undefined) {
      throw new Error('Failed to create category - no insert ID returned');
    }

    const newCategory = await this.findById(insertId);
    if (!newCategory) {
      throw new Error('Failed to create category');
    }

    return newCategory;
  }

  async update(id: number, updates: Partial<Category>): Promise<Category> {
    await this.ensureInitialized();
    
    const existingCategory = await this.findById(id);
    if (!existingCategory) {
      throw new Error(`Category with id ${id} not found`);
    }

    const setClause: string[] = [];
    const values: any[] = [];

    if (updates.name !== undefined) {
      setClause.push('name = ?');
      values.push(updates.name);
    }
    if (updates.parent_id !== undefined) {
      setClause.push('parent_id = ?');
      values.push(updates.parent_id);
    }
    if (updates.category_type !== undefined) {
      setClause.push('category_type = ?');
      values.push(updates.category_type);
    }
    if (updates.color_code !== undefined) {
      setClause.push('color_code = ?');
      values.push(updates.color_code);
    }
    if (updates.icon_name !== undefined) {
      setClause.push('icon_name = ?');
      values.push(updates.icon_name);
    }
    if (updates.is_system !== undefined) {
      setClause.push('is_system = ?');
      values.push(updates.is_system ? 1 : 0);
    }
    if (updates.sync_status !== undefined) {
      setClause.push('sync_status = ?');
      values.push(updates.sync_status);
    }

    values.push(id);

    await this.db.executeSql(
      `UPDATE categories SET ${setClause.join(', ')} WHERE id = ?`,
      values
    );

    const updatedCategory = await this.findById(id);
    if (!updatedCategory) {
      throw new Error('Failed to retrieve updated category');
    }

    return updatedCategory;
  }

  async delete(id: number): Promise<boolean> {
    await this.ensureInitialized();
    
    // Check if category has child categories
    const children = await this.findByParent(id);
    if (children.length > 0) {
      throw new Error('Cannot delete category with child categories');
    }

    // Check if category is used in transactions
    const [transactionCheck] = await this.db.executeSql(
      'SELECT COUNT(*) as count FROM transactions WHERE category_id = ?',
      [id]
    );

    if (transactionCheck.rows.item(0).count > 0) {
      throw new Error('Cannot delete category that is used in transactions');
    }

    const [result] = await this.db.executeSql(
      'DELETE FROM categories WHERE id = ? AND is_system = 0',
      [id]
    );

    return result.rowsAffected > 0;
  }

  async getTopCategories(type: Category['category_type'], limit: number = 5): Promise<(Category & {usage_count: number})[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      `SELECT c.*, COUNT(t.id) as usage_count
       FROM categories c
       LEFT JOIN transactions t ON c.id = t.category_id
       WHERE c.category_type = ?
       GROUP BY c.id
       ORDER BY usage_count DESC, c.name
       LIMIT ?`,
      [type, limit]
    );

    const categories: (Category & {usage_count: number})[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      categories.push(result.rows.item(i));
    }

    return categories;
  }

  async getCategoryHierarchy(): Promise<(Category & {children?: Category[]})[]> {
    await this.ensureInitialized();
    
    const allCategories = await this.findAll();
    
    // Create a map for quick lookup
    const categoryMap = new Map<number, Category & {children: Category[]}>();
    allCategories.forEach(cat => {
      categoryMap.set(cat.id, { ...cat, children: [] });
    });

    const rootCategories: (Category & {children: Category[]})[] = [];

    // Build hierarchy
    allCategories.forEach(cat => {
      const categoryWithChildren = categoryMap.get(cat.id)!;
      
      if (cat.parent_id === null) {
        rootCategories.push(categoryWithChildren);
      } else {
        const parent = categoryMap.get(cat.parent_id);
        if (parent) {
          parent.children.push(categoryWithChildren);
        }
      }
    });

    return rootCategories;
  }

  async initializeSystemCategories(): Promise<void> {
    // This method is deprecated - use SystemCategoriesService instead
    console.warn('CategoryRepository.initializeSystemCategories is deprecated. Use SystemCategoriesService instead.');
    
    const { SystemCategoriesService } = require('@/business/services/SystemCategoriesService');
    const systemCategoriesService = new SystemCategoriesService();
    await systemCategoriesService.initializeSystemCategories();
  }

  async getCategoryUsageStats(categoryId: number): Promise<{usage_count: number, total_amount: number, last_used: string | null}> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      `SELECT 
        COUNT(t.id) as usage_count,
        SUM(ABS(t.amount)) as total_amount,
        MAX(t.transaction_date) as last_used
       FROM transactions t
       WHERE t.category_id = ?`,
      [categoryId]
    );

    return result.rows.item(0);
  }

  async reassignTransactionsFromCategory(fromCategoryId: number, toCategoryId: number): Promise<number> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'UPDATE transactions SET category_id = ? WHERE category_id = ?',
      [toCategoryId, fromCategoryId]
    );

    return result.rowsAffected;
  }

  async getCategoryWithTransactionCount(categoryId: number): Promise<Category & {transaction_count: number} | null> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      `SELECT c.*, COUNT(t.id) as transaction_count
       FROM categories c
       LEFT JOIN transactions t ON c.id = t.category_id
       WHERE c.id = ?
       GROUP BY c.id`,
      [categoryId]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows.item(0) as Category & {transaction_count: number};
  }
}