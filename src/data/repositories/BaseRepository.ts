import { DatabaseService } from '@/data/database/DatabaseService';

export abstract class BaseRepository<T> {
  protected db: DatabaseService;
  private initializationChecked: boolean = false;

  constructor() {
    this.db = DatabaseService.getInstance();
  }

  protected async ensureInitialized(): Promise<void> {
    // Only check initialization once per repository instance
    if (!this.initializationChecked) {
      if (!this.db.isInitialized()) {
        await this.db.initialize();
      }
      this.initializationChecked = true;
    }
  }

  protected generateHash(data: string): string {
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  protected getCurrentTimestamp(): string {
    return new Date().toISOString();
  }

  // Abstract methods to be implemented by concrete repositories
  abstract findById(id: number): Promise<T | null>;
  abstract findAll(): Promise<T[]>;
  abstract create(entity: Omit<T, 'id' | 'created_at' | 'updated_at'>): Promise<T>;
  abstract update(id: number, updates: Partial<T>): Promise<T>;
  abstract delete(id: number): Promise<boolean>;
}