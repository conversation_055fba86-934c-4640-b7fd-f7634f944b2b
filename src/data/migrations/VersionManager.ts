/**
 * SQLite Version Manager
 * 
 * Manages database version using SQLite's built-in PRAGMA user_version.
 * Replaces external table-based version tracking with atomic SQLite feature.
 */

import type { DatabaseService } from '../database/DatabaseService';

/**
 * Version Manager for SQLite PRAGMA user_version
 * 
 * Uses SQLite's built-in user_version pragma for atomic version management.
 * This is more reliable than external tables and provides better consistency.
 */
export class VersionManager {
  private readonly db: DatabaseService;
  private currentVersion: number | null = null;
  private lastCheckTime: Date | null = null;

  constructor(database: DatabaseService) {
    this.db = database;
  }

  /**
   * Get current database version using PRAGMA user_version
   */
  public async getCurrentVersion(): Promise<number> {
    try {
      // Check cache first (valid for 1 minute)
      if (this.currentVersion !== null && this.isCacheValid()) {
        return this.currentVersion;
      }

      const result = await this.db.executeSql('PRAGMA user_version');
      
      // Handle different result formats
      let version = 0;
      if (Array.isArray(result) && result.length > 0) {
        // DatabaseService.executeSql returns array of results
        const rows = result[0].rows;
        if (rows && rows.length > 0) {
          version = rows.item(0)?.user_version || 0;
        }
      } else if (result && typeof result === 'object' && 'rows' in result) {
        // Direct result format
        version = result.rows.item(0)?.user_version || 0;
      }
      
      // Cache the version
      this.currentVersion = version;
      this.lastCheckTime = new Date();
      
      return version;
    } catch (error) {
      console.error('Failed to get database version:', error);
      throw new Error(`Version check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Set database version atomically using PRAGMA user_version
   */
  public async setVersion(version: number): Promise<void> {
    if (!Number.isInteger(version) || version < 0) {
      throw new Error(`Invalid version number: ${version}. Must be a non-negative integer.`);
    }

    if (version > 999) {
      throw new Error(`Version ${version} exceeds recommended maximum (999)`);
    }

    try {
      // Use PRAGMA user_version for atomic update
      await this.db.executeSql('PRAGMA user_version = ?', [version]);
      
      // Update cache
      this.currentVersion = version;
      this.lastCheckTime = new Date();
      
      console.log(`✅ Database version updated to ${version}`);
    } catch (error) {
      console.error('Failed to set database version:', error);
      throw new Error(`Version update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Set database version within an existing transaction
   */
  public async setVersionInTransaction(tx: any, version: number): Promise<void> {
    if (!Number.isInteger(version) || version < 0) {
      throw new Error(`Invalid version number: ${version}. Must be a non-negative integer.`);
    }

    if (version > 999) {
      throw new Error(`Version ${version} exceeds recommended maximum (999)`);
    }

    try {
      // Use PRAGMA user_version within the transaction
      await new Promise<void>((resolve, reject) => {
        tx.executeSql(
          'PRAGMA user_version = ?',
          [version],
          () => {
            // Update cache
            this.currentVersion = version;
            this.lastCheckTime = new Date();
            console.log(`✅ Database version updated to ${version} (within transaction)`);
            resolve();
          },
          (_, error: any) => {
            reject(error);
            return false;
          }
        );
      });
    } catch (error) {
      console.error('Failed to set database version within transaction:', error);
      throw new Error(`Version update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Increment version atomically (for migration execution)
   */
  public async incrementVersion(): Promise<number> {
    const currentVersion = await this.getCurrentVersion();
    const newVersion = currentVersion + 1;
    await this.setVersion(newVersion);
    return newVersion;
  }

  /**
   * Check if a specific version has been applied
   */
  public async isVersionApplied(version: number): Promise<boolean> {
    const currentVersion = await this.getCurrentVersion();
    return currentVersion >= version;
  }

  /**
   * Get version change history (for debugging)
   * 
   * Note: SQLite PRAGMA user_version doesn't store history.
   * This method checks if we can determine version progression.
   */
  public async getVersionHistory(): Promise<{ version: number; appliedAt: Date | null }[]> {
    const currentVersion = await this.getCurrentVersion();
    
    // Since PRAGMA user_version doesn't store timestamps,
    // we can only return the current version
    return [
      {
        version: currentVersion,
        appliedAt: this.lastCheckTime
      }
    ];
  }

  /**
   * Validate version consistency
   * 
   * Checks if the database version is in a valid state
   */
  public async validateVersion(): Promise<{
    isValid: boolean;
    version: number;
    issues: string[];
  }> {
    const issues: string[] = [];
    let isValid = true;

    try {
      const version = await this.getCurrentVersion();

      // Check for negative version
      if (version < 0) {
        issues.push(`Negative version number: ${version}`);
        isValid = false;
      }

      // Check for unreasonably high version
      if (version > 999) {
        issues.push(`Version ${version} exceeds recommended maximum (999)`);
        isValid = false;
      }

      // Check if version is an integer
      if (!Number.isInteger(version)) {
        issues.push(`Non-integer version: ${version}`);
        isValid = false;
      }

      return { isValid, version, issues };

    } catch (error) {
      issues.push(`Version check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { 
        isValid: false, 
        version: -1, 
        issues 
      };
    }
  }

  /**
   * Reset version to zero (development only)
   */
  public async resetVersion(): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Version reset not allowed in production');
    }

    try {
      await this.setVersion(0);
      console.log('🔄 Database version reset to 0 (development mode)');
    } catch (error) {
      console.error('Failed to reset version:', error);
      throw error;
    }
  }

  /**
   * Compare with legacy knex_migrations table version (for migration)
   */
  public async compareLegacyVersion(): Promise<{
    pragmaVersion: number;
    knexVersion: number | null;
    needsMigration: boolean;
    recommendation: string;
  }> {
    const pragmaVersion = await this.getCurrentVersion();
    let knexVersion: number | null = null;
    let needsMigration = false;
    let recommendation = '';

    try {
      // First, check if knex_migrations table exists
      const tableCheckResult = await this.db.executeSql(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='knex_migrations'"
      );
      
      let tableExists = false;
      if (Array.isArray(tableCheckResult) && tableCheckResult.length > 0) {
        tableExists = tableCheckResult[0].rows.length > 0;
      } else if (tableCheckResult && typeof tableCheckResult === 'object' && 'rows' in tableCheckResult) {
        tableExists = tableCheckResult.rows.length > 0;
      }

      if (tableExists) {
        // Table exists, try to read from it
        const result = await this.db.executeSql(
          'SELECT MAX(batch) as version FROM knex_migrations'
        );
        
        let legacyVersion = null;
        if (Array.isArray(result) && result.length > 0) {
          legacyVersion = result[0].rows.item(0)?.version;
        } else if (result && typeof result === 'object' && 'rows' in result) {
          legacyVersion = result.rows.item(0)?.version;
        }
        
        knexVersion = legacyVersion !== null && legacyVersion !== undefined ? legacyVersion : null;

        if (knexVersion !== null && pragmaVersion === 0) {
          needsMigration = true;
          recommendation = `Migrate from knex_migrations (v${knexVersion}) to PRAGMA user_version`;
        } else if (knexVersion !== null && knexVersion !== pragmaVersion) {
          needsMigration = true;
          recommendation = `Version mismatch: knex=${knexVersion}, pragma=${pragmaVersion}. Update PRAGMA to match.`;
        } else if (knexVersion === null && pragmaVersion > 0) {
          recommendation = `PRAGMA user_version is authoritative (v${pragmaVersion})`;
        } else {
          recommendation = 'Versions are synchronized';
        }
      } else {
        // knex_migrations table doesn't exist - this is fine for new installations
        recommendation = `PRAGMA user_version is authoritative (v${pragmaVersion})`;
      }

    } catch (error) {
      // Any error checking knex_migrations means we should use PRAGMA user_version
      console.log(`ℹ️ Legacy migration table check: ${error instanceof Error ? error.message : 'Unknown error'}`);
      recommendation = `PRAGMA user_version is authoritative (v${pragmaVersion})`;
    }

    return {
      pragmaVersion,
      knexVersion,
      needsMigration,
      recommendation
    };
  }

  /**
   * Migrate from legacy knex_migrations to PRAGMA user_version
   */
  public async migrateLegacyVersion(): Promise<void> {
    const comparison = await this.compareLegacyVersion();
    
    if (!comparison.needsMigration) {
      console.log('✅ No legacy version migration needed');
      return;
    }

    if (comparison.knexVersion === null) {
      console.log('✅ No legacy knex_migrations found');
      return;
    }

    try {
      console.log(`🔄 Migrating from knex_migrations (v${comparison.knexVersion}) to PRAGMA user_version`);
      
      // Set PRAGMA user_version to match knex_migrations
      await this.setVersion(comparison.knexVersion);
      
      console.log(`✅ Legacy migration complete: PRAGMA user_version = ${comparison.knexVersion}`);
      
      // Optionally, we could drop the knex_migrations table here,
      // but we'll leave it for now for safety
      
    } catch (error) {
      console.error('Legacy version migration failed:', error);
      throw new Error(`Legacy migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get detailed version information
   */
  public async getVersionInfo(): Promise<{
    currentVersion: number;
    lastChecked: Date | null;
    isValid: boolean;
    issues: string[];
    legacyComparison: {
      pragmaVersion: number;
      knexVersion: number | null;
      needsMigration: boolean;
      recommendation: string;
    };
  }> {
    const validation = await this.validateVersion();
    const legacyComparison = await this.compareLegacyVersion();

    return {
      currentVersion: validation.version,
      lastChecked: this.lastCheckTime,
      isValid: validation.isValid,
      issues: validation.issues,
      legacyComparison
    };
  }

  /**
   * Clear version cache (force refresh on next read)
   */
  public clearCache(): void {
    this.currentVersion = null;
    this.lastCheckTime = null;
  }

  /**
   * Check if cached version is still valid (1 minute cache)
   */
  private isCacheValid(): boolean {
    if (!this.lastCheckTime) {
      return false;
    }

    const cacheTimeout = 60 * 1000; // 1 minute
    return Date.now() - this.lastCheckTime.getTime() < cacheTimeout;
  }

  /**
   * Create backup of current version state (development utility)
   */
  public async createVersionSnapshot(): Promise<{
    timestamp: Date;
    version: number;
    validation: any;
  }> {
    const validation = await this.validateVersion();
    
    return {
      timestamp: new Date(),
      version: validation.version,
      validation
    };
  }

  /**
   * Atomic version transaction helper
   * 
   * Ensures version is updated only if operation succeeds
   */
  public async transactionalVersionUpdate<T>(
    targetVersion: number,
    operation: () => Promise<T>
  ): Promise<T> {
    const originalVersion = await this.getCurrentVersion();
    
    try {
      // Execute the operation first
      const result = await operation();
      
      // Only update version if operation succeeded
      await this.setVersion(targetVersion);
      
      return result;
    } catch (error) {
      // Ensure version remains unchanged on failure
      await this.setVersion(originalVersion);
      throw error;
    }
  }
}