/**
 * Migration Validation System
 * 
 * Advanced pre-flight validation with SQL analysis, performance assessment,
 * and safety scoring. Provides comprehensive validation before migration execution.
 */

import type { 
  Migration, 
  MigrationValidationResult,
  MigrationResourceRequirements,
  MigrationErrorType
} from '../../shared/types/migration';
import type { DatabaseService } from '../database/DatabaseService';

/**
 * SQL operation patterns for analysis
 */
interface SQLPattern {
  pattern: RegExp;
  type: 'safe' | 'warning' | 'dangerous';
  message: string;
  performanceImpact: 'low' | 'medium' | 'high';
  category: 'ddl' | 'dml' | 'query' | 'index' | 'constraint';
}

/**
 * Validation rule configuration
 */
interface ValidationConfig {
  /** Environment mode affects validation strictness */
  environment: 'development' | 'production' | 'test';
  
  /** Enable strict mode (fails on warnings) */
  strictMode: boolean;
  
  /** Maximum allowed execution time (ms) */
  maxExecutionTime: number;
  
  /** Maximum allowed memory usage (MB) */
  maxMemoryUsage: number;
  
  /** Allow destructive operations */
  allowDestructive: boolean;
  
  /** Enable performance analysis */
  enablePerformanceAnalysis: boolean;
  
  /** Custom validation rules */
  customRules: SQLPattern[];
}

/**
 * Default validation configuration
 */
const DEFAULT_CONFIG: ValidationConfig = {
  environment: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  strictMode: process.env.NODE_ENV === 'production',
  maxExecutionTime: 30000, // 30 seconds
  maxMemoryUsage: 50, // 50MB
  allowDestructive: process.env.NODE_ENV !== 'production',
  enablePerformanceAnalysis: true,
  customRules: []
};

/**
 * Built-in SQL validation patterns
 */
const SQL_PATTERNS: SQLPattern[] = [
  // Dangerous operations
  {
    pattern: /DROP\s+TABLE\s+\w+(?!\s+IF\s+EXISTS)/gi,
    type: 'dangerous',
    message: 'DROP TABLE without IF EXISTS - risk of data loss',
    performanceImpact: 'low',
    category: 'ddl'
  },
  {
    pattern: /DELETE\s+FROM\s+\w+(?!\s+WHERE)/gi,
    type: 'dangerous',
    message: 'DELETE without WHERE clause - risk of data loss',
    performanceImpact: 'high',
    category: 'dml'
  },
  {
    pattern: /UPDATE\s+\w+\s+SET\s+.*(?!\s+WHERE)/gi,
    type: 'dangerous',
    message: 'UPDATE without WHERE clause - risk of data corruption',
    performanceImpact: 'high',
    category: 'dml'
  },
  {
    pattern: /TRUNCATE\s+TABLE/gi,
    type: 'dangerous',
    message: 'TRUNCATE TABLE - irreversible data loss',
    performanceImpact: 'medium',
    category: 'dml'
  },
  {
    pattern: /ALTER\s+TABLE\s+\w+\s+DROP\s+COLUMN/gi,
    type: 'dangerous',
    message: 'DROP COLUMN - risk of data loss',
    performanceImpact: 'low',
    category: 'ddl'
  },
  
  // Warning operations
  {
    pattern: /CREATE\s+INDEX\s+\w+\s+ON\s+\w+\s*\([^)]+\)(?!\s+WHERE)/gi,
    type: 'warning',
    message: 'Non-partial index creation - may impact performance on large tables',
    performanceImpact: 'high',
    category: 'index'
  },
  {
    pattern: /ALTER\s+TABLE\s+\w+\s+ADD\s+COLUMN\s+\w+.*NOT\s+NULL(?!\s+DEFAULT)/gi,
    type: 'warning',
    message: 'Adding NOT NULL column without DEFAULT - may fail on existing data',
    performanceImpact: 'medium',
    category: 'ddl'
  },
  {
    pattern: /VACUUM(?!\s+INTO)/gi,
    type: 'warning',
    message: 'VACUUM operation - database will be locked during execution',
    performanceImpact: 'high',
    category: 'ddl'
  },
  {
    pattern: /ANALYZE/gi,
    type: 'warning',
    message: 'ANALYZE operation - may take time on large databases',
    performanceImpact: 'medium',
    category: 'ddl'
  },
  {
    pattern: /FOREIGN\s+KEY.*ON\s+DELETE\s+CASCADE/gi,
    type: 'warning',
    message: 'CASCADE delete - may affect related data unexpectedly',
    performanceImpact: 'low',
    category: 'constraint'
  },
  
  // Performance impact operations
  {
    pattern: /CREATE\s+UNIQUE\s+INDEX/gi,
    type: 'warning',
    message: 'UNIQUE INDEX creation - will validate all existing data',
    performanceImpact: 'high',
    category: 'index'
  },
  {
    pattern: /INSERT\s+INTO\s+\w+\s+SELECT/gi,
    type: 'warning',
    message: 'INSERT FROM SELECT - may process large datasets',
    performanceImpact: 'high',
    category: 'dml'
  },
  {
    pattern: /WITH\s+RECURSIVE/gi,
    type: 'warning',
    message: 'Recursive CTE - may have performance implications',
    performanceImpact: 'medium',
    category: 'query'
  }
];

/**
 * Migration Validator
 * 
 * Comprehensive validation system for migration safety and performance analysis
 */
export class MigrationValidator {
  private readonly config: ValidationConfig;
  private readonly sqlPatterns: SQLPattern[];

  constructor(config: Partial<ValidationConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.sqlPatterns = [...SQL_PATTERNS, ...this.config.customRules];
  }

  /**
   * Validate a single migration comprehensively
   */
  public async validateMigration(migration: Migration, db?: DatabaseService): Promise<MigrationValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let safetyScore = 1.0;
    let performanceImpact: 'low' | 'medium' | 'high' = 'low';

    // Basic structure validation
    const structureValidation = this.validateMigrationStructure(migration);
    errors.push(...structureValidation.errors);
    warnings.push(...structureValidation.warnings);
    safetyScore *= structureValidation.safetyScore;

    // SQL content validation
    if (migration.up) {
      const sqlValidation = await this.validateSQL(migration.up, 'up');
      errors.push(...sqlValidation.errors);
      warnings.push(...sqlValidation.warnings);
      safetyScore *= sqlValidation.safetyScore;
      performanceImpact = this.combinePerformanceImpact(performanceImpact, sqlValidation.performanceImpact);
    }

    // Rollback SQL validation
    if (migration.down) {
      const rollbackValidation = await this.validateSQL(migration.down, 'down');
      warnings.push(...rollbackValidation.warnings.map(w => `Rollback: ${w}`));
      errors.push(...rollbackValidation.errors.map(e => `Rollback: ${e}`));
    }

    // Database schema validation (if database provided)
    if (db) {
      const schemaValidation = await this.validateAgainstSchema(migration, db);
      errors.push(...schemaValidation.errors);
      warnings.push(...schemaValidation.warnings);
    }

    // Performance analysis
    const resourceRequirements = await this.analyzeResourceRequirements(migration);
    
    // Check against limits
    if (resourceRequirements.executionTimeMS > this.config.maxExecutionTime) {
      warnings.push(`Estimated execution time (${resourceRequirements.executionTimeMS}ms) exceeds limit (${this.config.maxExecutionTime}ms)`);
      performanceImpact = 'high';
    }

    if (resourceRequirements.memoryMB > this.config.maxMemoryUsage) {
      errors.push(`Estimated memory usage (${resourceRequirements.memoryMB}MB) exceeds limit (${this.config.maxMemoryUsage}MB)`);
    }

    // Environment-specific validation
    const envValidation = this.validateForEnvironment(migration);
    errors.push(...envValidation.errors);
    warnings.push(...envValidation.warnings);
    safetyScore *= envValidation.safetyScore;

    // Strict mode handling
    if (this.config.strictMode && warnings.length > 0) {
      errors.push(...warnings.map(w => `Strict mode: ${w}`));
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      safetyScore,
      performanceImpact,
      estimatedDuration: resourceRequirements.executionTimeMS,
      resourceRequirements
    };
  }

  /**
   * Validate multiple migrations as a batch
   */
  public async validateMigrationBatch(migrations: Migration[], db?: DatabaseService): Promise<{
    overallValid: boolean;
    results: Map<number, MigrationValidationResult>;
    batchWarnings: string[];
    batchErrors: string[];
  }> {
    const results = new Map<number, MigrationValidationResult>();
    const batchWarnings: string[] = [];
    const batchErrors: string[] = [];

    // Validate individual migrations
    for (const migration of migrations) {
      const result = await this.validateMigration(migration, db);
      results.set(migration.version, result);
    }

    // Batch-level validations
    this.validateVersionSequence(migrations, batchErrors, batchWarnings);
    this.validateBatchConsistency(migrations, batchErrors, batchWarnings);
    await this.validateBatchPerformance(migrations, batchErrors, batchWarnings);

    const allValid = Array.from(results.values()).every(r => r.isValid) && batchErrors.length === 0;

    return {
      overallValid: allValid,
      results,
      batchWarnings,
      batchErrors
    };
  }

  /**
   * Validate migration structure and metadata
   */
  private validateMigrationStructure(migration: Migration): {
    errors: string[];
    warnings: string[];
    safetyScore: number;
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    let safetyScore = 1.0;

    // Required fields
    if (!migration.version || !Number.isInteger(migration.version) || migration.version <= 0) {
      errors.push('Invalid version: must be positive integer');
    }

    if (!migration.name || typeof migration.name !== 'string' || migration.name.trim().length === 0) {
      errors.push('Invalid name: must be non-empty string');
    }

    if (!migration.up || typeof migration.up !== 'string' || migration.up.trim().length === 0) {
      errors.push('Invalid up migration: must be non-empty SQL string');
    }

    // Name convention validation
    if (migration.name && !/^[a-z_]+$/.test(migration.name)) {
      warnings.push('Migration name should use lowercase letters and underscores only');
      safetyScore *= 0.95;
    }

    // Version range validation
    if (migration.version > 999) {
      warnings.push(`Version ${migration.version} exceeds recommended range (1-999)`);
    }

    // Metadata validation
    if (migration.metadata) {
      if (migration.metadata.estimatedDuration && migration.metadata.estimatedDuration > this.config.maxExecutionTime) {
        warnings.push(`Metadata indicates long execution time: ${migration.metadata.estimatedDuration}ms`);
      }

      if (migration.metadata.isBreaking && this.config.environment === 'production') {
        warnings.push('Breaking change detected in production environment');
        safetyScore *= 0.8;
      }
    }

    return { errors, warnings, safetyScore };
  }

  /**
   * Validate SQL content using pattern matching and analysis
   */
  private async validateSQL(sql: string, direction: 'up' | 'down'): Promise<{
    errors: string[];
    warnings: string[];
    safetyScore: number;
    performanceImpact: 'low' | 'medium' | 'high';
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let safetyScore = 1.0;
    let performanceImpact: 'low' | 'medium' | 'high' = 'low';

    // Basic SQL validation
    const basicValidation = this.validateBasicSQL(sql);
    errors.push(...basicValidation.errors);
    warnings.push(...basicValidation.warnings);

    // Pattern-based validation
    for (const pattern of this.sqlPatterns) {
      if (pattern.pattern.test(sql)) {
        const message = `${pattern.message} [${pattern.category}]`;
        
        if (pattern.type === 'dangerous') {
          if (this.config.allowDestructive) {
            warnings.push(`Destructive operation: ${message}`);
            safetyScore *= 0.7;
          } else {
            errors.push(`Forbidden destructive operation: ${message}`);
          }
        } else if (pattern.type === 'warning') {
          warnings.push(message);
          safetyScore *= 0.9;
        }

        // Update performance impact
        performanceImpact = this.combinePerformanceImpact(performanceImpact, pattern.performanceImpact);
      }
    }

    // SQL syntax validation
    const syntaxValidation = await this.validateSQLSyntax(sql);
    errors.push(...syntaxValidation.errors);
    warnings.push(...syntaxValidation.warnings);

    // Transaction compatibility
    const transactionValidation = this.validateTransactionCompatibility(sql);
    warnings.push(...transactionValidation.warnings);

    return { errors, warnings, safetyScore, performanceImpact };
  }

  /**
   * Basic SQL content validation
   */
  private validateBasicSQL(sql: string): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Empty SQL
    if (!sql.trim()) {
      errors.push('Empty SQL statement');
      return { errors, warnings };
    }

    // SQL injection patterns (basic check)
    const suspiciousPatterns = [
      /;.*?DROP\s/gi,
      /UNION.*?SELECT/gi,
      /\/\*.*?\*\//g // SQL comments that might hide malicious code
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(sql)) {
        warnings.push('SQL contains suspicious patterns - review for security');
        break;
      }
    }

    // Multiple statements detection
    const statements = sql.split(';').filter(s => s.trim().length > 0);
    if (statements.length > 10) {
      warnings.push(`Migration contains ${statements.length} statements - consider breaking into smaller migrations`);
    }

    // Long SQL detection
    if (sql.length > 10000) {
      warnings.push('Very long SQL detected - may impact readability and debugging');
    }

    return { errors, warnings };
  }

  /**
   * Advanced SQL syntax validation
   */
  private async validateSQLSyntax(sql: string): Promise<{ errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic syntax checks
    const syntaxChecks = [
      {
        pattern: /CREATE\s+TABLE\s+\w+\s*\([^)]*\)\s*(?!;|$)/gi,
        message: 'CREATE TABLE statement appears incomplete'
      },
      {
        pattern: /INSERT\s+INTO\s+\w+\s*\([^)]*\)\s*VALUES\s*\([^)]*\)(?!\s*[;,])/gi,
        message: 'INSERT statement may have syntax issues'
      },
      {
        pattern: /SELECT.*FROM.*WHERE.*AND.*OR/gi,
        message: 'Complex WHERE clause detected - verify operator precedence'
      }
    ];

    for (const check of syntaxChecks) {
      if (check.pattern.test(sql)) {
        warnings.push(check.message);
      }
    }

    // Balanced parentheses check
    const openParens = (sql.match(/\(/g) || []).length;
    const closeParens = (sql.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      errors.push('Unbalanced parentheses in SQL');
    }

    // Quote balance check
    const singleQuotes = (sql.match(/'/g) || []).length;
    const doubleQuotes = (sql.match(/"/g) || []).length;
    if (singleQuotes % 2 !== 0) {
      warnings.push('Unbalanced single quotes detected');
    }
    if (doubleQuotes % 2 !== 0) {
      warnings.push('Unbalanced double quotes detected');
    }

    return { errors, warnings };
  }

  /**
   * Validate migration against existing database schema
   */
  private async validateAgainstSchema(migration: Migration, db: DatabaseService): Promise<{
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Get existing tables
      const [result] = await db.executeSql(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `);

      const existingTables: string[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        existingTables.push(result.rows.item(i).name);
      }

      // Analyze SQL for table operations
      const sql = migration.up.toUpperCase();
      
      // Check for table creation conflicts
      const createTableMatches = sql.match(/CREATE\s+TABLE\s+(\w+)/gi);
      if (createTableMatches) {
        for (const match of createTableMatches) {
          const tableName = match.replace(/CREATE\s+TABLE\s+/gi, '').toLowerCase();
          if (existingTables.includes(tableName) && !sql.includes('IF NOT EXISTS')) {
            errors.push(`Table '${tableName}' already exists - use CREATE TABLE IF NOT EXISTS`);
          }
        }
      }

      // Check for operations on non-existent tables
      const alterTableMatches = sql.match(/ALTER\s+TABLE\s+(\w+)/gi);
      if (alterTableMatches) {
        for (const match of alterTableMatches) {
          const tableName = match.replace(/ALTER\s+TABLE\s+/gi, '').toLowerCase();
          if (!existingTables.includes(tableName)) {
            errors.push(`Cannot ALTER non-existent table '${tableName}'`);
          }
        }
      }

    } catch (error) {
      warnings.push(`Schema validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return { errors, warnings };
  }

  /**
   * Analyze resource requirements for migration
   */
  private async analyzeResourceRequirements(migration: Migration): Promise<MigrationResourceRequirements> {
    const sql = migration.up.toUpperCase();
    let memoryMB = 5; // Base memory usage
    let executionTimeMS = 1000; // Base execution time
    let blocksDatabase = false;
    const sqliteFeatures: string[] = [];

    // Analyze SQL patterns for resource usage
    const resourcePatterns = [
      {
        pattern: /CREATE\s+INDEX/gi,
        memoryMultiplier: 2,
        timeMultiplier: 5,
        blocks: true
      },
      {
        pattern: /INSERT\s+INTO\s+\w+\s+SELECT/gi,
        memoryMultiplier: 5,
        timeMultiplier: 10,
        blocks: false
      },
      {
        pattern: /UPDATE\s+\w+\s+SET/gi,
        memoryMultiplier: 3,
        timeMultiplier: 8,
        blocks: false
      },
      {
        pattern: /DELETE\s+FROM/gi,
        memoryMultiplier: 2,
        timeMultiplier: 6,
        blocks: false
      },
      {
        pattern: /VACUUM/gi,
        memoryMultiplier: 10,
        timeMultiplier: 20,
        blocks: true
      },
      {
        pattern: /ALTER\s+TABLE/gi,
        memoryMultiplier: 3,
        timeMultiplier: 4,
        blocks: true
      }
    ];

    for (const pattern of resourcePatterns) {
      if (pattern.pattern.test(sql)) {
        memoryMB *= pattern.memoryMultiplier;
        executionTimeMS *= pattern.timeMultiplier;
        if (pattern.blocks) {
          blocksDatabase = true;
        }
      }
    }

    // Detect required SQLite features
    const featurePatterns = [
      { pattern: /FOREIGN\s+KEY/gi, feature: 'foreign_keys' },
      { pattern: /JSON_\w+/gi, feature: 'json1' },
      { pattern: /WITH\s+RECURSIVE/gi, feature: 'cte' },
      { pattern: /FTS\d*/gi, feature: 'fts' },
      { pattern: /RTREE/gi, feature: 'rtree' }
    ];

    for (const { pattern, feature } of featurePatterns) {
      if (pattern.test(sql) && !sqliteFeatures.includes(feature)) {
        sqliteFeatures.push(feature);
      }
    }

    // Apply metadata estimates if available
    if (migration.metadata?.estimatedDuration) {
      executionTimeMS = Math.max(executionTimeMS, migration.metadata.estimatedDuration);
    }

    if (migration.metadata?.isDataHeavy) {
      memoryMB *= 3;
      executionTimeMS *= 2;
    }

    return {
      memoryMB: Math.min(memoryMB, 200), // Cap at 200MB
      diskSpaceMB: 10, // Estimate disk space needed
      executionTimeMS: Math.min(executionTimeMS, 300000), // Cap at 5 minutes
      blocksDatabase,
      sqliteFeatures
    };
  }

  /**
   * Environment-specific validation rules
   */
  private validateForEnvironment(migration: Migration): {
    errors: string[];
    warnings: string[];
    safetyScore: number;
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    let safetyScore = 1.0;

    if (this.config.environment === 'production') {
      // Production-specific rules
      const sql = migration.up.toUpperCase();

      // No VACUUM in production (blocks database)
      if (/VACUUM/gi.test(sql)) {
        errors.push('VACUUM not allowed in production - blocks database access');
      }

      // Warn about performance impact
      if (/CREATE\s+INDEX.*ON\s+\w+\s*\([^)]+\)(?!\s+WHERE)/gi.test(sql)) {
        warnings.push('Index creation in production - consider maintenance window');
        safetyScore *= 0.9;
      }

      // Breaking changes
      if (migration.metadata?.isBreaking) {
        warnings.push('Breaking change in production - ensure compatibility');
        safetyScore *= 0.8;
      }

    } else if (this.config.environment === 'development') {
      // Development-specific warnings
      const sql = migration.up.toUpperCase();

      if (/DROP\s+TABLE/gi.test(sql)) {
        warnings.push('DROP TABLE in development - consider data backup');
      }
    }

    return { errors, warnings, safetyScore };
  }

  /**
   * Validate transaction compatibility
   */
  private validateTransactionCompatibility(sql: string): { warnings: string[] } {
    const warnings: string[] = [];
    const upperSQL = sql.toUpperCase();

    // Operations that can't be rolled back in SQLite
    const nonTransactionalOps = [
      { pattern: /CREATE\s+INDEX/gi, op: 'CREATE INDEX' },
      { pattern: /DROP\s+INDEX/gi, op: 'DROP INDEX' },
      { pattern: /VACUUM/gi, op: 'VACUUM' },
      { pattern: /ANALYZE/gi, op: 'ANALYZE' }
    ];

    for (const { pattern, op } of nonTransactionalOps) {
      if (pattern.test(upperSQL)) {
        warnings.push(`${op} cannot be rolled back if migration fails`);
      }
    }

    // Explicit transaction statements
    if (/BEGIN|COMMIT|ROLLBACK/gi.test(upperSQL)) {
      warnings.push('Migration contains explicit transaction statements - may conflict with automatic transaction handling');
    }

    return { warnings };
  }

  /**
   * Validate version sequence in batch
   */
  private validateVersionSequence(migrations: Migration[], errors: string[], warnings: string[]): void {
    const versions = migrations.map(m => m.version).sort((a, b) => a - b);
    
    // Check for gaps
    for (let i = 1; i < versions.length; i++) {
      if (versions[i] - versions[i - 1] > 1) {
        warnings.push(`Version gap detected: ${versions[i - 1]} to ${versions[i]}`);
      }
    }

    // Check for duplicates
    const duplicates = versions.filter((v, i) => versions.indexOf(v) !== i);
    if (duplicates.length > 0) {
      errors.push(`Duplicate versions: ${duplicates.join(', ')}`);
    }
  }

  /**
   * Validate batch consistency
   */
  private validateBatchConsistency(migrations: Migration[], errors: string[], warnings: string[]): void {
    const names = migrations.map(m => m.name);
    const duplicateNames = names.filter((n, i) => names.indexOf(n) !== i);
    
    if (duplicateNames.length > 0) {
      warnings.push(`Duplicate migration names: ${duplicateNames.join(', ')}`);
    }

    // Check for conflicting operations
    const allSQL = migrations.map(m => m.up.toUpperCase()).join(' ');
    
    // CREATE and DROP same table
    const createTables = [...allSQL.matchAll(/CREATE\s+TABLE\s+(\w+)/gi)].map(m => m[1]);
    const dropTables = [...allSQL.matchAll(/DROP\s+TABLE\s+(\w+)/gi)].map(m => m[1]);
    
    for (const table of createTables) {
      if (dropTables.includes(table)) {
        warnings.push(`Table '${table}' is both created and dropped in this batch`);
      }
    }
  }

  /**
   * Validate batch performance impact
   */
  private async validateBatchPerformance(migrations: Migration[], errors: string[], warnings: string[]): Promise<void> {
    let totalEstimatedTime = 0;
    let totalEstimatedMemory = 0;

    for (const migration of migrations) {
      const resources = await this.analyzeResourceRequirements(migration);
      totalEstimatedTime += resources.executionTimeMS;
      totalEstimatedMemory = Math.max(totalEstimatedMemory, resources.memoryMB);
    }

    if (totalEstimatedTime > 120000) { // 2 minutes
      warnings.push(`Batch estimated time: ${Math.round(totalEstimatedTime / 1000)}s - consider breaking into smaller batches`);
    }

    if (totalEstimatedMemory > this.config.maxMemoryUsage) {
      errors.push(`Batch estimated memory: ${totalEstimatedMemory}MB exceeds limit: ${this.config.maxMemoryUsage}MB`);
    }

    // Check for too many database-blocking operations
    const blockingOps = migrations.filter(m => 
      /CREATE\s+INDEX|ALTER\s+TABLE|VACUUM/gi.test(m.up)
    ).length;

    if (blockingOps > 3) {
      warnings.push(`${blockingOps} database-blocking operations in batch - may impact availability`);
    }
  }

  /**
   * Combine performance impact levels
   */
  private combinePerformanceImpact(
    current: 'low' | 'medium' | 'high',
    new_impact: 'low' | 'medium' | 'high'
  ): 'low' | 'medium' | 'high' {
    const levels = { low: 1, medium: 2, high: 3 };
    const combined = Math.max(levels[current], levels[new_impact]);
    
    switch (combined) {
      case 3: return 'high';
      case 2: return 'medium';
      default: return 'low';
    }
  }

  /**
   * Add custom validation rule
   */
  public addCustomRule(rule: SQLPattern): void {
    this.sqlPatterns.push(rule);
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<ValidationConfig>): void {
    Object.assign(this.config, config);
  }

  /**
   * Get current validation statistics
   */
  public getValidationStats(): {
    totalRules: number;
    dangerousRules: number;
    warningRules: number;
    safeRules: number;
    environment: string;
    strictMode: boolean;
  } {
    const dangerousRules = this.sqlPatterns.filter(p => p.type === 'dangerous').length;
    const warningRules = this.sqlPatterns.filter(p => p.type === 'warning').length;
    const safeRules = this.sqlPatterns.filter(p => p.type === 'safe').length;

    return {
      totalRules: this.sqlPatterns.length,
      dangerousRules,
      warningRules,
      safeRules,
      environment: this.config.environment,
      strictMode: this.config.strictMode
    };
  }
}