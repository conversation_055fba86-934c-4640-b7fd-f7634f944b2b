/**
 * Migration: Create Budget Categories Table
 * 
 * Creates the junction table linking budgets to categories with allocation tracking.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 5,
  name: 'create_budget_categories',
  up: `
    CREATE TABLE IF NOT EXISTS budget_categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      budget_id INTEGER NOT NULL,
      category_id INTEGER NOT NULL,
      allocated_amount DECIMAL(15, 2) NOT NULL,
      spent_amount DECIMAL(15, 2) DEFAULT 0.00,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (budget_id) REFERENCES budgets (id) ON DELETE CASCADE,
      FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
      UNIQUE (budget_id, category_id)
    );

    CREATE INDEX IF NOT EXISTS idx_budget_categories_budget ON budget_categories (budget_id);
    CREATE INDEX IF NOT EXISTS idx_budget_categories_category ON budget_categories (category_id);
    CREATE INDEX IF NOT EXISTS idx_budget_categories_amounts ON budget_categories (allocated_amount, spent_amount);
  `,
  down: `
    DROP INDEX IF EXISTS idx_budget_categories_amounts;
    DROP INDEX IF EXISTS idx_budget_categories_category;
    DROP INDEX IF EXISTS idx_budget_categories_budget;
    DROP TABLE IF EXISTS budget_categories;
  `,
  metadata: {
    description: 'Creates budget-category junction table with spending tracking',
    estimatedDuration: 500,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false,
    requirements: ['foreign_keys']
  }
};