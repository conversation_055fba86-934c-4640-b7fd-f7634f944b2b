/**
 * Migration: Create Feedback Sync Queue Table
 * 
 * Creates the sync queue table for managing offline feedback synchronization
 * with retry logic and error handling.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 15,
  name: 'create_feedback_sync_queue',
  up: `
    CREATE TABLE IF NOT EXISTS feedback_sync_queue (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
      table_name TEXT NOT NULL,
      local_id INTEGER NOT NULL,
      payload TEXT NOT NULL,
      retry_count INTEGER DEFAULT 0,
      max_retries INTEGER DEFAULT 3,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      last_attempt_at TIMESTAMP,
      next_retry_at TIMESTAMP,
      error_message TEXT,
      is_processed BOOLEAN DEFAULT 0
    );

    CREATE INDEX IF NOT EXISTS idx_sync_queue_operation ON feedback_sync_queue(operation_type);
    CREATE INDEX IF NOT EXISTS idx_sync_queue_table ON feedback_sync_queue(table_name);
    CREATE INDEX IF NOT EXISTS idx_sync_queue_retry ON feedback_sync_queue(retry_count);
    CREATE INDEX IF NOT EXISTS idx_sync_queue_created ON feedback_sync_queue(created_at);
    CREATE INDEX IF NOT EXISTS idx_sync_queue_table_id ON feedback_sync_queue(table_name, local_id);
    CREATE INDEX IF NOT EXISTS idx_sync_queue_processed ON feedback_sync_queue(is_processed);
    CREATE INDEX IF NOT EXISTS idx_sync_queue_next_retry ON feedback_sync_queue(next_retry_at);
    CREATE INDEX IF NOT EXISTS idx_sync_queue_pending ON feedback_sync_queue(is_processed, next_retry_at);
  `,
  down: `
    DROP INDEX IF EXISTS idx_sync_queue_pending;
    DROP INDEX IF EXISTS idx_sync_queue_next_retry;
    DROP INDEX IF EXISTS idx_sync_queue_processed;
    DROP INDEX IF EXISTS idx_sync_queue_table_id;
    DROP INDEX IF EXISTS idx_sync_queue_created;
    DROP INDEX IF EXISTS idx_sync_queue_retry;
    DROP INDEX IF EXISTS idx_sync_queue_table;
    DROP INDEX IF EXISTS idx_sync_queue_operation;
    DROP TABLE IF EXISTS feedback_sync_queue;
  `,
  metadata: {
    description: 'Creates feedback sync queue table for offline synchronization',
    estimatedDuration: 800,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false
  }
};