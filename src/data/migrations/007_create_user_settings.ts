/**
 * Migration: Create User Settings Table
 * 
 * Creates the user settings table for app configuration.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 7,
  name: 'create_user_settings',
  up: `
    CREATE TABLE IF NOT EXISTS user_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      setting_key TEXT NOT NULL UNIQUE,
      setting_value TEXT NOT NULL,
      setting_type TEXT NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
      is_encrypted BOOLEAN DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE UNIQUE INDEX IF NOT EXISTS idx_user_settings_key ON user_settings (setting_key);
    CREATE INDEX IF NOT EXISTS idx_user_settings_type ON user_settings (setting_type);
    CREATE INDEX IF NOT EXISTS idx_user_settings_encrypted ON user_settings (is_encrypted);

    -- Insert default settings
    INSERT OR IGNORE INTO user_settings (setting_key, setting_value, setting_type) VALUES
    ('currency', 'USD', 'string'),
    ('language', 'en', 'string'),
    ('theme', 'system', 'string'),
    ('biometric_enabled', 'false', 'boolean'),
    ('sync_enabled', 'false', 'boolean'),
    ('notification_enabled', 'true', 'boolean'),
    ('sms_parsing_enabled', 'true', 'boolean'),
    ('ml_categorization_enabled', 'true', 'boolean');
  `,
  down: `
    DROP INDEX IF EXISTS idx_user_settings_encrypted;
    DROP INDEX IF EXISTS idx_user_settings_type;
    DROP INDEX IF EXISTS idx_user_settings_key;
    DROP TABLE IF EXISTS user_settings;
  `,
  metadata: {
    description: 'Creates user settings table with default configuration',
    estimatedDuration: 500,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false
  }
};