/**
 * Migration Auto-Discovery System
 * 
 * Automatically discovers and validates migration files using React Native
 * compatible file system scanning. Eliminates hardcoded migration arrays.
 */

import type { 
  Migration, 
  MigrationDiscoveryResult, 
  MigrationFileInfo,
  MigrationValidationResult 
} from '../../shared/types/migration';

/**
 * Migration file naming convention:
 * {version}_{name}.ts
 * 
 * Examples:
 * - 001_create_accounts.ts
 * - 002_add_user_preferences.ts
 * - 015_optimize_transaction_indexes.ts
 */

/**
 * Migration Discovery Service
 * 
 * Handles automatic discovery, validation, and ordering of migration files.
 * Uses build-time generated registry for React Native compatibility.
 */
export class MigrationDiscovery {
  private discoveredMigrations: Map<number, Migration> = new Map();
  private lastDiscoveryTime: Date | null = null;


  /**
   * Discover all migration files and return validated results
   */
  public async discoverMigrations(): Promise<MigrationDiscoveryResult> {
    const startTime = new Date();
    const warnings: string[] = [];
    const errors: string[] = [];
    let filesScanned = 0;

    try {
      // Get migration files using React Native compatible approach
      const migrationFiles = await this.getMigrationFiles();
      filesScanned = migrationFiles.length;

      // Clear previous discoveries
      this.discoveredMigrations.clear();

      // Process each migration file
      for (const fileInfo of migrationFiles) {
        try {
          const migration = await this.loadMigrationFile(fileInfo);
          
          if (migration) {
            // Validate migration structure
            const validation = await this.validateMigration(migration, fileInfo);
            
            if (validation.isValid) {
              this.discoveredMigrations.set(migration.version, migration);
            } else {
              errors.push(`Migration ${fileInfo.name}: ${validation.errors.join(', ')}`);
            }
            
            // Collect warnings
            warnings.push(...validation.warnings);
          }
        } catch (loadError) {
          errors.push(`Failed to load ${fileInfo.name}: ${loadError instanceof Error ? loadError.message : 'Unknown error'}`);
        }
      }

      // Validate version sequence
      await this.validateVersionSequence(warnings, errors);

      this.lastDiscoveryTime = startTime;

      return {
        migrations: Array.from(this.discoveredMigrations.values()).sort((a, b) => a.version - b.version),
        warnings,
        errors,
        discoveredAt: startTime,
        filesScanned
      };

    } catch (error) {
      errors.push(`Discovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      return {
        migrations: [],
        warnings,
        errors,
        discoveredAt: startTime,
        filesScanned
      };
    }
  }

  /**
   * Get migration files using build-time generated registry
   * 
   * Uses the auto-generated migration registry created by build script.
   * Truly dynamic - no hardcoded lists, just reads the generated registry.
   */
  private async getMigrationFiles(): Promise<MigrationFileInfo[]> {
    try {
      console.log('🔍 Loading migrations from auto-generated registry...');
      
      // Use the auto-generated registry (created by npm run generate-migrations)
      const registryModule = require('./migrations.registry');
      const migrations = registryModule.getAllMigrations();
      const stats = registryModule.getRegistryStats();
      
      console.log(`✅ Registry loaded: ${stats.totalMigrations} migrations (generated ${stats.generatedAt})`);
      
      // Convert registry migrations to MigrationFileInfo format
      const migrationFiles: MigrationFileInfo[] = migrations.map((migration: any) => ({
        filePath: `./${String(migration.version).padStart(3, '0')}_${migration.name}.ts`,
        version: migration.version,
        name: migration.name,
        modifiedAt: new Date(migration.metadata?.createdAt || stats.generatedAt),
        isValidNaming: true,
        sizeBytes: 0
      }));

      // Validate registry integrity
      const validation = registryModule.validateRegistry();
      if (!validation.isValid) {
        console.error('❌ Migration registry validation failed:', validation.errors);
        throw new Error(`Registry validation failed: ${validation.errors.join(', ')}`);
      }

      if (validation.warnings.length > 0) {
        console.warn('⚠️ Registry validation warnings:', validation.warnings);
      }

      console.log(`🎉 Successfully loaded ${migrationFiles.length} migrations from registry`);
      return migrationFiles.sort((a, b) => a.version - b.version);

    } catch (error) {
      console.error('❌ Failed to load migration registry. Did you run "npm run generate-migrations"?');
      console.error('Error:', error);
      
      // Provide helpful error message
      throw new Error(
        `Migration registry not found or invalid. Please run "npm run generate-migrations" to generate the migration registry from your migration files.`
      );
    }
  }


  /**
   * Load and validate a migration file
   */
  private async loadMigrationFile(fileInfo: MigrationFileInfo): Promise<Migration | null> {
    try {
      // Since we're using the registry, get migration directly from it
      const registryModule = require('./migrations.registry');
      const migration = registryModule.getMigrationByVersion(fileInfo.version);

      if (!migration) {
        throw new Error(`No migration found for version ${fileInfo.version}`);
      }

      // Ensure version matches (should always match since it comes from registry)
      if (migration.version !== fileInfo.version) {
        throw new Error(`Version mismatch: expected=${fileInfo.version}, got=${migration.version}`);
      }

      return migration;

    } catch {
      console.warn(`Failed to load migration ${fileInfo.filePath}`);
      return null;
    }
  }

  /**
   * Validate migration structure and content
   */
  private async validateMigration(
    migration: Migration, 
    fileInfo: MigrationFileInfo
  ): Promise<MigrationValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let safetyScore = 1.0;
    let performanceImpact: 'low' | 'medium' | 'high' = 'low';

    // Required fields validation
    if (!migration.version || typeof migration.version !== 'number') {
      errors.push('Missing or invalid version number');
    }

    if (!migration.name || typeof migration.name !== 'string') {
      errors.push('Missing or invalid migration name');
    }

    if (!migration.up || typeof migration.up !== 'string') {
      errors.push('Missing or invalid up migration SQL');
    }

    // SQL content validation
    if (migration.up) {
      const sqlValidation = this.validateSQL(migration.up);
      errors.push(...sqlValidation.errors);
      warnings.push(...sqlValidation.warnings);
      safetyScore *= sqlValidation.safetyScore;
      performanceImpact = sqlValidation.performanceImpact;
    }

    // Rollback validation
    if (migration.down) {
      if (typeof migration.down !== 'string') {
        errors.push('Invalid rollback SQL type');
      } else {
        const rollbackValidation = this.validateSQL(migration.down);
        warnings.push(...rollbackValidation.warnings.map(w => `Rollback: ${w}`));
      }
    }

    // Naming convention validation
    const expectedName = fileInfo.name.replace(/_/g, '_');
    if (migration.name !== expectedName) {
      warnings.push(`Migration name "${migration.name}" doesn't match filename "${expectedName}"`);
    }

    // Metadata validation
    if (migration.metadata) {
      if (migration.metadata.estimatedDuration && migration.metadata.estimatedDuration > 30000) {
        warnings.push('Migration may take longer than 30 seconds');
        performanceImpact = 'high';
      }

      if (migration.metadata.isDataHeavy) {
        warnings.push('Migration involves large dataset operations');
        performanceImpact = performanceImpact === 'low' ? 'medium' : 'high';
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      safetyScore,
      performanceImpact,
      estimatedDuration: migration.metadata?.estimatedDuration || 1000,
      resourceRequirements: {
        memoryMB: migration.metadata?.isDataHeavy ? 100 : 10,
        diskSpaceMB: 5,
        executionTimeMS: migration.metadata?.estimatedDuration || 1000,
        blocksDatabase: this.doesBlockDatabase(migration.up),
        sqliteFeatures: this.extractRequiredFeatures(migration.up)
      }
    };
  }

  /**
   * Validate SQL content for safety and performance
   */
  private validateSQL(sql: string): {
    errors: string[];
    warnings: string[];
    safetyScore: number;
    performanceImpact: 'low' | 'medium' | 'high';
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    let safetyScore = 1.0;
    let performanceImpact: 'low' | 'medium' | 'high' = 'low';

    const upperSQL = sql.toUpperCase();

    // Dangerous operations detection
    const dangerousOperations = [
      { pattern: /DROP\s+TABLE/g, warning: 'Contains DROP TABLE - data loss risk' },
      { pattern: /DELETE\s+FROM.*WITHOUT.*WHERE/g, warning: 'DELETE without WHERE - data loss risk' },
      { pattern: /TRUNCATE/g, warning: 'TRUNCATE operation - data loss risk' },
      { pattern: /ALTER\s+TABLE.*DROP\s+COLUMN/g, warning: 'DROP COLUMN - data loss risk' }
    ];

    for (const { pattern, warning } of dangerousOperations) {
      if (pattern.test(upperSQL)) {
        warnings.push(warning);
        safetyScore *= 0.7;
      }
    }

    // Performance impact detection
    const heavyOperations = [
      { pattern: /CREATE\s+INDEX/g, impact: 'medium' },
      { pattern: /ALTER\s+TABLE.*ADD\s+COLUMN/g, impact: 'low' },
      { pattern: /UPDATE.*SET/g, impact: 'medium' },
      { pattern: /INSERT\s+INTO.*SELECT/g, impact: 'high' }
    ];

    for (const { pattern, impact } of heavyOperations) {
      if (pattern.test(upperSQL)) {
        if (impact === 'high' || (impact === 'medium' && performanceImpact === 'low')) {
          performanceImpact = impact as 'low' | 'medium' | 'high';
        }
      }
    }

    // Basic SQL syntax validation
    if (!upperSQL.trim()) {
      errors.push('Empty SQL statement');
    }

    if (upperSQL.includes('--') || upperSQL.includes('/*')) {
      warnings.push('SQL contains comments - ensure they are safe');
    }

    return { errors, warnings, safetyScore, performanceImpact };
  }

  /**
   * Check if SQL blocks database access
   */
  private doesBlockDatabase(sql: string): boolean {
    const upperSQL = sql.toUpperCase();
    const blockingOperations = [
      /ALTER\s+TABLE/,
      /CREATE\s+INDEX/,
      /DROP\s+INDEX/,
      /VACUUM/,
      /ANALYZE/
    ];

    return blockingOperations.some(pattern => pattern.test(upperSQL));
  }

  /**
   * Extract required SQLite features from SQL
   */
  private extractRequiredFeatures(sql: string): string[] {
    const features: string[] = [];
    const upperSQL = sql.toUpperCase();

    const featurePatterns = [
      { pattern: /FOREIGN\s+KEY/g, feature: 'foreign_keys' },
      { pattern: /WITH\s+RECURSIVE/g, feature: 'cte' },
      { pattern: /JSON_EXTRACT|JSON_/g, feature: 'json1' },
      { pattern: /MATCH/g, feature: 'fts' },
      { pattern: /RTREE/g, feature: 'rtree' }
    ];

    for (const { pattern, feature } of featurePatterns) {
      if (pattern.test(upperSQL) && !features.includes(feature)) {
        features.push(feature);
      }
    }

    return features;
  }

  /**
   * Validate migration version sequence
   */
  private async validateVersionSequence(warnings: string[], errors: string[]): Promise<void> {
    const versions = Array.from(this.discoveredMigrations.keys()).sort((a, b) => a - b);

    // Check for gaps in version sequence
    for (let i = 1; i <= versions.length; i++) {
      if (!versions.includes(i)) {
        errors.push(`Missing migration version ${i} - versions must be sequential`);
      }
    }

    // Check for duplicates
    const duplicates = versions.filter((version, index) => versions.indexOf(version) !== index);
    if (duplicates.length > 0) {
      errors.push(`Duplicate migration versions: ${duplicates.join(', ')}`);
    }

    // Check for reasonable version range
    const maxVersion = Math.max(...versions);
    if (maxVersion > 999) {
      warnings.push(`Version ${maxVersion} exceeds recommended range (001-999)`);
    }
  }

  /**
   * Get cached migrations if available
   */
  public getCachedMigrations(): Migration[] {
    return Array.from(this.discoveredMigrations.values()).sort((a, b) => a.version - b.version);
  }

  /**
   * Get specific migration by version
   */
  public getMigration(version: number): Migration | null {
    return this.discoveredMigrations.get(version) || null;
  }

  /**
   * Check if discovery needs refresh
   */
  public needsRefresh(): boolean {
    if (!this.lastDiscoveryTime) {
      return true;
    }

    // Refresh every 5 minutes in development
    const fiveMinutes = 5 * 60 * 1000;
    return Date.now() - this.lastDiscoveryTime.getTime() > fiveMinutes;
  }

  /**
   * Clear cached discoveries
   */
  public clearCache(): void {
    this.discoveredMigrations.clear();
    this.lastDiscoveryTime = null;
  }
}