/**
 * Migration: Create Dashboard Summary Table
 * 
 * Creates the dashboard summary table for caching daily financial summaries.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 9,
  name: 'create_dashboard_summary',
  up: `
    CREATE TABLE IF NOT EXISTS dashboard_summary (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      summary_date DATE NOT NULL UNIQUE,
      total_income DECIMAL(15, 2) DEFAULT 0.00,
      total_expenses DECIMAL(15, 2) DEFAULT 0.00,
      net_worth DECIMAL(15, 2) DEFAULT 0.00,
      account_balances TEXT, -- JSO<PERSON> string
      top_categories TEXT, -- JSON string
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE UNIQUE INDEX IF NOT EXISTS idx_dashboard_summary_date ON dashboard_summary (summary_date DESC);
    CREATE INDEX IF NOT EXISTS idx_dashboard_summary_updated ON dashboard_summary (updated_at DESC);
  `,
  down: `
    DROP INDEX IF EXISTS idx_dashboard_summary_updated;
    DROP INDEX IF EXISTS idx_dashboard_summary_date;
    DROP TABLE IF EXISTS dashboard_summary;
  `,
  metadata: {
    description: 'Creates dashboard summary table for performance optimization',
    estimatedDuration: 400,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false
  }
};