/**
 * Migration Registry (Auto-generated)
 * 
 * DO NOT EDIT THIS FILE MANUALLY!
 * Generated by: npm run generate-migrations
 * Generated at: 2025-08-14T08:47:30.051Z
 * 
 * This file is automatically generated from migration files in the migrations directory.
 * To add a new migration, create a new file following the pattern: 001_migration_name.ts
 * Then run: npm run generate-migrations
 */

import type { Migration } from '../../shared/types/migration';

// Auto-generated imports
import { migration as migration001 } from './001_create_accounts';
import { migration as migration002 } from './002_create_categories';
import { migration as migration003 } from './003_create_transactions';
import { migration as migration004 } from './004_create_budgets';
import { migration as migration005 } from './005_create_budget_categories';
import { migration as migration006 } from './006_create_sms_patterns';
import { migration as migration007 } from './007_create_user_settings';
import { migration as migration008 } from './008_create_due_dates';
import { migration as migration009 } from './009_create_dashboard_summary';
import { migration as migration010 } from './010_add_account_metadata';
import { migration as migration011 } from './011_add_transaction_metadata';
import { migration as migration012 } from './012_create_feedback_submissions';
import { migration as migration013 } from './013_create_feature_votes';
import { migration as migration014 } from './014_create_early_access_tokens';
import { migration as migration015 } from './015_create_feedback_sync_queue';

/**
 * Auto-generated registry of all migrations
 */
export const MIGRATION_REGISTRY: Record<number, Migration> = {
  1: migration001,
  2: migration002,
  3: migration003,
  4: migration004,
  5: migration005,
  6: migration006,
  7: migration007,
  8: migration008,
  9: migration009,
  10: migration010,
  11: migration011,
  12: migration012,
  13: migration013,
  14: migration014,
  15: migration015
};

/**
 * Get all registered migrations sorted by version
 */
export function getAllMigrations(): Migration[] {
  return Object.values(MIGRATION_REGISTRY).sort((a, b) => a.version - b.version);
}

/**
 * Get migration by version
 */
export function getMigrationByVersion(version: number): Migration | null {
  return MIGRATION_REGISTRY[version] || null;
}

/**
 * Get latest migration version
 */
export function getLatestVersion(): number {
  return 15;
}

/**
 * Get migration statistics (auto-generated)
 */
export function getRegistryStats() {
  return {
    totalMigrations: 15,
    latestVersion: 15,
    generatedAt: '2025-08-14T08:47:30.051Z',
    migrations: [
      { version: 1, name: 'create_accounts', file: '001_create_accounts.ts' },
      { version: 2, name: 'create_categories', file: '002_create_categories.ts' },
      { version: 3, name: 'create_transactions', file: '003_create_transactions.ts' },
      { version: 4, name: 'create_budgets', file: '004_create_budgets.ts' },
      { version: 5, name: 'create_budget_categories', file: '005_create_budget_categories.ts' },
      { version: 6, name: 'create_sms_patterns', file: '006_create_sms_patterns.ts' },
      { version: 7, name: 'create_user_settings', file: '007_create_user_settings.ts' },
      { version: 8, name: 'create_due_dates', file: '008_create_due_dates.ts' },
      { version: 9, name: 'create_dashboard_summary', file: '009_create_dashboard_summary.ts' },
      { version: 10, name: 'add_account_metadata', file: '010_add_account_metadata.ts' },
      { version: 11, name: 'add_transaction_metadata', file: '011_add_transaction_metadata.ts' },
      { version: 12, name: 'create_feedback_submissions', file: '012_create_feedback_submissions.ts' },
      { version: 13, name: 'create_feature_votes', file: '013_create_feature_votes.ts' },
      { version: 14, name: 'create_early_access_tokens', file: '014_create_early_access_tokens.ts' },
      { version: 15, name: 'create_feedback_sync_queue', file: '015_create_feedback_sync_queue.ts' }
    ]
  };
}

/**
 * Validate registry integrity
 */
export function validateRegistry(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const versions = Object.keys(MIGRATION_REGISTRY).map(Number).sort((a, b) => a - b);
  const migrations = Object.values(MIGRATION_REGISTRY);

  // Check for sequential versions
  for (let i = 1; i <= versions.length; i++) {
    if (!versions.includes(i)) {
      errors.push(`Missing migration version ${i}`);
    }
  }

  // Check for version mismatches
  for (const migration of migrations) {
    if (MIGRATION_REGISTRY[migration.version] !== migration) {
      errors.push(`Version mismatch for migration ${migration.version}`);
    }
  }

  // Check for duplicate names
  const names = migrations.map(m => m.name);
  const duplicateNames = names.filter((name, index) => names.indexOf(name) !== index);
  if (duplicateNames.length > 0) {
    warnings.push(`Duplicate migration names: ${duplicateNames.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
