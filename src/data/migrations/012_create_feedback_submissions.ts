/**
 * Migration: Create Feedback Submissions Table
 * 
 * Creates the feedback submissions table for user feedback and feature requests.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 12,
  name: 'create_feedback_submissions',
  up: `
    CREATE TABLE IF NOT EXISTS feedback_submissions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      supabase_id TEXT UNIQUE,
      category TEXT NOT NULL CHECK (category IN ('bug_report', 'feature_request', 'general_feedback', 'praise_complaint')),
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      priority_level TEXT NOT NULL CHECK (priority_level IN ('low', 'medium', 'high', 'critical')),
      device_id TEXT NOT NULL,
      contact_info TEXT,
      reward_eligible BOOLEAN DEFAULT 0,
      screenshot_path TEXT,
      submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed', 'conflict')),
      last_synced_at TIMESTAMP,
      status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'acknowledged', 'in_review', 'implemented', 'rejected')),
      admin_feedback TEXT,
      version INTEGER DEFAULT 1
    );

    CREATE INDEX IF NOT EXISTS idx_feedback_device_id ON feedback_submissions(device_id);
    CREATE INDEX IF NOT EXISTS idx_feedback_category ON feedback_submissions(category);
    CREATE INDEX IF NOT EXISTS idx_feedback_sync_status ON feedback_submissions(sync_status);
    CREATE INDEX IF NOT EXISTS idx_feedback_submitted_date ON feedback_submissions(submitted_at DESC);
    CREATE INDEX IF NOT EXISTS idx_feedback_category_status ON feedback_submissions(category, status);
    CREATE INDEX IF NOT EXISTS idx_feedback_priority ON feedback_submissions(priority_level);
    CREATE UNIQUE INDEX IF NOT EXISTS idx_feedback_supabase_id ON feedback_submissions(supabase_id);
  `,
  down: `
    DROP INDEX IF EXISTS idx_feedback_supabase_id;
    DROP INDEX IF EXISTS idx_feedback_priority;
    DROP INDEX IF EXISTS idx_feedback_category_status;
    DROP INDEX IF EXISTS idx_feedback_submitted_date;
    DROP INDEX IF EXISTS idx_feedback_sync_status;
    DROP INDEX IF EXISTS idx_feedback_category;
    DROP INDEX IF EXISTS idx_feedback_device_id;
    DROP TABLE IF EXISTS feedback_submissions;
  `,
  metadata: {
    description: 'Creates feedback submissions table for user feedback system',
    estimatedDuration: 700,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false
  }
};