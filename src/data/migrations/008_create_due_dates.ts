/**
 * Migration: Create Due Dates Table
 * 
 * Creates the due dates table for tracking payment due dates and EMIs.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 8,
  name: 'create_due_dates',
  up: `
    CREATE TABLE IF NOT EXISTS due_dates (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      account_id INTEGER NOT NULL,
      due_type TEXT NOT NULL CHECK (due_type IN ('credit_card_payment', 'loan_emi', 'bill_payment', 'subscription')),
      amount DECIMAL(15, 2),
      due_date DATE NOT NULL,
      is_recurring BOOLEAN DEFAULT 0,
      recurrence_pattern TEXT,
      is_completed BOOLEAN DEFAULT 0,
      completed_at TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE
    );

    CREATE INDEX IF NOT EXISTS idx_due_dates_account ON due_dates (account_id);
    CREATE INDEX IF NOT EXISTS idx_due_dates_type ON due_dates (due_type);
    CREATE INDEX IF NOT EXISTS idx_due_dates_date ON due_dates (due_date);
    CREATE INDEX IF NOT EXISTS idx_due_dates_pending ON due_dates (is_completed, due_date);
    CREATE INDEX IF NOT EXISTS idx_due_dates_recurring ON due_dates (is_recurring);
  `,
  down: `
    DROP INDEX IF EXISTS idx_due_dates_recurring;
    DROP INDEX IF EXISTS idx_due_dates_pending;
    DROP INDEX IF EXISTS idx_due_dates_date;
    DROP INDEX IF EXISTS idx_due_dates_type;
    DROP INDEX IF EXISTS idx_due_dates_account;
    DROP TABLE IF EXISTS due_dates;
  `,
  metadata: {
    description: 'Creates due dates table for payment tracking',
    estimatedDuration: 600,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false,
    requirements: ['foreign_keys']
  }
};