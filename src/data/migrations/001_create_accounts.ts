/**
 * Migration: Create Accounts Table
 * 
 * Creates the core accounts table for storing user bank accounts.
 * This migration is compatible with the existing DatabaseService initialization.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 1,
  name: 'create_accounts',
  up: `
    CREATE TABLE IF NOT EXISTS accounts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('checking', 'savings', 'credit', 'loan', 'investment')),
      balance DECIMAL(15, 2) NOT NULL DEFAULT 0.00,
      currency TEXT NOT NULL DEFAULT 'USD',
      is_active BOOLEAN DEFAULT 1,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      sync_status TEXT DEFAULT 'local' CHECK (sync_status IN ('local', 'synced', 'pending', 'conflict'))
    );

    CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts (type);
    CREATE INDEX IF NOT EXISTS idx_accounts_active ON accounts (is_active);
    CREATE INDEX IF NOT EXISTS idx_accounts_sync_status ON accounts (sync_status);
  `,
  down: `
    DROP INDEX IF EXISTS idx_accounts_sync_status;
    DROP INDEX IF EXISTS idx_accounts_active;
    DROP INDEX IF EXISTS idx_accounts_type;
    DROP TABLE IF EXISTS accounts;
  `,
  metadata: {
    description: 'Creates the core accounts table with proper indexes',
    estimatedDuration: 500,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false
  }
};