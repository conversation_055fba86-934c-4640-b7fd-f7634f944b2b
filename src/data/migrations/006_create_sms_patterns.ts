/**
 * Migration: Create SMS Patterns Table
 * 
 * Creates the SMS parsing patterns table for transaction extraction.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 6,
  name: 'create_sms_patterns',
  up: `
    CREATE TABLE IF NOT EXISTS sms_patterns (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      bank_identifier TEXT NOT NULL,
      pattern_regex TEXT NOT NULL,
      amount_group INTEGER NOT NULL DEFAULT 1,
      description_group INTEGER,
      date_group INTEGER,
      account_group INTEGER,
      transaction_type TEXT NOT NULL DEFAULT 'expense' CHECK (
        transaction_type IN (
          'income', 'expense', 'transfer',
          'credit_payment', 'credit_charge', 'credit_interest', 'credit_fee',
          'loan_emi', 'loan_interest', 'loan_principal', 'loan_fee', 'loan_prepayment'
        )
      ),
      confidence_score DECIMAL(3, 2) DEFAULT 0.8,
      is_active BOOLEAN DEFAULT 1,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE INDEX IF NOT EXISTS idx_sms_patterns_bank ON sms_patterns (bank_identifier);
    CREATE INDEX IF NOT EXISTS idx_sms_patterns_active ON sms_patterns (is_active);
    CREATE INDEX IF NOT EXISTS idx_sms_patterns_confidence ON sms_patterns (confidence_score DESC);
    CREATE INDEX IF NOT EXISTS idx_sms_patterns_type ON sms_patterns (transaction_type);
  `,
  down: `
    DROP INDEX IF EXISTS idx_sms_patterns_type;
    DROP INDEX IF EXISTS idx_sms_patterns_confidence;
    DROP INDEX IF EXISTS idx_sms_patterns_active;
    DROP INDEX IF EXISTS idx_sms_patterns_bank;
    DROP TABLE IF EXISTS sms_patterns;
  `,
  metadata: {
    description: 'Creates SMS parsing patterns table for transaction extraction',
    estimatedDuration: 400,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false
  }
};