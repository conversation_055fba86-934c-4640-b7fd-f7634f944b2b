/**
 * Migration: Create Budgets Table
 * 
 * Creates the budgets table for budget planning and tracking.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 4,
  name: 'create_budgets',
  up: `
    CREATE TABLE IF NOT EXISTS budgets (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      total_amount DECIMAL(15, 2) NOT NULL,
      spent_amount DECIMAL(15, 2) DEFAULT 0.00,
      period_type TEXT NOT NULL CHECK (period_type IN ('monthly', 'weekly', 'yearly')),
      start_date DATE NOT NULL,
      end_date DATE NOT NULL,
      is_active BOOLEAN DEFAULT 1,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      sync_status TEXT DEFAULT 'local' CHECK (sync_status IN ('local', 'synced', 'pending', 'conflict'))
    );

    CREATE INDEX IF NOT EXISTS idx_budgets_period ON budgets (period_type);
    CREATE INDEX IF NOT EXISTS idx_budgets_active ON budgets (is_active);
    CREATE INDEX IF NOT EXISTS idx_budgets_dates ON budgets (start_date, end_date);
    CREATE INDEX IF NOT EXISTS idx_budgets_sync_status ON budgets (sync_status);
  `,
  down: `
    DROP INDEX IF EXISTS idx_budgets_sync_status;
    DROP INDEX IF EXISTS idx_budgets_dates;
    DROP INDEX IF EXISTS idx_budgets_active;
    DROP INDEX IF EXISTS idx_budgets_period;
    DROP TABLE IF EXISTS budgets;
  `,
  metadata: {
    description: 'Creates budgets table for financial planning',
    estimatedDuration: 600,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false
  }
};