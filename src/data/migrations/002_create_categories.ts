/**
 * Migration: Create Categories Table
 * 
 * Creates the transaction categories table with hierarchical support.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 2,
  name: 'create_categories',
  up: `
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      parent_id INTEGER,
      category_type TEXT NOT NULL CHECK (category_type IN ('income', 'expense')),
      color_code TEXT NOT NULL DEFAULT '#6B7280',
      icon_name TEXT NOT NULL DEFAULT 'folder',
      is_system BOOLEAN DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      sync_status TEXT DEFAULT 'local' CHECK (sync_status IN ('local', 'synced', 'pending', 'conflict')),
      FOREIGN KEY (parent_id) REFERENCES categories (id) ON DELETE SET NULL
    );

    CREATE INDEX IF NOT EXISTS idx_categories_type ON categories (category_type);
    CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories (parent_id);
    CREATE INDEX IF NOT EXISTS idx_categories_system ON categories (is_system);
    CREATE INDEX IF NOT EXISTS idx_categories_sync_status ON categories (sync_status);

    -- Insert default system categories
    INSERT INTO categories (name, category_type, color_code, icon_name, is_system) VALUES
    ('Food & Dining', 'expense', '#EF4444', 'restaurant', 1),
    ('Transportation', 'expense', '#3B82F6', 'car', 1),
    ('Shopping', 'expense', '#8B5CF6', 'shopping-bag', 1),
    ('Entertainment', 'expense', '#EC4899', 'play', 1),
    ('Bills & Utilities', 'expense', '#F59E0B', 'receipt', 1),
    ('Health & Medical', 'expense', '#10B981', 'heart', 1),
    ('Salary', 'income', '#059669', 'dollar-sign', 1),
    ('Investment', 'income', '#DC2626', 'trending-up', 1),
    ('Other Income', 'income', '#6366F1', 'plus', 1),
    ('Other Expense', 'expense', '#6B7280', 'more-horizontal', 1);
  `,
  down: `
    DROP INDEX IF EXISTS idx_categories_sync_status;
    DROP INDEX IF EXISTS idx_categories_system;
    DROP INDEX IF EXISTS idx_categories_parent;
    DROP INDEX IF EXISTS idx_categories_type;
    DROP TABLE IF EXISTS categories;
  `,
  metadata: {
    description: 'Creates categories table with default system categories',
    estimatedDuration: 800,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false,
    requirements: ['foreign_keys']
  }
};