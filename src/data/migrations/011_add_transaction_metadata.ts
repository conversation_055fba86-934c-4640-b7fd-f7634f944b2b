/**
 * Migration: Add Transaction Metadata Table
 * 
 * Creates the transaction metadata table for storing additional
 * transaction details like merchant info, location, receipts, etc.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 11,
  name: 'add_transaction_metadata',
  up: `
    CREATE TABLE IF NOT EXISTS transaction_metadata (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      transaction_id INTEGER NOT NULL,
      merchant_name TEXT,
      merchant_category TEXT,
      location TEXT,
      payment_method TEXT,
      original_currency TEXT,
      exchange_rate DECIMAL(10, 6),
      tags TEXT, -- JSON array
      notes TEXT,
      receipt_path TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (transaction_id) REFERENCES transactions (id) ON DELETE CASCADE,
      UNIQUE (transaction_id)
    );

    CREATE UNIQUE INDEX IF NOT EXISTS idx_transaction_metadata_transaction ON transaction_metadata (transaction_id);
    CREATE INDEX IF NOT EXISTS idx_transaction_metadata_merchant ON transaction_metadata (merchant_name);
    CREATE INDEX IF NOT EXISTS idx_transaction_metadata_category ON transaction_metadata (merchant_category);
    CREATE INDEX IF NOT EXISTS idx_transaction_metadata_payment_method ON transaction_metadata (payment_method);
    
    -- Index for location-based queries (if location tracking is enabled)
    CREATE INDEX IF NOT EXISTS idx_transaction_metadata_location ON transaction_metadata (location);
  `,
  down: `
    DROP INDEX IF EXISTS idx_transaction_metadata_location;
    DROP INDEX IF EXISTS idx_transaction_metadata_payment_method;
    DROP INDEX IF EXISTS idx_transaction_metadata_category;
    DROP INDEX IF EXISTS idx_transaction_metadata_merchant;
    DROP INDEX IF EXISTS idx_transaction_metadata_transaction;
    DROP TABLE IF EXISTS transaction_metadata;
  `,
  metadata: {
    description: 'Creates transaction metadata table for enhanced transaction details',
    estimatedDuration: 900,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false,
    requirements: ['foreign_keys']
  }
};