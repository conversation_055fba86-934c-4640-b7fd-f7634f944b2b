/**
 * New Migration Manager
 * 
 * Orchestrates the complete migration system with auto-discovery,
 * validation, and safe execution. Replaces the hardcoded legacy system.
 */

import type { DatabaseService } from '../database/DatabaseService';
import type {
  Migration,
  MigrationSystemStatus,
  MigrationResult,
  MigrationConfig,
  MigrationEvent,
  MigrationEventPayload
} from '../../shared/types/migration';

import { MigrationDiscovery } from './MigrationDiscovery';
import { VersionManager } from './VersionManager';
import { MigrationRunner } from './MigrationRunner';
import { MigrationValidator } from './MigrationValidator';

/**
 * New Migration Manager
 * 
 * Main orchestration class that brings together all migration system components:
 * - Discovery: Auto-finds migrations from build-generated registry
 * - Validation: Pre-flight safety and performance checks  
 * - Execution: Transaction-safe migration runner
 * - Version Management: SQLite PRAGMA user_version integration
 */
export class MigrationManager {
  private readonly db: DatabaseService;
  private readonly discovery: MigrationDiscovery;
  private readonly versionManager: VersionManager;
  private readonly runner: MigrationRunner;
  private readonly validator: MigrationValidator;

  private static instance: MigrationManager | null = null;

  private constructor(database: DatabaseService, config: Partial<MigrationConfig> = {}) {
    this.db = database;
    this.discovery = new MigrationDiscovery();
    this.versionManager = new VersionManager(database);
    this.validator = new MigrationValidator(config);
    this.runner = new MigrationRunner(database, this.versionManager, config);
  }

  /**
   * Get singleton instance
   */
  public static getInstance(database?: DatabaseService, config?: Partial<MigrationConfig>): MigrationManager {
    if (!MigrationManager.instance) {
      if (!database) {
        throw new Error('Database service is required for first-time initialization');
      }
      MigrationManager.instance = new MigrationManager(database, config);
    }
    return MigrationManager.instance;
  }

  /**
   * Initialize migration system and migrate to latest version
   * 
   * This is the main entry point - call this once during app startup
   */
  public async initialize(): Promise<MigrationSystemStatus> {
    console.log('🚀 Initializing FinVibe migration system...');

    try {
      // Step 1: Migrate from legacy knex_migrations if needed
      console.log('📋 Checking for legacy migration state...');
      await this.versionManager.migrateLegacyVersion();

      // Step 2: Discover all available migrations
      console.log('🔍 Discovering migrations...');
      const discoveryResult = await this.discovery.discoverMigrations();
      
      if (discoveryResult.errors.length > 0) {
        console.error('❌ Migration discovery errors:', discoveryResult.errors);
        throw new Error(`Migration discovery failed: ${discoveryResult.errors.join(', ')}`);
      }

      if (discoveryResult.warnings.length > 0) {
        console.warn('⚠️ Migration discovery warnings:', discoveryResult.warnings);
      }

      console.log(`✅ Discovered ${discoveryResult.migrations.length} migrations`);

      // Step 3: Check current state
      const currentVersion = await this.versionManager.getCurrentVersion();
      console.log(`📊 Current database version: ${currentVersion}`);

      // Step 4: Find pending migrations
      const pendingMigrations = await this.getPendingMigrations();
      
      if (pendingMigrations.length === 0) {
        console.log('✅ Database is up to date - no migrations needed');
        return await this.getSystemStatus();
      }

      console.log(`📝 Found ${pendingMigrations.length} pending migrations`);

      // Step 5: Validate pending migrations
      console.log('🔍 Validating pending migrations...');
      const batchValidation = await this.validator.validateMigrationBatch(pendingMigrations, this.db);
      
      if (!batchValidation.overallValid) {
        console.error('❌ Migration validation failed:', batchValidation.batchErrors);
        throw new Error(`Migration validation failed: ${batchValidation.batchErrors.join(', ')}`);
      }

      if (batchValidation.batchWarnings.length > 0) {
        console.warn('⚠️ Migration validation warnings:', batchValidation.batchWarnings);
      }

      // Step 6: Execute pending migrations
      console.log(`🔄 Executing ${pendingMigrations.length} pending migrations...`);
      const migrationResults = await this.runner.runMigrations(pendingMigrations);

      // Step 7: Check results
      const successfulMigrations = migrationResults.filter(r => r.status === 'completed').length;
      const failedMigrations = migrationResults.filter(r => r.status === 'failed').length;

      if (failedMigrations > 0) {
        console.error(`❌ ${failedMigrations} migrations failed`);
        throw new Error(`Migration execution failed: ${failedMigrations} migrations failed`);
      }

      console.log(`🎉 Migration complete! Successfully applied ${successfulMigrations} migrations`);

      // Step 8: Return final status
      return await this.getSystemStatus();

    } catch (error) {
      console.error('💥 Migration system initialization failed:', error);
      throw error;
    }
  }

  /**
   * Get pending migrations that need to be executed
   */
  public async getPendingMigrations(): Promise<Migration[]> {
    const discoveryResult = await this.discovery.discoverMigrations();
    const currentVersion = await this.versionManager.getCurrentVersion();

    return discoveryResult.migrations.filter(m => m.version > currentVersion);
  }

  /**
   * Get complete system status
   */
  public async getSystemStatus(): Promise<MigrationSystemStatus> {
    try {
      const discoveryResult = await this.discovery.discoverMigrations();
      const currentVersion = await this.versionManager.getCurrentVersion();
      const runnerStatus = this.runner.getStatus();
      const versionInfo = await this.versionManager.getVersionInfo();
      
      const pendingMigrations = discoveryResult.migrations.filter(m => m.version > currentVersion);
      const latestVersion = Math.max(...discoveryResult.migrations.map(m => m.version));

      return {
        currentVersion,
        latestVersion,
        pendingCount: pendingMigrations.length,
        pendingMigrations,
        executionHistory: runnerStatus.executionHistory,
        isHealthy: discoveryResult.errors.length === 0 && versionInfo.isValid,
        lastMigrationAt: runnerStatus.executionHistory.length > 0 ? 
          runnerStatus.executionHistory[runnerStatus.executionHistory.length - 1].endTime || new Date() :
          undefined,
        performanceMetrics: this.calculatePerformanceMetrics(runnerStatus.executionHistory)
      };
    } catch (error) {
      console.error('Failed to get system status:', error);
      throw error;
    }
  }

  /**
   * Force refresh migration discovery (useful for development)
   */
  public async refreshDiscovery(): Promise<void> {
    this.discovery.clearCache();
    console.log('🔄 Migration discovery cache cleared');
  }

  /**
   * Validate a specific migration without running it
   */
  public async validateMigration(version: number): Promise<any> {
    const discoveryResult = await this.discovery.discoverMigrations();
    const migration = discoveryResult.migrations.find(m => m.version === version);
    
    if (!migration) {
      throw new Error(`Migration version ${version} not found`);
    }

    return await this.validator.validateMigration(migration, this.db);
  }

  /**
   * Get detailed version information
   */
  public async getVersionInfo(): Promise<any> {
    return await this.versionManager.getVersionInfo();
  }

  /**
   * Register event handler for migration lifecycle events
   */
  public onMigrationEvent(event: MigrationEvent, handler: (payload: MigrationEventPayload) => void): void {
    this.runner.on(event, handler);
  }

  /**
   * Remove event handler
   */
  public offMigrationEvent(event: MigrationEvent, handler: (payload: MigrationEventPayload) => void): void {
    this.runner.off(event, handler);
  }

  /**
   * Development utilities
   */
  public async getDevelopmentInfo(): Promise<{
    discoveredMigrations: Migration[];
    versionInfo: any;
    runnerStatus: any;
    validationStats: any;
  }> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Development info not available in production');
    }

    const discoveryResult = await this.discovery.discoverMigrations();
    const versionInfo = await this.versionManager.getVersionInfo();
    const runnerStatus = this.runner.getStatus();
    const validationStats = this.validator.getValidationStats();

    return {
      discoveredMigrations: discoveryResult.migrations,
      versionInfo,
      runnerStatus,
      validationStats
    };
  }

  /**
   * Emergency reset (development only)
   */
  public async emergencyReset(): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Emergency reset not allowed in production');
    }

    console.warn('🚨 Emergency reset requested - this will reset the database version to 0');
    
    // Stop any running migrations
    await this.runner.emergencyStop();
    
    // Reset version to 0
    await this.versionManager.resetVersion();
    
    // Clear caches
    this.discovery.clearCache();
    this.runner.clearHistory();

    console.log('🔄 Emergency reset complete - database version reset to 0');
  }

  /**
   * Calculate performance metrics from execution history
   */
  private calculatePerformanceMetrics(history: MigrationResult[]): any {
    if (history.length === 0) {
      return {
        averageExecutionTime: 0,
        totalMigrations: 0,
        successRate: 1,
        peakMemoryUsage: 0
      };
    }

    const completedMigrations = history.filter(r => r.status === 'completed');
    const totalDuration = completedMigrations.reduce((sum, r) => sum + (r.duration || 0), 0);
    const peakMemory = Math.max(...history
      .filter(r => r.memoryStats)
      .map(r => r.memoryStats!.peakMB)
    );

    return {
      averageExecutionTime: totalDuration / completedMigrations.length,
      totalMigrations: history.length,
      successRate: completedMigrations.length / history.length,
      peakMemoryUsage: peakMemory || 0
    };
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    MigrationManager.instance = null;
    console.log('🧹 Migration manager destroyed');
  }
}