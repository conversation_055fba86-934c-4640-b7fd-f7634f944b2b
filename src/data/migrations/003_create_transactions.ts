/**
 * Migration: Create Transactions Table
 * 
 * Creates the core transactions table with enhanced type support and metadata.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 3,
  name: 'create_transactions',
  up: `
    CREATE TABLE IF NOT EXISTS transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      account_id INTEGER NOT NULL,
      amount DECIMAL(15, 2) NOT NULL,
      description TEXT NOT NULL,
      category_id INTEGER,
      transaction_type TEXT NOT NULL CHECK (
        transaction_type IN (
          'income', 'expense', 'transfer',
          'credit_payment', 'credit_charge', 'credit_interest', 'credit_fee',
          'loan_emi', 'loan_interest', 'loan_principal', 'loan_fee', 'loan_prepayment'
        )
      ),
      transaction_date DATE NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      sms_source TEXT,
      confidence_score DECIMAL(3, 2),
      is_recurring BOOLEAN DEFAULT 0,
      recurring_pattern TEXT,
      sync_status TEXT DEFAULT 'local' CHECK (sync_status IN ('local', 'synced', 'pending', 'conflict')),
      hash TEXT UNIQUE,
      FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE,
      FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL
    );

    -- Performance indexes for the most common queries
    CREATE INDEX IF NOT EXISTS idx_transactions_account_date ON transactions (account_id, transaction_date DESC);
    CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions (category_id);
    CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions (transaction_type);
    CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions (transaction_date DESC);
    CREATE INDEX IF NOT EXISTS idx_transactions_amount ON transactions (amount);
    CREATE INDEX IF NOT EXISTS idx_transactions_sync_status ON transactions (sync_status);
    CREATE INDEX IF NOT EXISTS idx_transactions_hash ON transactions (hash);
    CREATE INDEX IF NOT EXISTS idx_transactions_recurring ON transactions (is_recurring);
    
    -- Composite index for dashboard queries
    CREATE INDEX IF NOT EXISTS idx_transactions_account_type_date ON transactions (account_id, transaction_type, transaction_date DESC);
  `,
  down: `
    DROP INDEX IF EXISTS idx_transactions_account_type_date;
    DROP INDEX IF EXISTS idx_transactions_recurring;
    DROP INDEX IF EXISTS idx_transactions_hash;
    DROP INDEX IF EXISTS idx_transactions_sync_status;
    DROP INDEX IF EXISTS idx_transactions_amount;
    DROP INDEX IF EXISTS idx_transactions_date;
    DROP INDEX IF EXISTS idx_transactions_type;
    DROP INDEX IF EXISTS idx_transactions_category;
    DROP INDEX IF EXISTS idx_transactions_account_date;
    DROP TABLE IF EXISTS transactions;
  `,
  metadata: {
    description: 'Creates transactions table with comprehensive indexing for performance',
    estimatedDuration: 1200,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false,
    requirements: ['foreign_keys']
  }
};