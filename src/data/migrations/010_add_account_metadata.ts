/**
 * Migration: Add Account Metadata Table
 * 
 * Creates the account metadata table for storing account-specific details
 * like credit limits, EMI information, etc.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 10,
  name: 'add_account_metadata',
  up: `
    CREATE TABLE IF NOT EXISTS account_metadata (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      account_id INTEGER NOT NULL,
      credit_limit DECIMAL(15, 2),
      outstanding_balance DECIMAL(15, 2),
      minimum_payment_due DECIMAL(15, 2),
      payment_due_date TEXT,
      last_statement_date TEXT,
      principal_amount DECIMAL(15, 2),
      current_principal DECIMAL(15, 2),
      interest_rate DECIMAL(5, 2),
      emi_amount DECIMAL(15, 2),
      tenure INTEGER,
      remaining_tenure INTEGER,
      next_emi_date TEXT,
      loan_start_date TEXT,
      total_interest_paid DECIMAL(15, 2),
      last_updated TEXT,
      auto_calculate BOOLEAN DEFAULT 1,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE,
      UNIQUE (account_id)
    );

    CREATE UNIQUE INDEX IF NOT EXISTS idx_account_metadata_account_id ON account_metadata (account_id);
    CREATE INDEX IF NOT EXISTS idx_account_metadata_payment_due_date ON account_metadata (payment_due_date);
    CREATE INDEX IF NOT EXISTS idx_account_metadata_next_emi_date ON account_metadata (next_emi_date);
    CREATE INDEX IF NOT EXISTS idx_account_metadata_auto_calculate ON account_metadata (auto_calculate);
  `,
  down: `
    DROP INDEX IF EXISTS idx_account_metadata_auto_calculate;
    DROP INDEX IF EXISTS idx_account_metadata_next_emi_date;
    DROP INDEX IF EXISTS idx_account_metadata_payment_due_date;
    DROP INDEX IF EXISTS idx_account_metadata_account_id;
    DROP TABLE IF EXISTS account_metadata;
  `,
  metadata: {
    description: 'Creates account metadata table for enhanced account information',
    estimatedDuration: 800,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false,
    requirements: ['foreign_keys']
  }
};