/**
 * Migration System Exports
 * 
 * Central export point for the new dynamic migration system
 */

export { MigrationDiscovery } from './MigrationDiscovery';
export { VersionManager } from './VersionManager';
export { MigrationRunner } from './MigrationRunner';
export { MigrationValidator } from './MigrationValidator';
export { MigrationManager } from './MigrationManager';

// Auto-generated migration registry (run: npm run generate-migrations)
export * as migrations from './migrations.registry';