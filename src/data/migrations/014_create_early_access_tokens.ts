/**
 * Migration: Create Early Access Tokens Table
 * 
 * Creates the early access tokens table for managing contributor rewards
 * and community wave access.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 14,
  name: 'create_early_access_tokens',
  up: `
    CREATE TABLE IF NOT EXISTS early_access_tokens (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      supabase_id TEXT UNIQUE,
      device_id TEXT NOT NULL,
      token_type TEXT NOT NULL CHECK (token_type IN ('contributor', 'community_wave')),
      granted_for TEXT NOT NULL,
      expires_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed')),
      is_active BOOLEAN DEFAULT 1
    );

    CREATE INDEX IF NOT EXISTS idx_tokens_device_id ON early_access_tokens(device_id);
    CREATE INDEX IF NOT EXISTS idx_tokens_type ON early_access_tokens(token_type);
    CREATE INDEX IF NOT EXISTS idx_tokens_expiry ON early_access_tokens(expires_at);
    CREATE INDEX IF NOT EXISTS idx_tokens_device_active ON early_access_tokens(device_id, is_active);
    CREATE INDEX IF NOT EXISTS idx_tokens_sync_status ON early_access_tokens(sync_status);
    CREATE UNIQUE INDEX IF NOT EXISTS idx_tokens_supabase_id ON early_access_tokens(supabase_id);
    CREATE INDEX IF NOT EXISTS idx_tokens_granted_for ON early_access_tokens(granted_for);
  `,
  down: `
    DROP INDEX IF EXISTS idx_tokens_granted_for;
    DROP INDEX IF EXISTS idx_tokens_supabase_id;
    DROP INDEX IF EXISTS idx_tokens_sync_status;
    DROP INDEX IF EXISTS idx_tokens_device_active;
    DROP INDEX IF EXISTS idx_tokens_expiry;
    DROP INDEX IF EXISTS idx_tokens_type;
    DROP INDEX IF EXISTS idx_tokens_device_id;
    DROP TABLE IF EXISTS early_access_tokens;
  `,
  metadata: {
    description: 'Creates early access tokens table for contributor rewards',
    estimatedDuration: 500,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false
  }
};