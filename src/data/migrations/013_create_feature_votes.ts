/**
 * Migration: Create Feature Votes Table
 * 
 * Creates the feature votes table for community-driven feature prioritization.
 */

import type { Migration } from '../../shared/types/migration';

export const migration: Migration = {
  version: 13,
  name: 'create_feature_votes',
  up: `
    CREATE TABLE IF NOT EXISTS feature_votes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      supabase_id TEXT UNIQUE,
      feature_request_id TEXT NOT NULL,
      device_id TEXT NOT NULL,
      vote_value INTEGER NOT NULL CHECK (vote_value IN (1, -1)),
      voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed')),
      last_synced_at TIMESTAMP,
      UNIQUE(feature_request_id, device_id)
    );

    CREATE INDEX IF NOT EXISTS idx_votes_device_id ON feature_votes(device_id);
    CREATE INDEX IF NOT EXISTS idx_votes_feature_id ON feature_votes(feature_request_id);
    CREATE INDEX IF NOT EXISTS idx_votes_sync_status ON feature_votes(sync_status);
    CREATE INDEX IF NOT EXISTS idx_votes_date ON feature_votes(voted_at DESC);
    CREATE INDEX IF NOT EXISTS idx_votes_value ON feature_votes(vote_value);
    CREATE UNIQUE INDEX IF NOT EXISTS idx_votes_supabase_id ON feature_votes(supabase_id);
    CREATE UNIQUE INDEX IF NOT EXISTS idx_votes_feature_device ON feature_votes(feature_request_id, device_id);
  `,
  down: `
    DROP INDEX IF EXISTS idx_votes_feature_device;
    DROP INDEX IF EXISTS idx_votes_supabase_id;
    DROP INDEX IF EXISTS idx_votes_value;
    DROP INDEX IF EXISTS idx_votes_date;
    DROP INDEX IF EXISTS idx_votes_sync_status;
    DROP INDEX IF EXISTS idx_votes_feature_id;
    DROP INDEX IF EXISTS idx_votes_device_id;
    DROP TABLE IF EXISTS feature_votes;
  `,
  metadata: {
    description: 'Creates feature votes table for community feature prioritization',
    estimatedDuration: 600,
    isDataHeavy: false,
    author: 'FinVibe Team',
    createdAt: '2024-01-01T00:00:00Z',
    isBreaking: false
  }
};