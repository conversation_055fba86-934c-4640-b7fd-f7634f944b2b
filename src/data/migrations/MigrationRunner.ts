/**
 * Migration Execution Engine
 * 
 * Transaction-safe migration runner with automatic rollback, performance monitoring,
 * and error recovery. Provides atomic execution guarantees for database migrations.
 */

import type { 
  Migration, 
  MigrationResult, 
  MigrationError, 
  MigrationStatus,
  MigrationProgress,
  MigrationMemoryStats,
  MigrationEventPayload,
  MigrationEvent
} from '../../shared/types/migration';
import type { DatabaseService } from '../database/DatabaseService';
import { VersionManager } from './VersionManager';

/**
 * Migration execution configuration
 */
interface MigrationRunnerConfig {
  /** Timeout for individual migrations (ms) */
  migrationTimeout: number;
  
  /** Memory limit per migration (MB) */
  memoryLimitMB: number;
  
  /** Enable rollback on failure */
  enableRollback: boolean;
  
  /** Enable performance monitoring */
  enableMetrics: boolean;
  
  /** Enable progress reporting */
  enableProgressReporting: boolean;
  
  /** Maximum concurrent migrations */
  maxConcurrentMigrations: number;
  
  /** Development mode settings */
  isDevelopment: boolean;
}

/**
 * Default configuration
 */
const DEFAULT_CONFIG: MigrationRunnerConfig = {
  migrationTimeout: 30000, // 30 seconds
  memoryLimitMB: 50,
  enableRollback: true,
  enableMetrics: true,
  enableProgressReporting: true,
  maxConcurrentMigrations: 1, // Sequential execution for safety
  isDevelopment: process.env.NODE_ENV !== 'production'
};

/**
 * Migration Runner - Transaction-Safe Execution Engine
 * 
 * Handles migration execution with atomic transactions, performance monitoring,
 * and comprehensive error handling. Integrates with VersionManager for
 * consistent state management.
 */
export class MigrationRunner {
  private readonly db: DatabaseService;
  private readonly versionManager: VersionManager;
  private readonly config: MigrationRunnerConfig;
  private readonly eventHandlers: Map<MigrationEvent, Array<(payload: MigrationEventPayload) => void>> = new Map();
  
  private isRunning: boolean = false;
  private currentExecution: Promise<MigrationResult[]> | null = null;
  private executionHistory: MigrationResult[] = [];
  private runningMigrations: Set<number> = new Set();

  constructor(
    database: DatabaseService, 
    versionManager: VersionManager,
    config: Partial<MigrationRunnerConfig> = {}
  ) {
    this.db = database;
    this.versionManager = versionManager;
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Execute a batch of migrations with transaction safety
   */
  public async runMigrations(migrations: Migration[]): Promise<MigrationResult[]> {
    if (this.isRunning) {
      throw new Error('Migration runner is already executing. Wait for current execution to complete.');
    }

    // Validate migrations before execution
    await this.validateMigrationsForExecution(migrations);

    this.isRunning = true;
    this.currentExecution = this.executeMigrations(migrations);

    try {
      const results = await this.currentExecution;
      return results;
    } finally {
      this.isRunning = false;
      this.currentExecution = null;
    }
  }

  /**
   * Execute a single migration with full safety guarantees
   */
  public async runSingleMigration(migration: Migration): Promise<MigrationResult> {
    const results = await this.runMigrations([migration]);
    return results[0];
  }

  /**
   * Internal migration execution with comprehensive error handling
   */
  private async executeMigrations(migrations: Migration[]): Promise<MigrationResult[]> {
    const results: MigrationResult[] = [];
    const totalMigrations = migrations.length;
    let completedCount = 0;

    // Sort migrations by version to ensure correct order
    const sortedMigrations = [...migrations].sort((a, b) => a.version - b.version);

    console.log(`🚀 Starting migration execution: ${totalMigrations} migrations`);
    this.emitEvent('migration_started', { 
      progress: this.createProgressInfo(0, totalMigrations, completedCount, 'Starting migrations...')
    });

    for (const migration of sortedMigrations) {
      // Check if migration is already applied
      const isApplied = await this.versionManager.isVersionApplied(migration.version);
      if (isApplied) {
        console.log(`⏭️ Skipping migration ${migration.version} (already applied)`);
        
        const skippedResult: MigrationResult = {
          migration,
          status: 'skipped',
          startTime: new Date(),
          endTime: new Date(),
          duration: 0
        };
        
        results.push(skippedResult);
        completedCount++;
        continue;
      }

      // Execute migration with full safety
      const result = await this.executeSingleMigrationSafely(
        migration,
        completedCount,
        totalMigrations
      );

      results.push(result);
      this.executionHistory.push(result);

      if (result.status === 'completed') {
        completedCount++;
      } else if (result.status === 'failed') {
        // Stop execution on failure unless configured otherwise
        console.error(`❌ Migration ${migration.version} failed, stopping execution`);
        break;
      }
    }

    const successfulMigrations = results.filter(r => r.status === 'completed').length;
    console.log(`✅ Migration execution complete: ${successfulMigrations}/${totalMigrations} successful`);
    
    this.emitEvent('all_migrations_completed', {
      progress: this.createProgressInfo(totalMigrations, totalMigrations, successfulMigrations, 'All migrations completed')
    });

    return results;
  }

  /**
   * Execute single migration with comprehensive safety measures
   */
  private async executeSingleMigrationSafely(
    migration: Migration,
    currentIndex: number,
    totalMigrations: number
  ): Promise<MigrationResult> {
    const startTime = new Date();
    let memoryStats: MigrationMemoryStats | undefined;

    // Track running migrations
    this.runningMigrations.add(migration.version);

    const result: MigrationResult = {
      migration,
      status: 'running',
      startTime,
      duration: 0
    };

    try {
      console.log(`🔄 Executing migration ${migration.version}: ${migration.name}`);
      
      // Emit progress update
      this.emitEvent('migration_started', {
        migration,
        result,
        progress: this.createProgressInfo(
          totalMigrations, 
          currentIndex + 1, 
          currentIndex, 
          `Executing ${migration.name}...`
        )
      });

      // Collect memory stats if enabled
      if (this.config.enableMetrics) {
        memoryStats = await this.collectMemoryStats('before');
      }

      // Pre-flight validation
      if (migration.validate) {
        console.log(`🔍 Running pre-flight validation for ${migration.version}`);
        await Promise.race([
          migration.validate(this.db),
          this.createTimeoutPromise(5000, 'Validation timeout')
        ]);
      }

      // Execute migration using database transaction method
      console.log(`📝 Executing migration ${migration.version} within transaction`);
      
      await this.db.transaction(async (tx) => {
        // Execute migration SQL using the transaction
        const sqlStatements = this.splitSQLStatements(migration.up);
        
        for (const sql of sqlStatements) {
          if (sql.trim()) {
            await new Promise<void>((resolve, reject) => {
              tx.executeSql(
                sql,
                [],
                () => resolve(),
                (_, error) => {
                  reject(error);
                  return false;
                }
              );
            });
          }
        }

        // Post-migration verification within the same transaction
        if (migration.verify) {
          console.log(`✅ Running post-migration verification for ${migration.version}`);
          const verificationResult = await Promise.race([
            migration.verify(this.db),
            this.createTimeoutPromise(5000, 'Verification timeout')
          ]);

          if (!verificationResult) {
            throw new Error('Post-migration verification failed');
          }
        }

        // Update version within the same transaction
        await this.versionManager.setVersionInTransaction(tx, migration.version);
      });

      // Collect final memory stats
      if (this.config.enableMetrics && memoryStats) {
        const finalStats = await this.collectMemoryStats('after');
        memoryStats.afterMB = finalStats.beforeMB;
        memoryStats.peakMB = Math.max(memoryStats.peakMB, finalStats.beforeMB);
      }

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      // Update result with success
      Object.assign(result, {
        status: 'completed' as MigrationStatus,
        endTime,
        duration,
        memoryStats
      });

      console.log(`✅ Migration ${migration.version} completed successfully (${duration}ms)`);
      
      this.emitEvent('migration_completed', {
        migration,
        result
      });

      return result;

    } catch (error) {
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      // Transaction was automatically rolled back by the database service
      console.error(`❌ Migration ${migration.version} failed and was rolled back:`, error);

      // Execute rollback SQL if available and configured
      if (this.config.enableRollback && migration.down) {
        try {
          console.log(`🔙 Executing rollback SQL for migration ${migration.version}`);
          await this.executeMigrationSQL({
            ...migration,
            up: migration.down,
            down: ''
          });
        } catch (rollbackError) {
          console.error(`❌ Rollback failed for migration ${migration.version}:`, rollbackError);
        }
      }

      // Create detailed error information
      const migrationError = this.createMigrationError(error, migration);

      // Update result with failure
      Object.assign(result, {
        status: 'failed' as MigrationStatus,
        endTime,
        duration,
        error: migrationError,
        memoryStats
      });

      console.error(`❌ Migration ${migration.version} failed:`, migrationError);
      
      this.emitEvent('migration_failed', {
        migration,
        result,
        error: migrationError
      });

      return result;

    } finally {
      // Clean up
      this.runningMigrations.delete(migration.version);
      
      // Force garbage collection if available (development)
      if (this.config.isDevelopment && global.gc) {
        global.gc();
      }
    }
  }

  /**
   * Execute migration SQL statements
   */
  private async executeMigrationSQL(migration: Migration): Promise<void> {
    const sqlStatements = this.splitSQLStatements(migration.up);
    
    for (const sql of sqlStatements) {
      if (sql.trim()) {
        await this.db.executeSql(sql);
      }
    }
  }

  /**
   * Split SQL into individual statements for execution
   */
  private splitSQLStatements(sql: string): string[] {
    // Simple SQL statement splitting (handles basic cases)
    // For production, might want to use a proper SQL parser
    return sql
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0);
  }

  /**
   * Create timeout promise for operation limits
   */
  private createTimeoutPromise(timeoutMs: number, message: string): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(message));
      }, timeoutMs);
    });
  }

  /**
   * Collect memory usage statistics
   */
  private async collectMemoryStats(phase: 'before' | 'after'): Promise<MigrationMemoryStats> {
    // React Native memory monitoring is limited
    // This is a simplified implementation
    const memoryUsage = process.memoryUsage ? process.memoryUsage() : null;
    const usedMB = memoryUsage ? Math.round(memoryUsage.heapUsed / 1024 / 1024) : 0;

    return {
      beforeMB: phase === 'before' ? usedMB : 0,
      peakMB: usedMB,
      afterMB: phase === 'after' ? usedMB : 0
    };
  }

  /**
   * Create detailed migration error
   */
  private createMigrationError(error: unknown, migration: Migration): MigrationError {
    const message = error instanceof Error ? error.message : 'Unknown error';
    
    return {
      type: this.classifyError(error),
      message,
      failedSQL: migration.up,
      originalError: error instanceof Error ? error : new Error(message),
      isRecoverable: this.isRecoverableError(error),
      recoverySuggestions: this.generateRecoverySuggestions(error, migration),
      context: {
        migrationVersion: migration.version,
        databaseVersionBefore: 0, // Will be filled by caller if needed
        environment: this.config.isDevelopment ? 'development' : 'production',
        deviceInfo: this.getDeviceInfo()
      }
    };
  }

  /**
   * Classify error type for better debugging
   */
  private classifyError(error: unknown): MigrationError['type'] {
    if (!(error instanceof Error)) {
      return 'unknown';
    }

    const message = error.message.toLowerCase();

    if (message.includes('syntax error')) {
      return 'sql_syntax';
    } else if (message.includes('constraint')) {
      return 'constraint_violation';
    } else if (message.includes('timeout')) {
      return 'timeout';
    } else if (message.includes('permission') || message.includes('denied')) {
      return 'permission_denied';
    } else if (message.includes('memory') || message.includes('out of memory')) {
      return 'memory_limit';
    } else if (message.includes('disk') || message.includes('space')) {
      return 'disk_space';
    } else if (message.includes('corrupt')) {
      return 'corruption';
    } else {
      return 'unknown';
    }
  }

  /**
   * Determine if error is recoverable
   */
  private isRecoverableError(error: unknown): boolean {
    if (!(error instanceof Error)) {
      return false;
    }

    const recoverableTypes = ['timeout', 'memory_limit', 'disk_space'];
    return recoverableTypes.includes(this.classifyError(error));
  }

  /**
   * Generate recovery suggestions based on error type
   */
  private generateRecoverySuggestions(error: unknown, migration: Migration): string[] {
    const suggestions: string[] = [];
    const errorType = this.classifyError(error);

    switch (errorType) {
      case 'sql_syntax':
        suggestions.push('Check SQL syntax in migration file');
        suggestions.push('Verify table/column names are correct');
        break;
      case 'timeout':
        suggestions.push('Increase migration timeout');
        suggestions.push('Break large operations into smaller chunks');
        break;
      case 'memory_limit':
        suggestions.push('Reduce batch size for data operations');
        suggestions.push('Add memory optimization to migration');
        break;
      case 'constraint_violation':
        suggestions.push('Check foreign key constraints');
        suggestions.push('Verify data consistency before migration');
        break;
      default:
        suggestions.push('Check migration logs for details');
        suggestions.push('Verify database is accessible');
    }

    return suggestions;
  }

  /**
   * Get device information for error context
   */
  private getDeviceInfo(): MigrationError['context']['deviceInfo'] {
    return {
      platform: process.platform || 'unknown',
      osVersion: 'unknown',
      appVersion: 'unknown',
      availableMemoryMB: 0,
      availableStorageMB: 0
    };
  }

  /**
   * Create progress information
   */
  private createProgressInfo(
    total: number,
    current: number,
    completed: number,
    currentStep: string
  ): MigrationProgress {
    return {
      currentMigration: current,
      totalMigrations: total,
      percentage: Math.round((completed / total) * 100),
      currentStep,
      completedMigrations: Array.from({ length: completed }, (_, i) => i + 1),
      failedMigrations: []
    };
  }

  /**
   * Validate migrations before execution
   */
  private async validateMigrationsForExecution(migrations: Migration[]): Promise<void> {
    if (migrations.length === 0) {
      throw new Error('No migrations provided for execution');
    }

    // Check for version conflicts
    const versions = migrations.map(m => m.version);
    const duplicates = versions.filter((v, i) => versions.indexOf(v) !== i);
    if (duplicates.length > 0) {
      throw new Error(`Duplicate migration versions: ${duplicates.join(', ')}`);
    }

    // Validate version sequence
    const sortedVersions = [...versions].sort((a, b) => a - b);
    for (let i = 1; i < sortedVersions.length; i++) {
      if (sortedVersions[i] - sortedVersions[i - 1] > 1) {
        const currentVersion = await this.versionManager.getCurrentVersion();
        const gaps = [];
        for (let v = currentVersion + 1; v < sortedVersions[0]; v++) {
          gaps.push(v);
        }
        if (gaps.length > 0) {
          throw new Error(`Missing migrations: ${gaps.join(', ')}`);
        }
      }
    }
  }

  /**
   * Event system for migration lifecycle
   */
  public on(event: MigrationEvent, handler: (payload: MigrationEventPayload) => void): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  /**
   * Remove event handler
   */
  public off(event: MigrationEvent, handler: (payload: MigrationEventPayload) => void): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Emit migration event
   */
  private emitEvent(event: MigrationEvent, payload: Partial<MigrationEventPayload> = {}): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers && handlers.length > 0) {
      const eventPayload: MigrationEventPayload = {
        event,
        timestamp: new Date(),
        ...payload
      };

      handlers.forEach(handler => {
        try {
          handler(eventPayload);
        } catch (error) {
          console.error(`Event handler error for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get current execution status
   */
  public getStatus(): {
    isRunning: boolean;
    runningMigrations: number[];
    executionHistory: MigrationResult[];
    currentExecution: Promise<MigrationResult[]> | null;
  } {
    return {
      isRunning: this.isRunning,
      runningMigrations: Array.from(this.runningMigrations),
      executionHistory: [...this.executionHistory],
      currentExecution: this.currentExecution
    };
  }

  /**
   * Clear execution history
   */
  public clearHistory(): void {
    this.executionHistory = [];
  }

  /**
   * Emergency stop (development only)
   */
  public async emergencyStop(): Promise<void> {
    if (!this.config.isDevelopment) {
      throw new Error('Emergency stop only available in development mode');
    }

    console.warn('🛑 Emergency stop requested - rolling back current transaction');
    
    try {
      await this.db.executeSql('ROLLBACK');
    } catch {
      // Ignore rollback errors during emergency stop
    }
    
    this.isRunning = false;
    this.runningMigrations.clear();
  }
}