import { TransactionType } from '../shared/types';

export interface SMSPattern {
  patterns: RegExp[];
  transactionType: TransactionType;
  extractFields: string[];
  description: string;
  confidence: number; // Base confidence score
}

export interface SMSParsingResult {
  amount: number;
  transactionType: TransactionType;
  description: string;
  extractedFields: Record<string, any>;
  confidence: number;
  patternMatched: string;
  bankIdentifier?: string;
}

// Credit Card SMS Patterns
export const creditCardPatterns: Record<string, SMSPattern> = {
  purchase: {
    patterns: [
      // Standard purchase patterns
      /(?:spent|charged|purchase|txn).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:transaction|payment).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)\s*(?:at|on)\s*([^.]+)/i,
      /credit card.*?(?:used|charged).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      // Bank specific patterns
      /(?:HDFC|hdfc).*?(?:spent|charged).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)\s*(?:at|on)\s*([^.]+)/i,
      /(?:ICICI|icici).*?(?:transaction|txn).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)\s*(?:at|on)\s*([^.]+)/i,
      /(?:SBI|sbi).*?(?:purchase|spent).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:AXIS|axis).*?(?:charged|debited).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)\s*(?:at|on)\s*([^.]+)/i
    ],
    transactionType: 'credit_charge',
    extractFields: ['amount', 'merchantName', 'transactionDate'],
    description: 'Credit card purchase transaction',
    confidence: 0.9
  },

  payment: {
    patterns: [
      // Credit card payment confirmation patterns
      /(?:payment|credited|received).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /thank you.*?payment.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /credit card.*?payment.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:bill|dues).*?paid.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      // Bank specific credit card payment patterns  
      /(?:HDFC|hdfc).*?(?:card|credit).*?payment.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:ICICI|icici).*?(?:card|credit).*?credited.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:SBI|sbi).*?(?:card|credit).*?payment.*?received.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:AXIS|axis).*?(?:card|credit).*?payment.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'credit_payment',
    extractFields: ['amount', 'paymentDate'],
    description: 'Credit card payment received',
    confidence: 0.95
  },

  availableCredit: {
    patterns: [
      /available.*?(?:credit|limit).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:credit|limit).*?available.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:current|remaining).*?(?:credit|limit).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /outstanding.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?).*?available.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'credit_charge', // This is used for updating metadata, not creating transaction
    extractFields: ['availableCredit', 'outstandingAmount'],
    description: 'Available credit limit information',
    confidence: 0.85
  },

  interest: {
    patterns: [
      /interest.*?charged.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /finance.*?charge.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:late|penalty).*?charge.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /interest.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?).*?outstanding/i
    ],
    transactionType: 'credit_interest',
    extractFields: ['amount', 'interestRate'],
    description: 'Credit card interest charges',
    confidence: 0.9
  },

  fee: {
    patterns: [
      /annual.*?fee.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:joining|membership).*?fee.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:cash.*?advance|overlimit).*?fee.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /service.*?charge.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'credit_fee',
    extractFields: ['amount', 'feeType'],
    description: 'Credit card fees',
    confidence: 0.9
  }
};

// Enhanced Loan SMS Patterns
export const loanEMIPatterns: Record<string, SMSPattern> = {
  emiDeducted: {
    patterns: [
      // Standard EMI patterns - capture amount immediately after EMI/installment keywords
      /(?:EMI|installment)\s+(?:of\s+)?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:loan\s+)?(?:EMI|installment)\s+(?:amount\s+)?(?:of\s+)?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:home|personal|car|education)\s*loan\s+(?:EMI|installment)\s+(?:of\s+)?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:monthly\s+)?(?:EMI|installment)\s+(?:of\s+)?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)\s+(?:has\s+been\s+)?(?:debited|deducted|charged)/i,
      // Bank specific EMI patterns - more precise matching
      /(?:HDFC|hdfc).*?(?:loan\s+)?(?:EMI|installment)\s+(?:of\s+)?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:ICICI|icici).*?(?:loan\s+)?(?:EMI|installment)\s+(?:of\s+)?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:SBI|sbi).*?(?:loan\s+)?(?:EMI|installment)\s+(?:of\s+)?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)\s+(?:has\s+been\s+)?(?:debited|deducted)/i,
      /(?:AXIS|axis).*?(?:loan\s+)?(?:EMI|installment)\s+(?:of\s+)?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'loan_emi',
    extractFields: ['amount', 'emiNumber', 'nextEmiDate', 'principalComponent', 'interestComponent'],
    description: 'Loan EMI payment deduction',
    confidence: 0.95
  },

  outstandingBalance: {
    patterns: [
      /outstanding.*?(?:balance|amount).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:principal|balance).*?outstanding.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /remaining.*?(?:principal|balance).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /loan.*?balance.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'loan_emi', // This is used for updating metadata
    extractFields: ['outstandingBalance', 'remainingTenure'],
    description: 'Loan outstanding balance information',
    confidence: 0.85
  },

  interestRateChange: {
    patterns: [
      /interest.*?rate.*?(?:changed|revised|updated).*?([0-9.]+)%/i,
      /(?:new|revised)\s*(?:interest\s*)?rate.*?([0-9.]+)%/i,
      /rate.*?revision.*?([0-9.]+)%/i,
      /interest.*?([0-9.]+)%.*?effective/i
    ],
    transactionType: 'loan_interest',
    extractFields: ['newInterestRate', 'effectiveDate', 'oldInterestRate'],
    description: 'Loan interest rate change notification',
    confidence: 0.8
  },

  prepayment: {
    patterns: [
      /(?:loan|emi).*?(?:prepayment|pre-payment|part.*?payment|excess.*?payment).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:prepayment|pre-payment).*?(?:loan|emi).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /part.*?payment.*?(?:loan|emi|principal).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /(?:early|advance).*?(?:loan|emi).*?payment.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /excess.*?payment.*?(?:loan|principal|outstanding).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'loan_prepayment',
    extractFields: ['amount', 'savingsInInterest', 'newOutstandingBalance'],
    description: 'Loan prepayment or part payment',
    confidence: 0.95
  },

  paymentConfirmation: {
    patterns: [
      /loan.*?payment.*?(?:received|credited).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /thank you.*?loan.*?payment.*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i,
      /payment.*?(?:received|processed).*?(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/i
    ],
    transactionType: 'loan_emi',
    extractFields: ['amount', 'paymentDate'],
    description: 'Loan payment confirmation',
    confidence: 0.9
  }
};

// Bank identifier patterns
export const bankIdentifierPatterns: Record<string, RegExp> = {
  'HDFC': /(?:HDFC|hdfc)/i,
  'ICICI': /(?:ICICI|icici)/i,
  'SBI': /(?:SBI|sbi|state bank)/i,
  'AXIS': /(?:AXIS|axis)/i,
  'KOTAK': /(?:KOTAK|kotak)/i,
  'PNB': /(?:PNB|pnb|punjab national)/i,
  'BOI': /(?:BOI|bank of india)/i,
  'CANARA': /(?:CANARA|canara)/i,
  'UNION': /(?:UNION|union bank)/i,
  'IDBI': /(?:IDBI|idbi)/i,
  'INDIAN': /(?:INDIAN|indian bank)/i,
  'CENTRAL': /(?:CENTRAL|central bank)/i
};

// Common SMS patterns for amount extraction
export const commonPatterns = {
  amount: /(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.\d{2})?)/gi,
  dateTime: /(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4}|\d{1,2}\s(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s\d{2,4})/gi,
  merchantName: /\b(?:at|on)\s+([A-Z][A-Z0-9\s&.'-]+?)(?:\s+(?:on|avl|available|bal|balance|\d)|\.|$)/i,
  cardNumber: /(?:card|ending)\s*(?:no\.?|number)?\s*[x*]*(\d{4})/i,
  referenceNumber: /(?:ref|reference|txn|transaction)\s*(?:no\.?|number|id)?\s*:?\s*(\w+)/i
};

/**
 * Extracts bank identifier from SMS text
 */
export function identifyBankFromSMS(smsText: string): string | null {
  for (const [bankName, pattern] of Object.entries(bankIdentifierPatterns)) {
    if (pattern.test(smsText)) {
      return bankName;
    }
  }
  return null;
}

/**
 * Parses amount from SMS text and removes formatting
 */
export function parseAmountFromSMS(amountText: string): number {
  // Remove commas and spaces, convert to number
  const cleanAmount = amountText.replace(/[,\s]/g, '');
  return parseFloat(cleanAmount);
}

/**
 * Extracts merchant name from SMS text
 */
export function extractMerchantName(smsText: string): string | null {
  const match = smsText.match(commonPatterns.merchantName);
  if (match && match[1]) {
    return match[1].trim();
  }
  return null;
}

/**
 * Extracts reference number from SMS text
 */
export function extractReferenceNumber(smsText: string): string | null {
  const match = smsText.match(commonPatterns.referenceNumber);
  if (match && match[1]) {
    return match[1].trim();
  }
  return null;
}

/**
 * Extracts card number (last 4 digits) from SMS text
 */
export function extractCardNumber(smsText: string): string | null {
  const match = smsText.match(commonPatterns.cardNumber);
  if (match && match[1]) {
    return match[1];
  }
  return null;
}